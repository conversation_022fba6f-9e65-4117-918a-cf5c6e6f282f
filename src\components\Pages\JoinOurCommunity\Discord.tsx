'use client'
import Link from 'next/link'
import Logo from 'public/join/logo.png'
import ImageShortcut from '@/components/Ui/Image'
import { LogoDark } from '../../../../public/marketing'

const Icon = () => (
    <svg width="64" height="48" viewBox="0 0 64 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M54.214 4.02004C50.0713 2.14204 45.6415 0.777227 41.0104 0C40.4417 0.996294 39.7772 2.33635 39.319 3.40237C34.3961 2.68499 29.5182 2.68499 24.6858 3.40237C24.2277 2.33635 23.5481 0.996294 22.9743 0C18.3382 0.777227 13.9034 2.14703 9.76052 4.02992C1.40436 16.264 -0.860818 28.1944 0.271773 39.9551C5.81405 43.9651 11.1851 46.401 16.4655 47.9951C17.7693 46.2566 18.9321 44.4085 19.9338 42.4608C18.026 41.7584 16.1987 40.8917 14.4722 39.8855C14.9302 39.5566 15.3783 39.213 15.8112 38.8593C26.3419 43.6314 37.7837 43.6314 48.1886 38.8593C48.6266 39.213 49.0746 39.5566 49.5276 39.8855C47.796 40.8966 45.9636 41.7634 44.0559 42.4658C45.0576 44.4085 46.2154 46.2616 47.5242 48C52.8097 46.406 58.1857 43.9702 63.728 39.9551C65.0569 26.3214 61.4578 14.5006 54.214 4.02004ZM21.3685 32.7224C18.2073 32.7224 15.6149 29.8631 15.6149 26.3811C15.6149 22.8992 18.1519 20.0349 21.3685 20.0349C24.5852 20.0349 27.1775 22.8941 27.1222 26.3811C27.1271 29.8631 24.5852 32.7224 21.3685 32.7224ZM42.6313 32.7224C39.4701 32.7224 36.8777 29.8631 36.8777 26.3811C36.8777 22.8992 39.4146 20.0349 42.6313 20.0349C45.8479 20.0349 48.4404 22.8941 48.385 26.3811C48.385 29.8631 45.8479 32.7224 42.6313 32.7224Z"
            fill="white"
        />
    </svg>
)
const IconSmall = () => (
    <svg width="48" height="36" viewBox="0 0 48 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M40.6605 3.01503C37.5534 1.60653 34.2311 0.58292 30.7578 0C30.3312 0.747221 29.8329 1.75226 29.4893 2.55178C25.797 2.01374 22.1387 2.01374 18.5143 2.55178C18.1708 1.75226 17.6611 0.747221 17.2308 0C13.7537 0.58292 10.4276 1.61028 7.32039 3.02244C1.05327 12.198 -0.645613 21.1458 0.20383 29.9664C4.36054 32.9738 8.38884 34.8008 12.3492 35.9963C13.327 34.6924 14.1991 33.3064 14.9503 31.8456C13.5195 31.3188 12.1491 30.6688 10.8541 29.9141C11.1977 29.6675 11.5337 29.4098 11.8584 29.1444C19.7564 32.7236 28.3378 32.7236 36.1415 29.1444C36.47 29.4098 36.8059 29.6675 37.1457 29.9141C35.847 30.6724 34.4727 31.3226 33.0419 31.8494C33.7932 33.3064 34.6616 34.6962 35.6431 36C39.6073 34.8045 43.6393 32.9776 47.796 29.9664C48.7927 19.741 46.0933 10.8755 40.6605 3.01503ZM16.0263 24.5418C13.6555 24.5418 11.7111 22.3973 11.7111 19.7858C11.7111 17.1744 13.6139 15.0262 16.0263 15.0262C18.4389 15.0262 20.3831 17.1706 20.3416 19.7858C20.3453 22.3973 18.4389 24.5418 16.0263 24.5418ZM31.9735 24.5418C29.6026 24.5418 27.6583 22.3973 27.6583 19.7858C27.6583 17.1744 29.561 15.0262 31.9735 15.0262C34.3859 15.0262 36.3303 17.1706 36.2888 19.7858C36.2888 22.3973 34.3859 24.5418 31.9735 24.5418Z"
            fill="white"
        />
    </svg>
)

const Discord = () => {
    return (
        <section id="discord" className="container mx-auto -mt-16 overflow-hidden bg-bottom">
            <Link
                aria-label="Discord"
                href="https://discord.com/invite/M9cwwCP49c"
                target="_blank"
                className="bgg relative flex h-[218px] w-[738px] items-center justify-between rounded-xl border border-[#8070D9] bg-[#2647B2] px-20 shadow max-md:hidden">
                <LogoDark />
                <Icon />
                <p className="absolute bottom-7 left-0 right-0 select-none text-center font-sans text-sub2 font-semibold text-green">
                    Join our discord server &gt;
                </p>
            </Link>

            <div className="hidden rounded-xl bg-[#2647B2] max-md:block">
                <Link
                    aria-label="Discord"
                    href="https://discord.com/invite/M9cwwCP49c"
                    target="_blank"
                    className="bgg-small relative h-[270px] w-full items-start justify-center rounded-xl border border-[#8070D9] pt-7 shadow max-md:flex">
                    <div className="flex items-center justify-start gap-4">
                        <ImageShortcut
                            src={Logo}
                            width={1440}
                            height={1440}
                            className={'h-[49.05px] w-[69.25px] object-contain'}
                            alt="Logo"
                        />
                        <span className="pr-2 font-sans text-[21px] leading-[28px] text-white">x</span>
                        <IconSmall />
                    </div>

                    <p className="absolute bottom-7 left-0 right-0 select-none text-center font-sans text-sub2 font-medium text-green">
                        Join our discord server &gt;
                    </p>
                </Link>
            </div>
        </section>
    )
}

export default Discord
