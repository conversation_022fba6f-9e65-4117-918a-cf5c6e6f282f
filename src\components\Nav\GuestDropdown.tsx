'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import ImageShortcut from '../Ui/Image'
import ButtonMobile from '../Ui/ButtonMobile'
import { Disclosure, Transition } from '@headlessui/react'

const GuestDropdown = ({ nav, border, handleOpen }: { nav: any; border: boolean; handleOpen: any }) => {
    const [open, setOpen] = useState(false)
    const handleClick = () => {
        setOpen(!open)
        handleOpen()
    }
    return (
        <Disclosure>
            {({ open }) => (
                <div
                    className={cn(
                        'flex w-full flex-col gap-5 font-sans text-sub3',
                        border && 'border-t border-gray-700 pt-6'
                    )}>
                    <Disclosure.Button
                        className="flex w-full items-center justify-between gap-4"
                        onClick={() => setOpen(!open)}>
                        <p className="font-semibold">{nav.title}</p>
                        <span className="rotate-180">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="10"
                                height="7"
                                fill="none"
                                viewBox="0 0 10 7">
                                <path
                                    stroke="#000"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.5"
                                    d="M9.242 5.5l-4.12-4.083L1 5.5"
                                />
                            </svg>
                        </span>
                    </Disclosure.Button>
                    <Transition
                        show={open}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0">
                        <ul
                            className={cn(
                                'hidden h-0 w-full space-y-4 transition-[height_margin] duration-300',
                                open && 'block h-auto'
                            )}>
                            {nav.items.map((item: any, index: number) => (
                                <li key={index} className="flex w-full flex-col">
                                    {item.type === 'link' ? (
                                        <Link
                                            aria-label={item.title + ' page'}
                                            onClick={handleClick}
                                            href={item.href}
                                            className="flex w-full items-center gap-3"
                                            target={item.target ? '_blank' : ''}>
                                            {item.img !== null && <ImageShortcut src={item.img} alt={item.title} />}
                                            {item.title}
                                        </Link>
                                    ) : (
                                        <Link
                                            aria-label={item.title + ' page'}
                                            onClick={handleClick}
                                            href={item.href}
                                            className="w-full"
                                            target={item.target ? '_blank' : ''}>
                                            <ButtonMobile>{item.title}</ButtonMobile>
                                        </Link>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </Transition>
                </div>
            )}
        </Disclosure>
    )
}

export default GuestDropdown
