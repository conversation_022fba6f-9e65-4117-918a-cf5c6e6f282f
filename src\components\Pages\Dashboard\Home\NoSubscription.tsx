import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'

const NoSubscription = () => {
    return (
        <div className="mb-14 flex w-full flex-col items-center justify-center gap-11 text-center">
            <ImageShortcut src={'/dashboard/empty.png'} width={385} height={315} priority alt="Empty Course Image" />
            <div className="flex max-w-[600px] flex-col items-center gap-4">
                <h1 className="text-headline font-medium">Purchase alpha group today!</h1>
                <p className="px-4 text-sub2 text-gray-700">
                    You don&apos;t have any subscriptions yet, but we can help you find the perfect one.
                </p>
                <div>
                    <Link href="/subscriptions">
                        <Button variant="primary" rounded>
                            Explore Subscriptions
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default NoSubscription
