import QuestionTemplate from '@/components/Pages/Dashboard/Quiz/QuestionTemplate'
import { GetCourses, GetOneCourse, getCourseById, getCurrentLesson } from '@/config/validationCourseDashboard'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { CourseModelV2 } from '@/types/CourseModel'
import { redirect } from 'next/navigation'
import fetch from '@/lib/fetch'

type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
        lesson: string
    }
}
interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

const LessonQuiz = async ({ searchParams, params }: Props) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })
    if (courses === undefined) {
        redirect('/dashboard')
    }

    const courseId = getCourseById({ courses, params })
    const course = await GetOneCourse(user, courseId?.slug ?? '')

    if (courseId === null || course === undefined) {
        redirect('/dashboard')
    }

    const currentLesson = getCurrentLesson({ course, params })
    if ((currentLesson?.quizzes.length ?? 0) === 0) {
        redirect('/dashboard')
    }
    const IsAnswered = async () => {
        try {
            const response = await fetch('/user-quizzes/quiz/' + currentLesson?.quizzes[0].id, {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
    const Answered: Answered = await IsAnswered()
    return (
        <div className="container mx-auto max-h-screen">
            <div className="flex h-full items-center justify-center max-md:py-20 max-md:items-start max-md:justify-start ">
                {currentLesson?.quizzes.map((quiz, index) => {
                    return <QuestionTemplate key={index} quiz={quiz} user={user} Answered={Answered} />
                })}
            </div>
        </div>
    )
}

export default LessonQuiz
