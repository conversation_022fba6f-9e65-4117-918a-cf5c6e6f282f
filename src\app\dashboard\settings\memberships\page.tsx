import Subscription from '@/components/Pages/Dashboard/Settings/Subscription'
import { authOptions } from '@/lib/auth'
import fetchInstance from '@/lib/fetch'
import { getCurrentUser } from '@/lib/session'
import UserSubscriptionModel from '@/types/UserSubscriptionModel'
import { redirect } from 'next/navigation'

interface Model {
    userSubscriptions: UserSubscriptionModel[]
    tax: number
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

const MembershipPage = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const { pageNumber } = searchParams
    const GetUserMemberships = async () => {
        try {
            const pageNumber1 = typeof pageNumber === 'string' && pageNumber !== undefined ? pageNumber : '1'
            const response = await fetchInstance('/auth/profile/subscriptions?pageSize=10&pageNumber=' + pageNumber1, {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response
        } catch (error) {
            console.error('Error Users: ', error)
            return error
        }
    }

    const userSubscriptions: Model = await GetUserMemberships()
    return (
        <div className="container mx-auto py-12">
            <div className="grid grid-cols-12 gap-5">
                <div className="max-md:hidden col-span-12 grid grid-cols-12 place-items-start place-content-center rounded-xl bg-gray-300 px-6 py-4 text-cap2 font-semibold text-gray-700">
                    <p className="col-span-2">PRODUCT </p>
                    <p className="col-span-1">ORDER ID</p>
                    <p className="col-span-2">DATE STARTED</p>
                    <p className="col-span-1">EXPIRY DATE</p>
                    <p className="col-span-1">STATUS</p>
                    <p className="col-span-2">AUTO RENEWAL</p>
                    <p className="col-span-2">PRICE</p>
                    <p className="col-span-1">ACTIONS</p>
                </div>
                {userSubscriptions.userSubscriptions.map((userSubscription, index) => (
                    <div key={index} className="col-span-12">
                        <Subscription user={user} tax={userSubscriptions.tax} userSubscription={userSubscription} />
                    </div>
                ))}
            </div>
        </div>
    )
}

export default MembershipPage
