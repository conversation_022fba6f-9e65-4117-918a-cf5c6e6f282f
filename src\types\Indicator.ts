interface IndicatorModel {
    id: number,
    title: string,
    description: string;
    short_description: string
    image: File | null;
    price: string;
    sale: string;
    status: string;
    video: string;
    about: string;
    visuals: string;
    inputs: string;
    properties: string;
    style: string;
    visibility: string;
    instructor_id: number
    created_at: string;
    updated_at: string;
    priceAfterDiscount: number;
    originalPrice: number
    priceAfterTax: number
}