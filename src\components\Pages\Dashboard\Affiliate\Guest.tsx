import ImageShortcut from "@/components/Ui/Image"
import Link from "next/link"


const Guest = () => {
    return (
        <div>
            <div className="flex justify-center items-center">
                <ImageShortcut
                    src={'/affiliate/treasurebox.png'}
                    width={250}
                    height={250}
                    alt="thumbnail"
                    className="mx-2  object-scale-down  h-250 w-250"
                    priority
                />
            </div>
            <div className="text-center">
                <h1 className="text-black text-headline mb-2 mt-21">Looks like you are not affiliate yet</h1>
                <p className="text-black pt-2 mb-2">Register as an affiliate and start earning today</p>
            </div>
            <div className="flex justify-center items-center">
                <Link
                    aria-label="View products"
                    href={"/dashboard/affiliate/registration"}
                    className="bg-blue hover:bg-[#0040a0]  active:bg-[#017933] text-white rounded-lg text-sub3 font-semibold py-3 px-6 mt-4 transition-colors duration-150"
                >
                    Register As Affiliate
                </Link>
            </div>
            <div className="flex justify-center items-center mt-2 p-2">
                <Link
                    aria-label="Learn More"
                    href={`/affiliates`}
                    className="text-cap1 font-semibold underline-offset-4 text-blue mt-4 underline  focus:underline active:underline">
                    Learn More &gt;
                </Link>
            </div>
        </div>
    )
}

export default Guest
