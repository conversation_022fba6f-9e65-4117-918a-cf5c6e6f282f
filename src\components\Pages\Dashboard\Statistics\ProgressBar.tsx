interface ProgressBarProps {
    percent: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ percent }) => {
    let color = "red";

    if (percent <= 50) {
        color = "orange";
    } else if (percent > 75) {
        color = "green";
    }

    return (
        <div className="relative w-full h-[16px] sm:h-[8px] bg-[#DBDBDB] rounded-full">
            <div
                className={`absolute top-0 left-0 h-full bg-${color}-500 rounded-full transition-all duration-500`}
                style={{ width: `${percent}%`, backgroundColor: `${color}` }}
            />
        </div>
    );
};

export default ProgressBar;


// interface ProgressBarProps {
//     percent: number;
// }
// const ProgressBar: React.FC<ProgressBarProps> = ({ percent }) => {
//     let color = "red";
//     if (percent <= 50) {
//         color = "orange";
//     } else if (percent > 75) {
//         color = "green";
//     }
//     return (
//         <div className="relative w-full h-[12px] sm:h-[4px] bg-[#DBDBDB] rounded-full">
//             <div
//                 className={`absolute top-0 left-0 h-full bg-${color}-500 rounded-full transition-all duration-500`}
//                 style={{ width: `${percent}%`, backgroundColor: `${color}` }}
//             />
//         </div>
//     );
// };
// export default ProgressBar;