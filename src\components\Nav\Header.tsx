'use client'
import Guest from './Guest'
import Link from 'next/link'
import Search from './Search'
import { Logo } from '@public/marketing'
import CartDropdown from './CartDropdown'
import ExploreDropdown from './ExploreDropdown'
import WishlistDropdown from './WishlistDropdown'
import CoinWBannerDesktop from '../Banners/CoinWBannerDesktop'
import CoinWBannerMobile from '../Banners/CoinWBannerMobile'
import CoinWTrading from '../Banners/CoinWTrading'

const Header = () => {
    return (
        <>
            <header className="fixed top-0 z-50 flex h-[80px] w-full select-none items-center border-b border-gray-700 bg-white max-[869px]:hidden">
                <nav className="container mx-auto flex items-center justify-between">
                    <div className="flex items-center gap-[4.5rem] max-xl:gap-0">
                        <Link aria-label='Home page' href="/" className="flex items-center gap-1">
                            <Logo />
                            <span className="font-sans text-sub3 font-semibold">Crypto University</span>
                        </Link>

                        <ul className="flex items-center gap-6 font-sans">
                            <Search />
                            <ExploreDropdown />
                            <li>
                                <Link aria-label='Blog Page' href="/alpha-group" className="text-sub3">
                                    AlphaGroup
                                </Link>
                            </li>
                            <li>
                                <Link aria-label='Blog Page' href="/consultation" className="text-sub3">
                                    Coaching
                                </Link>
                            </li>
                            <li>
                                <Link aria-label='Blog Page' href="/courses" className="text-sub3">
                                    Courses
                                </Link>
                            </li>
                            <li>
                                <Link aria-label='Blog Page' href="/indicators" className="text-sub3">
                                    Signals
                                </Link>
                            </li>
                            <li>
                                <Link aria-label='Blog Page' href="/blog" className="text-sub3">
                                    Blog
                                </Link>
                            </li>

                            {/* <CohortDropdown /> */}
                        </ul>
                    </div>
                    <div className="flex items-center gap-6">
                        <ul className="flex items-center gap-5">
                            <WishlistDropdown />
                            <CartDropdown />
                        </ul>
                        <Guest />
                    </div>
                </nav>
            </header>

            {/* <div className='hidden sm:block absolute'>
                <CoinWBannerDesktop />
            </div>

            <div className='block sm:hidden'>
                <CoinWBannerMobile />
            </div> */}
            <CoinWTrading/>
        </>
    )
}

export default Header