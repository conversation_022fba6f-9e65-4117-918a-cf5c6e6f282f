import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Card from '@/components/Pages/Blog/Card'
import Post from '@/components/Pages/Blog/Post'
import { capitalizeWords } from '@/lib/capitalize'
import { removeMarkdown } from '@/lib/removeMarkdown'
import Sections from '@/components/Pages/Blog/Sections'
import HeroSlug from '@/components/Pages/Blog/HeroSlug'
import GlobalSide from '@/components/Pages/Blog/GlobalSide'
import ImageShortcut from '@/components/Ui/Image'
import { Blogs } from '@/types/BlogModel'
import { BlogPost } from '@/types/BlogPost'


interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}

async function getPostBySlug(slug: string) {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/published/${slug}`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.post
    } catch (error) {
        console.error(error)
        return null
    }
}

async function getRelatedPosts(slug: string) {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/related/${slug}?pageNumber=1&pageSize=3`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.posts
    } catch (error) {
        console.error(error)
        return null
    }
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug as string
    if (slug !== undefined)
        try {
            const post: BlogPost = await getPostBySlug(slug)
            if (post === null) return {}
            else if (post.image !== null) {
                return {
                    title: capitalizeWords(post.title),
                    description: removeMarkdown(post.excerpt),
                    openGraph: {
                        title: capitalizeWords(post.title),
                        description: removeMarkdown(post.excerpt),
                        type: 'website',
                        url: post.slug,
                        images: [
                            {
                                url: post.image,
                                width: 1928,
                                height: 1004,
                                alt: post.title,
                            },
                        ],
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: capitalizeWords(post.title),
                        description: removeMarkdown(post.excerpt),
                        images: [post.image],
                    },
                }
            } else {
                return {
                    title: capitalizeWords(post.title),
                    description: removeMarkdown(post.excerpt),
                    openGraph: {
                        title: capitalizeWords(post.title),
                        description: removeMarkdown(post.excerpt),
                        type: 'website',
                        url: post.slug,
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: capitalizeWords(post.title),
                        description: removeMarkdown(post.excerpt),
                    },
                }
            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const BlogPostPage = async ({ params }: { params: PageProps }) => {
    const { slug } = params
    const post = await getPostBySlug(slug);
 
    if (post === null) {
        console.error("Post not found for slug:", slug);
        notFound();
    }

    const blogs: Blogs = await getRelatedPosts(slug)

    return (
        <>
            <HeroSlug />

            <div className="container mx-auto flex justify-between gap-16 pb-14 pt-11 max-md:flex-col max-md:gap-16 max-md:pb-12 max-md:pt-4">
                <Post post={post} />
                <GlobalSide />
            </div>

            <Sections>
                <div className="container mx-auto grid grid-cols-3 gap-8 max-md:grid-cols-2 max-sm:grid-cols-1">
                    {(blogs || []).map((blog, index) => (
                        <Card key={index} {...blog} />
                    ))}
                </div>
            </Sections>
        </>
    )
}

export default BlogPostPage
