import { Blogs } from '@/types/BlogModel'
import Image from 'next/image'
import Link from 'next/link'
import { MdKeyboardArrowRight } from 'react-icons/md'

const FeaturedArticle = ({ blogs }: { blogs: Blogs }) => {
  
    const truncateText = (text: string, length: number) => {
        if (text.length <= length) {
          return text;
        }
        return text.substring(0, length) + '...';
      };

    return (
        <div className="p-[32px] md:p-[64px]">
            <section className="mx-auto w-full">
                <h2 className="my-3 text-[22px] font-[600] md:mb-5 md:text-center md:text-[36px]">Featured Articles</h2>
            </section>
            <section className="grid gap-4 md:grid-cols-3">
                {blogs.length > 0 &&
                    blogs.map((article: any, index: any) => (
                        <section
                            key={index}
                            className="grid max-w-[344px] items-center rounded-[8px] border-[1px] border-[#FCC229] bg-[#FEEB8810] p-8 md:grid md:max-w-[664px] md:text-left">
                            <section className="mb-3 flex gap-3">
                                {article.categories &&
                                    article.categories.length > 0 &&
                                    article.categories.map((category: any, categoryIndex: any) => (
                                        <span
                                            key={categoryIndex}
                                            className="inline-flex h-8 items-center rounded-[64px] border-[1px] border-[#FCC229] bg-[#FCC22933] px-3 text-[12px] text-[#BD921F]">
                                            {category.category.name}
                                        </span>
                                    ))}
                            </section>

                            <div>
                                <Image
                                    src={article.image || ''}
                                    alt={article.title || ''}
                                    width={260}
                                    height={146.65}
                                    className="mt-3 block rounded-[4.0px]"
                                />
                                    <div className="mt-1 text-[18px] font-[600] md:mt-3" dangerouslySetInnerHTML={{ __html: article.title }} />
                                    <div className="my-3 text-[14px] font-[400] text-[#5B5B5B]" dangerouslySetInnerHTML={{ __html: truncateText(article.excerpt, 150) }} />
                                <Link className="float-right text-[#2655FF] md:float-none" href={`/blog/${article.slug}`}>Read More</Link>
                            </div>
                        </section>
                    ))}
            </section>

            <div className="relative mx-auto w-full justify-center text-center">
                <Link href={'/blog'} className="mx-auto mt-6 flex h-[48px] w-[284px] items-center justify-center rounded-[8px] border-[1px] border-[#2655FF] text-center text-[14px] font-[600] text-[#2655FF] md:h-[62px] md:w-[344px]">
                    Explore More Blogs <MdKeyboardArrowRight className="mb-[2px] h-5 w-5 md:h-6 md:w-6" />
                </Link>
            </div>
        </div>
    )
}

export default FeaturedArticle