'use client'
import Link from 'next/link'
import { useState, useEffect } from 'react'
import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { CourseModel } from '@/types/CourseModel'
import { Heart } from '../../../../public/global'
import useWishlist from '@/hooks/useWishlist'

interface Props {
    course: CourseModel
}
const Title = ({ course }: Props) => {
    const wishlist = useWishlist()

    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])
    const OnAddToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.addproduct(course)
    }
    const OnRemoveToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.removeproduct(course.id)
    }
    return (
        <div className=" container mx-auto   w-full ">
            <div className="flex items-center gap-12 border-gray-700  py-11 max-md:flex-col max-md:items-start max-md:gap-6 max-md:border-b max-md:px-0 max-md:py-9">
                <ImageShortcut
                    src={course.image}
                    width={235}
                    priority
                    height={235}
                    className={'max-md:h-auto max-md:w-[162px]'}
                    alt={'Profile Picture of the course'}
                />
                <div className="flex w-[780px] flex-col gap-6 max-md:w-full max-md:gap-3">
                    <h1 className="text-headline font-medium capitalize max-md:text-callout">{course.name}</h1>
                    <p className="font-sans text-sub1 text-gray-900 max-md:text-sub3">{course.short_description}</p>
                    <div className="flex items-center gap-2 font-sans md:hidden">
                        {course.sale && course?.sale > 0 ? (
                            <p className=" text-cap1 text-gray-700">${course.priceWithoutTax}</p>
                        ) : (
                            ''
                        )}

                        <p className="text-callout font-semibold">
                            ${course.finalPrice - (parseInt(course.price ?? '0') - course.priceWithoutTax)}
                        </p>
                        {course.sale && course?.sale > 0 ? (
                            <p className="text-cap1 text-green-dark">(-{course.sale}%)</p>
                        ) : (
                            ''
                        )}
                    </div>
                    <div className="hidden max-md:block">
                        <Link aria-label="Start Now" href={'/checkout/course/' + course.slug}>
                            <Button variant="primary" rounded>
                                Start Now
                            </Button>
                        </Link>
                    </div>
                    <div className="hidden max-md:block">
                        {isMounted ? (
                            wishlist.productExists(course) ? (
                                <>
                                    <Button className="flex items-center" rounded onClick={OnRemoveToWishlist}>
                                        <span>
                                            <Heart active={true} />
                                        </span>
                                        Wishlisted
                                    </Button>
                                </>
                            ) : (
                                <Button className="flex items-center" rounded onClick={OnAddToWishlist}>
                                    <span>
                                        <Heart active={false} />
                                    </span>
                                    Add to wishlist
                                </Button>
                            )
                        ) : (
                            ''
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Title
