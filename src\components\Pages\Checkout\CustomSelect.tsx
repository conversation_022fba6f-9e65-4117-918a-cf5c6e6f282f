import Select, { GroupBase, StylesConfig } from 'react-select'
import { FieldAttributes, FormikProps } from '@/lib/formik'
type OptionType = {
    value: string
    label: string | React.JSX.Element
}

interface CustomSelectProps extends FieldAttributes<any> {
    label?: string
    options: OptionType[]
    formik: FormikProps<any>
    style: StylesConfig<OptionType, false, GroupBase<OptionType>>
}

const CustomSelect: React.FC<CustomSelectProps> = ({
    label,
    options,
    formik,
    style,
    isDisabled,
    isSearchable,
    defaultValue,
    setIsFocus,
    ...props
}) => {
    const handleChange = (selectedOption: OptionType | null) => {
        formik.setFieldValue(props.name, selectedOption ? selectedOption.value : null)
    }

    const handleBlur = () => {
        formik.setFieldTouched(props.name, true)
        setIsFocus(false)
    }

    return (
        <Select
            {...props}
            onChange={handleChange}
            onBlur={handleBlur}
            options={options}
            isDisabled={isDisabled}
            instanceId={props.name}
            value={options.find(option => option.value === props.value)}
            theme={theme => ({ ...theme, borderRadius: 0 })}
            placeholder={label}
            isSearchable={isSearchable}
            styles={{
                ...style,
            }}
            openMenuOnFocus
            onFocus={() => {
                if (props.name === 'phonePreffix' || props.name === 'phoneSuffix') {
                    formik.setFieldTouched('phonePreffix', false)
                    formik.setFieldTouched('phoneSufffix', false)
                } else {
                    formik.setFieldTouched(props.name, false)
                }
                setIsFocus(true)
            }}
        />
    )
}

export default CustomSelect
