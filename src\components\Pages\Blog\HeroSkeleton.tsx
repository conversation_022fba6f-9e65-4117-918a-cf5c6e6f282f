const HeroSkeleton = () => {
    return (
        <section className="relative mb-5 bg-yellow-light py-[4.5rem] pt-[4.8125rem] max-sm:mb-[5.25rem]">
            <div className="container mx-auto">
                <div className="space-y-8">
                    <div className="text-h2 font-medium max-md:text-b2 h-[28px] w-full max-w-[400px] bg-gray-500/60 rounded" />

                    <div className="relative flex items-center max-sm:absolute max-sm:-bottom-6 max-sm:mx-auto max-sm:w-[calc(100vw-32px)]">
                        <div className="h-[50px] w-[319px] animate-pulse rounded-full bg-gray-500/60 leading-[20.8px] shadow-[2px_4px_30px_0px_#0000001A] outline-none max-md:w-full" />
                    </div>
                </div>
            </div>
        </section>
    )
}

export default HeroSkeleton
