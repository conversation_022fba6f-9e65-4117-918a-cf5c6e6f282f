import { NextResponse } from 'next/server'

export async function GET() {
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.WHOP_BEARER}`,
        },
    }

    const response = await (
        await fetch((process.env.WHOP_PRODUCT_API as string) + 'prod_B4FkZoS6b6Q6X?expand=plans', options)
    ).json()

    return NextResponse.json(
        {
            icon: {
                src: '/memberships/card3.png',
                alt: 'Alpha Group 6 Months Plan Icon',
            },
            billed: {
                text: '6 Months',
                color: 'white',
            },
            price: {
                text: "349",
                color: 'white',
            },
            sub: {
                text: "Billed <strong>$2,099</strong> at once",
                color: 'white',
            },
            button: {
                text: "Get 6 Months",
                color: 'white',
            },
            plan: {
                text: '30% off',
                color: 'white',
            },
          
            // plans: response.plans[0].direct_link,
            plans: '/checkout/subscription/' + 'alpha-group-6-months' + '?previous=' + '/subscriptions',
        },
        { status: 200 }
    )
}
