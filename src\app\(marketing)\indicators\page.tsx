import Indicators from '@/components/Pages/Indicators/Indicators'
import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'

export const metadata = {
    title: 'Indicators',
    description:
        "Crypto University is the world's #1 Cryptocurrency education platform. Get access to our customized courses to help you succeed in crypto.",
    keywords: ['Indicators', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const GetIndicators = async () => {
    try {
        const response = await fetchInstance('/indicator/all/published', {
            next: { revalidate: 10 },
        })
        return response.indicator
    } catch (error) {
        console.error('Error Calls:', error)
        return error
    }
}

const IndicatorsPage = async () => {
    const indicators = await GetIndicators()
    return (
        <section className="flex w-full flex-col text-black">
            <Indicators indicators={indicators} />
            <CTA />
        </section>
    )
}

export default IndicatorsPage
