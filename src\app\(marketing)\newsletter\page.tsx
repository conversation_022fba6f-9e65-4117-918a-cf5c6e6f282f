import getCountryInfo from '@/lib/countries'
import { Country } from '@/types/CountryModel'
import Hero from '@/components/Pages/Newsletter/Hero'
import Contact from '@/components/Pages/JoinOurCommunity/Contact'
import Socials from '@/components/Pages/JoinOurCommunity/Socials'

export const metadata = {
    title: 'Newsletter',
    description:
        'Sign up to our newsletter to receive weekly crypto news, events, community updates ... Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
    keywords: [
        'Newsletter',
        'Crypto University',
        'Crypto U',
        'Crypto',
        'Blockchain',
        'Cryptocurrency',
        'Earn',
        'Trade',
        'Invest',
    ],
}

const NewsletterPage = async () => {
    const countries: Country[] = await getCountryInfo()

    return (
        <section className="flex w-full flex-col text-black">
            <Hero countries={countries} />
            <Socials />
            <Contact />
        </section>
    )
}

export default NewsletterPage
