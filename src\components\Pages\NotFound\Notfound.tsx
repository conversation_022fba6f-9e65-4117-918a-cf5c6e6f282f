import ImageShortcut from '@/components/Ui/Image'
import Link from 'next/link'
import NotFoundImage from 'public/global/404.png'

const Notfound = () => {
    return (
        <section className="container mx-auto flex items-center justify-between pt-20 text-black max-md:flex-col-reverse max-md:justify-start max-md:self-start max-md:pb-10 max-md:pt-0 md:flex-grow">
            <div className="space-y-[3.25rem] max-md:space-y-8">
                <p className="max-w-[633px] text-h3 font-semibold max-md:text-b1">
                    Sorry! The page you’re looking for cannot be found.
                </p>
                <Link
                    aria-label="Go to Homepage"
                    href="/"
                    className="gradient flex w-[310px] items-center justify-center rounded-full py-5 font-sans text-sub1 text-white max-md:w-[267px] max-md:py-4 max-md:text-sub2">
                    Go to Homepage
                </Link>
            </div>

            <div className="flex justify-end max-md:justify-start">
                <ImageShortcut
                    src={NotFoundImage}
                    width={520}
                    height={520}
                    alt="404"
                    priority
                    className="max-md:h-auto max-md:w-[336px]"
                />
            </div>
        </section>
    )
}

export default Notfound
