import Link from "next/link"
import Image from "../Ui/Image"
import Button from "../Ui/ButtonNav"
import { Call } from "../../../public/global"
import { Menu, Transition } from "@/lib/headlessui"
import { Noones,Brazil,COinW, UAE, USA, Zambia, UK } from "../../../public/countries"
import { cn } from "@/lib/cn"

interface Props {
    img: any
    name: string
    link: string
    blank: boolean
}
const countries = [
    {
        img: COinW,
        name: 'CoinW Trading Competition',
        link: '/coinw-trading-competition',
        blank: false,
    },
    {
        img: Noones,
        name: 'P2P Academy',
        link: '/noones-p2p-academy',
        blank: false,
    },
    {
        img: Zambia,
        name: "Zambia Dev Program",
        link: "/zambia-web3-developer-programme",
        blank: false
    }
    ,
    {
        img: Zambia,
        name: "Zambia Bootcamp",
        link: "/bootcamp",
        blank: false
    },
    {
        img: USA,
        name: "USA",
        link: '/get-in-touch',
        blank: false
    },
    {
        img: UK,
        name: "UK",
        link: '/get-in-touch',
        blank: false
    },
    {
        img: Brazil,
        name: "Brazil",
        link: '/get-in-touch',
        blank: false
    },
    {
        img: UAE,
        name: "UAE",
        link: '/get-in-touch',
        blank: false
    },
]
const Country = ({ img, name, link, blank }: Props) => (
    <Link aria-label="Country page" href={link} target={blank ? "_blank" : undefined} className="w-full">
        <Menu.Item as="li">
            {({ active }) => (
                <div className="w-full flex items-center gap-[0.625rem] transition-[color] duration-150">
                    <Image
                        src={img}
                        alt={name}
                        width={31}
                        height={30}
                        className="rounded-full"
                    />
                    <span className={cn("text-sub3", active && "text-blue")}>{name}</span>
                </div>
            )}
        </Menu.Item>
    </Link>
)

const CohortDropdown = () => {
    return (
        <Menu as="li">
            {({ open, close }) => (
                <>
                    <Menu.Button className={"cursor-pointer text-sub3 flex items-center gap-[0.625rem] transition-[font-weight] duration-300"}>
                        Cohort Program
                        <svg className={cn(open && "rotate-180", "transition[transform] duration-150")} xmlns="http://www.w3.org/2000/svg" width="15" height="8" fill="none" viewBox="0 0 15 8">
                            <path stroke="#2655FF" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M13.736 1l-6 6-6-6" />
                        </svg>
                    </Menu.Button>
                    <Transition
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                    >
                        <Menu.Items className="absolute max-lg:right-0 select-none text-sub3 mt-10 p-6 max-w-[760px] font-sans space-y-6 bg-white rounded-[0.5rem] shadow-[0px_40px_80px_rgba(0,0,0,0.1)]">
                            <ul className="flex flex-col gap-4">
                                {countries.map((country, i) => (
                                    <Country key={i} img={country.img} name={country.name} link={country.link} blank={country.blank} />
                                ))}
                            </ul>
                            <div className="w-[343px] rounded-full" onClick={close}>
                                <Button href="/get-in-touch">
                                    <Call />
                                    <span className="text-sub3 text-blue leading-none">Get in Touch</span>
                                </Button>
                            </div>
                        </Menu.Items>
                    </Transition>
                </>
            )}
        </Menu>
    )
}

export default CohortDropdown