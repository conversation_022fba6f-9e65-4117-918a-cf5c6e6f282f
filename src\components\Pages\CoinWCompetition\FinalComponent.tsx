"use client"
import Link from 'next/link'
import ImageShortcut from '@/components/Ui/Image'
import { cryptoULogo, coinw } from '@public/coinw-trading-competion'
import { GetCurrentUserClient } from '@/lib/session'

const FinalComponent = () => {
    const user = GetCurrentUserClient()
    return (
        <section className="px-12 py-6 sm:py-12 text-white flex flex-col items-center">  {/* Use flex-col for vertical alignment */}
            <div className="ml-6 flex flex-col items-center sm:flex-row sm:items-start">
                <div className="max-w-md">
                    <ImageShortcut
                        src={cryptoULogo}
                        width={100}
                        height={100}
                        alt="thumbnail"
                        className="object-contain h-100 w-100 p-2"
                        priority={true}
                    />
                </div>
                <div className="divider divider-horizontal"></div>
                <div className="max-w-md ">
                    <ImageShortcut
                        src={coinw}
                        width={150}
                        height={130}
                        alt="thumbnail"
                        className="object-contain h-130 w-auto p-2 -mt-4"
                        priority={true}
                    />
                </div>
            </div>
            <div className="text-center w-full">
                <div className="bg-gradient-to-r from-transparent via-[#544717] py-3 to-transparent mt-[5rem] opacity-100">
                    <h1 className="text-b2 sm:text-[36px] font-bold mt-2 mx-1 sm:mx-[9rem] pl-1 sm:pl-12 text-white">CoinW Trading Competition</h1>
                </div>

                <h1 className="hidden sm:block text-sub3  mt-4 mb-6">Open to all CU traders, no matter your experience.</h1>
                <h1 className="text-sub1 sm:text-sub3 font-bold mt-10">April 17th 2024 - May 28th 2024</h1>
            </div>

            {user ? (
                <Link
                    aria-label="View products"
                    target="_blank"
                    href={'https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform?pli=1'}
                    // href={'https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US'}
                    className="bg-[#FCC229] text-black hover:bg-yellow-600 mx-2 sm:mx-12 active:bg-yellow-800 rounded-lg text-sub3 sm:text-sub2 font-semibold py-3 px-2 sm:px-8 mt-6 transition-colors duration-150 inline-block"
                >
                    Register Here {'>'}
                </Link>) : (<Link
                    aria-label="View products"
                    href={{ pathname: '/login', query: { source: 'coinw' } }}
                    className="bg-[#FCC229] text-black hover:bg-yellow-600 mx-2 sm:mx-12 active:bg-yellow-800 rounded-lg text-sub3 sm:text-sub2 font-semibold py-3 px-2 sm:px-8 mt-6 transition-colors duration-150 inline-block"
                >
                    Register Here {'>'}
                </Link>)}

        </section>
    )
}

export default FinalComponent
