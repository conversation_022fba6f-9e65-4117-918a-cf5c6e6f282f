import { MembershipModel } from '@/types/MembershipModel'
import Card from './Card'
import SwiperCards from './SwiperCards'

const Pricing = async ({ data }: { data: MembershipModel[] }) => {


    return (
        <div id="plans" className="pb- container mx-auto flex flex-col gap-[106px] max-sm:gap-10">
            <div className="flex flex-col items-center gap-4">
                <h1 className="text-b3 font-semibold uppercase text-[#4A3AFF] max-sm:text-cap1">pricing</h1>
                <p className="text-center text-[42px] font-medium leading-[142%] max-sm:text-sub2">
                    One Membership. Unlimited Returns.
                </p>
            </div>
            <div className="flex w-full items-center justify-between max-md:hidden">
                {data.map((item, index) => (
                    <Card key={index} item={item} index={index} />
                ))}
            </div>
            <SwiperCards data={data} />
        </div>
    )
}

export default Pricing
