import Counter from "@/components/Ui/Counter";
import {
  Rating,
  Profile,
  RatingMobile,
  TrustPilotMobile,
  TrustPilot,
} from "../../../../public/home";
import { Counters as Counterss } from "@/components/Ui/Counters";
import { Success } from "../../../../public/global";

export const Counters = ({
  step,
  className,
}: {
  step: number;
  className: string;
}) => {
  return (
    <div className={className}>
      {step == -1 ? (
        <Counterss />
      ) : step == 0 || step == 1 ? (
        <div className="flex gap-[0.875rem] max-md:gap-2">
          <div className="flex flex-col items-center max-md:items-start">
            <span className="text-cap1 max-md:text-cap4">Rated</span>
            <p className="text-b1 max-md:text-sub1 font-semibold">4.5</p>
            <span className="text-cap1 max-md:text-cap4">Out of 5</span>
          </div>
          <div className="hidden max-md:flex flex-col gap-1 py-[2px] justify-end">
            <TrustPilotMobile />
            <RatingMobile />
          </div>
          <div className="max-md:hidden flex flex-col gap-3 py-[2px] justify-end">
            <TrustPilot />
            <Rating />
          </div>
        </div>
      ) : step == 2 ? (
        <span className="flex gap-1 items-center">
          <Success />
          <span> {"Don’t worry, we don’t spam!"}</span>{" "}
        </span>
      ) : step == 3 ? (
        <Counter
          icon={<Profile />}
          number={30000}
          unit={"Members"}
          className="w-full !text-b1 lg:justify-start justify-center"
        />
      ) : (
        ""
      )}
    </div>
  );
};
