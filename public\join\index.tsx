const Discord = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M50.7431 15.4338C47.2476 13.8297 43.51 12.6639 39.6025 12C39.1226 12.851 38.562 13.9956 38.1754 14.9062C34.0217 14.2934 29.906 14.2934 25.8286 14.9062C25.4421 13.9956 24.8688 12.851 24.3846 12C20.4729 12.6639 16.731 13.8339 13.2354 15.4422C6.18493 25.8922 4.27369 36.0827 5.22931 46.1284C9.90561 49.5535 14.4374 51.6342 18.8928 52.9958C19.9929 51.5108 20.9739 49.9323 21.8191 48.2686C20.2095 47.6687 18.6677 46.9283 17.2109 46.0689C17.5974 45.788 17.9755 45.4944 18.3407 45.1923C27.2259 49.2685 36.88 49.2685 45.6591 45.1923C46.0287 45.4944 46.4067 45.788 46.7889 46.0689C45.3279 46.9325 43.7818 47.6729 42.1722 48.2729C43.0174 49.9323 43.9943 51.5151 45.0985 53C49.5582 51.6385 54.0942 49.5579 58.7705 46.1284C59.8918 34.4828 56.855 24.3859 50.7431 15.4338ZM23.0296 39.9504C20.3624 39.9504 18.175 37.508 18.175 34.5338C18.175 31.5598 20.3156 29.1132 23.0296 29.1132C25.7437 29.1132 27.931 31.5554 27.8843 34.5338C27.8885 37.508 25.7437 39.9504 23.0296 39.9504ZM40.9702 39.9504C38.3029 39.9504 36.1156 37.508 36.1156 34.5338C36.1156 31.5598 38.2561 29.1132 40.9702 29.1132C43.6842 29.1132 45.8716 31.5554 45.8249 34.5338C45.8249 37.508 43.6842 39.9504 40.9702 39.9504Z" fill="#5865F2" />
    </svg>
)
const Facebook = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M32.5 59C46.5832 59 58 47.1355 58 32.5C58 17.8645 46.5832 6 32.5 6C18.4168 6 7 17.8645 7 32.5C7 47.1355 18.4168 59 32.5 59Z" fill="url(#paint0_linear_175_5)" />
        <path d="M41.9964 40.6047L43.1292 33.1251H36.0431V28.2735C36.0431 26.2268 37.0312 24.2305 40.205 24.2305H43.4286V17.8628C43.4286 17.8628 40.5043 17.3574 37.7099 17.3574C31.8713 17.3574 28.0589 20.9405 28.0589 27.4244V33.1251H21.5715V40.6047H28.0589V58.6869C29.3612 58.8942 30.6936 59.0002 32.0509 59.0002C33.4082 59.0002 34.7408 58.8942 36.0431 58.6869V40.6047H41.9964Z" fill="white" />
        <defs>
            <linearGradient id="paint0_linear_175_5" x1="32.5" y1="6" x2="32.5" y2="58.8428" gradientUnits="userSpaceOnUse">
                <stop stopColor="#18ACFE" />
                <stop offset="1" stopColor="#0163E0" />
            </linearGradient>
        </defs>
    </svg>
)
const Instagram = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_176_31)">
            <path d="M32.5 15.6951C37.9735 15.6951 38.6227 15.7156 40.7854 15.8147C46.3409 16.0675 48.9359 18.7035 49.1887 24.218C49.2878 26.379 49.3066 27.0282 49.3066 32.5017C49.3066 37.9769 49.2861 38.6244 49.1887 40.7854C48.9342 46.2948 46.346 48.9359 40.7854 49.1887C38.6227 49.2878 37.9769 49.3083 32.5 49.3083C27.0265 49.3083 26.3773 49.2878 24.2163 49.1887C18.6471 48.9342 16.0658 46.2863 15.813 40.7837C15.7139 38.6227 15.6934 37.9752 15.6934 32.5C15.6934 27.0265 15.7156 26.379 15.813 24.2163C16.0675 18.7035 18.6557 16.0658 24.2163 15.813C26.379 15.7156 27.0265 15.6951 32.5 15.6951ZM32.5 12C26.9325 12 26.2355 12.0239 24.0489 12.123C16.604 12.4647 12.4664 16.5954 12.1247 24.0472C12.0239 26.2355 12 26.9325 12 32.5C12 38.0675 12.0239 38.7662 12.123 40.9528C12.4647 48.3978 16.5954 52.5353 24.0472 52.877C26.2355 52.9761 26.9325 53 32.5 53C38.0675 53 38.7662 52.9761 40.9528 52.877C48.3909 52.5353 52.5388 48.4046 52.8753 40.9528C52.9761 38.7662 53 38.0675 53 32.5C53 26.9325 52.9761 26.2355 52.877 24.0489C52.5422 16.6108 48.4063 12.4664 40.9545 12.1247C38.7662 12.0239 38.0675 12 32.5 12ZM32.5 21.9732C26.6865 21.9732 21.9732 26.6865 21.9732 32.5C21.9732 38.3135 26.6865 43.0285 32.5 43.0285C38.3135 43.0285 43.0268 38.3152 43.0268 32.5C43.0268 26.6865 38.3135 21.9732 32.5 21.9732ZM32.5 39.3333C28.7263 39.3333 25.6667 36.2754 25.6667 32.5C25.6667 28.7263 28.7263 25.6667 32.5 25.6667C36.2737 25.6667 39.3333 28.7263 39.3333 32.5C39.3333 36.2754 36.2737 39.3333 32.5 39.3333ZM43.4436 19.0981C42.0838 19.0981 40.9819 20.2 40.9819 21.5581C40.9819 22.9162 42.0838 24.0181 43.4436 24.0181C44.8017 24.0181 45.9019 22.9162 45.9019 21.5581C45.9019 20.2 44.8017 19.0981 43.4436 19.0981Z" fill="black" />
        </g>
        <defs>
            <clipPath id="clip0_176_31">
                <rect width="41" height="41" fill="white" transform="translate(12 12)" />
            </clipPath>
        </defs>
    </svg>
)
const Telegram = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M57.9091 32.4999C57.9091 46.5385 46.5386 57.909 32.5 57.909C18.4615 57.909 7.09094 46.5385 7.09094 32.4999C7.09094 18.4614 18.4615 7.09082 32.5 7.09082C46.5386 7.09082 57.9091 18.4614 57.9091 32.4999Z" fill="url(#paint0_linear_175_10)" />
        <path d="M27.8416 44.1452C27.0158 44.1452 27.1641 43.8275 26.8676 43.0441L24.4537 35.0826L39.4451 25.7236L41.2026 26.1894L39.7416 30.1702L27.8416 44.1452Z" fill="#C8DAEA" />
        <path d="M27.8418 44.1459C28.477 44.1459 28.7523 43.8495 29.1122 43.5107C29.6627 42.9814 36.735 36.0997 36.735 36.0997L32.3943 35.041L28.3711 37.5819L27.8418 43.9342V44.1459Z" fill="#A9C9DD" />
        <path d="M28.265 37.6658L38.5133 45.225C39.6779 45.8602 40.5249 45.5426 40.8213 44.1451L44.9926 24.4955C45.4161 22.7803 44.3362 22.018 43.214 22.5262L18.7366 31.9699C17.0638 32.6475 17.085 33.5792 18.4401 33.9815L24.7289 35.9507L39.2756 26.7822C39.9531 26.3588 40.5884 26.5916 40.0802 27.0575L28.265 37.6658Z" fill="url(#paint1_linear_175_10)" />
        <defs>
            <linearGradient id="paint0_linear_175_10" x1="39.947" y1="15.1222" x2="27.2424" y2="44.7657" gradientUnits="userSpaceOnUse">
                <stop stopColor="#37AEE2" />
                <stop offset="1" stopColor="#1E96C8" />
            </linearGradient>
            <linearGradient id="paint1_linear_175_10" x1="34.4036" y1="33.2943" x2="39.6972" y2="41.764" gradientUnits="userSpaceOnUse">
                <stop stopColor="#EFF7FC" />
                <stop offset="1" stopColor="white" />
            </linearGradient>
        </defs>
    </svg>
)
const Twitter = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M55 18.3802C53.3465 19.1231 51.5663 19.6255 49.6988 19.85C51.6059 18.6955 53.0692 16.8648 53.7585 14.6841C51.973 15.7531 50 16.5308 47.8922 16.9477C46.2124 15.1331 43.8114 14 41.1543 14C36.0591 14 31.9255 18.1824 31.9255 23.3402C31.9255 24.0724 32.0048 24.7834 32.1633 25.4675C24.4902 25.0773 17.6888 21.3626 13.1326 15.705C12.3376 17.0894 11.8833 18.6955 11.8833 20.4059C11.8833 23.6449 13.513 26.5044 15.9905 28.18C14.4797 28.1346 13.0533 27.7096 11.8066 27.0149V27.1297C11.8066 31.6569 14.9895 35.4331 19.2156 36.2882C18.4416 36.5073 17.6255 36.6169 16.7829 36.6169C16.1886 36.6169 15.6075 36.5608 15.0449 36.4513C16.2203 40.1606 19.6276 42.8625 23.6688 42.9346C20.5098 45.4413 16.5267 46.9353 12.2028 46.9353C11.458 46.9353 10.7211 46.8925 10 46.807C14.0861 49.4527 18.9409 51 24.1521 51C41.1357 51 50.4199 36.7693 50.4199 24.4252C50.4199 24.019 50.412 23.6128 50.3962 23.2146C52.2002 21.8971 53.7665 20.2535 55 18.3802Z" fill="#00AAEC" />
    </svg>
)
const Whatsapp = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M52.3847 12.2305C47.0602 6.92609 39.9802 4.00323 32.4368 4C16.8928 4 4.24299 16.5893 4.2368 32.0633C4.23498 37.0097 5.53319 41.838 8.00073 46.0941L4 60.6367L18.9492 56.7343C23.068 58.9705 27.7055 60.1489 32.4247 60.1508H32.4367C47.9783 60.1508 60.6301 47.5594 60.6367 32.086C60.6392 24.5867 57.7086 17.5352 52.3847 12.2305ZM32.4368 55.4108H32.4277C28.2212 55.4086 24.0961 54.2843 20.4981 52.1588L19.6419 51.6535L10.7707 53.969L13.1391 45.3614L12.5812 44.479C10.2348 40.7648 8.99612 36.4725 8.99784 32.0651C9.00301 19.2036 19.517 8.74019 32.4457 8.74019C38.7065 8.74281 44.5908 11.1723 49.0165 15.5811C53.4417 19.9899 55.877 25.8509 55.8752 32.084C55.8697 44.9459 45.3553 55.4108 32.4368 55.4108Z" fill="#E0E0E0" />
        <path d="M5.29443 59.022L9.11241 45.1436C6.75694 41.082 5.51826 36.4737 5.51978 31.7543C5.52596 16.9875 17.598 4.97363 32.4307 4.97363C39.6297 4.97686 46.3865 7.76595 51.4667 12.8282C56.5479 17.8905 59.3445 24.6192 59.3416 31.7752C59.3355 46.5425 47.2627 58.5575 32.4318 58.5575C32.4325 58.5575 32.4307 58.5575 32.4318 58.5575H32.4198C27.9161 58.556 23.4908 57.4305 19.5605 55.298L5.29443 59.022Z" fill="url(#paint0_linear_175_17)" />
        <path fillRule="evenodd" clipRule="evenodd" d="M25.4683 20.148C24.9466 18.9932 24.3972 18.9701 23.9009 18.9499C23.4949 18.9325 23.0302 18.9336 22.5661 18.9336C22.1017 18.9336 21.3471 19.1074 20.7089 19.8012C20.0703 20.4951 18.2708 22.1725 18.2708 25.584C18.2708 28.9958 20.7669 32.2926 21.1148 32.7556C21.4631 33.2179 25.9335 40.4427 33.0131 43.2221C38.8969 45.5319 40.0943 45.0725 41.3713 44.9569C42.6484 44.8413 45.4922 43.2798 46.0727 41.6606C46.6532 40.0416 46.6532 38.6539 46.4791 38.3638C46.3049 38.0749 45.8406 37.9015 45.1439 37.5548C44.4473 37.208 41.023 35.5303 40.3845 35.2991C39.7459 35.0679 39.2815 34.9523 38.8172 35.6466C38.3527 36.3402 37.0186 37.9015 36.6123 38.3638C36.2059 38.8273 35.7996 38.8851 35.1029 38.5383C34.4063 38.1905 32.163 37.459 29.5018 35.0968C27.4312 33.259 26.0332 30.9892 25.6268 30.295C25.2204 29.6015 25.5833 29.2259 25.9327 28.8802C26.2454 28.5696 26.6293 28.0707 26.9776 27.6659C27.3252 27.2609 27.4412 26.9719 27.6735 26.5096C27.9057 26.0465 27.7896 25.6417 27.6154 25.295C27.4412 24.9481 26.0876 21.5191 25.4683 20.148Z" fill="white" />
        <path d="M52.1543 12.1399C46.8909 6.89638 39.8921 4.00714 32.4355 4.00391C17.0698 4.00391 4.56522 16.4486 4.55904 31.7449C4.55721 36.6346 5.84052 41.4075 8.27976 45.6147L4.32507 59.9904L19.1027 56.1327C23.1742 58.3433 27.7584 59.5082 32.4235 59.5099H32.4353C47.7984 59.5099 60.3051 47.0631 60.3115 31.7674C60.3141 24.3542 57.4171 17.3837 52.1543 12.1399ZM32.4355 54.8245H32.4264C28.2682 54.8223 24.1905 53.7109 20.6338 51.6099L19.7872 51.1103L11.018 53.3993L13.3592 44.8905L12.8077 44.0182C10.4883 40.3466 9.26379 36.1036 9.26552 31.7469C9.27059 19.033 19.6639 8.68977 32.4443 8.68977C38.6332 8.6923 44.4499 11.0939 48.8248 15.4521C53.1993 19.8104 55.6066 25.6039 55.6048 31.7656C55.5993 44.4798 45.2057 54.8245 32.4355 54.8245Z" fill="white" />
        <defs>
            <linearGradient id="paint0_linear_175_17" x1="32.3181" y1="59.022" x2="32.3181" y2="4.97363" gradientUnits="userSpaceOnUse">
                <stop stopColor="#20B038" />
                <stop offset="1" stopColor="#60D66A" />
            </linearGradient>
        </defs>
    </svg>
)
const Youtube = () => (
    <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M47.225 14H16.8062C10.8381 14 6 18.8619 6 24.8595V40.1405C6 46.1381 10.8381 51 16.8062 51H47.225C53.1931 51 58.0313 46.1381 58.0313 40.1405V24.8595C58.0313 18.8619 53.1931 14 47.225 14ZM39.9169 33.2435L25.689 40.0627C25.3099 40.2444 24.872 39.9667 24.872 39.5446V25.4799C24.872 25.0518 25.3214 24.7744 25.7013 24.9679L39.9292 32.2134C40.3522 32.4288 40.3448 33.0384 39.9169 33.2435Z" fill="#F61C0D" />
    </svg>
)

export { Facebook, Instagram, Youtube, Twitter, Discord, Telegram, Whatsapp }