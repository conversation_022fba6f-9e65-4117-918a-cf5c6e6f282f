'use client'
import <PERSON><PERSON> from '@/components/Ui/Button'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import axios from '@/lib/axios'

interface Props {
    quiz: QuizModel | undefined
    user: any
    Answered?: {
        id: number
        user_id: number
        quiz_id: number
        status: string
        success: boolean
    }
}

const QuestionTemplate = ({ quiz, user, Answered }: Props) => {
    const [selectedAnswer, setSelectedAnswer] = useState('')
    const [showResult, setShowResult] = useState(false)
    const [isCorrect, setIsCorrect] = useState(false)
    const onAnswerSelected = (string: string) => {
        if (Answered?.success === true) return
        setSelectedAnswer(string)
    }
    const onShowResult = async () => {
        if (Answered?.success === true) return
        setShowResult(true)
        if (selectedAnswer === quiz?.answer) {
            setIsCorrect(true)
        } else {
            setIsCorrect(false)
        }
        try {
            await axios.post(
                '/user-quizzes',
                {
                    quiz_id: quiz?.id,
                    answer: selectedAnswer,
                },
                {
                    headers: {
                        Authorization: `Bear<PERSON> ${user?.access_token}`,
                    },
                }
            )
        } catch (error: any) {}
    }
    return (
        <div className="flex w-[600px] flex-col items-center gap-11 max-md:max-w-full max-md:container max-md:mx-auto max-md:items-start">
            <h1 className="text-center w-full text-b3 font-semibold max-md:gap-7 max-md:text-sub2">{quiz?.question}</h1>
            <div className="flex flex-col  gap-5 w-full ">
                {Answered?.success || showResult ? (
                    <div className="flex justify-center border bg-yellow-light py-8 capitalize max-md:py-4">
                        {Answered?.status === 'correct' || isCorrect ? (
                            <p className="text-sub2 text-green max-md:text-cap2">You answer is correct</p>
                        ) : (
                            <p className="text-sub2 text-[#BD0013] max-md:text-cap2">You answer is wrong</p>
                        )}
                    </div>
                ) : (
                    ''
                )}
                {quiz?.choices.map((choice, index) => (
                    <div
                        key={index}
                        onClick={() => (!showResult ? onAnswerSelected(choice) : '')}
                        className={cn(
                            'flex w-full items-center gap-4 rounded-lg border cursor-pointer border-gray-300 px-6 py-4 shadow-md max-md:px-[10px] max-md:py-[11px]',
                            !Answered?.success && selectedAnswer === choice ? 'bg-green-light' : '',
                            showResult ? 'cursor-not-allowed' : '',
                            showResult && selectedAnswer === choice && isCorrect ? 'bg-green-light cursor-not-allowed' : '',
                            showResult && selectedAnswer === choice && !isCorrect ? 'bg-[#BD0013] text-white cursor-not-allowed' : '',
                            showResult && !isCorrect && choice === quiz?.answer ? 'bg-green-light cursor-not-allowed' : '',
                            !Answered?.success && !showResult ? 'hover:bg-green-light ' : '',
                            Answered?.success && choice === quiz?.answer ? 'bg-green-light cursor-not-allowed' : '',
                            Answered?.success && choice !== quiz?.answer ? 'bg-[#BD0013] text-white cursor-not-allowed' : ''
                        )}>
                        <div className="flex h-8 max-h-[32px] min-h-[32px] w-8 min-w-[32px] max-w-[32px]  items-center justify-center rounded-full bg-black text-sub2 font-medium text-white max-md:h-[28px] max-md:max-h-[28px] max-md:min-h-[28px] max-md:w-[28px] max-md:min-w-[28px] max-md:max-w-[28px] max-md:text-cap1">
                            {String.fromCharCode(65 + index)}
                        </div>
                        <p className="text-cap1 max-md:text-cap3 ">{choice}</p>
                    </div>
                ))}

                <Button disabled={showResult || Answered?.success} onClick={() => onShowResult()}>
                    Show Result
                </Button>
            </div>
        </div>
    )
}

export default QuestionTemplate
