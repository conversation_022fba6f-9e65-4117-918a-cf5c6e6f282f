const Heart = ({ strok }: { strok?: string }) => (
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.87187 12.0983C1.79887 8.74832 3.05287 4.91932 6.56987 3.78632C8.41987 3.18932 10.4619 3.54132 11.9999 4.69832C13.4549 3.57332 15.5719 3.19332 17.4199 3.78632C20.9369 4.91932 22.1989 8.74832 21.1269 12.0983C19.4569 17.4083 11.9999 21.4983 11.9999 21.4983C11.9999 21.4983 4.59787 17.4703 2.87187 12.0983Z"
            stroke={strok ?? '#2655ff'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M16 7.19995C17.07 7.54595 17.826 8.50095 17.917 9.62195"
            stroke={strok ?? '#2655ff'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const Heart2 = ({ strok }: { strok?: string }) => (
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.87187 12.0983C1.79887 8.74832 3.05287 4.91932 6.56987 3.78632C8.41987 3.18932 10.4619 3.54132 11.9999 4.69832C13.4549 3.57332 15.5719 3.19332 17.4199 3.78632C20.9369 4.91932 22.1989 8.74832 21.1269 12.0983C19.4569 17.4083 11.9999 21.4983 11.9999 21.4983C11.9999 21.4983 4.59787 17.4703 2.87187 12.0983Z"
            stroke={strok ?? '#6E4C31'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M16 7.19995C17.07 7.54595 17.826 8.50095 17.917 9.62195"
            stroke={strok ?? '#6E4C31'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)
const Profile = ({ strok }: { strok?: string }) => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.4829 17.2644C9.13179 17.2644 5.41608 17.9223 5.41608 20.5569C5.41608 23.1915 9.10822 23.873 13.4829 23.873C17.8339 23.873 21.5486 23.214 21.5486 20.5805C21.5486 17.9469 17.8575 17.2644 13.4829 17.2644Z"
            stroke={strok ?? '#2655ff'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.4829 13.5066C16.3382 13.5066 18.6525 11.1913 18.6525 8.3359C18.6525 5.48055 16.3382 3.16626 13.4829 3.16626C10.6275 3.16626 8.31216 5.48055 8.31216 8.3359C8.30252 11.1816 10.6018 13.497 13.4464 13.5066H13.4829Z"
            stroke={strok ?? '#2655ff'}
            strokeWidth="1.42857"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const Profile2 = ({ strok }: { strok?: string }) => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.4829 17.2644C9.13179 17.2644 5.41608 17.9223 5.41608 20.5569C5.41608 23.1915 9.10822 23.873 13.4829 23.873C17.8339 23.873 21.5486 23.214 21.5486 20.5805C21.5486 17.9469 17.8575 17.2644 13.4829 17.2644Z"
            stroke={strok ?? '#6E4C31'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.4829 13.5066C16.3382 13.5066 18.6525 11.1913 18.6525 8.3359C18.6525 5.48055 16.3382 3.16626 13.4829 3.16626C10.6275 3.16626 8.31216 5.48055 8.31216 8.3359C8.30252 11.1816 10.6018 13.497 13.4464 13.5066H13.4829Z"
            stroke={strok ?? '#6E4C31'}
            strokeWidth="1.42857"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)
const ProfileBlack = () => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.4829 17.2644C9.13179 17.2644 5.41608 17.9223 5.41608 20.5569C5.41608 23.1915 9.10822 23.873 13.4829 23.873C17.8339 23.873 21.5486 23.214 21.5486 20.5805C21.5486 17.9469 17.8575 17.2644 13.4829 17.2644Z"
            stroke="#081228"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.4829 13.5066C16.3382 13.5066 18.6525 11.1913 18.6525 8.3359C18.6525 5.48055 16.3382 3.16626 13.4829 3.16626C10.6275 3.16626 8.31216 5.48055 8.31216 8.3359C8.30252 11.1816 10.6018 13.497 13.4464 13.5066H13.4829Z"
            stroke="#081228"
            strokeWidth="1.42857"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)
const Video = ({ strok }: { strok?: string }) => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.3339 17.4806C18.4249 19.5417 16.7615 21.2846 14.6189 21.3721C14.4611 21.3788 6.76716 21.3633 6.76716 21.3633C4.6349 21.5251 2.76873 19.9929 2.60055 17.9396C2.58788 17.7866 2.59133 9.53122 2.59133 9.53122C2.49687 7.46792 4.15799 5.72061 6.30177 5.6297C6.4619 5.62194 14.1454 5.63635 14.1454 5.63635C16.2881 5.4767 18.16 7.02001 18.3259 9.0833C18.3374 9.23187 18.3339 17.4806 18.3339 17.4806Z"
            stroke={strok ?? '#2655ff'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M18.3375 11.2273L22.0421 8.19544C22.9601 7.44394 24.3371 8.09869 24.336 9.28332L24.3225 17.5509C24.3214 18.7356 22.9432 19.3847 22.0275 18.6332L18.3375 15.6013"
            stroke={strok ?? '#2655ff'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const Video2 = ({ strok }: { strok?: string }) => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.3339 17.4806C18.4249 19.5417 16.7615 21.2846 14.6189 21.3721C14.4611 21.3788 6.76716 21.3633 6.76716 21.3633C4.6349 21.5251 2.76873 19.9929 2.60055 17.9396C2.58788 17.7866 2.59133 9.53122 2.59133 9.53122C2.49687 7.46792 4.15799 5.72061 6.30177 5.6297C6.4619 5.62194 14.1454 5.63635 14.1454 5.63635C16.2881 5.4767 18.16 7.02001 18.3259 9.0833C18.3374 9.23187 18.3339 17.4806 18.3339 17.4806Z"
            stroke={strok ?? '#6E4C31'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M18.3375 11.2273L22.0421 8.19544C22.9601 7.44394 24.3371 8.09869 24.336 9.28332L24.3225 17.5509C24.3214 18.7356 22.9432 19.3847 22.0275 18.6332L18.3375 15.6013"
            stroke={strok ?? '#6E4C31'}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)
const Clock = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2s10 4.48 10 10z"></path>
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M15.71 15.18l-3.1-1.85c-.54-.32-.98-1.09-.98-1.72v-4.1"></path>
    </svg>
)
const Check = () => (
    <svg
        width="33"
        height="25"
        className="max-md:hidden"
        viewBox="0 0 33 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.81823 22.217L1.40638 13.2139C0.254754 11.9813 0.320856 10.0312 1.55344 8.87958C2.78602 7.72795 4.73613 7.79423 5.88776 9.02663L12.3763 15.9712L22.6468 6.37511C22.7392 6.28873 22.8357 6.20959 22.9355 6.13678L27.7653 1.62426C28.9979 0.472626 30.9482 0.538909 32.0996 1.77149C33.2512 3.00389 33.185 4.95418 31.9526 6.10581L17.201 19.8886L17.1853 19.8718L12.0826 24.6395L9.81823 22.217Z"
            fill="white"
        />
    </svg>
)
const CheckMobile = () => (
    <svg
        width="13"
        height="10"
        className="md:hidden"
        viewBox="0 0 13 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M3.84623 8.64472L0.737501 5.31747C0.311899 4.86195 0.336328 4.14126 0.791848 3.71566C1.24737 3.29006 1.96806 3.31455 2.39366 3.77001L4.7916 6.33647L8.58722 2.7901C8.62135 2.75817 8.65703 2.72892 8.6939 2.70202L10.4788 1.03435C10.9344 0.608743 11.6551 0.633239 12.0807 1.08876C12.5063 1.54421 12.4818 2.26497 12.0263 2.69057L6.57466 7.78422L6.56884 7.77799L4.68304 9.53997L3.84623 8.64472Z"
            fill="white"
        />
    </svg>
)

const CourseVideo = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            fillRule="evenodd"
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            clipRule="evenodd"
            d="M16.297 15.538c.08 1.832-1.398 3.382-3.302 3.46-.14.005-6.98-.008-6.98-.008-1.895.144-3.554-1.218-3.703-3.044-.012-.136-.009-7.474-.009-7.474-.084-1.834 1.393-3.387 3.299-3.468.142-.007 6.972.006 6.972.006 1.904-.142 3.568 1.23 3.716 ************.007 7.464.007 7.464z"></path>
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M16.3 9.98l3.293-2.695c.816-.668 2.04-.086 2.039.967L21.62 15.6c-.001 1.053-1.226 1.63-2.04.962l-3.28-2.695"></path>
    </svg>
)
const Certificate = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            fill="#081228"
            fillRule="evenodd"
            d="M5.25 12a.75.75 0 01.75-.75h8a.75.75 0 010 1.5H6a.75.75 0 01-.75-.75zM5.25 16a.75.75 0 01.75-.75h4a.75.75 0 010 1.5H6a.75.75 0 01-.75-.75z"
            clipRule="evenodd"></path>
        <path
            fill="#081228"
            d="M7.945 3.25c-1.367 0-2.47 0-3.337.117-.9.12-1.658.38-2.26.981-.602.602-.86 1.36-.981 2.26-.117.867-.117 1.97-.117 3.337v4.11c0 1.367 0 2.47.117 3.337.12.9.38 1.658.981 2.26.602.602 1.36.86 2.26.982.867.116 1.97.116 3.337.116h7.594a5.934 5.934 0 01-1.496-1.5H8c-1.435 0-2.437-.002-3.192-.103-.734-.099-1.122-.28-1.399-.556-.277-.277-.457-.665-.556-1.4-.101-.755-.103-1.756-.103-3.191v-4c0-1.435.002-2.437.103-3.192.099-.734.28-1.122.556-1.399.277-.277.665-.457 1.4-.556C5.562 4.752 6.564 4.75 8 4.75h8c1.435 0 2.436.002 3.192.103.734.099 1.122.28 1.399.556.277.277.457.665.556 1.4.101.754.103 1.756.103 3.191v.487a5.901 5.901 0 011.5.942V9.945c0-1.367 0-2.47-.116-3.337-.122-.9-.38-1.658-.982-2.26-.602-.602-1.36-.86-2.26-.981-.867-.117-1.97-.117-3.337-.117h-8.11z"></path>
        <path
            fill="#081228"
            fillRule="evenodd"
            d="M19.25 11.25a4 4 0 00-3 6.646V22a.75.75 0 001.097.665l1.953-1.02 1.953 1.02A.75.75 0 0022.35 22v-4.222a4 4 0 00-3.1-6.528zm-2.5 4a2.5 2.5 0 115 0 2.5 2.5 0 01-5 0zm4.1 3.667a3.988 3.988 0 01-1.6.333c-.53 0-1.037-.103-1.5-.29v1.803l1.203-.628a.75.75 0 01.694 0l1.203.628v-1.846z"
            clipRule="evenodd"></path>
        <path stroke="#081228" strokeLinecap="round" strokeWidth="1.5" d="M6 12h8M6 16h4"></path>
    </svg>
)
const Whishlist = ({ active, black }: { active?: boolean; black?: boolean }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        fill={active ? 'red' : black ? '#081228' : 'none'}
        viewBox="0 0 24 24">
        <path
            stroke={active ? 'red' : '#081228'}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M2.872 11.598c-1.073-3.35.18-7.179 3.698-8.312a6.007 6.007 0 015.43.912c1.455-1.125 3.572-1.505 5.42-.912 3.517 1.133 4.779 4.962 3.707 8.312-1.67 5.31-9.127 9.4-9.127 9.4s-7.402-4.028-9.128-9.4z"
            clipRule="evenodd"></path>
        <path
            stroke={black ? '#fff' : active ? 'white' : '#081228'}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M16 6.7a2.781 2.781 0 011.917 2.422"></path>
    </svg>
)

const Cart = ({ active, black }: { active?: boolean; black?: boolean }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        fill={active ? 'red' : black ? '#081228' : 'none'}
        stroke="black"
        viewBox="0 0 24 24">
        <path
            fillRule="evenodd"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M16.514 21.5H8.166c-3.066 0-5.419-1.108-4.75-5.565l.778-6.041c.411-2.225 1.83-3.076 3.075-3.076h10.179c1.263 0 2.599.915 3.075 3.076l.778 6.04c.568 3.955-1.72 5.566-4.787 5.566z"
            clipRule="evenodd"></path>
        <path
            stroke={black ? '#081228' : '#081228'}
            fill={black ? 'white' : 'none'}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M16.651 6.598a4.32 4.32 0 00-4.32-4.32v0a4.32 4.32 0 00-4.339 4.32h0M15.296 11.102h-.045M9.466 11.102H9.42"></path>
    </svg>
)

const TrustPilot = ({ width, height, fill }: { width?: string; height?: string; fill?: string }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width ? width : '98'}
        height={height ? height : '24'}
        fill="none"
        viewBox="0 0 98 24">
        <path
            fill={fill ? fill : '#191919'}
            d="M25.695 8.505h9.89v1.841H31.69v10.38h-2.134v-10.38h-3.878l.017-1.84zm9.466 3.373h1.825v1.71h.032c.066-.243.18-.472.343-.7a2.71 2.71 0 01.602-.619c.228-.195.49-.342.782-.456.277-.114.57-.18.864-.18.228 0 .391.017.472.**************.033.261.033v1.873c-.13-.032-.277-.049-.423-.065-.147-.016-.277-.032-.424-.032a2.26 2.26 0 00-.929.195c-.293.13-.538.326-.766.57a3.09 3.09 0 00-.52.945c-.131.375-.197.815-.197 1.304v4.204h-1.955l.033-8.799zm14.142 8.847h-1.922v-1.238h-.033c-.244.456-.602.798-1.075 1.075a2.838 2.838 0 01-1.45.408c-1.157 0-2.004-.277-2.526-.864-.521-.57-.782-1.45-.782-2.607v-5.62h1.956v5.425c0 .782.146 1.336.456 1.645.293.326.717.49 1.254.49.408 0 .75-.066 1.027-.196.277-.13.505-.294.668-.505.179-.212.293-.473.375-.766a3.62 3.62 0 00.114-.962v-5.132h1.955v8.847h-.017zm3.324-2.835c.066.57.277.962.652 1.206.375.228.831.342 1.352.342.18 0 .392-.016.62-.049.228-.032.456-.081.651-.163.212-.081.375-.195.505-.358a.834.834 0 00.18-.603c-.017-.244-.098-.456-.277-.62-.18-.162-.391-.276-.652-.39-.26-.098-.57-.18-.913-.244-.342-.066-.684-.147-1.042-.229-.359-.081-.717-.179-1.06-.293a3.037 3.037 0 01-.912-.472 2.031 2.031 0 01-.635-.75c-.163-.31-.245-.668-.245-1.124 0-.489.114-.88.359-1.206.228-.326.538-.586.896-.782a4.065 4.065 0 011.206-.424c.44-.081.863-.114 1.254-.114.456 0 .896.05 1.304.147.407.098.798.244 1.124.473.342.211.62.505.83.847.229.342.36.766.425 1.254h-2.037c-.098-.472-.31-.782-.636-.945a2.623 2.623 0 00-1.156-.244c-.13 0-.31.016-.49.033a2.88 2.88 0 00-.537.13c-.163.065-.31.163-.424.277a.652.652 0 00-.179.472c0 .245.082.424.245.57.163.147.374.261.651.375.261.098.57.18.913.245.342.065.7.146 1.059.228.358.081.7.18 1.043.293.342.114.651.26.912.473.26.195.489.44.652.733.163.293.244.668.244 1.091 0 .522-.114.962-.358 1.337a3.048 3.048 0 01-.929.896 4.605 4.605 0 01-1.27.505 6.823 6.823 0 01-1.386.163 5.833 5.833 0 01-1.548-.196 3.94 3.94 0 01-1.238-.57c-.342-.26-.62-.57-.815-.961-.195-.375-.31-.831-.325-1.369h1.971v.016h.016zm6.436-6.012h1.483V9.206H62.5v2.656h1.76v1.45H62.5v4.725c0 .212.016.375.033.538.016.146.065.277.114.374.065.098.163.18.277.228.13.05.293.082.521.082.13 0 .277 0 .407-.016.13-.017.277-.033.408-.066v1.516c-.212.032-.424.049-.636.065a4.046 4.046 0 01-.635.033c-.522 0-.929-.05-1.238-.147a1.78 1.78 0 01-.734-.424 1.516 1.516 0 01-.358-.7 5.567 5.567 0 01-.114-.978v-5.214h-1.483v-1.482.032zm6.566 0h1.841v1.206h.033c.277-.522.652-.88 1.14-1.108a3.694 3.694 0 011.597-.342c.701 0 1.304.114 1.825.374.522.245.945.57 1.287 1.01.343.424.603.93.766 1.5.163.57.26 1.189.26 1.824 0 .603-.08 1.174-.227 1.728-.163.553-.391 1.059-.7 1.482-.31.424-.701.766-1.19 1.027-.489.26-1.043.39-1.695.39a4.97 4.97 0 01-.847-.08 3.61 3.61 0 01-.815-.245 2.928 2.928 0 01-.717-.44 2.483 2.483 0 01-.553-.62H67.6V24h-1.955V11.878h-.017zm6.827 4.432c0-.391-.049-.782-.163-1.157a2.95 2.95 0 00-.472-.994 2.61 2.61 0 00-.766-.7c-.31-.18-.668-.261-1.059-.261-.815 0-1.434.277-1.857.847-.408.57-.62 1.336-.62 2.281 0 .456.05.864.163 1.238.114.375.261.701.49.978.211.277.472.489.781.652.31.163.652.244 1.06.244.456 0 .814-.098 1.123-.277.31-.179.555-.423.766-.7.196-.294.343-.62.424-.994.081-.391.13-.766.13-1.157zm3.438-7.805h1.955v1.841h-1.955v-1.84zm0 3.373h1.955v8.847h-1.955v-8.847zm3.699-3.373h1.955v12.22h-1.955V8.505zM87.51 20.97c-.7 0-1.336-.114-1.89-.359a4.29 4.29 0 01-1.4-.961 4.184 4.184 0 01-.865-1.483 5.793 5.793 0 01-.31-1.873c0-.668.099-1.288.31-1.858.196-.57.49-1.059.864-1.483a3.907 3.907 0 011.401-.96 4.946 4.946 0 011.89-.36c.7 0 1.336.115 1.89.36.554.227 1.01.553 1.401.96.375.408.669.913.864 1.483.196.57.31 1.19.31 1.858 0 .684-.098 1.303-.31 1.873a4.61 4.61 0 01-.864 1.483 3.907 3.907 0 01-1.4.961 4.885 4.885 0 01-1.89.359zm0-1.548c.424 0 .815-.098 1.124-.277.326-.18.57-.424.783-.717a3.07 3.07 0 00.456-.994 4.467 4.467 0 000-2.265 2.92 2.92 0 00-.456-.994 2.477 2.477 0 00-.783-.7c-.325-.18-.7-.277-1.124-.277-.423 0-.814.098-1.124.277-.326.18-.57.424-.782.7a2.92 2.92 0 00-.456.994 4.437 4.437 0 000 2.265c.097.375.244.7.456.994.212.293.472.538.782.717.326.195.7.277 1.124.277zm5.051-7.544h1.483V9.206H96v2.656h1.76v1.45H96v4.725c0 .212.016.375.033.538.016.146.065.277.114.374.065.098.163.18.277.228.13.05.293.082.521.082.13 0 .277 0 .408-.016.13-.017.276-.033.407-.066v1.516a8.439 8.439 0 01-.636.065c-.212.033-.407.033-.635.033-.522 0-.929-.05-1.238-.147a1.78 1.78 0 01-.734-.424 1.515 1.515 0 01-.358-.7 5.557 5.557 0 01-.114-.978v-5.214h-1.483v-1.482.032z"></path>
        <path
            fill={fill ? fill : '#191919'}
            d="M23.413 8.505H14.47L11.715 0l-2.77 8.505L0 8.49l7.234 5.262-2.77 8.506 7.235-5.263 7.234 5.263-2.754-8.506 7.235-5.246z"></path>
        <path fill="#005128" d="M16.799 15.675l-.62-1.923-4.464 3.242 5.084-1.32z"></path>
    </svg>
)
const TrustPilotMobile = ({ fill }: { fill?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="66" height="17" fill="none" viewBox="0 0 66 17">
        <path
            fill={fill ? fill : '#191919'}
            d="M17.347 5.742h6.677v1.243h-2.63v7.007h-1.44V6.985h-2.618l.01-1.243zm6.39 2.277h1.233v1.155h.022c.044-.165.12-.319.23-.473.11-.154.243-.297.408-.418a1.8 1.8 0 01.528-.308c.187-.077.385-.12.583-.12.154 0 .264.01.319.01a.963.963 0 00.176.022v1.265a2.226 2.226 0 00-.286-.044c-.1-.01-.187-.022-.286-.022-.22 0-.43.044-.627.132a1.59 1.59 0 00-.517.385 2.086 2.086 0 00-.352.638c-.088.253-.132.55-.132.88v2.838h-1.32l.022-5.94zm9.549 5.973h-1.298v-.836h-.022a1.843 1.843 0 01-.726.726 1.916 1.916 0 01-.98.275c-.78 0-1.352-.187-1.704-.583-.352-.385-.528-.979-.528-1.76V8.02h1.32v3.663c0 .528.099.902.308 1.111.198.22.484.33.847.33.275 0 .506-.044.693-.132.187-.088.34-.198.45-.34.122-.144.199-.32.254-.518.055-.198.077-.418.077-.649V8.02h1.32v5.973h-.011zm2.244-1.914c.044.385.187.65.44.814.253.154.56.231.913.231.12 0 .264-.01.418-.033.154-.022.308-.055.44-.11a.774.774 0 00.341-.242.563.563 0 00.121-.407.592.592 0 00-.187-.418 1.558 1.558 0 00-.44-.264 3.815 3.815 0 00-.616-.165c-.231-.044-.462-.099-.704-.154a7.606 7.606 0 01-.715-.198 2.05 2.05 0 01-.616-.319 1.372 1.372 0 01-.429-.506c-.11-.209-.165-.45-.165-.759 0-.33.077-.594.242-.814.154-.22.363-.396.605-.528s.517-.23.814-.286a4.66 4.66 0 01.847-.077c.308 0 .605.033.88.1.275.065.539.164.759.318.231.143.418.341.561.572.154.231.242.517.286.847H37.95c-.066-.319-.209-.528-.429-.638a1.772 1.772 0 00-.781-.165 4.13 4.13 0 00-.33.022 1.944 1.944 0 00-.363.088.86.86 0 00-.286.187.44.44 0 00-.121.32c0 .164.055.285.165.384.11.1.253.176.44.253.176.066.385.121.616.165.231.044.473.1.715.154.242.055.473.121.704.198.231.077.44.176.616.32.176.131.33.296.44.494.11.198.165.451.165.737 0 .352-.077.65-.242.902a2.057 2.057 0 01-.627.605 3.11 3.11 0 01-.858.341c-.319.066-.627.11-.935.11a3.94 3.94 0 01-1.045-.132 2.661 2.661 0 01-.836-.385 1.847 1.847 0 01-.55-.649 2.12 2.12 0 01-.22-.924h1.33v.011h.012zm4.345-4.059h1.001V6.215h1.32v1.793h1.188v.98h-1.188v3.19c0 .142.011.252.022.362a.762.762 0 00.077.253.43.43 0 00.187.154.981.981 0 00.352.055c.088 0 .187 0 .275-.01.088-.012.187-.023.275-.045v1.023c-.143.022-.286.033-.429.044-.143.022-.275.022-.429.022-.352 0-.627-.033-.836-.099a1.201 1.201 0 01-.495-.286 1.022 1.022 0 01-.242-.473 3.754 3.754 0 01-.077-.66v-3.52h-1.001v-1 .021zm4.433 0h1.243v.814h.022c.187-.352.44-.594.77-.748.33-.154.682-.23 1.078-.23.473 0 .88.076 1.232.252.352.165.638.385.869.682.231.286.407.627.517 1.012.11.385.176.803.176 1.232 0 .407-.055.792-.154 1.166-.11.374-.264.715-.473 1.001a2.34 2.34 0 01-.803.693c-.33.176-.704.264-1.144.264-.187 0-.385-.022-.572-.055a2.434 2.434 0 01-.55-.165 1.973 1.973 0 01-.484-.297 1.676 1.676 0 01-.374-.418h-.022v2.981h-1.32V8.02h-.011zm4.609 2.992a2.68 2.68 0 00-.11-.78 1.992 1.992 0 00-.319-.672 1.76 1.76 0 00-.517-.473 1.407 1.407 0 00-.715-.176c-.55 0-.968.187-1.254.572-.275.385-.418.902-.418 1.54 0 .308.033.583.11.836s.176.473.33.66c.143.187.319.33.528.44.209.11.44.165.715.165.308 0 .55-.066.759-.187.209-.12.374-.286.517-.473.132-.198.231-.418.286-.67.055-.265.088-.518.088-.782zm2.321-5.269h1.32v1.243h-1.32V5.742zm0 2.277h1.32v5.973h-1.32V8.02zm2.497-2.277h1.32v8.25h-1.32v-8.25zm5.346 8.415c-.473 0-.902-.077-1.276-.242a2.896 2.896 0 01-.946-.649 2.826 2.826 0 01-.583-1A3.91 3.91 0 0156.067 11c0-.45.066-.869.209-1.254.132-.385.33-.715.583-1 .253-.276.572-.496.946-.65a3.339 3.339 0 011.276-.242c.473 0 .902.077 1.276.242.374.154.682.374.946.65.253.274.451.615.583 1 .132.385.209.803.209 1.254 0 .462-.066.88-.209 1.265-.143.385-.33.715-.583 1.001a2.638 2.638 0 01-.946.65 3.298 3.298 0 01-1.276.241zm0-1.045c.286 0 .55-.066.759-.187.22-.12.385-.286.528-.484.143-.198.242-.429.308-.67a3.017 3.017 0 000-1.53 1.972 1.972 0 00-.308-.67 1.671 1.671 0 00-.528-.474 1.552 1.552 0 00-.759-.187c-.286 0-.55.066-.759.187-.22.121-.385.286-.528.473a1.972 1.972 0 00-.308.671 2.996 2.996 0 000 1.53c.066.252.165.472.308.67.143.198.319.363.528.484.22.132.473.187.759.187zm3.41-5.093h1.001V6.215h1.32v1.793H66v.98h-1.188v3.19c0 .142.011.252.022.362a.762.762 0 00.077.253.43.43 0 00.187.154.981.981 0 00.352.055c.088 0 .187 0 .275-.01.088-.012.187-.023.275-.045v1.023c-.143.022-.286.033-.429.044-.143.022-.275.022-.429.022-.352 0-.627-.033-.836-.099a1.201 1.201 0 01-.495-.286 1.022 1.022 0 01-.242-.473 3.754 3.754 0 01-.077-.66v-3.52h-1.001v-1 .021z"></path>
        <path
            fill={fill ? fill : '#191919'}
            d="M15.807 5.742H9.768L7.909 0l-1.87 5.742L0 5.731l4.884 3.553-1.87 5.742 4.884-3.553 4.884 3.553-1.859-5.742 4.884-3.542z"></path>
        <path fill="#005128" d="M11.341 10.582l-.418-1.298-3.014 2.19 3.432-.892z"></path>
    </svg>
)
const Rating = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="143" height="27" fill="none" viewBox="0 0 143 27">
        <path
            fill="#00B67A"
            d="M0 0h26.813v26.813H0V0zm29.047 0h26.812v26.813H29.047V0zm29.047 0h26.812v26.813H58.094V0zM87.14 0h26.812v26.813H87.141V0zm29.047 0H143v26.813h-26.812V0z"></path>
        <path
            fill="#fff"
            d="M13.406 18.07l4.078-1.033 1.704 5.251-5.782-4.217zm9.385-6.786h-7.178l-2.207-6.76-2.206 6.76H4.022l5.81 4.19-2.207 6.758 5.81-4.19 3.574-2.569 5.782-4.19zm19.662 6.787l4.078-1.034 1.704 5.251-5.782-4.217zm9.385-6.787H44.66l-2.207-6.76-2.206 6.76h-7.178l5.81 4.19-2.207 6.758 5.81-4.19 3.574-2.569 5.782-4.19zM71.5 18.07l4.078-1.034 1.703 5.251-5.78-4.217zm9.385-6.787h-7.178L71.5 4.524l-2.206 6.76h-7.178l5.81 4.19-2.207 6.758 5.809-4.19 3.575-2.569 5.782-4.19zm19.662 6.787l4.078-1.034 1.703 5.251-5.781-4.217zm9.384-6.787h-7.178l-2.206-6.76-2.206 6.76h-7.178l5.809 4.19-2.206 6.758 5.809-4.19 3.575-2.569 5.781-4.19zm19.663 6.787l4.078-1.034 1.703 5.251-5.781-4.217zm9.384-6.787H131.8l-2.206-6.76-2.207 6.76h-7.178l5.81 4.19-2.207 6.758 5.81-4.19 3.575-2.569 5.781-4.19z"></path>
    </svg>
)
const RatingMobile = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="99" height="19" fill="none" viewBox="0 0 99 19">
        <path
            fill="#00B67A"
            d="M0 0h18.456v18.456H0V0zm19.994 0h18.455v18.456H19.994V0zm19.993 0h18.456v18.456H39.987V0zm19.994 0h18.455v18.456H59.981V0zm19.993 0H98.43v18.456H79.974V0z"></path>
        <path
            fill="#fff"
            d="M9.228 12.439l2.807-.712 1.172 3.615-3.979-2.903zm6.46-4.672h-4.941L9.228 3.115 7.709 7.767H2.77l3.998 2.884-1.518 4.652 3.998-2.884 2.461-1.768 3.98-2.884zm13.534 4.672l2.806-.712 1.173 3.615-3.98-2.903zm6.46-4.672H30.74l-1.518-4.652-1.52 4.652h-4.94l3.999 2.884-1.519 4.652 3.999-2.884 2.46-1.768 3.98-2.884zm13.533 4.672l2.807-.712 1.173 3.615-3.98-2.903zm6.46-4.672h-4.941l-1.519-4.652-1.519 4.652h-4.94l3.998 2.884-1.518 4.652 3.998-2.884 2.461-1.768 3.98-2.884zm13.534 4.672l2.807-.712 1.172 3.615-3.98-2.903zm6.46-4.672h-4.941l-1.52-4.652-1.518 4.652h-4.94l3.998 2.884-1.519 4.652 3.999-2.884 2.46-1.768 3.98-2.884zm13.533 4.672l2.807-.712 1.173 3.615-3.98-2.903zm6.46-4.672h-4.94l-1.52-4.652-1.518 4.652h-4.941l3.999 2.884-1.52 4.652 4-2.884 2.46-1.768 3.98-2.884z"></path>
    </svg>
)

const Close = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M6.998 17l10-10M16.998 17l-10-10"></path>
    </svg>
)
const Google = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" fill="none" viewBox="0 0 25 24">
        <g clipPath="url(#clip0_449_83508)">
            <path
                fill="#4285F4"
                d="M24.266 12.277c0-.816-.066-1.636-.207-2.439H12.74v4.621h6.482a5.554 5.554 0 01-2.399 3.647v2.998h3.867c2.271-2.09 3.576-5.177 3.576-8.827z"></path>
            <path
                fill="#34A853"
                d="M12.74 24c3.237 0 5.966-1.062 7.955-2.896l-3.867-2.998c-1.076.731-2.465 1.146-4.083 1.146-3.131 0-5.786-2.112-6.738-4.952h-3.99v3.091a12.002 12.002 0 0010.723 6.61z"></path>
            <path
                fill="#FBBC04"
                d="M6.003 14.3a7.188 7.188 0 010-4.594V6.615H2.017a12.01 12.01 0 000 10.776l3.986-3.09z"></path>
            <path
                fill="#EA4335"
                d="M12.74 4.75a6.52 6.52 0 014.603 1.799l3.427-3.426A11.533 11.533 0 0012.74 0 11.998 11.998 0 002.017 6.615l3.986 3.09C6.95 6.863 9.609 4.75 12.74 4.75z"></path>
        </g>
        <defs>
            <clipPath id="clip0_449_83508">
                <path fill="#fff" d="M0 0H24V24H0z" transform="translate(.5)"></path>
            </clipPath>
        </defs>
    </svg>
)

export {
    Heart,
    Google,
    Close,
    ProfileBlack,
    Profile,
    Video,
    Check,
    CheckMobile,
    CourseVideo,
    Certificate,
    Whishlist,
    TrustPilot,
    TrustPilotMobile,
    Rating,
    RatingMobile,
    Clock,
    Cart,
    Profile2,
    Video2,
    Heart2,
}
