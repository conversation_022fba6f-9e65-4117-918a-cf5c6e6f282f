interface MentorshipModel {
    id: number,
    name: string,
    description: string;
    short_description: string
    image: File | null;
    category: string;
    status: string;
    price: string;
    sale: string;
    txHash: null | string;
    coaching: boolean;
    instructor_id: number
    created_at: string;
    updated_at: string;
    priceAfterDiscount: number;
    originalPrice: number
    priceAfterTax: number
}