{"name": "cu", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.8.0", "@headlessui/react": "^1.7.16", "@intercom/messenger-js-sdk": "^0.0.14", "@paypal/react-paypal-js": "^8.1.3", "@portabletext/react": "^3.0.4", "@react-pdf/renderer": "^3.3.8", "@react-spring/web": "^9.7.3", "@tanstack/react-query": "^5.13.4", "@types/intl-tel-input": "^18.1.4", "@types/node": "^20.4.9", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "autoprefixer": "10.4.14", "axios": "^1.4.0", "clsx": "^2.0.0", "cu": "file:", "encoding": "^0.1.13", "eslint": "^8.46.0", "eslint-config-next": "^13.4.13", "formik": "^2.4.3", "framer-motion": "^11.1.7", "graphql": "^16.7.1", "graphql-request": "^6.1.0", "gray-matter": "^4.0.3", "html2canvas": "^1.4.1", "install": "^0.13.0", "intl-tel-input": "^24.4.0", "jspdf": "^2.5.1", "markdown-to-jsx": "^7.3.2", "next": "^13.4.12", "next-auth": "^4.22.3", "next-sanity": "^5.1.2", "next-share": "^0.24.0", "payment": "^2.4.6", "postcss": "^8.4.27", "ps-scrollbar-tailwind": "^0.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.0", "react-icons": "^5.3.0", "react-iframe": "^1.8.5", "react-paginate": "^8.2.0", "react-phone-number-input": "^3.4.5", "react-query": "^3.39.3", "react-responsive-modal": "^6.4.2", "react-select": "^5.7.4", "react-toastify": "^9.1.3", "sass": "^1.65.1", "shadcn-ui": "^0.9.0", "sharp": "^0.32.4", "swiper": "^9.4.1", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.3", "typescript": "^5.1.6", "yup": "^1.2.0", "zustand": "^4.4.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@types/payment": "^2.1.4", "prettier-plugin-tailwindcss": "^0.4.1"}}