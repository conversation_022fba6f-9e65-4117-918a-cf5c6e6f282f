import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'
// import Hero from '@/components/Pages/Home/Hero'
import { CourseModel } from '@/types/CourseModel'
import WhyCU from '@/components/Pages/Home/WhyCU'
// import Courses from '@/components/Pages/Home/Courses'
import Partners from '@/components/Pages/Home/Partners'
import OtherInfo from '@/components/Pages/Home/OtherInfo'
import Membership from '@/components/Pages/Memberships/membership/Membership'
import AlphaGroupPricing from '@/components/Pages/Memberships/AlphaGroupPricing'
import FeaturedVideos from '@/components/Pages/Memberships/FeaturedVideos'
import OurStudents from '@/components/Pages/Memberships/OurStudents'
import FeaturedArticle from '@/components/Pages/Memberships/FeaturedArticle'
import ICPWeb from '@/components/Pages/Memberships/ICPWeb/ICPWeb'
import Discord from '@/components/Pages/Memberships/Discord'
import { AlphaGroupModel } from '@/types/AlphaGroupModel';
import { Blogs } from '@/types/BlogModel'

// Enhanced UI components
import EnhancedHero from '@/components/Pages/Home/EnhancedHero'
import EnhancedCourses from '@/components/Pages/Home/EnhancedCourses'
import EnhancedTestimonials from '@/components/Pages/Home/EnhancedTestimonials'
import EnhancedNewsletter from '@/components/Pages/Home/EnhancedNewsletter'
import SocialProofExplosion from '@/components/Pages/Home/SocialProofExplosion'
import ScarcityUrgency from '@/components/Pages/Home/ScarcityUrgency'
import RiskReversalGuarantee from '@/components/Pages/Home/RiskReversalGuarantee'
async function GetFeatured() {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/featured?pageNumber=1&pageSize=3`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.posts
    } catch (error) {
        console.error(error)
        return null
    }
}

const PublishedCourses = async () => {
    try {
        const response = await fetchInstance('/website-home-page/top-courses', {
            next: { revalidate: 60 },
        })
        return response.popularCourses
    } catch (error) {
        console.error('Error:', error)
        return error
    }
}

const fetchMembershipPlans = async (): Promise<AlphaGroupModel[]> => {
    const monthlyPlan: AlphaGroupModel = {
        title: {
            text: 'Monthly Special',
            color: 'white',
        },
        cards: {
            text: ['Flexible Plan'],
            color: 'white',
        },
        icon: {
            src: '/memberships/card1.png',
            alt: 'Alpha Group Monthly Plan',
        },
        billed: {
            text: 'Monthly',
            color: 'white',
        },
        price: {
            text: "500",
            color: 'white',
        },
        sub: {
            text: "Billed <strong>Monthly</strong>",
            color: 'white',
        },
        button: {
            text: "Get 1 Month",
            color: 'white',
        },
        plan: {
            text: 'Flexible Plan',
            color: 'white',
        },
        plans: '/checkout/subscription/' + 'alpha-group-monthly' + '?previous=' + '/subscriptions',
    };

    const semiAnnualPlan: AlphaGroupModel = {
        title: {
            text: 'Alpha Group 6 Months Special',
            color: 'white',
        },
        cards: {
            text: ['Discounted Plan'],
            color: 'white',
        },
        icon: {
            src: '/memberships/card3.png',
            alt: 'Alpha Group 6 Months Plan Icon',
        },
        billed: {
            text: '6 Months',
            color: 'white',
        },
        price: {
            text: "349",
            color: 'white',
        },
        sub: {
            text: "Billed <strong>$2,099</strong> at once",
            color: 'white',
        },
        button: {
            text: "Get 6 Months",
            color: 'white',
        },
        plan: {
            text: '30% off',
            color: 'white',
        },
        plans: '/checkout/subscription/' + 'alpha-group-6-months' + '?previous=' + '/subscriptions',
    };

    const annualPlan: AlphaGroupModel = {
        title: {
            text: 'Alpha Group Annual Plan',
            color: 'white',
        },
        cards: {
            text: ['Best Value Plan'],
            color: 'white',
        },
        icon: {
            src: '/memberships/card2.png',
            alt: 'Alpha Group 12 Months Plan Icon',
        },
        billed: {
            text: '12 Months',
            color: 'white',
        },
        price: {
            text: "249",
            color: 'white',
        },
        sub: {
            text: "Billed <strong>$2,999</strong> at once",
            color: 'white',
        },
        button: {
            text: "Get One Year",
            color: 'white',
        },
        plan: {
            text: '50% off',
            color: 'white',
        },
        plans: '/checkout/subscription/' + 'alpha-group-yearly' + '?previous=' + '/subscriptions'
    };

    // Return all plans as an array
    return [monthlyPlan, semiAnnualPlan, annualPlan];
};


export default async function IndexPage() {
    const courses: CourseModel[] = await PublishedCourses()
    const data = await fetchMembershipPlans();
    const featuredBlogs: Blogs = await GetFeatured()

    return (
        <section className="-mt-20 w-full pt-[4.125rem] font-manrope max-md:pt-14">
            <EnhancedHero />
            <SocialProofExplosion />
            <EnhancedCourses />
            <ScarcityUrgency />
            <OurStudents />
            <FeaturedArticle blogs={featuredBlogs} />
            <ICPWeb />
            <EnhancedTestimonials />
            <WhyCU />
            <RiskReversalGuarantee />
            <OtherInfo />
            <Partners />
            <EnhancedNewsletter />
            <CTA border />
            <Discord />
        </section>
    )
}
