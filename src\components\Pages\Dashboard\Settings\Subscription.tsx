'use client'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import UserSubscriptionModel from '@/types/UserSubscriptionModel'
import OrderSubscriptionModel from './OrderSubscriptionModel'

interface Props {
    userSubscription: UserSubscriptionModel
    tax: number
    user:any
}

const Subscription = ({ userSubscription, tax, user }: Props) => {
    const [openModel, setOpenModel] = useState(false)
    return (
        <>
            <div className={cn('absolute inset-0 top-0 z-50 flex justify-center', openModel ? '' : 'hidden')}>
                <OrderSubscriptionModel user={user} tax={tax} setOpenModel={setOpenModel} openModel={openModel} product={userSubscription} />
            </div>
            <div className="grid grid-cols-12 place-content-center place-self-center border-b border-gray-400 pb-4 text-cap1 font-medium capitalize max-md:hidden">
                <div className="col-span-2 flex items-center gap-2">
                    <ImageShortcut
                        src={userSubscription.subscriptions.image}
                        alt="product"
                        width={40}
                        height={40}
                        className="object-cover"
                    />
                    <p>
                        {userSubscription.subscriptions.name}
                    </p>
                </div>
                <p className="col-span-1 flex items-center">#{userSubscription.order_id}</p>
                <p className="col-span-2 flex items-center">{userSubscription.start_date.split('T')[0]}</p>
                <p className="col-span-1 flex items-center">{userSubscription.expiry_date.split('T')[0]}</p>
                <p
                    className={cn(
                        'col-span-1 flex items-center text-cap1',
                        userSubscription.status == 'pending'
                            ? 'text-yellow'
                            : userSubscription.status == 'active'
                                ? 'text-green-dark'
                                : 'text-red'
                    )}>
                    {userSubscription.status}
                </p>
                <p
                    className={cn(
                        'col-span-2 flex items-center text-cap1',
                        userSubscription.auto_renewal_status == 'cancelled'
                            ? 'text-red'
                            : userSubscription.auto_renewal_status == 'active'
                                ? 'text-green-dark'
                                : 'text-red'
                    )}>
                    {userSubscription.auto_renewal_status}
                </p>
                {userSubscription.subscriptions.interval_count === 1 ? (
                    <p className="col-span-2 flex items-center ">${userSubscription.subscriptions.price}/{userSubscription.subscriptions.subscription_interval}  </p>)
                    : (<p className="col-span-2 flex items-center ">${userSubscription.subscriptions.price} every {userSubscription.subscriptions.interval_count} {userSubscription.subscriptions.subscription_interval}s  </p>)}

                <p
                    onClick={() => setOpenModel(true)}
                    className="col-span-1 flex cursor-pointer items-center text-sub3 font-semibold text-blue underline">
                    Manage
                </p>
            </div>
            <div className="flex w-full flex-col gap-4 rounded-md p-4 shadow-md md:hidden">
                <div className="flex items-center gap-3">
                    <ImageShortcut
                        src={
                            userSubscription.subscriptions.image
                        }
                        alt="product"
                        width={62}
                        height={62}
                        className="object-cover"
                    />
                    <div className="flex flex-col">
                        <p className="text-cap1 font-medium capitalize">
                            {userSubscription.subscriptions.name}
                        </p>
                        <p className="text-cap2 text-gray-700">Order ID: #{userSubscription.order_id}</p>
                    </div>
                </div>
                <div className="border-b border-gray-300" />
                <div className="flex flex-col gap-2">
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Start Date</p>
                        <p className="font-medium">{userSubscription.created_at.split('T')[0]}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Total Amount</p>
                        {userSubscription.subscriptions.interval_count === 1 ? (<p className="font-medium">${userSubscription.subscriptions.price}/{userSubscription.subscriptions.subscription_interval}</p>) : (<p className="font-medium">${userSubscription.subscriptions.price} every {userSubscription.subscriptions.interval_count} {userSubscription.subscriptions.subscription_interval}s </p>)}

                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Status</p>
                        <p
                            className={cn(
                                'col-span-2 flex items-center text-cap2',
                                userSubscription.status == 'pending'
                                    ? 'text-yellow'
                                    : userSubscription.status == 'active'
                                        ? 'text-green-dark'
                                        : 'text-red'
                            )}>
                            {userSubscription.status}
                        </p>
                    </div>
                </div>
                <p
                    onClick={() => setOpenModel(true)}
                    className="col-span-1 flex cursor-pointer items-center justify-center text-sub3 font-semibold text-blue underline">
                    Manage
                </p>
            </div>
        </>
    )
}

export default Subscription
