import Link from 'next/link'
import { Blog } from '@/types/BlogModel'
import { Markdown } from '@/lib/markdown'
import formatDate from '@/lib/formatBlogDate'
import ImageShortcut from '@/components/Ui/Image'

const CardFlex = ({ ...blog }: Blog) => {
    const author = blog.author

    return (
        <div className="flex gap-6">
            <Link href={`/blog/${blog.slug}`} className="flex-1">
                {blog.image !== null ? (
                    <ImageShortcut
                        src={blog.image || ''}
                        width={416}
                        height={240}
                        alt={blog.title || ''}
                        className="h-[200px] w-auto bg-blue/60 object-contain max-md:h-[228px]"
                    />
                ) : (
                    <div className="h-[200px] w-auto bg-blue/60 object-contain max-md:h-[228px] animate-pulse" />
                )}
            </Link>

            <div className="flex flex-1 flex-col gap-6">
                {/* Info */}
                <div className="space-y-3 max-md:space-y-2">
                    <span className="text-[14px] font-medium leading-[20px] text-blue">
                        <Link
                            aria-label={author.name + ' page'}
                            href={`/blog/author/${author.slug}`}
                            className="hover:underline">
                            {author.name}
                        </Link>{' '}
                        • {formatDate(blog.published_date)}
                    </span>

                    <h4 className="text-sub2 font-medium">
                        <Link aria-label={blog.title} href={`/blog/${blog.slug}`}>
                            {blog.title}
                        </Link>
                    </h4>

                    <div className="text-sub3 text-[#667085]">
                        <Markdown>
                            {blog.excerpt
                                ?.replace(/(<([^>]+)>)/gi, '')
                                .replace(/&hellip;/g, '...')
                                .replace(/[\[\]']+/g, '')
                                .replace(/&nbsp;/g, ' ')
                                .replace(/&#8217;/g, "'")
                                .replace(/&#8220;/g, '"')
                                .replace(/&#8221;/g, '"')
                                .replace(/&#8211;/g, '-')
                                .replace(/&#038;/g, '&')
                                .replace(/&#8230;/g, '...')
                                .replace(/&#8216;/g, "'")
                                .replace(/&#8218;/g, "'")
                                .slice(0, 230) + ''}
                        </Markdown>
                    </div>
                </div>
                {/* Categories */}
                <div className="flex flex-wrap gap-2">
                    {blog.categories.length > 0 &&
                        blog.categories.map((category, index) => (
                            <Link
                                aria-label={category.category.name + ' page'}
                                href={`/blog/category/${category.category.slug}`}
                                key={index}
                                className="select-none rounded-full bg-gray-300 px-3 py-1 text-[14px] font-semibold leading-[20px] transition-colors duration-150 hover:bg-gray-400">
                                {category.category.name}
                            </Link>
                        ))}
                </div>
            </div>
        </div>
    )
}

export default CardFlex
