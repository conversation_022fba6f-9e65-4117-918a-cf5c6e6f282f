const Discord = () => (
  <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group 1">
      <path id="Vector" d="M8.88806 10.0664C8.20406 10.0664 7.66406 10.6664 7.66406 11.3984C7.66406 12.1304 8.21606 12.7304 8.88806 12.7304C9.57206 12.7304 10.1121 12.1304 10.1121 11.3984C10.1241 10.6664 9.57206 10.0664 8.88806 10.0664ZM13.2681 10.0664C12.5841 10.0664 12.0441 10.6664 12.0441 11.3984C12.0441 12.1304 12.5961 12.7304 13.2681 12.7304C13.9521 12.7304 14.4921 12.1304 14.4921 11.3984C14.4921 10.6664 13.9521 10.0664 13.2681 10.0664Z" fill="#7289DA" />
      <path id="Vector_2" d="M19.1001 0H3.02006C1.66406 0 0.560059 1.104 0.560059 2.472V18.696C0.560059 20.064 1.66406 21.168 3.02006 21.168H16.6281L15.9921 18.948L17.5281 20.376L18.9801 21.72L21.5601 24V2.472C21.5601 1.104 20.4561 0 19.1001 0ZM14.4681 15.672C14.4681 15.672 14.0361 15.156 13.6761 14.7C15.2481 14.256 15.8481 13.272 15.8481 13.272C15.3561 13.596 14.8881 13.824 14.4681 13.98C13.8681 14.232 13.2921 14.4 12.7281 14.496C11.5761 14.712 10.5201 14.652 9.62006 14.484C8.93606 14.352 8.34806 14.16 7.85606 13.968C7.58006 13.86 7.28006 13.728 6.98006 13.56C6.94406 13.536 6.90806 13.524 6.87206 13.5C6.84806 13.488 6.83606 13.476 6.82406 13.464C6.60806 13.344 6.48806 13.26 6.48806 13.26C6.48806 13.26 7.06406 14.22 8.58806 14.676C8.22806 15.132 7.78406 15.672 7.78406 15.672C5.13206 15.588 4.12406 13.848 4.12406 13.848C4.12406 9.984 5.85206 6.852 5.85206 6.852C7.58006 5.556 9.22406 5.592 9.22406 5.592L9.34406 5.736C7.18406 6.36 6.18806 7.308 6.18806 7.308C6.18806 7.308 6.45206 7.164 6.89606 6.96C8.18006 6.396 9.20006 6.24 9.62006 6.204C9.69206 6.192 9.75206 6.18 9.82406 6.18C10.5561 6.084 11.3841 6.06 12.2481 6.156C13.3881 6.288 14.6121 6.624 15.8601 7.308C15.8601 7.308 14.9121 6.408 12.8721 5.784L13.0401 5.592C13.0401 5.592 14.6841 5.556 16.4121 6.852C16.4121 6.852 18.1401 9.984 18.1401 13.848C18.1401 13.848 17.1201 15.588 14.4681 15.672Z" fill="#7289DA" />
    </g>
  </svg>

);
const Facebook = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Social icon" clip-path="url(#clip0_2194_48341)">
      <path id="Vector" d="M24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 17.9895 4.3882 22.954 10.125 23.8542V15.4688H7.07812V12H10.125V9.35625C10.125 6.34875 11.9166 4.6875 14.6576 4.6875C15.9701 4.6875 17.3438 4.92188 17.3438 4.92188V7.875H15.8306C14.34 7.875 13.875 8.80008 13.875 9.75V12H17.2031L16.6711 15.4688H13.875V23.8542C19.6118 22.954 24 17.9895 24 12Z" fill="#1877F2" />
      <path id="Vector_2" d="M16.6711 15.4688L17.2031 12H13.875V9.75C13.875 8.80102 14.34 7.875 15.8306 7.875H17.3438V4.92188C17.3438 4.92188 15.9705 4.6875 14.6576 4.6875C11.9166 4.6875 10.125 6.34875 10.125 9.35625V12H7.07812V15.4688H10.125V23.8542C11.3674 24.0486 12.6326 24.0486 13.875 23.8542V15.4688H16.6711Z" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_2194_48341">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
const Twitter = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Social icon">
      <path id="Vector" d="M7.54752 21.7512C16.6042 21.7512 21.5578 14.2479 21.5578 7.74101C21.5578 7.52789 21.5578 7.31573 21.5434 7.10453C22.507 6.40748 23.3389 5.54441 24 4.55573C23.1014 4.95413 22.148 5.21528 21.1718 5.33045C22.1998 4.71514 22.9692 3.74723 23.3366 2.60693C22.3701 3.18054 21.3126 3.58475 20.2099 3.80213C19.4675 3.01271 18.4856 2.48997 17.4162 2.31481C16.3468 2.13966 15.2494 2.32184 14.294 2.83318C13.3385 3.34452 12.5782 4.1565 12.1307 5.14348C11.6833 6.13045 11.5735 7.23739 11.8186 8.29301C9.8609 8.19481 7.94576 7.68604 6.19745 6.79973C4.44915 5.91343 2.90676 4.66939 1.6704 3.14837C1.04073 4.23236 0.847872 5.5156 1.1311 6.73679C1.41433 7.95798 2.15234 9.02532 3.19488 9.72149C2.41123 9.69853 1.64465 9.48712 0.96 9.10517V9.16757C0.960311 10.3044 1.35385 11.4062 2.07387 12.2859C2.79389 13.1657 3.79606 13.7693 4.9104 13.9944C4.18548 14.1922 3.42487 14.2211 2.68704 14.0789C3.00181 15.0573 3.61443 15.9128 4.43924 16.5259C5.26405 17.139 6.25983 17.479 7.28736 17.4985C6.26644 18.3009 5.09731 18.8942 3.84687 19.2444C2.59643 19.5947 1.28921 19.6949 0 19.5394C2.25183 20.9844 4.87192 21.7509 7.54752 21.7474" fill="#1DA1F2" />
    </g>
  </svg>
);
const Instagram = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3663 9.24444C10.646 9.24444 9.24211 10.6484 9.24211 12.3687C9.24211 14.089 10.646 15.4929 12.3663 15.4929C14.0866 15.4929 15.4905 14.089 15.4905 12.3687C15.4905 10.6484 14.0866 9.24444 12.3663 9.24444ZM21.7366 12.3687C21.7366 11.0749 21.7484 9.79288 21.6757 8.50148C21.6031 7.00148 21.2609 5.67023 20.164 4.57335C19.0648 3.47413 17.7359 3.13429 16.2359 3.06163C14.9421 2.98898 13.6601 3.0007 12.3687 3.0007C11.0749 3.0007 9.79289 2.98898 8.50148 3.06163C7.00148 3.13429 5.67023 3.47648 4.57335 4.57335C3.47413 5.67257 3.13429 7.00148 3.06163 8.50148C2.98898 9.79523 3.0007 11.0773 3.0007 12.3687C3.0007 13.6601 2.98898 14.9444 3.06163 16.2358C3.13429 17.7358 3.47648 19.0671 4.57335 20.164C5.67257 21.2632 7.00148 21.603 8.50148 21.6757C9.79523 21.7483 11.0773 21.7366 12.3687 21.7366C13.6624 21.7366 14.9445 21.7483 16.2359 21.6757C17.7359 21.603 19.0671 21.2608 20.164 20.164C21.2632 19.0648 21.6031 17.7358 21.6757 16.2358C21.7507 14.9444 21.7366 13.6624 21.7366 12.3687V12.3687ZM12.3663 17.1757C9.70617 17.1757 7.55929 15.0288 7.55929 12.3687C7.55929 9.70851 9.70617 7.56163 12.3663 7.56163C15.0265 7.56163 17.1734 9.70851 17.1734 12.3687C17.1734 15.0288 15.0265 17.1757 12.3663 17.1757ZM17.3702 8.48741C16.7491 8.48741 16.2476 7.98585 16.2476 7.36476C16.2476 6.74366 16.7491 6.2421 17.3702 6.2421C17.9913 6.2421 18.4929 6.74366 18.4929 7.36476C18.4931 7.51224 18.4642 7.65831 18.4078 7.7946C18.3515 7.93089 18.2688 8.05472 18.1645 8.15901C18.0602 8.26329 17.9364 8.34598 17.8001 8.40233C17.6638 8.45869 17.5177 8.4876 17.3702 8.48741V8.48741Z"
      fill="white"
    />
  </svg>
);
const Youtube = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.7609 7.20005C23.7609 7.20005 23.5266 5.54536 22.8047 4.8188C21.8906 3.86255 20.8688 3.85786 20.4 3.80161C17.0438 3.55786 12.0047 3.55786 12.0047 3.55786H11.9953C11.9953 3.55786 6.95625 3.55786 3.6 3.80161C3.13125 3.85786 2.10938 3.86255 1.19531 4.8188C0.473438 5.54536 0.24375 7.20005 0.24375 7.20005C0.24375 7.20005 0 9.14536 0 11.086V12.9047C0 14.8454 0.239062 16.7907 0.239062 16.7907C0.239062 16.7907 0.473437 18.4454 1.19062 19.1719C2.10469 20.1282 3.30469 20.0954 3.83906 20.1985C5.76094 20.3813 12 20.4375 12 20.4375C12 20.4375 17.0438 20.4282 20.4 20.1891C20.8688 20.1329 21.8906 20.1282 22.8047 19.1719C23.5266 18.4454 23.7609 16.7907 23.7609 16.7907C23.7609 16.7907 24 14.85 24 12.9047V11.086C24 9.14536 23.7609 7.20005 23.7609 7.20005ZM9.52031 15.1125V8.36724L16.0031 11.7516L9.52031 15.1125Z"
      fill="white"
    />
  </svg>
);
const Telegram = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Artboard">
      <path id="Oval" d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="url(#paint0_linear_2535_42158)" />
      <path id="Path 3" fill-rule="evenodd" clip-rule="evenodd" d="M5.43201 11.872C8.93026 10.3479 11.263 9.34305 12.4301 8.85759C15.7627 7.47147 16.4551 7.23069 16.9065 7.22274C17.0058 7.22099 17.2277 7.24559 17.3715 7.36226C17.4929 7.46076 17.5263 7.59384 17.5423 7.68723C17.5583 7.78063 17.5782 7.99339 17.5623 8.15963C17.3817 10.0571 16.6003 14.6618 16.2028 16.787C16.0346 17.6863 15.7034 17.9878 15.3827 18.0173C14.6858 18.0814 14.1567 17.5568 13.4817 17.1143C12.4256 16.422 11.8289 15.991 10.8037 15.3154C9.61896 14.5347 10.387 14.1056 11.0622 13.4043C11.2389 13.2208 14.3093 10.428 14.3687 10.1747C14.3762 10.143 14.3831 10.0249 14.3129 9.9625C14.2427 9.90014 14.1392 9.92146 14.0644 9.93843C13.9585 9.96247 12.2713 11.0777 9.00276 13.284C8.52385 13.6129 8.09007 13.7731 7.70141 13.7647C7.27295 13.7554 6.44876 13.5224 5.83606 13.3233C5.08456 13.079 4.48728 12.9498 4.53929 12.535C4.56638 12.3189 4.86395 12.0979 5.43201 11.872Z" fill="white" />
    </g>
    <defs>
      <linearGradient id="paint0_linear_2535_42158" x1="12" y1="0" x2="12" y2="23.822" gradientUnits="userSpaceOnUse">
        <stop stop-color="#2AABEE" />
        <stop offset="1" stop-color="#229ED9" />
      </linearGradient>
    </defs>
  </svg>

);

const LinkedIn = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g id="Social icon" clip-path="url(#clip0_2194_48343)">
    <path id="Vector" d="M22.2283 0H1.77167C1.30179 0 0.851161 0.186657 0.518909 0.518909C0.186657 0.851161 0 1.30179 0 1.77167V22.2283C0 22.6982 0.186657 23.1488 0.518909 23.4811C0.851161 23.8133 1.30179 24 1.77167 24H22.2283C22.6982 24 23.1488 23.8133 23.4811 23.4811C23.8133 23.1488 24 22.6982 24 22.2283V1.77167C24 1.30179 23.8133 0.851161 23.4811 0.518909C23.1488 0.186657 22.6982 0 22.2283 0ZM7.15333 20.445H3.545V8.98333H7.15333V20.445ZM5.34667 7.395C4.93736 7.3927 4.53792 7.2692 4.19873 7.04009C3.85955 6.81098 3.59584 6.48653 3.44088 6.10769C3.28591 5.72885 3.24665 5.31259 3.32803 4.91145C3.40941 4.51032 3.6078 4.14228 3.89816 3.85378C4.18851 3.56529 4.55782 3.36927 4.95947 3.29046C5.36112 3.21165 5.77711 3.25359 6.15495 3.41099C6.53279 3.56838 6.85554 3.83417 7.08247 4.17481C7.30939 4.51546 7.43032 4.91569 7.43 5.325C7.43386 5.59903 7.38251 5.87104 7.27901 6.1248C7.17551 6.37857 7.02198 6.6089 6.82757 6.80207C6.63316 6.99523 6.40185 7.14728 6.14742 7.24915C5.893 7.35102 5.62067 7.40062 5.34667 7.395ZM20.4533 20.455H16.8467V14.1933C16.8467 12.3467 16.0617 11.7767 15.0483 11.7767C13.9783 11.7767 12.9283 12.5833 12.9283 14.24V20.455H9.32V8.99167H12.79V10.58H12.8367C13.185 9.875 14.405 8.67 16.2667 8.67C18.28 8.67 20.455 9.865 20.455 13.365L20.4533 20.455Z" fill="#0A66C2" />
  </g>
  <defs>
    <clipPath id="clip0_2194_48343">
      <rect width="24" height="24" fill="white" />
    </clipPath>
  </defs>
</svg>
)

const RedIt = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g id="Social icon" clip-path="url(#clip0_2194_48344)">
    <path id="Vector" d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="#FF4500" />
    <path id="Vector_2" d="M20.0002 11.9983C20.0002 11.0299 19.2143 10.2439 18.2459 10.2439C17.7687 10.2439 17.3476 10.4264 17.0388 10.7352C15.8459 9.87903 14.1897 9.31763 12.3652 9.24745L13.1652 5.50008L15.7617 6.04745C15.7897 6.7071 16.3371 7.24043 17.0108 7.24043C17.6985 7.24043 18.2599 6.67903 18.2599 5.99131C18.2599 5.30359 17.6985 4.74219 17.0108 4.74219C16.5195 4.74219 16.0985 5.02289 15.902 5.44394L12.9967 4.8264C12.9125 4.81236 12.8283 4.8264 12.7581 4.8685C12.688 4.91061 12.6459 4.98078 12.6178 5.06499L11.7336 9.24745C9.86692 9.30359 8.19674 9.85096 6.98972 10.7352C6.68095 10.4404 6.24586 10.2439 5.78271 10.2439C4.81429 10.2439 4.02832 11.0299 4.02832 11.9983C4.02832 12.7141 4.44937 13.3176 5.06692 13.5983C5.03885 13.7667 5.02481 13.9492 5.02481 14.1317C5.02481 16.8264 8.15464 19.0018 12.0283 19.0018C15.902 19.0018 19.0318 16.8264 19.0318 14.1317C19.0318 13.9492 19.0178 13.7808 18.9897 13.6124C19.5652 13.3317 20.0002 12.7141 20.0002 11.9983ZM8.00025 13.2475C8.00025 12.5597 8.56165 11.9983 9.24937 11.9983C9.93709 11.9983 10.4985 12.5597 10.4985 13.2475C10.4985 13.9352 9.93709 14.4966 9.24937 14.4966C8.56165 14.4966 8.00025 13.9352 8.00025 13.2475ZM14.9757 16.5457C14.1195 17.4018 12.4915 17.458 12.0143 17.458C11.5371 17.458 9.89499 17.3878 9.05288 16.5457C8.92657 16.4194 8.92657 16.2089 9.05288 16.0825C9.1792 15.9562 9.38972 15.9562 9.51604 16.0825C10.0494 16.6159 11.2003 16.8124 12.0283 16.8124C12.8564 16.8124 13.9932 16.6159 14.5406 16.0825C14.6669 15.9562 14.8774 15.9562 15.0038 16.0825C15.102 16.2229 15.102 16.4194 14.9757 16.5457ZM14.7511 14.4966C14.0634 14.4966 13.502 13.9352 13.502 13.2475C13.502 12.5597 14.0634 11.9983 14.7511 11.9983C15.4388 11.9983 16.0002 12.5597 16.0002 13.2475C16.0002 13.9352 15.4388 14.4966 14.7511 14.4966Z" fill="white" />
  </g>
  <defs>
    <clipPath id="clip0_2194_48344">
      <rect width="24" height="24" fill="white" />
    </clipPath>
  </defs>
</svg>
)

const CopyIcon = () => (
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 111.07 122.88" ><style type="text/css"></style><g><path className="st0" d="M97.67,20.81L97.67,20.81l0.01,0.02c3.7,0.01,7.04,1.51,9.46,3.93c2.4,2.41,3.9,5.74,3.9,9.42h0.02v0.02v75.28 v0.01h-0.02c-0.01,3.68-1.51,7.03-3.93,9.46c-2.41,2.4-5.74,3.9-9.42,3.9v0.02h-0.02H38.48h-0.01v-0.02 c-3.69-0.01-7.04-1.5-9.46-3.93c-2.4-2.41-3.9-5.74-3.91-9.42H25.1c0-25.96,0-49.34,0-75.3v-0.01h0.02 c0.01-3.69,1.52-7.04,3.94-9.46c2.41-2.4,5.73-3.9,9.42-3.91v-0.02h0.02C58.22,20.81,77.95,20.81,97.67,20.81L97.67,20.81z M0.02,75.38L0,13.39v-0.01h0.02c0.01-3.69,1.52-7.04,3.93-9.46c2.41-2.4,5.74-3.9,9.42-3.91V0h0.02h59.19 c7.69,0,8.9,9.96,0.01,10.16H13.4h-0.02v-0.02c-0.88,0-1.68,0.37-2.27,0.97c-0.59,0.58-0.96,1.4-0.96,2.27h0.02v0.01v3.17 c0,19.61,0,39.21,0,58.81C10.17,83.63,0.02,84.09,0.02,75.38L0.02,75.38z M100.91,109.49V34.2v-0.02h0.02 c0-0.87-0.37-1.68-0.97-2.27c-0.59-0.58-1.4-0.96-2.28-0.96v0.02h-0.01H38.48h-0.02v-0.02c-0.88,0-1.68,0.38-2.27,0.97 c-0.59,0.58-0.96,1.4-0.96,2.27h0.02v0.01v75.28v0.02h-0.02c0,0.88,0.38,1.68,0.97,2.27c0.59,0.59,1.4,0.96,2.27,0.96v-0.02h0.01 h59.19h0.02v0.02c0.87,0,1.68-0.38,2.27-0.97c0.59-0.58,0.96-1.4,0.96-2.27L100.91,109.49L100.91,109.49L100.91,109.49 L100.91,109.49z"/></g></svg>)


export { Discord, Facebook, Instagram, Telegram, Youtube, Twitter, LinkedIn, RedIt, CopyIcon };
