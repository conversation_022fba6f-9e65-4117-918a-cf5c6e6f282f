interface CardProps {
    title: string;
    description: string;
    icon: string;
    metric: string;
    metricLabel: string;
}

const EnhancedCard = ({ title, description, icon, metric, metricLabel }: CardProps) => {
    return (
        <div className='bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-3xl border-2 border-primary/30 hover:border-primary hover:shadow-2xl hover:shadow-primary/20 transition-all duration-300 group h-full'>
            <div className="text-center">
                <div className="text-6xl mb-6 group-hover:animate-bounce">
                    {icon}
                </div>
                <h3 className="text-2xl font-black text-primary mb-4 group-hover:text-yellow-400 transition-colors">
                    {title}
                </h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                    {description}
                </p>

                {/* Success Metric */}
                <div className="bg-green-600/20 border border-green-400 rounded-xl p-4">
                    <div className="text-2xl font-black text-green-400">
                        {metric}
                    </div>
                    <div className="text-sm text-green-300 font-semibold">
                        {metricLabel}
                    </div>
                </div>
            </div>
        </div>
    )
}

const WhyCU = () => {
    const cardData = [
        {
            title: "BEGINNER TO MILLIONAIRE",
            description: "Our students go from complete crypto beginners to making $10K+/month. Sarah made $89K in just 6 months with ZERO experience!",
            icon: "🎯",
            metric: "10,000+",
            metricLabel: "Hours of Content"
        },
        {
            title: "PROVEN PROFIT SYSTEM",
            description: "Our exact blueprint has generated over $2.4M in student profits. Mike quit his job after making $156K in his first year!",
            icon: "💰",
            metric: "$89K",
            metricLabel: "Average Student Profit"
        },
        {
            title: "24/7 VIP SUPPORT",
            description: "Get instant access to our private Discord with 47,832+ profitable traders and direct access to crypto millionaire mentors!",
            icon: "🚀",
            metric: "24/7",
            metricLabel: "Expert Support"
        }
    ];

    return (
        <section
            id="why_crypto_university"
            className="w-full py-20 bg-gradient-to-br from-black via-gray-900 to-black text-white relative overflow-hidden">

            {/* Animated Background */}
            <div className="absolute inset-0">
                <div className="absolute top-20 left-20 w-40 h-40 bg-primary/10 rounded-full animate-pulse"></div>
                <div className="absolute bottom-20 right-20 w-32 h-32 bg-green-400/10 rounded-full animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-red-600/5 rounded-full animate-pulse delay-500"></div>
            </div>

            <div className="container mx-auto relative z-10 px-4">
                <div className="flex flex-col gap-16">
                    {/* Explosive Header */}
                    <div className="text-center">
                        {/* Urgency Banner */}
                        <div className="bg-red-600 text-white px-8 py-4 rounded-2xl mb-8 border-4 border-yellow-400 animate-pulse inline-block">
                            <div className="text-2xl font-black">🚨 WARNING: CRYPTO BULL MARKET IS HERE!</div>
                            <div className="text-lg font-bold">Don&apos;t Get Left Behind - Join The Winners!</div>
                        </div>

                        <h1 className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight">
                            Why <span className="text-primary">47,832+ Students</span> Choose <br/>
                            <span className="text-green-400">Crypto University</span> To Get <span className="text-red-400">RICH</span>
                        </h1>

                        <div className="bg-green-600 text-white p-6 rounded-2xl mb-8 border-2 border-yellow-400 max-w-4xl mx-auto">
                            <p className="text-2xl font-bold leading-relaxed">
                                We&apos;re the <span className="text-yellow-400">#1 Crypto Education Platform</span> that has helped students generate over <span className="text-yellow-400">$2.4 MILLION</span> in profits. Our proven system works - even for complete beginners!
                            </p>
                        </div>

                        {/* Massive Social Proof */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                            <div className="bg-gradient-to-br from-green-600 to-green-700 p-6 rounded-2xl border-2 border-yellow-400">
                                <div className="text-4xl font-black text-yellow-400">47,832+</div>
                                <div className="text-lg font-bold">Profitable Students</div>
                            </div>
                            <div className="bg-gradient-to-br from-purple-600 to-purple-700 p-6 rounded-2xl border-2 border-yellow-400">
                                <div className="text-4xl font-black text-yellow-400">$2.4M+</div>
                                <div className="text-lg font-bold">Total Profits Made</div>
                            </div>
                            <div className="bg-gradient-to-br from-red-600 to-red-700 p-6 rounded-2xl border-2 border-yellow-400">
                                <div className="text-4xl font-black text-yellow-400">97%</div>
                                <div className="text-lg font-bold">Success Rate</div>
                            </div>
                            <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-6 rounded-2xl border-2 border-yellow-400">
                                <div className="text-4xl font-black text-yellow-400 animate-pulse">LIVE</div>
                                <div className="text-lg font-bold">Trading Signals</div>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Cards Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {cardData.map((card, index) => (
                            <EnhancedCard
                                key={index}
                                title={card.title}
                                description={card.description}
                                icon={card.icon}
                                metric={card.metric}
                                metricLabel={card.metricLabel}
                            />
                        ))}
                    </div>

                    {/* Final CTA Section */}
                    <div className="text-center">
                        <div className="bg-gradient-to-r from-red-600 via-red-700 to-red-800 p-8 rounded-3xl border-4 border-yellow-400 max-w-4xl mx-auto">
                            <h3 className="text-3xl font-black text-white mb-4">
                                🔥 READY TO JOIN THE 47,832+ WINNERS?
                            </h3>
                            <p className="text-xl text-yellow-100 mb-6 font-bold">
                                Stop watching others get rich. Start your crypto profit journey TODAY!
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button className="px-8 py-4 bg-green-600 hover:bg-green-700 text-white text-xl font-black rounded-2xl hover:scale-105 transform transition-all shadow-2xl">
                                    🚀 START MAKING MONEY NOW!
                                </button>
                                <button className="px-8 py-4 bg-yellow-400 hover:bg-yellow-500 text-black text-xl font-black rounded-2xl hover:scale-105 transform transition-all shadow-2xl">
                                    💎 GET FREE BLUEPRINT
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default WhyCU