import mix from '@/config/videoLink'
import Iframe from 'react-iframe'

const IntroductionSection = ({ video }: { video: string }) => {
    if (video !== undefined && video.length > 0 && video !== null)
        return (
            <section id="introduction" className="bg-[#FCFBF4] py-20">
                <div className="container mx-auto space-y-14">
                    <h2 className="text-center text-headline font-semibold">Introduction Video</h2>

                    <div className="text-center">
                        <Iframe
                            url={mix(video)}
                            width="100%"
                            height="100%"
                            className="rounded-card z-[10] aspect-video h-[400px] w-full max-w-[768px] cursor-pointer select-none rounded-[1rem] object-cover max-md:h-[190px] max-md:min-h-[300px] max-sm:min-h-[220px] md:object-cover"
                            display="initial"
                            position="relative"
                            allowFullScreen
                        />
                    </div>
                </div>
            </section>
        )
}

export default IntroductionSection
