import { cn } from "@/lib/cn";
import { Logout } from "@public/global";
import { signOut } from "next-auth/react";

const SignoutBtn = ({
  className,
  currentRoute,
  nav,
}: {
  className?: string;
  currentRoute?: string;
  nav?: boolean;
}) => {
  const navClass =
    "rounded-full select-none px-3 py-2 transition-[background] duration-150 flex items-center justify-center gap-4 w-full border border-red";

  return (
    <button
      onClick={() => signOut({ redirect: true, callbackUrl: currentRoute })}
      className={
        nav
          ? cn(navClass)
          : cn(
              "rounded-[0.5rem] hover:bg-red/10 select-none px-3 py-2 transition-[background] duration-150 flex items-center gap-4 w-full",
              className
            )
      }
    >
      <Logout />
      <span className="text-sub3 text-red">Log Out</span>
    </button>
  );
};

export default SignoutBtn;
