import { StringToID } from '@/lib/stringToId'
import Hero from '@/components/Pages/Faqs/Hero'
import { Faq, FaqCategories } from '@/types/FaqModel'
import SideBar from '@/components/Pages/Faqs/sidebar'
import DisclosurePage from '@/components/Pages/Faqs/Disclosure'
import { getFAQCategories, getFAQs } from '../../../../sanity/sanity-utils'

export const metadata = {
    title: 'FAQs',
    description: "Frequently asked questions about Crypto University's services and products.",
    keywords: ['FAQs', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const FAQsPage = async () => {
    const faq: Faq = await getFAQs()
    const categories: FaqCategories[] = await getFAQCategories()

    return (
        <section className="flex flex-grow gap-14 max-md:gap-0 ">
            <SideBar categories={categories[0]} />
            <div className="flex flex-col items-start gap-16 py-16 max-md:py-0 max-md:pb-4">
                <Hero faq={faq} />
                <section className="container mx-auto w-full font-sans">
                    {faq.map(faqs => (
                        <div key={faqs._id} className="space-y-[4.5rem]">
                            {faqs.categories.map((category, i) => {
                                return (
                                    <div
                                        key={i}
                                        id={StringToID(category.title)}
                                        className="space-y-[1.875rem] max-md:space-y-[1rem]">
                                        <h2 className="font-manrope text-b3 font-medium">{category.title}</h2>
                                        <DisclosurePage category={category} />
                                    </div>
                                )
                            })}
                        </div>
                    ))}
                </section>
            </div>
        </section>
    )
}

export default FAQsPage
