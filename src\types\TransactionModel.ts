import { CourseModel } from "./CourseModel"

 export default interface TransactionModel {
    id: number
    tx_hash: string
    paymentMethod: string
    amount: number
    status: string
    user_id: number
    affiliate_id: number | null
    course_id: number | null
    bundle_id: number | null
    mentorship_id: number | null
    indicator_id: number | null
    purchaseEntity: string
    created_at: string
    updated_at: string
    courses: CourseModel;
    bundles:BundelModel;
    mentorships:MentorshipModel;
    indicators:IndicatorModel;
}
