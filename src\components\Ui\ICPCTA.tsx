'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import Button from '@/components/Ui/Button'
import { useState, useEffect } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import numberWithCommas from '@/lib/formatNumber'

interface props {
    count?: number
    border?: boolean
}
const ICPCTA = ({ count, border = false }: props) => {
    const [number, setNumber] = useState(0)
    useEffect(() => {
        const interval = setInterval(() => {
            setNumber(prevNumber => {
                if (prevNumber === 30000) {
                    clearInterval(interval)
                    return prevNumber
                }
                return prevNumber + 100
            })
        }, 1)

        return () => clearInterval(interval)
    }, [])
    return (
        <section
            id="cta"
            className={cn(
                'w-full overflow-hidden px-10 py-5 text-black max-sm:pb-16 max-sm:pt-0',
                border && ' max-md:border-none'
            )}>
            <div className="container mx-auto flex flex-wrap items-center gap-7 max-md:flex-col max-md:items-start">
                <div className='mx-auto flex flex-wrap items-center max-md:flex-col max-md:justify-center'>
                    <ImageShortcut
                        src={'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1706973238/dev_program_dr5q5k.jpg'}
                        width={486}
                        height={361}
                        className="w-[486px] relative object-contain rounded-lg overflow-hidden sm:px-4 sm:object-center max-md:-ml-10 object-center max-md:h-[241px] max-md:w-[241px]"
                        alt="Join"
                    />
                </div>
                <div className="flex max-w-[520px] flex-col items-start gap-[36px]">
                    <h1 className="font-sans text-h3 max-md:text-b2 max-md:font-medium">
                        Join The ICP Web 3.0 Developers Program
                        {/* Join <span className="text-blue md:font-semibold">{numberWithCommas(number)}+</span> learners
                        worldwide */}
                    </h1>

                    <p className="max-w-[575px] text-sub3 max-md:leading-[18px]">
                        We offer a range of support, including workshops, training, networking events, and access to funding opportunities.
                    </p>
                    <div className="w-[274px] max-md:w-auto">
                        <Link aria-label="Start Now" href="/courses/web3-dev-program" className="rounded-full">
                            <Button rounded variant="primary">
                                Enroll Now &gt;
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default ICPCTA
