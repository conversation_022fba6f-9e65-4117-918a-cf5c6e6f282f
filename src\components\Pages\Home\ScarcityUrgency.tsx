"use client";
import { useState, useEffect } from 'react';
import Link from 'next/link';
import Button from '@/components/Ui/Button';

const ScarcityUrgency = () => {
  const [timeLeft, setTimeLeft] = useState(3600); // 1 hour
  const [spotsLeft, setSpotsLeft] = useState(23);
  const [peopleViewing, setPeopleViewing] = useState(147);
  const [recentSignups, setRecentSignups] = useState(89);

  useEffect(() => {
    // Countdown timer
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Simulate decreasing spots
    const spotsTimer = setInterval(() => {
      setSpotsLeft((prev) => {
        if (prev > 5) {
          return prev - Math.floor(Math.random() * 2) - 1;
        }
        return prev;
      });
    }, 30000); // Every 30 seconds

    // Simulate people viewing
    const viewingTimer = setInterval(() => {
      setPeopleViewing((prev) => {
        const change = Math.floor(Math.random() * 10) - 5;
        const newValue = prev + change;
        return Math.max(100, Math.min(200, newValue));
      });
    }, 5000);

    // Simulate recent signups
    const signupTimer = setInterval(() => {
      setRecentSignups((prev) => prev + Math.floor(Math.random() * 3) + 1);
    }, 15000);

    return () => {
      clearInterval(timer);
      clearInterval(spotsTimer);
      clearInterval(viewingTimer);
      clearInterval(signupTimer);
    };
  }, []);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getUrgencyLevel = () => {
    if (timeLeft <= 600) return 'CRITICAL'; // Last 10 minutes
    if (timeLeft <= 1800) return 'HIGH'; // Last 30 minutes
    return 'MEDIUM';
  };

  const getUrgencyColor = () => {
    const level = getUrgencyLevel();
    if (level === 'CRITICAL') return 'from-red-600 to-red-800';
    if (level === 'HIGH') return 'from-orange-600 to-orange-800';
    return 'from-red-600 to-red-700';
  };

  return (
    <section className="py-20 bg-gradient-to-br from-red-900 via-black to-red-900 text-white relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-red-400/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-yellow-400/20 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-orange-400/20 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto relative z-10 px-4">
        {/* Main Countdown Timer */}
        <div className="text-center mb-16">
          <div className={`bg-gradient-to-r ${getUrgencyColor()} text-white p-8 rounded-3xl border-4 border-yellow-400 animate-pulse mb-8`}>
            <div className="text-4xl md:text-5xl font-black mb-4">
              ⚠️ {getUrgencyLevel()} ALERT ⚠️
            </div>
            <div className="text-2xl md:text-3xl font-bold mb-4">
              SPECIAL OFFER EXPIRES IN:
            </div>
            <div className="text-5xl md:text-6xl font-black text-yellow-400 mb-4 font-mono">
              {formatTime(timeLeft)}
            </div>
            <div className="text-xl font-bold">
              {getUrgencyLevel() === 'CRITICAL' ? '🚨 FINAL MINUTES!' : 
               getUrgencyLevel() === 'HIGH' ? '⚡ HURRY UP!' : 
               '🔥 LIMITED TIME!'}
            </div>
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-tight">
            🚨 <span className="text-red-400">LAST CHANCE:</span> Don&apos;t Miss Your <span className="text-yellow-400">CRYPTO FORTUNE!</span>
          </h1>
        </div>

        {/* Scarcity Indicators */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <div className="bg-gradient-to-br from-red-600 to-red-800 p-6 rounded-2xl border-4 border-yellow-400 text-center">
            <div className="text-4xl font-black text-yellow-400 animate-pulse mb-2">
              {spotsLeft}
            </div>
            <div className="text-lg font-bold">Spots Left</div>
            <div className="text-sm text-red-200 mt-2">
              {spotsLeft <= 10 ? '🚨 Almost Gone!' : '⚡ Filling Fast!'}
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-600 to-orange-800 p-6 rounded-2xl border-4 border-yellow-400 text-center">
            <div className="text-4xl font-black text-yellow-400 mb-2">
              {peopleViewing}
            </div>
            <div className="text-lg font-bold">People Viewing</div>
            <div className="text-sm text-orange-200 mt-2">
              👀 Right Now!
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-600 to-green-800 p-6 rounded-2xl border-4 border-yellow-400 text-center">
            <div className="text-4xl font-black text-yellow-400 mb-2">
              {recentSignups}
            </div>
            <div className="text-lg font-bold">Joined Today</div>
            <div className="text-sm text-green-200 mt-2">
              🔥 And Counting!
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-600 to-purple-800 p-6 rounded-2xl border-4 border-yellow-400 text-center">
            <div className="text-4xl font-black text-yellow-400 animate-pulse mb-2">
              97%
            </div>
            <div className="text-lg font-bold">Discount</div>
            <div className="text-sm text-purple-200 mt-2">
              💎 Today Only!
            </div>
          </div>
        </div>

        {/* Fear of Missing Out Section */}
        <div className="bg-black/70 backdrop-blur-sm p-12 rounded-3xl border-4 border-red-600 mb-16">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-black text-red-400 mb-8">
              ⚠️ WHAT HAPPENS WHEN THE TIMER HITS ZERO?
            </h2>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="bg-red-600 p-6 rounded-2xl border-2 border-yellow-400">
                <div className="text-2xl font-black text-white mb-4">
                  😱 YOU MISS OUT ON:
                </div>
                <ul className="text-left space-y-3 text-lg font-semibold">
                  <li className="flex items-center">
                    <span className="text-red-300 mr-3">❌</span>
                    <span>97% Discount (Worth $3,179)</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-300 mr-3">❌</span>
                    <span>Free $2,847 Bonus Package</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-300 mr-3">❌</span>
                    <span>VIP Discord Access</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-300 mr-3">❌</span>
                    <span>Personal Success Coach</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-300 mr-3">❌</span>
                    <span>Lifetime Updates</span>
                  </li>
                </ul>
              </div>

              <div className="bg-green-600 p-6 rounded-2xl border-2 border-yellow-400">
                <div className="text-2xl font-black text-white mb-4">
                  🚀 ACT NOW & GET:
                </div>
                <ul className="text-left space-y-3 text-lg font-semibold">
                  <li className="flex items-center">
                    <span className="text-green-300 mr-3">✅</span>
                    <span>Complete System for $97</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-300 mr-3">✅</span>
                    <span>$6,026 in Free Bonuses</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-300 mr-3">✅</span>
                    <span>Instant Access</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-300 mr-3">✅</span>
                    <span>30-Day Money Back Guarantee</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-300 mr-3">✅</span>
                    <span>Join 47,832+ Winners</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-yellow-400 text-black p-6 rounded-2xl mb-8">
              <div className="text-2xl font-black mb-2">
                💰 PRICE GOES UP TO $3,276 AFTER TIMER EXPIRES!
              </div>
              <div className="text-lg font-bold">
                Save $3,179 - But Only If You Act NOW!
              </div>
            </div>
          </div>
        </div>

        {/* Final Urgent CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-red-600 via-red-700 to-red-800 p-12 rounded-3xl border-4 border-yellow-400 relative overflow-hidden">
            {/* Pulsing elements */}
            <div className="absolute top-4 left-4 animate-bounce">
              <div className="text-4xl">⚡</div>
            </div>
            <div className="absolute top-4 right-4 animate-bounce delay-300">
              <div className="text-4xl">🚨</div>
            </div>
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-pulse">
              <div className="text-4xl">💰</div>
            </div>

            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-black text-white mb-6">
                🔥 FINAL CALL: SECURE YOUR SPOT NOW!
              </h3>

              <div className="bg-yellow-400 text-black p-6 rounded-2xl mb-8 inline-block">
                <div className="text-2xl font-black">ONLY {spotsLeft} SPOTS LEFT!</div>
                <div className="text-lg font-bold">Timer: {formatTime(timeLeft)}</div>
              </div>

              <p className="text-2xl text-yellow-100 mb-8 font-bold max-w-4xl mx-auto">
                This is it! The moment that separates the <span className="text-green-400">winners from the wishers</span>. 
                Don&apos;t let this opportunity slip away - <span className="text-red-300">you&apos;ll regret it forever!</span>
              </p>

              <div className="flex flex-col lg:flex-row gap-6 justify-center items-center mb-8">
                <Link href="/register" className="w-full lg:w-auto">
                  <Button className="w-full lg:w-auto px-16 py-8 text-3xl font-black bg-green-600 hover:bg-green-700 hover:scale-110 transform transition-all shadow-2xl rounded-2xl">
                    🚀 YES! SECURE MY SPOT NOW!
                  </Button>
                </Link>
              </div>

              <div className="bg-black/50 backdrop-blur-sm text-white p-6 rounded-2xl border-2 border-yellow-400">
                <div className="text-xl font-bold mb-2">🔒 100% RISK-FREE GUARANTEE</div>
                <div className="text-lg mb-4">
                  Not satisfied? Get every penny back within 30 days. No questions asked.
                </div>
                <div className="text-yellow-200 font-bold animate-pulse">
                  ⚠️ But this price expires in {formatTime(timeLeft)}!
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ScarcityUrgency;
