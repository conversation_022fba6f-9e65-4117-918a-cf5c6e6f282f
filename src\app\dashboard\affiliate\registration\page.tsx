import axios from '@/lib/axios'
import { getCurrentUser } from '@/lib/session'
import Avatar from '@/components/Pages/Dashboard/Settings/Avatar'
import { Country } from '@/types/CountryModel'
import getCountryInfo from '@/lib/countries'
import RegistrationForm from '@/components/Pages/Dashboard/Affiliate/RegistrationForm'

export const metadata = {
    title: 'Settings',
    description: 'Settings page for your Crypto University account',
    keywords: ['Settings', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const getUserData = async (token: string) => {
    try {
        const data = await axios.get('/auth/profile', {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        })
        return data
    } catch (error: any) {
        console.error(error)
        throw error
    }
}

const AffiliateRegistrationPage = async () => {
    const user = await getCurrentUser()
    const countries: Country[] = await getCountryInfo()
    const accessToken = user?.access_token || ''
    const { data } = await getUserData(accessToken)
    return (
        <section className="container mx-auto flex flex-col gap-12 py-14">
            {/* <div className="border-b border-gray-700" /> */}
            <div className="flex flex-col gap-9">
                <h1 className="text-b3 font-semibold">Affiliate registration Form</h1>
                <RegistrationForm token={accessToken} user={data.user} />
            </div>
            <div className="border-b border-gray-700" />
        </section>
    )
}

export default AffiliateRegistrationPage

