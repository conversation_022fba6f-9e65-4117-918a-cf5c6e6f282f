import fetch from '@/lib/fetch'
import fetchInstance from '@/lib/fetch'
import { CourseDetailsModel, CourseModel, CourseModelV2 } from '@/types/CourseModel'
import InstructorModel from '@/types/InstructorModel'

interface CoursesProps {
    user: any
    booleanFilter: boolean
    filter: string | string[] | undefined
    pageNumber: string | string[] | undefined
}
interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}
interface lessons {
    description: string
    id: number
    name: string
    rank: number
    video_link: string
    quizzes: [
        {
            id: number
            question: string
            choices: string[]
            answer: string
            lesson_id: number | null
            course_id: number | null
            created_at: Date
            updated_at: Date
            archived_course_id: number | null
        }
    ]
    course_topics: topics[]
}
interface topics {
    id: number
    name: string
    rank: number
    video_link: string
    description: string
}
interface course {
    course: CourseModel
    instructor: InstructorModel
}

//Get Course With Stat
const getStats = async (slug: string) => {
    try {
        const response1: { response: CourseDetailsModel } = await fetch('/course/course-details/' + slug, {
            next: { revalidate: 10 },
        })
        const response: CourseDetailsModel = response1.response
        return response
    } catch (error) {
        console.error('Error:', error)
        return error
    }
}

// Get courses
const GetCourses = async ({ user, booleanFilter, filter, pageNumber }: CoursesProps) => {
    try {
        const response = await fetchInstance(
            '/user-course/my-courses?type=' +
                (booleanFilter ? filter : 'all') +
                '&pageNumber=' +
                pageNumber +
                '&pageSize=30',
            {
                next: { revalidate: 5 },
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            }
        )
        return response
    } catch (error) {
        console.error('Error Users: ', error)
        return error
    }
}

// Check if course exists
function getCourseById({ courses, params }: { courses: Model; params: { course: string } }): CourseModelV2 | null {
    const course = courses.courses.find(c => c.slug === params.course)
    return course || null
}

// Get course details
const GetOneCourse = async (user: any, slug: string) => {
    if (slug !== undefined) {
        try {
            const response: { course: CourseModel } = await fetch('/course/courseslug/' + slug, {
                next: { revalidate: 10 },
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            const course: CourseModel = response.course

            const res: { instructor: InstructorModel } = await fetch('/instructor/' + course.instructor_id, {
                next: { revalidate: 10 },
            })
            const instructor: InstructorModel = res.instructor

            return { course, instructor }
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
}

//Get Quizzes For Course
const GetQuizzesCourse = async (user: any, slug: string) => {
    if (slug !== undefined) {
        try {
            const response = await fetchInstance('/quiz/courses/' + slug, {
                next: { revalidate: 10 },
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response.quiz
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
}

//Get Assignemnts For Course
const GetAssignmentsCourse = async (user: any, slug: string) => {
    if (slug !== undefined) {
        try {
            const response = await fetchInstance('/assignment/courseslug/' + slug, {
                next: { revalidate: 10 },
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response.assignments
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
}

// Get One Assignment
const GetOneAssignment = async (user: any, id: string) => {
    if (id !== undefined) {
        try {
            const response = await fetchInstance('/assignment/' + id, {
                next: { revalidate: 10 },
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response.assignment
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
}

// Get lessons
const getFirstLesson = ({ course }: { course: course }): lessons | undefined => {
    const firstLesson = course?.course.course_lessons
        .filter(lesson => lesson.course_topics.length > 0)
        .sort((a, b) => a.rank - b.rank)[0]
    return firstLesson
}

const GetFirstTopic = ({ course }: { course: course }): topics | undefined => {
    const firstTopic = getFirstLesson({ course })?.course_topics.find(topic => topic.rank === 1)
    return firstTopic
}

const getCurrentLesson = ({
    course,
    params,
}: {
    course: course
    params: { course: string; lesson: string; topic?: string }
}): lessons | undefined => {
    const lesson = course?.course.course_lessons.find(
        lesson => lesson.name.toLowerCase().replace(/\s/g, '-') === params.lesson
    )
    return lesson
}

const checkLessons = ({
    course,
    params,
}: {
    course: course
    params: { course: string; lesson: string; topic: string }
}): boolean => {
    const lesson = course?.course.course_lessons.find(
        lesson => lesson.name.toLowerCase().replace(/\s/g, '-') === params.lesson
    )
    return lesson ? true : false
}

const checkTopics = ({
    course,
    params,
}: {
    course: course
    params: { course: string; lesson: string; topic: string }
}): boolean => {
    const topic = getCurrentLesson({ course, params })?.course_topics.find(
        topic => topic.name.toLowerCase().replace(/\s/g, '-') === params.topic
    )
    return topic ? true : false
}

const getTopic = ({
    course,
    params,
}: {
    course: course
    params: { course: string; lesson: string; topic: string }
}): topics | undefined => {
    const topic = getCurrentLesson({ course, params })?.course_topics.find(
        topic => topic.name.toLowerCase().replace(/\s/g, '-') === params.topic
    )
    return topic
}

export {
    GetCourses,
    getCourseById,
    GetOneCourse,
    getFirstLesson,
    GetFirstTopic,
    getCurrentLesson,
    checkLessons,
    checkTopics,
    getTopic,
    getStats,
    GetQuizzesCourse,
    GetAssignmentsCourse,
    GetOneAssignment,
}
