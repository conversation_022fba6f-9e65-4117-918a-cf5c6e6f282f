'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import fetch from '@/lib/fetch'
import { Logout } from '@public/global'
import Side from '../Course/Side/Lesson'
import { signOut } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { CourseModel } from '@/types/CourseModel'
import { Menu, Myclass, Coaching, Affiliate, Statistic, FAQ, Help, Setting, Indicators,Subscriptions } from '@public/dashboard'

interface LinkItemProps {
    href: string
    icon: (active: boolean) => React.ReactNode
    active: boolean
    disabled?: boolean
    text: string
}

const LinkItem = ({ active, href, disabled, icon, text }: LinkItemProps) => {
    if (disabled)
        return (
            <p
                aria-label={text}
                className={cn(
                    'flex cursor-pointer gap-[1.375rem] rounded-xl bg-transparent py-[1.1rem] pl-8 text-gray-500 transition-[background] duration-150'
                )}>
                {icon(active)}
                <span>{text}</span>
            </p>
        )
    else
        return (
            <Link
                aria-label={text}
                href={href}
                className={cn(
                    'flex cursor-pointer gap-[1.375rem] rounded-xl py-[1.1rem] pl-8 ',
                    active
                        ? 'bg-blue text-white'
                        : 'bg-transparent text-black transition-[background] duration-150 hover:bg-gray-200'
                )}>
                {icon(active)}
                <span>{text}</span>
            </Link>
        )
}

const navItems = [
    {
        href: '/dashboard',
        text: 'My Courses',
        icon: (active: boolean) => Myclass({ active }),
    },
    {
        href: '/dashboard/subscriptions',
        icon: (active: boolean) => Subscriptions({ active }),
        text: 'My Subscriptions',
    },
    {
        href: '/dashboard/indicators',
        icon: (active: boolean) => Indicators({ active }),
        text: 'My Indicators',
    },
    {
        href: '/dashboard/consultation',
        icon: (active: boolean) => Coaching({ active }),
        text: 'My Mentorships',
    },
    {
        href: '/dashboard/affiliate',
        icon: (active: boolean) => Affiliate({ active }),
        text: 'Affiliate',
    },
    {
        href: '/dashboard/statistics',
        icon: (active: boolean) => Statistic({ active }),
        disabled: false,
        text: 'My Statistics',
    },
]
const navItems2 = [
    {
        href: '/dashboard/settings',
        icon: (active: boolean) => Setting({ active }),
        text: 'Setting',
    },
    {
        href: '/help',
        text: 'Help',
        icon: (active: boolean) => Help({ active }),
    },
    {
        href: '/faqs',
        icon: (active: boolean) => FAQ({ active }),
        text: 'FAQs',
    },
]

export const Sidebar = ({ user }: { user: any }) => {
    const currentRoute = usePathname()
    const [data, setData] = useState<CourseModel>()
    const [quizzes, setQuizzes] = useState<QuizModel[]>([])
    const [assignments, setAssignments] = useState<AssignmentModel[]>([])
    const [isLoaded, setIsLoaded] = useState(true)
    useEffect(() => {
        const fetchData = async () => {
            try {
                const course = await fetch('/course/courseslug/' + currentRoute.split('/')[2], {
                    headers: {
                        Authorization: `Bearer ${user.access_token}`,
                    },
                })
                const quizzes = await fetch('/quiz/courses/' + currentRoute.split('/')[2], {
                    headers: {
                        Authorization: `Bearer ${user.access_token}`,
                    },
                })
                const assignments = await fetch('/assignment/courseslug/' + currentRoute.split('/')[2], {
                    headers: {
                        Authorization: `Bearer ${user.access_token}`,
                    },
                })

                const data1 = await course.course
                const data2 = await quizzes.quiz
                const data3 = await assignments.assignments
                setData(data1)
                setQuizzes(data2)
                setAssignments(data3)
                setIsLoaded(false)
            } catch (error) {
                setIsLoaded(false)
                console.error(error)
            }
        }
        fetchData()
    }, [currentRoute, user.access_token])

    if (
        '/dashboard' === currentRoute ||
        '/dashboard/affiliate' === currentRoute ||
        '/dashboard/statitsics' === currentRoute ||
        '/dashboard/settings' === currentRoute ||
        '/dashboard/settings/order-history' === currentRoute ||
        '/dashboard/settings/memberships' === currentRoute ||
        '/dashboard/statistics' === currentRoute ||
        '/dashboard/affiliate' === currentRoute ||
        '/dashboard/affiliate/payouts' === currentRoute ||
        '/dashboard/affiliate/referrals' === currentRoute ||
        '/dashboard/affiliate/registration' === currentRoute ||
        '/dashboard/consultation' === currentRoute ||
        '/dashboard/indicators' === currentRoute ||
        '/dashboard/subscriptions' === currentRoute
    ) {
        return (
            <div className="select-none border-r border-gray-700 bg-white font-sans font-medium max-lg:hidden ">
                <ul className=" sticky top-[0px] flex h-[calc(100vh-7rem)] select-none flex-col gap-6 overflow-hidden scrollbar scrollbar-hidden hover:scrollbar-auto">
                    <li>
                        <button className="flex gap-4 pb-8 pl-8 pr-44 pt-28 max-[1367px]:pb-5 max-[1367px]:pt-16">
                            <Menu /> <span>Menu</span>
                        </button>
                    </li>
                    <ul className="space-y-2 pl-6 pr-10 ">
                        {navItems.map((item, index) => (
                            <li key={index}>
                                <LinkItem
                                    href={item.href}
                                    active={item.href === currentRoute}
                                    disabled={item.disabled}
                                    icon={item.icon}
                                    text={item.text}
                                />
                            </li>
                        ))}
                    </ul>
                    <div className="px-9">
                        <hr className="text-gray" />
                    </div>
                    <ul className="space-y-2 pl-6 pr-10">
                        {navItems2.map((item, index) => (
                            <li key={index}>
                                <LinkItem
                                    href={item.href}
                                    active={item.href === currentRoute}
                                    icon={(active: boolean) => {
                                        return item.icon(active)
                                    }}
                                    text={item.text}
                                />
                            </li>
                        ))}
                    </ul>
                    <div className="px-9">
                        <hr className="text-gray" />
                    </div>
                    <div className="space-y-2 pl-6 pr-10">
                        <button
                            aria-label={'logout button'}
                            type="button"
                            onClick={() => signOut()}
                            className={
                                'flex w-full cursor-pointer items-center gap-[1.375rem] rounded-xl bg-transparent py-[1.1rem] pl-8 leading-none text-red transition-[background] duration-150 hover:bg-red/10'
                            }>
                            <Logout />
                            <span>Log Out</span>
                        </button>
                    </div>
                </ul>
            </div>
        )
    }
    return (
        <div className="border-r border-gray-700 bg-white font-sans font-medium max-lg:hidden ">
            <ul className="sticky top-[0px] flex h-[calc(100vh-7rem)] select-none flex-col gap-6 overflow-hidden scrollbar scrollbar-hidden hover:scrollbar-auto ">
                <li>
                    <Link aria-label="Back" href="/dashboard">
                        <button className="mb-8 ml-8 flex items-center gap-4 pr-44 pt-8 max-[1367px]:mb-5 max-[1367px]:mt-16">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="36"
                                height="36"
                                fill="none"
                                viewBox="0 0 36 36">
                                <path
                                    stroke="#676A73"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.5"
                                    d="M21.5 25l-7-7 7-7"></path>
                                <rect width="35" height="35" x="0.5" y="0.5" stroke="#676A73" rx="17.5"></rect>
                            </svg>{' '}
                            <span>Back</span>
                        </button>
                    </Link>
                </li>
                {isLoaded ? (
                    <ul className="space-y-4 pl-6 pr-10">
                        {[...Array(5)].map((_, index) => (
                            <li className="flex flex-col gap-6" key={index}>
                                <div className="h-5 w-40 animate-pulse rounded-3xl bg-gray-500" />
                                <ul className="flex flex-col gap-3">
                                    {[...Array(5)].map((_, topicIndex) => (
                                        <li key={topicIndex}>
                                            <div className="flex animate-pulse cursor-pointer items-center justify-between gap-12 rounded-full bg-gray-500 px-[14px] py-6" />
                                        </li>
                                    ))}
                                </ul>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <ul className="space-y-4 pl-6 pr-10">
                        {data?.course_lessons
                            .sort((a, b) => a.rank - b.rank)
                            .filter(lesson => lesson.course_topics.length > 0)
                            .map((lesson, lessonIndex) => (
                                <li className="flex flex-col gap-6" key={lessonIndex}>
                                    <h1 className="max-w-[248px] text-sub3 font-semibold capitalize">{lesson.name}</h1>
                                    <ul className="flex flex-col gap-3">
                                        {lesson.video_link && (
                                            <li key={lessonIndex}>
                                                <Side
                                                    isPlay={
                                                        '/dashboard/' +
                                                            data.slug +
                                                            '/' +
                                                            lesson.name.toLowerCase().replace(/\s/g, '-') ==
                                                        currentRoute
                                                    }
                                                    isLesson
                                                    title={'Introduction of ' + lesson.name}
                                                    link={
                                                        '/dashboard/' +
                                                        data.slug +
                                                        '/' +
                                                        lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                        '/'
                                                    }
                                                />
                                            </li>
                                        )}
                                        {lesson.course_topics
                                            .sort((a, b) => a.rank - b.rank)
                                            .map((topic, topicIndex) => (
                                                <li key={topicIndex}>
                                                    <Side
                                                        isPlay={
                                                            '/dashboard/' +
                                                                data.slug +
                                                                '/' +
                                                                lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                                '/' +
                                                                topic.name.toLowerCase().replace(/\s/g, '-') ==
                                                            currentRoute
                                                        }
                                                        topicID={topic.id}
                                                        isDone={topic.isCompleted}
                                                        title={topic.name}
                                                        link={
                                                            '/dashboard/' +
                                                            data.slug +
                                                            '/' +
                                                            lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                            '/' +
                                                            topic.name.toLowerCase().replace(/\s/g, '-')
                                                        }
                                                    />
                                                </li>
                                            ))}
                                        {lesson?.quizzes.map((quiz, topicIndex) => (
                                            <li key={topicIndex}>
                                                <Side
                                                    isQuiz
                                                    quizID={quiz.id}
                                                    user={user}
                                                    isPlay={
                                                        '/dashboard/' +
                                                            data.slug +
                                                            '/' +
                                                            lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                            '/' +
                                                            'quiz' ==
                                                        currentRoute
                                                    }
                                                    title={'Quiz'}
                                                    link={
                                                        '/dashboard/' +
                                                        data.slug +
                                                        '/' +
                                                        lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                        '/' +
                                                        'quiz'
                                                    }
                                                />
                                            </li>
                                        ))}
                                    </ul>
                                </li>
                            ))}
                        {quizzes ? (
                            <li className="flex flex-col gap-6">
                                <h1 className="text-sub3 font-semibold capitalize text-yellow">Quizzes</h1>
                                <ul className="flex flex-col gap-3">
                                    {quizzes.map((quiz, quizIndex) => (
                                        <li key={quizIndex}>
                                            <Side
                                                isQuiz
                                                quizID={quiz.id}
                                                user={user}
                                                isPlay={'/dashboard/' + data?.slug + '/quiz/' + quiz.id == currentRoute}
                                                title={'Quiz ' + (quizIndex + 1)}
                                                link={'/dashboard/' + data?.slug + '/quiz/' + quiz.id}
                                            />
                                        </li>
                                    ))}
                                </ul>
                            </li>
                        ) : null}
                        {assignments ? (
                            <li className="flex flex-col gap-6">
                                <h1 className="text-sub3 font-semibold capitalize text-green-dark">Assignments</h1>
                                <ul className="flex flex-col gap-3">
                                    {assignments.map((ass, quizIndex) => (
                                        <li key={quizIndex}>
                                            <Side
                                                isAss
                                                assID={ass.id}
                                                user={user}
                                                isPlay={
                                                    '/dashboard/' + data?.slug + '/assignment/' + ass.id == currentRoute
                                                }
                                                title={ass.name.split('').slice(0, 15).join('') + '...'}
                                                link={'/dashboard/' + data?.slug + '/assignment/' + ass.id}
                                            />
                                        </li>
                                    ))}
                                </ul>
                            </li>
                        ) : null}
                    </ul>
                )}
            </ul>
        </div>
    )
}
