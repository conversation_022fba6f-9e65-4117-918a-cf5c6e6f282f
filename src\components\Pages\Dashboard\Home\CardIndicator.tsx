'use client'
import { cn } from '@/lib/cn'
import React, { useState } from 'react'
import Image from '@/components/Ui/Image'
import { IndicatorModel } from '@/types/IndicatorModel'

interface props {
    indicator: IndicatorModel
}

const CardIndicator = ({ indicator }: props) => {
    const [onCLick, setOnClick] = useState(false)
    function capitalizeIndicatorName(indicatorName: string) {
        return indicatorName.replace(/\b\w/g, function (match) {
            return match.toUpperCase()
        })
    }
    return (
        <div
            className={`flex h-[300px] sm:h-[250px]  w-[260px] min-w-[260px] max-w-[260px] cursor-pointer flex-col gap-6 overflow-hidden rounded-lg border border-gray-700 transition-all max-md:w-full max-md:min-w-full max-md:max-w-[100%]  ${onCLick ? 'h-full justify-between pb-2 ' : ''
                }`}>
            <div
                onClick={() => setOnClick(!onCLick)}
                className={cn(`flex flex-col gap-3 `, !onCLick ? 'pb-2' : 'pb-0')}>
                <div className="relative">
                    <Image
                        src={indicator.image}
                        width={207}
                        height={240}
                        className="h-[135px] w-full select-none rounded-lg object-cover  max-md:h-[154px]"
                        alt={indicator.title}
                    />
                </div>
                <div className="flex flex-col gap-3 px-3">
                    <h2 className="select-none text-sub3 font-medium">
                        {capitalizeIndicatorName(indicator.title).length > 42 ? (
                            <span>{capitalizeIndicatorName(indicator.title).substring(0, 42)}...</span>
                        ) : (
                            <span>{capitalizeIndicatorName(indicator.title)}</span>
                        )}
                    </h2>
                    <div className="flex items-center gap-2">
                    <p className="select-none font-bold text-[#38761d] font-sans text-cap1">
                           Price: ${indicator.price}
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <p className="select-none font-sans text-cap1">
                            {indicator.transactions.length == 0 ? 'N/A' : ( `Purchased on: ${indicator.transactions[0].created_at.split('T')[0]}`)}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CardIndicator
