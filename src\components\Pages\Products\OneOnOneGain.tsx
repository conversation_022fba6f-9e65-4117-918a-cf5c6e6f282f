import Image from 'next/image'
import React from 'react'
import crown from '../../../../public/icons/crown.svg'
import Link from 'next/link'
import yellowbutton from '../../../../public/icons/yellowbutton.svg'

import hodl from '../../../../public/icons/hodl.png'
import whale from '../../../../public/icons/whale.png'
import trader from '../../../../public/icons/trader.png'
import currency from '../../../../public/icons/currency (1).png'
import bullmarket from '../../../../public/icons/bull-market.png'
import information from '../../../../public/icons/information.png'
import onlinelearning from '../../../../public/icons/online-learning.png'


const OneOnOneGain = () => {

    const gain = [
        {
            title: 'Dedicated Mentorship',
            description: 'Direct access to Grey Jabesi. Your crypto journey is his priority.',
            image: whale,
        },
        {
            title: 'Intensive Learning',
            description: 'Five customizable sessions per week, crafted to deepen your understanding and expertise. ',
            image: onlinelearning,
        },
        {
            title: 'Complimentary Masterclass',
            description: 'Free access to the acclaimed Web 3 Masterclass, valued at $500. ',
            image: hodl,
        },
        {
            title: 'Exclusive Trading Signals',
            description: 'Get insider access to Grey’s personal trading signals.',
            image: bullmarket,
        },
        {
            title: 'Hands-On Trading Experience',
            description: 'Trade alongside Grey, learning practical trading strategies in real-time. ',
            image: trader,
        },
        {
            title: 'Innovative Skills',
            description:
                'Learn to create your own cryptocurrency and NFTs, opening new avenues for investment and creativity.',
            image: currency,
        },
    ]
  return (
      <div className="bg-[#FAFAFA] p-[32px] md:p-[64px]">
          <h2 className="mb-5 text-[24px] font-[600] text-[#222222] md:text-center md:text-[48px]">What You’ll Gain</h2>
          <div className="mx-auto grid  max-w-[1100px] justify-center gap-5 md:grid-cols-3 md:justify-start md:text-left">
              {gain.map((item, index) => (
                  <div
                      key={index}
                      className="flex w-[342px] justify-center gap-4 md:gap-2 border bg-white p-3 md:grid md:h-[282px] md:justify-start md:p-5">
                      <Image
                          src={item.image}
                          height={92}
                          width={92}
                          alt="crown"
                          className=" h-[53px] w-[53px] md:h-[92px] md:w-[92px]" // Add mx-auto class here
                      />
                      <div className="">
                          <h2 className="text-[15px] font-[600] text-[#222222] md:text-[22px]">{item.title}</h2>
                          <p className="w-[236px] text-[12px] font-[400] text-[#5B5B5B] md:w-[293px] md:text-[15px]">
                              {item.description}
                          </p>
                      </div>
                  </div>
              ))}
          </div>
          <div className="mx-auto my-5 flex max-w-[343px] justify-center border bg-white p-2 md:h-[209px] md:max-w-[762px] md:p-5">
              <Image
                  src={information}
                  height={92}
                  width={92}
                  alt="crown"
                  className=" mx-2 my-1 h-[53px] w-[53px] md:mx-5 md:h-[92px] md:w-[92px]" // Add mx-auto class here
              />
              <div className="p-2 md:p-5">
                  <h2 className="text-[18px] font-[600] text-[#222222] md:text-[24px]">Application Details</h2>
                  <p className="text-[12px] font-[400] text-[#5B5B5B] md:text-[16px]">
                      This mentorship is highly selective and tailored for individuals committed to serious growth in
                      the crypto space. Applying does not guarantee acceptance as we strive to ensure the best fit
                      between mentor and mentee to maximize results.
                  </p>
              </div>
              
          </div>
          <Link aria-label="View products" target='_blank' href={'https://noteforms.com/forms/mentorship-request-d8avmm?notionforms=1'} className="my-3">
                  <Image
                      src={yellowbutton}
                      height={54}
                      width={341}
                      alt="crown"
                      className="mx-auto my-5" // Add mx-auto class here
                  />
              </Link>
      </div>
  )
}

export default OneOnOneGain
