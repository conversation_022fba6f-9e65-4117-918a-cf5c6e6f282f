import DiscordSubscription from "@/components/Pages/Subscriptions/Discord";
import { getCurrentUser } from "@/lib/session";
import NoDiscord from "@/components/Pages/Subscriptions/NoDiscord";
import SuccessDiscord from "@/components/Pages/Subscriptions/SuccessDiscord";

interface PageProps {
  slug: any;
}
interface PageSlugProps {
  hash?: string;
  newUser?: string;
}

async function isSuccessPayment(searchParams: PageSlugProps, user: any) {
  const { hash, newUser } = searchParams;
  if (hash !== undefined)
    try {
      if (newUser === "true" && !user) return { user_status: false };
      else {
        const check = await fetch(
          `${process.env.API_URL}/payment/check_hax?hash=${hash as string}`,
          {
            headers: {
              Authorization: `Bearer ${user?.access_token}`,
            },
          }
        );
        const data = await check.json();
        return data;
      }
    } catch (error: any) {
      console.error("Hash Error:", error);
      return error;
    }
  else return { statusCode: 402 };
}

const DiscordSubscriptionPage = async ({
  params,
  searchParams,
}: {
  params: PageProps;
  searchParams: PageSlugProps;
}) => {
  const user = await getCurrentUser();

  console.log("searchParams:", searchParams); // Debugging: Check if searchParams are received

  if (searchParams.hash) {
    console.log("Tlowamo:");
    const hash = searchParams.hash;
    const data = await isSuccessPayment(searchParams, user);
    console.log("data:", data);
    console.log("user:", user);
    return (
        <section className="flex w-full flex-col">
        {user ? (
          data?.status === true && user?.discord_id === null ? (
            <DiscordSubscription hash={hash} />
          ) : (
            <SuccessDiscord />
          )
        ) : (
          <DiscordSubscription hash={hash} />
        )}
      </section>
    );
  }

  return (
    <section className="flex w-full flex-col">
      <SuccessDiscord />
    </section>
  );
};

export default DiscordSubscriptionPage;


// import DiscordSubscription from "@/components/Pages/Subscriptions/Discord"
// import { getCurrentUser } from '@/lib/session'
// import NoDiscord from "@/components/Pages/Subscriptions/NoDiscord"
// import SuccessDiscord from "@/components/Pages/Subscriptions/SuccessDiscord"

// interface PageProps {
//     slug: any
// }
// interface PageSlugProps {
//     hash?: string
//     newUser?: string
// }

// async function isSuccessPayment(searchParams: PageSlugProps, user: any) {
//     const { hash, newUser } = searchParams
//     if (hash !== undefined)
//         try {
//             if (newUser === 'true' && !user) return { user_status: false }
//             else {
//                 const check = await fetch(`${process.env.API_URL}/payment/check_hax?hash=${hash as string}`, {
//                     headers: {
//                         Authorization: `Bearer ${user?.access_token}`,
//                     },
//                 })
//                 const data = await check.json()
//                 return data
//             }
//         } catch (error: any) {
//             console.error('Hash Error:', error)
//             return error
//         }
//     else return { statusCode: 402 }
// }


// const DiscordSubscriptionPage = async ({ params, searchParams }: { params: PageProps; searchParams: PageSlugProps }) => {

//     const user = await getCurrentUser()
    

//     if (searchParams.hash) {

//         const hash = searchParams.hash;
//         const data = await isSuccessPayment(searchParams, user)
//         return (
//             <section className="flex w-full flex-col">
//                 {data?.status === true && user?.discord_id === null ? (
//                     <DiscordSubscription hash={hash} />
//                 ) : (<SuccessDiscord />)}
//             </section>
//         )
//     }

//     return (
//         <section className="flex w-full flex-col">
//             <SuccessDiscord />
//         </section>
//     )
// }

// export default DiscordSubscriptionPage



