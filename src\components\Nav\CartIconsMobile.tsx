'use client'
import useCart from '@/hooks/useCart'
import useWishlist from '@/hooks/useWishlist'
import { cn } from '@/lib/cn'
import { Whishlist as WhishlistIcon } from '@public/home'
import { Cart as CartIcon } from '@public/home'
import Link from 'next/link'
import { useEffect, useState } from 'react'

interface Props {
    handleOpen: () => void
}

const CartIconsMobile = ({ handleOpen}:Props) => {
    const wishlist = useWishlist()
    const cart = useCart()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])
    if (!isMounted) return null
    return (
        <div className="flex gap-4">
            <Link aria-label='Wishlist button' onClick={handleOpen} href={"/wishlist"} className='relative border cursor-pointer border-gray-700 rounded-full w-12 h-12 flex items-center justify-center'>
                <WhishlistIcon  />
                <div
                    className={cn(
                        'absolute inset-0 left-7 top-8 flex h-[20px] w-[20px] items-center justify-center rounded-full bg-blue ',
                        wishlist.products.length > 0 ? '' : 'hidden'
                    )}>
                    <p className="text-cap2 text-white"> {wishlist.products.length}</p>
                </div>
            </Link>
            <Link onClick={handleOpen} aria-label='Cart button' href={"/cart"} className='relative border cursor-pointer border-gray-700 rounded-full w-12 h-12 flex items-center justify-center'>
                <CartIcon  />
                <div
                    className={cn(
                        'absolute inset-0 left-7 top-8 flex h-[20px] w-[20px] items-center justify-center rounded-full bg-blue ',
                        cart.products.length > 0 ? '' : 'hidden'
                    )}>
                    <p className="text-cap2 text-white"> {cart.products.length}</p>
                </div>
            </Link>
        </div>
    )
}

export default CartIconsMobile
