"use client"
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useEffect, useRef } from 'react'
import { Search } from "@public/global";
import useBlogSearch from "@/hooks/useBlogSearch";

const BlueSearch = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
      <path
          d="M14.9354 16.7228C14.734 16.7228 14.5494 16.6389 14.3983 16.4879L10.4043 12.3932C10.119 12.0912 10.119 11.6212 10.421 11.3359C10.723 11.0507 11.193 11.0507 11.4783 11.3527L15.4723 15.4641C15.7576 15.7661 15.7576 16.2361 15.4556 16.5214C15.3213 16.6557 15.1198 16.7227 14.9352 16.7227L14.9354 16.7228Z"
          fill="#2655FF"
      />
      <path
          d="M6.83015 13.8033C3.05431 13.8033 6.71654e-05 10.7658 6.71654e-05 7.0403C-0.0165552 3.3148 3.05431 0.277344 6.83015 0.277344C10.606 0.277344 13.6602 3.3148 13.6602 7.0403C13.6602 10.7658 10.5892 13.8033 6.83015 13.8033ZM6.83015 1.7709C3.87663 1.7709 1.47684 4.13701 1.47684 7.0403C1.47684 9.94348 3.87654 12.3097 6.83015 12.3097C9.76692 12.3097 12.1667 9.94348 12.1667 7.0403C12.1668 4.13701 9.76698 1.7709 6.83015 1.7709Z"
          fill="#2655FF"
      />
  </svg>
)

const Hero = () => {

  const { data, error, isLoading, empty, SetSearch } = useBlogSearch()

  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleOutSideClick = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        SetSearch('')
      }
    }

    window.addEventListener('mousedown', handleOutSideClick)

    return () => {
      window.removeEventListener('mousedown', handleOutSideClick)
    }
  }, [ref, SetSearch])

  const renderTitle = (title: string) => {
    return { __html: title };
  };

  return (
    <section className="bg-yellow-light py-[4.5rem] pt-[4.8125rem] relative max-sm:mb-[5.25rem] mb-5">
      <div className="container mx-auto">
        <div className="space-y-8">
          <h1 className="text-h2 max-md:text-b2 font-medium">
            Crypto University Blog
          </h1>

          <div className="relative flex items-center max-sm:absolute max-sm:-bottom-6 max-sm:w-[calc(100vw-32px)] max-sm:mx-auto">
            <input
              type="search"
              name="search"
              id="search_blog"
              autoComplete="off"
              placeholder="Search blogs"
              onChange={e => {
                SetSearch(e.target.value)
              }}
              className="py-[13.5px] pl-14 w-[319px] max-md:w-full pr-4 
                        rounded-full shadow-[2px_4px_30px_0px_#0000001A]
                        outline-none text-sub3 max-sm:text-cap2 leading-[20.8px] font-medium
                        placeholder:text-black max-sm:py-[1.125rem] max-sm:placeholder:text-[#676A73]"
            />
            <div className="absolute left-6 max-sm:hidden">
              <Search />
            </div>
            <div className="absolute left-6 sm:hidden">
              <Search height={15} width={15} />
            </div>
          </div>

          <div
            ref={ref}
            className={cn(
              'absolute mt-7 flex w-[500px] flex-col rounded-lg bg-white p-6 shadow-[4px_8px_100px_0px_rgba(0,0,0,0.25)]',
              empty && 'hidden'
            )}>
            {isLoading ? (
              <div className="text-sub3 font-medium">Searching...</div>
            ) : data.length > 0 ? (
              <div className="space-y-6">
                <Link
                  aria-label={data[0].title as string}
                  href={`blog/${data[0].slug}`}
                  onClick={() => SetSearch('')}
                  className="text-sub3 capitalize text-black transition-colors duration-150 hover:text-blue"
                  dangerouslySetInnerHTML={renderTitle(data[0].title)}
                />
                {data.slice(1, 6).length > 0 && (
                  <div className="border-t border-gray-400 pt-6">
                    <div className="flex gap-2 pb-[13.5px] text-sub3 font-semibold">
                      <BlueSearch /> More blog results
                    </div>
                    <div className="flex flex-col gap-[15px]">
                      {data.slice(1, 6).map((item: any, index: number) => (
                        <Link
                          aria-label={item.title as string}
                          href={`blog/${item.slug}`}
                          key={index}
                          onClick={() => SetSearch('')}
                          className="text-sub3 capitalize text-black transition-colors duration-150 hover:text-blue"
                          dangerouslySetInnerHTML={renderTitle(item.title)}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : !error ? (
              <div className="text-sub3 font-semibold">No results</div>
            ) : (
              <div className="text-sub3 font-semibold">Error</div>
            )}
          </div>

        </div>
      </div>
    </section>
  );
};

export default Hero;
