export type Blog = {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    author_id: number;
    image: string;
    categories: {
        post_id: number;
        category_id: number;
        category: {
            name: string;
            slug: string;
        };
    }[];
    tags: string[];
    author: {
        name: string;
        slug: string;
    };
    status: string;
    published_date: string;
    created_at: string;
    updated_at: string;
};

export type Blogs = Blog[];

// export type Blog = {
//     pageInfo: {
//         offsetPagination: {
//             hasMore: boolean
//             hasPrevious: boolean
//             total: number
//         }
//         startCursor: string
//         endCursor: string
//     }
//     featuredImage: {
//         node: {
//             sourceUrl: string
//             altText: string
//         }
//     }
//     author: {
//         node: {
//             email: string
//             firstName: string
//             lastName: string
//             name: string
//             slug: string
//         }
//     }
//     title: string
//     date: string
//     slug: string
//     authorSlug: string
//     excerpt?: string
//     categories: {
//         nodes: {
//             name: string
//             slug: string
//         }[]
//     }
// }

// export type Blogs = Blog[]
