"use client"
import { useState } from "react";
import { signOut } from "next-auth/react";

const Signout = () => {
    const [isLoading, setIsLoading] = useState(false)

    const handleClick = async () => {
        setIsLoading(true)
        await signOut({ redirect: true, callbackUrl: '/' });
    }

    return (
        <button onClick={handleClick} className="px-4 py-2 bg-red/70 rounded hover:bg-red text-white">{isLoading ? "Loading..." : "Sign Out"}</button>
    )
}

export default Signout