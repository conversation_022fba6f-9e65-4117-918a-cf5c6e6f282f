'use client'
import { useState } from 'react'
import MarkdownComponent from '@/components/Ui/MarkdownComponent'
import { cn } from '@/lib/cn'

interface Props {
    description: string
}

const About = ({ description }: Props) => {
    const [showFullText, setShowFullText] = useState(false)
    return (
        <div id="about" className="flex flex-col pr-11 max-md:container max-md:mx-auto">
            <h1 className="text-b3 max-md:text-sub1">About</h1>
            <div className="flex flex-col">
                {/* Player */}
                <div className="font-sans text-sub2 text-gray-900 max-md:text-cap1">
                    {description.length > 300 && !showFullText ? (
                        <div className="flex flex-col items-start prose prose-h3:text-sub2 max-md:prose-h3:text-sub4">
                            <MarkdownComponent text={description.slice(0, 300) + '...'} />
                            <button className="text-blue" onClick={() => setShowFullText(true)}>
                                See All
                            </button>
                        </div>
                    ) : (
                        <div className="flex flex-col items-start prose prose-h3:text-sub2 max-md:prose-h3:text-sub4">
                            <MarkdownComponent text={description} />
                            <button className={cn("text-blue", description.length > 300 ? '' : 'hidden')} onClick={() => setShowFullText(false)}>
                                See less
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default About
