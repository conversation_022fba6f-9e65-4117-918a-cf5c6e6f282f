import Link from 'next/link'
import React from 'react'

const Arrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 6 10" fill="none">
        <path
            d="M5.0405 8.5722L0.959961 5.00045L5.0405 1.42871"
            stroke="#292929"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const HeroSlug = () => {
    return (
        <div className="bg-yellow-light py-8 text-black">
            <div className="container mx-auto flex">
                <Link aria-label='Blog page' href={'/blog'} className="flex items-center gap-[1.125rem]">
                    <div className="rounded-full bg-yellow px-[10px] py-[8px]">
                        <Arrow />
                    </div>
                    <p className="text-callout font-medium">Go Back to Crypto University Blogs</p>
                </Link>
            </div>
        </div>
    )
}

export default HeroSlug
