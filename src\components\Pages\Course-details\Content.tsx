'use client'
import AddModel from '../Cart/AddModel'
import Title from './Title'
import ListButtons from './ListButtons'
import Syllabus from './Syllabus'
import Briefly from './Briefly'
import { CourseDetailsModel, CourseModel } from '@/types/CourseModel'
import InstructorModel from '@/types/InstructorModel'
import { useState } from 'react'
import { cn } from '@/lib/cn'
import FaqsStatic from '@/components/Ui/FaqsStatic'

interface Props {
    product: CourseModel
    isEnrolled: boolean
    user: any
    slug: string
    instructor: InstructorModel
    stats :CourseDetailsModel
}


const Content = ({ product, isEnrolled, user, slug, instructor, stats }: Props) => {
    const [openModel, setOpenModel] = useState(false)
    return (
        <>
            <div className={cn('absolute inset-0 top-0 z-50 flex justify-center', openModel ? '' : 'hidden')}>
                <AddModel setOpenModel={setOpenModel} openModel={openModel} product={product} />
            </div>
            <div className="border-b border-gray-700 max-md:border-none">
                <Title course={product} />
            </div>
            <div className="border-b border-gray-700 max-md:border-none">
                <ListButtons isEnrolled={isEnrolled} user={user} course={product} slug={slug} />
            </div>
            <div>
                <div className=" mx-auto flex w-full md:container max-md:flex-col   ">
                    <Syllabus TopicsCount= {stats.count.topics} instructor={instructor} course={product} />
                    <div className="border-r" />
                    <Briefly setOpenModel={setOpenModel} course={product} reward={product.rewardable} isEnrolled={isEnrolled} />
                </div>
            </div>
            <div className="border-t max-md:border-none">
                <FaqsStatic />
            </div>
        </>
    )
}

export default Content
