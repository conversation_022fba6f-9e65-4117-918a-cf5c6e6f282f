import CryptoguideComponent from '@/components/Pages/CryptoGuide/CryptoguideComponent'
import { CryptoGuideCategoryPost, CryptoGuide, CryptoGuidePost } from '@/types/CryptoGuideCategoryPostModel'
import { notFound } from 'next/navigation'

export const metadata = {
    title: 'Crypto Guide',
    description: 'Learn about the basics of cryptocurrency and blockchain technology.',
    keywords: ['cryptocurrency', 'blockchain', 'guide', 'learn', 'beginner', 'Crypto university'],
}

// Fetch categories with posts
async function GetCategories() {
    try {
        const res = await fetch(`${process.env.API_URL}/cryptoguide/categories-with-posts`, {
            next: { revalidate: 10 }, // Revalidate data every 10 seconds
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.categories
    } catch (error) {
        console.error('Error fetching categories:', error)
        return null
    }
}

// Fetch default crypto guide post (e.g., the first post or a specific default post)
async function GetCryptoGuidePost() {
    try {
        const res = await fetch(`${process.env.API_URL}/cryptoguide/post-by-slug/cryptoguide-uniswap-tutorial-the-crypto-university`, {
            next: { revalidate: 10 }, // Revalidate data every 10 seconds
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.cryptoguide
    } catch (error) {
        console.error('Error fetching default crypto guide:', error)
        return null
    }
}

//get crypto guide titles
async function GetCryptoGuideTitles() {
    try {
        const res = await fetch(`${process.env.API_URL}/cryptoguide/titles`, {
            next: { revalidate: 10 }, // Revalidate data every 10 seconds
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.cryptoguides
    } catch (error) {
        console.error('Error fetching default crypto guide:', error)
        return null
    }
}

const CryptoGuidePage = async () => {
    // Fetch categories and default crypto guide
    const categories: CryptoGuideCategoryPost[] = await GetCategories()
    const cryptoGuide: CryptoGuide = await GetCryptoGuidePost()
    const categoriesTitles: CryptoGuidePost[] = await GetCryptoGuideTitles()

    // If no data is found, show a 404 page
    if (!categories || !cryptoGuide) {
        notFound()
    }

    return (
        <div className="w-full">
            <CryptoguideComponent categoriesTitles={categoriesTitles} categories={categories} cryptoGuide={cryptoGuide} />
        </div>
    )
}

export default CryptoGuidePage