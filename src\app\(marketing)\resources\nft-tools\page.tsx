import Skins from '@/components/Pages/Resources/Skins'
import { Axie, NextEarth, OpenSea, Rarible } from '@public/resources/tools'

export const metadata = {
    title: 'NFT Tools',
    description: 'NFT tools that Crypto University recommends to help you succeed in crypto.',
    keywords: ['NFT Tools', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const NFTPage = () => {
    const data = [
        {
            category: 'Market places',
            items: [
                { name: 'OpenSea', link: 'https://opensea.io/', img: OpenSea },
                { name: '<PERSON><PERSON><PERSON>', link: 'https://rarible.com/', img: Rarible },
            ],
        },
        {
            category: 'Metaverse',
            items: [
                {
                    name: 'Next Earth',
                    link: 'https://app.nextearth.io/buy-land',
                    img: NextEarth,
                },
            ],
        },
        {
            category: 'NFT Gaming',
            items: [
                {
                    name: 'Axie Infinity',
                    link: 'https://axieinfinity.com/',
                    img: Axie,
                },
            ],
        },
    ]

    return (
        <section className="w-full">
            <Skins data={data} />
        </section>
    )
}

export default NFTPage
