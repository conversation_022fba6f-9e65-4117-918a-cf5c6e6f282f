'use client'
import ImageShortcut from '@/components/Ui/Image'
import { Search } from '@public/global'
import { FiMenu } from "react-icons/fi";
import LeftSide from './LeftSide'
import RightSide from './RightSide'
import { useState } from 'react';
import { RiArrowUpSLine, RiArrowDownSLine, RiCloseLine } from "react-icons/ri";
import { CryptoGuideCategoryPost, CryptoGuide, CryptoGuidePost } from '@/types/CryptoGuideCategoryPostModel';
import Link from 'next/link';
import SearchCryptoguide from './SearchCryptoguide';

interface HeroProps {
    categories: CryptoGuideCategoryPost[];
    cryptoGuide: CryptoGuide;
    categoriesTitles: CryptoGuidePost[];
}

const CryptoguideComponent = ({ categories, cryptoGuide, categoriesTitles }: HeroProps) => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [openSection, setOpenSection] = useState<string | null>('Crypto 101');

    const toggleSection = (title: string) => {
        setOpenSection(openSection === title ? null : title);
    };

    const content = (
        <div className="h-full overflow-y-auto bg-white md:p-5">
            <div className="flex h-full flex-col">
                {/* Mobile Header */}
                <div className="flex items-center justify-between border-b border-gray-200 bg-[#FFF9EA] p-4 md:hidden">
                    <h2 className="text-xl font-bold">Topics</h2>
                    {!isMobileMenuOpen && (
                        <button
                            onClick={() => setIsMobileMenuOpen(false)}
                            className="text-2xl text-gray-500 hover:text-gray-700"
                            aria-label="Close menu">
                            <RiCloseLine />
                        </button>
                    )}
                </div>

                {/* Navigation List */}
                <nav className="flex-grow p-4">
                    <ul className="space-y-2">
                        {categories?.map(category => (
                            <li key={category.name} className="border-b md:border-b-0 border-gray-200 py-2">
                                <button
                                    onClick={() => toggleSection(category.name)}
                                    className="text-lg text-left flex w-full items-center justify-between font-medium">
                                    {category.name}
                                    {openSection === category.name ? <RiArrowUpSLine /> : <RiArrowDownSLine />}
                                </button>
                                {openSection === category.name && (
                                    <ul className="mt-2 space-y-2 pl-4">
                                        {category.posts.map((post, index) => (
                                            <li key={index}>
                                                <Link
                                                    aria-label={post.title + ' page'}
                                                    href={`/cryptoguide/${post.slug}`}
                                                    className="hover:underline capitalize">
                                                    {post.title}
                                                </Link>{' '}
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </li>
                        ))}
                    </ul>
                </nav>
            </div>
        </div>
    );

    return (
        <>
            {/* Mobile menu */}
            <div className="h-10 bg-[#fdfaf2] md:hidden">
                {isMobileMenuOpen && (
                    <div className="fixed inset-0 z-50 bg-white md:hidden">
                        {content}
                    </div>
                )}
                <div className="hidden md:block">
                    {content}
                </div>
                <button
                    className="text-2xl mr-8 flex items-center p-4 md:hidden"
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    aria-label="Toggle menu">
                    <FiMenu /> <span className="ml-2">Topics</span>
                </button>
            </div>

            <section id="hero" className="w-full border-b border-gray-700 bg-yellow-light">
                <div className="container mx-auto flex w-full items-center justify-between py-12">
                    <div className="flex items-center">
                        <ImageShortcut
                            src="/guide/hero.png"
                            alt="Crypto Guide"
                            className="object-contain"
                            width={190}
                            height={190}
                        />
                    </div>
                    <SearchCryptoguide />
                    <div className="flex flex-col gap-6">
                        <ImageShortcut
                            src="/guide/paxfull.png"
                            alt="Crypto Guide"
                            className="object-contain"
                            width={240}
                            height={75}
                        />
                        <ImageShortcut
                            src="/guide/crypto.png"
                            alt="Crypto Guide"
                            className="object-contain"
                            width={240}
                            height={75}
                        />
                    </div>
                </div>
            </section>

            <div className="flex">
                <div className='hidden md:block flex-[20%]'>
                    <LeftSide categories={categories} />
                </div>
                <div className='grid md:flex'>
                    <div className="mx-auto max-w-7xl p-8">
                        {/* Title Section */}

                        {cryptoGuide && (
                            <>
                                <h1 className="mb-4 mt-6 text-headline font-medium max-md:text-sub2">{cryptoGuide.title}</h1>
                                <div dangerouslySetInnerHTML={{ __html: cryptoGuide.content }} />
                            </>
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between mt-8 mb-12">
                            {cryptoGuide?.previous ? (
                                <a
                                    href={`/cryptoguide/${cryptoGuide.previous}`}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100 hover:text-blue-600 transition-colors duration-200"
                                >
                                    ← Prev
                                </a>
                            ) : (
                                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100 cursor-not-allowed" disabled>
                                    ← Prev
                                </button>
                            )}
                            {cryptoGuide?.next ? (
                                <a
                                    href={`/cryptoguide/${cryptoGuide.next}`}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100 hover:text-blue-600 transition-colors duration-200"
                                >
                                    Next →
                                </a>
                            ) : (
                                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100 cursor-not-allowed" disabled>
                                    Next →
                                </button>
                            )}
                        </div>
                        
                        {/* Explore All Topics */}
                        <div className="mt-12">
                            <h2 className="text-xl font-bold mb-4">Explore All Topics</h2>
                            <p className="text-[#424242] mb-4">
                                If this is your first semester at CryptoGuide, we recommend starting at the beginning and working your way through
                                this guide.
                            </p>
                            <ul className="space-y-2 text-[#0000FF]">
                                {categoriesTitles?.map((selectedPost, index) => (
                                    <li key={index}>
                                        <Link
                                            aria-label={selectedPost.title + ' page'}
                                            href={`/cryptoguide/${selectedPost.slug}`}
                                            className="hover:underline capitalize">
                                            {selectedPost.title}
                                        </Link>{' '}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                    <div className='flex-[20%]'>
                        <RightSide />
                    </div>
                </div>
            </div>
        </>
    )
}

export default CryptoguideComponent