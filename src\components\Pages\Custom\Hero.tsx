import Link from 'next/link'
import ImageShortcut from '@/components/Ui/Image'
import { CustomPageModel } from '@/types/CustomPageModel'
import MarkdownComponent from '@/components/Ui/MarkdownComponent'

const HeroSection = ({ data }: { data: Partial<CustomPageModel> }) => {
    return (
        <section className="relative flex items-center justify-center text-white md:min-h-[calc(80vh-80px)]">
            <div className="absolute inset-0 h-full w-full bg-[#004159]" />
            <ImageShortcut
                src={data.page_heros![0].image}
                width={1080}
                height={720}
                className="absolute inset-0 h-full w-full object-cover object-center"
                alt={data.page_heros![0].title + ' hero image'}
            />
            <div className="absolute inset-0 h-full w-full bg-[#0e242cd7]" />

            <div className="container z-20 mx-auto py-20">
                <div className="max-w-[712px] space-y-12">
                    <div className="space-y-4">
                        <h1 className="max-w-[496px] text-h2 font-semibold max-md:text-headline">
                            {data.page_heros![0].title}
                        </h1>
                        
                        {data.page_heros![0].description && (
                            <div className="prose-invert prose-h3:text-b3 max-md:prose-h3:text-sub1">
                                <MarkdownComponent text={data.page_heros![0].description} />
                            </div>
                        )}

                        <div className="grid grid-cols-3 gap-10 max-md:gap-3 max-sm:grid-cols-1">
                            {(data.page_heros![0].benefits == undefined || data.page_heros![0].benefits.length > 0) &&
                                data.page_heros![0].benefits.map((benefit, index) => (
                                    <div key={index} className="flex items-center gap-3 capitalize">
                                        <div className="h-3 w-3 rounded-full bg-white" />
                                        <p className="text-b3 font-medium max-md:text-sub1">{benefit.title}</p>
                                    </div>
                                ))}
                        </div>
                    </div>

                    <div className="space-y-9 font-medium">
                        {data.page_heros![0].ctaTitle && (
                            <Link href={data.page_heros![0].ctaLink!} target="_blank">
                                <button className="select-none rounded-md bg-blue px-[4.5rem] py-3 text-callout shadow-[0px_12px_30px_-6px_#0000008a] transition-colors duration-300 hover:bg-blue-primary max-md:px-[4rem] max-md:py-4 max-md:text-sub2">
                                    {data.page_heros![0].ctaTitle}
                                </button>
                            </Link>
                        )}
                        {data.page_heros![0].price && (
                            <p className="text-headline max-md:text-b2">Tuition: ${data.page_heros![0].price}</p>
                        )}
                    </div>

                    <div className="space-y-4">
                        <div className="flex flex-wrap gap-3">
                            {(data.page_heros![0].additionalDetails === undefined ||
                                data.page_heros![0].additionalDetails.length > 0) &&
                                data.page_heros![0].additionalDetails.map((detail, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center gap-3 text-b3 capitalize max-md:text-sub1">
                                        {index !== 0 && <span>|</span>}
                                        <p className="font-medium">{detail.title}</p>
                                    </div>
                                ))}
                        </div>
                        {data.page_heros![0].footer && (
                            <p className="text-sub3 max-md:text-cap1">{data.page_heros![0].footer}</p>
                        )}
                    </div>
                </div>
            </div>
        </section>
    )
}

export default HeroSection
