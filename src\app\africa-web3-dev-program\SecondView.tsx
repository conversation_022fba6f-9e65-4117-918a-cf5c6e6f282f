import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import yellowbutton from '../../../public/icons/yellowbutton.svg'
import enrollnowbutton from '../../../public/icons/enrollnowbutton.svg'
import Check from '../../../public/icons/yellowcheckbutton.svg'
import celo from '../../../public/icons/celo.svg'
import xrp from '../../../public/icons/xrp.svg'
import hedera from '../../../public/icons/eth.svg'
import eth from '../../../public/icons/hedera.svg'
import ICP from '../../../public/icons/ICP.svg'
import line from '../../../public/icons/line.svg'
const SecondView = () => {
  return (
    <>
      <div className="w-full border bg-white p-5">
              <section className="mx-auto grid max-w-[400px] grid-cols-2 items-center justify-center gap-3 md:gap-4 border-[#D2D2D2] py-3 sm:max-w-[700px] md:mx-auto md:max-w-[1400px] md:grid-cols-5 md:justify-between md:p-10 md:py-16">
                  <div className="flex items-center justify-center text-center">
                      <Image
                          src={celo}
                          alt="celo"
                          width={100}
                          height={28}
                          className="h-[28px] w-[100px] md:h-[58.9px] md:w-[157px]"
                      />
                      <Image src={line} alt="line" width={2} height={6} className="hidden px-2 md:block md:px-4" />
                  </div>
                  <div className="flex items-center justify-center text-center">
                      <Image
                          src={xrp}
                          alt="xrp"
                          width={70}
                          height={28}
                          className="h-[28px] w-[70px] md:h-[58.9px] md:w-[157px]"
                      />
                      <Image src={line} alt="line" width={1} height={6} className="hidden px-2 md:block md:px-4" />
                  </div>
                  <div className="flex items-center justify-center text-center">
                      <Image
                          src={eth}
                          alt="eth"
                          width={80}
                          height={28}
                          className="h-[28px] w-[80px] md:h-[58.9px] md:w-[157px]"
                      />
                      <Image src={line} alt="line" width={1} height={6} className="hidden px-2 md:block md:px-4" />
                  </div>
                  <div className="flex items-center justify-center text-center">
                      <Image
                          src={hedera}
                          alt="hedera"
                          width={80}
                          height={28}
                          className="h-[28px] w-[80px] md:h-[58.9px] md:w-[157px]"
                      />
                      <Image src={line} alt="line" width={1} height={6} className="hidden px-2 md:block md:px-4" />
                  </div>
                  <div className="flex items-center justify-center text-center">
                      <Image
                          src={ICP}
                          alt="ICP"
                          width={80}
                          height={28}
                          className="h-[28px] w-[80px] md:h-[58.9px] md:w-[157px]"
                      />
                  </div>
              </section>
          </div>

          <div className="mx-auto grid max-w-[400px] p-5 py-10 sm:max-w-[700px] md:max-w-[1400px]">
              <h2 className="text-[24px] font-[600] md:text-[36px]">Get Ready to Start</h2>
              <p className="mt-4 max-w-[1198px] text-[14px] font-[400] text-[#222222] md:text-[20px]">
                  We offer a range of support, including workshops, training, networking events, and access to funding
                  opportunities. Our program is very intense. To be prepared and make the most out of the four weeks,
                  you will:
              </p>
              <ul className=" my-3 grid gap-2 text-left text-[13.62px] font-[600] text-[#222222] md:my-7 md:text-[18px]">
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Be part of a community of like-minded individuals
                  </li>
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Have the opportunity to work on exciting projects
                  </li>
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Share your knowledge and skills
                  </li>
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Contribute to the development of the blockchain ecosystem
                  </li>
              </ul>
              <h3 className="my-3 text-[16px] text-[#2655FF] md:text-[22px]">
                  Build your network with top tech companies. Connect with our{' '}
                  <span className="text-[16px] font-[700] md:text-[22px]">15,000 alumni + hiring partners.</span>
              </h3>
              <Link href="https://cdn.forms-content.sg-form.com/b7c37a74-13e3-11ee-81a4-d673f9b62626" target='_blank' className="my-2">
                  <Image src={enrollnowbutton} alt="devProgram" width={385} height={68} />
              </Link>
              <p className="my-4 max-w-[1049px] text-[14px] font-[500] italic text-[#5B5B5B] md:text-[18px]">
                  We also offer opportunities to collaborate and network with other developers in the blockchain
                  community. This includes hackathons, meetups, and other events that bring together developers,
                  entrepreneurs, and investors.
              </p>
          </div>
    </>
  )
}

export default SecondView
