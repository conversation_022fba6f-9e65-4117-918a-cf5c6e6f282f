interface BundelModel {
    id: number;
    name: string;
    type: string;
    description: string;
    short_description: string
    image: File;
    price: number;
    status: string;
    rewardable: boolean;
    txHash: string;
    bannerImage: File;
    sale: number,
    created_at: string;
    updated_at: string;
    priceAfterDiscount: number;
    originalPrice: number
    priceAfterTax: number
    bundle_courses: [{
        courses: {
            id: number,
            name: string
        }
    }]
}