import { <PERSON>ada<PERSON> } from 'next'
import fetchInstance from '@/lib/fetch'
import { notFound } from 'next/navigation'
import getCountryInfo from '@/lib/countries'
import { getCurrentUser } from '@/lib/session'
import { Country } from '@/types/CountryModel'
import { capitalizeWords } from '@/lib/capitalize'
import SuccessModel from '@/components/Pages/Checkout/SuccessModel'
import { CheckoutFormNew } from '@/components/Pages/Checkout/CheckoutFormNew'
import SuccessModelNewUser from '@/components/Pages/Checkout/SuccessModelNewUser'

interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}
interface PageSlugProps {
    referral?: string
    hash?: string
    newUser?: string
}

async function getBootcampFromParams(params: PageProps) {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await fetchInstance(`/bootcamp/slug/${slug}`, { next: { revalidate: 60 } })
            return response.bootcamp
        } catch (error: any) {
            console.error('Bootcamp Error:', error)
            return undefined
        }
}
async function checkIfUserHasBootcamp(bootcampSlug: string, user: any) {
    try {
        const res = await fetchInstance(`/user-bootcamp/have-bootcamp?slug=${bootcampSlug}`, {
            headers: {
                Authorization: `Bearer ${user?.access_token}`,
            },
        })
        return res.success
    } catch (error: any) {
        console.error('User Bootcamp Error:', error)
        return error
    }
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await getBootcampFromParams({ slug })
            if (response === null) return {}
            else {
                return {
                    title: 'Checkout ' + capitalizeWords(response.name),
                    description: 'Checkout page for ' + response.name,
                    openGraph: {
                        title: response.name,
                        description: response.short_description,
                        type: 'website',
                        url: response.slug,
                        images: [
                            {
                                url: response.image,
                                width: 1200,
                                height: 630,
                                alt: response.name,
                            },
                        ],
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: response.name,
                        description: 'Checkout page for ' + response.name,
                        images: [response.image],
                    },
                }
            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const BootcampCheckoutPAge = async ({ params, searchParams }: { params: PageProps; searchParams: PageSlugProps }) => {
    //const slug = params?.slug
    const bootcamp = await getBootcampFromParams(params)
    const countries: Country[] = await getCountryInfo()

    if (bootcamp === null || bootcamp === undefined) notFound()

    const user = await getCurrentUser()

    let isEnrolled = false
    if (user !== null && user !== undefined) isEnrolled = await checkIfUserHasBootcamp(bootcamp.slug, user)

    if (searchParams.hash) {
        return (

            <section className="mx-auto pb-12">
                { searchParams.newUser == 'true' ? (
                        <SuccessModelNewUser success={true} />
                    ) : (
                        <SuccessModel success={true} type='bootcamp' />
                    )
                    }
            </section>
        )
    }

    return (
        <section className="mx-auto pb-12">
            <CheckoutFormNew
                countries={countries}
                bootcamp={bootcamp}
                user={user}
                disabled={isEnrolled}
            />
        </section>
    )
}

export default BootcampCheckoutPAge
