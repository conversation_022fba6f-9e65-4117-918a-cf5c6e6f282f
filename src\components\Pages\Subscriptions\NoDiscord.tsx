"use client"
import Link from 'next/link'
import <PERSON><PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import HeroImage from '@public/affiliate/hero.png'
import { signIn, signOut, useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import DiscordSuccessModel from '@/components/Pages/Checkout/DiscordSuccessModel'


const NoDiscord = () => {
  const { data: session } = useSession()
  const [isDiscordUser, setIsDiscordUser] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (session && session.user.provider === 'discord') {
        try {

        // Retrieve the hash from localStorage
          const hash = typeof window !== 'undefined' ? localStorage.getItem('hash') : null;

          const response = await fetch(`${process.env.API_URL}/discord/add-subscription`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: session.user.id,
              hash: hash
            }),
          });

          if (response.ok) {
            setIsDiscordUser(true)
            // Redirect to the home page on success
            // router.push('/');
          } else {
            // Handle other responses or errors if needed
            console.error('Error fetching data:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      }
    };

    fetchData();
  }, [session]);
  return (
    <>
      <section id='hero' className="w-full bg-yellow-light text-black">
        <div className="- container mx-auto flex flex-wrap max-md:flex-col-reverse max-md:items-center justify-between">
          <div className="flex flex-col items-start gap-9 max-md:gap-7 pb-40 max-md:pb-20 pt-24 max-md:pt-0 ">
            <div className="flex flex-col items-start justify-center gap-4 max-md:gap-2  max-md:items-center  max-md:pt-0">
            <h1 className="text-h3 font-semibold max-md:text-center max-md:text-b1 max-md:font-medium">
        Access Restricted
      </h1>
      <p className="max-w-[575px] text-sub3 max-md:px-4 max-md:text-center max-md:text-cap2 max-md:leading-[18px]">
        You are not eligible to access the Alpha Group at this time. To gain access, please purchase an Alpha Membership. As an Alpha Member, you will enjoy exclusive insights, discussions, and opportunities within our vibrant Crypto University community.
      </p>

            </div>

          </div>
          <ImageShortcut
            src={HeroImage}
            alt="Hero Image"
            priority
            className="object-contain max-md:h-auto max-md:w-[191px]"
          />
        </div>
      </section>

      <section id='question' className="container mx-auto pb-[20px] max-md:py-9">
        <div className="flex- flex items-center gap-10 max-md:flex-col max-md:items-start max-md:gap-0">
          <ImageShortcut
            src={'/join/contact.png'}
            width={280}
            height={280}
            className={'max-md:hidden max-md:h-auto max-md:w-[190px]'}
            alt={'Contact Icon'}
          />
          <div className="flex flex-col gap-8 max-md:gap-4">
            <div className="flex flex-col gap-2 max-md:gap-[0.375rem]">
              <p className="text-headline font-semibold max-md:text-b3">Have Any Questions?</p>
            </div>
            <div className="flex flex-col gap-5">
              <div className="w-[310px] max-md:w-[220px]">
                <Button gradient variant="primary" rounded>
                  <Link
                    href={'mailto:<EMAIL>'}
                    aria-label="Email crypto university">
                    Help {'>'}
                  </Link>
                </Button>
              </div>
              <div className="flex flex-wrap gap-1 text-callout max-md:flex-col max-md:gap-2 max-md:text-sub3">
                <p>OR Shoot us an email on:</p>{' '}
                <span className="text-blue">
                  {' '}
                  <Link
                    aria-label="Email crypto university"
                    href={'mailto:<EMAIL>'}>
                    <EMAIL>
                  </Link>
                </span>
              </div>
            </div>
          </div>
        </div>
    </section>
    </>
  )
}

export default NoDiscord
