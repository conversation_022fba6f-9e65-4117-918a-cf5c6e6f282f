import React from 'react'

const loading = () => {
    return (
        <div className="container mx-auto h-screen">
            <div className="flex h-full items-center justify-center max-md:items-start max-md:justify-start max-md:py-20 ">
                <div className="flex w-[600px] flex-col items-center gap-11 max-md:container max-md:mx-auto max-md:max-w-full max-md:items-start">
                    <h1 className="h-8 w-96 animate-pulse rounded-3xl bg-gray-400 text-center text-b3 font-semibold max-md:gap-7 max-md:text-sub2 max-sm:w-[250px]"></h1>
                    <div className="flex w-[600px] flex-col gap-5 max-md:w-[400px] max-sm:w-full ">
                        {[...Array(3)].map((_, index) => (
                            <div
                                key={index}
                                className="flex w-full animate-pulse items-center gap-4 rounded-lg border border-gray-300 bg-gray-400 px-6 py-4 shadow-md max-md:px-[10px] max-md:py-[11px]">
                                <div className="flex h-8 max-h-[32px] min-h-[32px] w-8 min-w-[32px] max-w-[32px]  animate-pulse items-center justify-center rounded-full bg-gray-700 text-sub2 font-medium text-white max-md:h-[28px] max-md:max-h-[28px] max-md:min-h-[28px] max-md:w-[28px] max-md:min-w-[28px] max-md:max-w-[28px] max-md:text-cap1"></div>
                                <p className="h-8 bg-gray-700 text-cap1 max-md:text-cap3 "></p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default loading
