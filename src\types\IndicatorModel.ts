export interface IndicatorModel {
    id: 3
    title: string
    short_description: string
    slug?: string
    description: string
    image: string
    price: string
    sale: number
    status: string
    video: string
    instructor_id: number
    created_at: string
    updated_at: string
    priceWithoutTax: number
    tax: number
    finalPrice: number
    transactions: Transactions[]
}

interface Transactions {
    created_at: string;
}