import Button from "@/components/Ui/Button";
import Link from "next/link";

const HowTo = () => {
    return (
        <section className="bg-gray-100 py-16 mx-auto sm:mx-40">
            <div className="container mx-auto text-left">
                <h2 className="text-headline font-bold mb-8">Get Ready to Start</h2>

                <p className="text-[#00008B] mb-8">
                    We offer a range of support, including workshops, training, networking events, and access to funding opportunities.
                    Our program is very intense. To be prepared and make the most out of the four weeks, you will:
                </p>

                <ul className="text-left list-disc ml-6 mb-8">
                    <li className="mb-2">Be part of a community of like-minded individuals</li>
                    <li className="mb-2">Have the opportunity to work on exciting projects</li>
                    <li className="mb-2">Share your knowledge and skills</li>
                    <li className="mb-2">Contribute to the development of the blockchain ecosystem</li>
                </ul>

                <p className="text-[#00008B] mb-8">
                    Build your network with top tech companies. Connect with our 15,000 alumni + hiring partners.
                </p>

                <p className="text-[#00008B] mb-8">
                    We also offer opportunities to collaborate and network with other developers in the blockchain community.
                    This includes hackathons, meetups, and other events that bring together developers, entrepreneurs, and investors.
                </p>

                <p className="text-green-500 font-bold  text-b2 mb-8">
                    So what are you waiting for?
                </p>
                <div className=" w-full max-w-[261px] max-md:max-w-full">
                    <Button className="" rounded variant="primary">
                        <Link href={'courses/web3-dev-program'}>
                            Enroll Now {'>'}
                        </Link>
                    </Button>
                </div>
            </div>
           
        </section>
    );
};

export default HowTo;
