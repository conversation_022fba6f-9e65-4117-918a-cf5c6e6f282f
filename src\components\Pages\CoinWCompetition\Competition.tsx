"use client"
import ImageShortcut from "@/components/Ui/Image"
import Link from "next/link"
import { mastercard, coinw, alphaImage, cryptoULogo, usdt, web3MasterClass, sponsoredByCoinW } from "@public/coinw-trading-competion"
import { GetCurrentUserClient } from '@/lib/session'


const CoinWCompetition = () => {

    const user = GetCurrentUserClient()

    const prizes = [
        {
            title: '$30 gift card',
            description: 'Crypto University users who open a CoinW account via <a aria-label="CU Account" href="https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US" target="_blank" class="text-[#FCC229]"> CU affiliate link  </a>.',
            icon: mastercard
        },
        {
            title: 'CoinW Champion’s Arena: Total Rewards: $1 Million ',
            description: '<p>1. Triumph with Strategy:$200,000</p><p class="pt-2">2. Early Bird Reward:$20,000 (2024/4/17 - 2024/4/30)</p><p class="pt-2">3. Player Squad v.s. Coach Squad Competiton:$20,000</p><p class="pt-2">4. Future Trading Competition:$560,000</p><p class="pt-2">5. Spot Trading Competition:$200,000</p>',
            icon: usdt
        },
        {
            title: 'Web3 Masterclass worth $500',
            description: 'Win a free beginner-friendly online course for those who sign-up to <a aria-label="CU Account" href="https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US" target="_blank" class="text-[#FCC229]"> CoinW </a> and deposit at least $200 during the competition period. Claim a $500 Web3 Master class coupon for free on Crypto University if they use code "**********"  (Discount applies to new customers only, see Step 4)',
            icon: web3MasterClass
        }
        ,
        {
            title: 'Alpha Group complimentary Membership',
            description: 'Community for trader pros (includes Live Coaching, Indicators, Trading Ideas, Arbitrage Trading, P2P Trading). Accumulated transaction volume reaches 1 million on CoinW during the event.  Use code"********" to get 1-month free access to Crypto University Alpha group (Discount applies to new customers and on monthly subscription only, see Step 4). ',
            icon: alphaImage
        }
    ]
    const timelines = [
        {
            step: 'Step 1',
            description: 'Important!!! Complete the  <a aria-label="CU Account" href="https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform?pli=1" target="_blank" class="text-[#FCC229]"> trading competition registration form </a>',
            dueDates: 'May 28th, 2024 - 23:59 GMT+2'
        },
        {
            step: 'Step 2',
            description: 'Participants may register a CoinW account via <a aria-label="CU Account" href="https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US" target="_blank" class="text-[#FCC229]"> CU affiliate link >  </a>    (Invitation Code 2620075)',
            dueDates: ''
        },
        {
            step: 'Step 3',
            description: 'The Competition Period ',
            dueDates: 'May 28th, 2024 - 23:59 GMT+2'
        },
        {
            step: 'Step 4',
            description: 'Redeem your prize Please join the CoinW x Crypto University Discord server: CoinW Global Discord Server.  <a aria-label="CU Account" href="https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform" target="_blank" class="text-[#FCC229]"> See more details here. </a> ',
            dueDates: 'June 14th, 2024 - 23:59 GMT+2'
        }
    ]


    return (
        <>
            <section>

                <section>
                    <div className="relative p-12" style={{ height: '700px' }}>
                        {/* Desktop View */}
                        <div className="hidden sm:block absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden" style={{
                            backgroundImage: `url('https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1712784126/Desktop_banner_pfptn7.jpg')`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                        }}>
                            <div className="mt-6 mx-[9rem] pl-12 flex flex-col items-center sm:flex-row sm:items-start">
                                <div className="max-w-md">
                                    <ImageShortcut
                                        src={cryptoULogo}
                                        width={100}
                                        height={100}
                                        alt="thumbnail"
                                        className="object-contain h-100 w-100 p-2"
                                        priority={true}
                                    />
                                </div>
                                <div className="divider divider-horizontal"></div>
                                <div className="max-w-md ">
                                    <ImageShortcut
                                        src={coinw}
                                        width={150}
                                        height={130}
                                        alt="thumbnail"
                                        className="object-contain h-130 w-auto p-2 -mt-4"
                                        priority={true}
                                    />
                                </div>
                            </div>
                            <div className="bg-gradient-to-r from-[#544717] mt-2 opacity-100 py-3">
                                <h1 className="text-left text-[36px] font-bold mt-2 mx-[9rem] pl-12 text-white">CoinW Trading Competition</h1>
                            </div>
                            <div className="my-2 px-12 p-6 text-white sm:mx-[9rem]">

                                <ImageShortcut
                                    src={sponsoredByCoinW}
                                    width={40}
                                    height={20} // Let Next.js handle the aspect ratio
                                    alt="thumbnail"
                                    className="object-cover h-[3rem] w-auto my-6"
                                    priority={true}
                                />
                                <h1 className="text-left text-b2 font-bold mt-10">Win up to <span className="text-[#FCC229]">$1,000,000 </span> in prizes!</h1>
                                <h1 className="text-left text-sub3  mt-4 mb-6">Open to all CU traders, no matter your experience.</h1>

                                {user ? (<Link
                                    aria-label="View products"
                                    target="_blank"
                                    href={'https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform?pli=1'}
                                    className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub2 font-semibold py-3 px-2 mt-10 transition-colors duration-150 inline-block"
                                >
                                    Register Here {'>'}
                                </Link>) : (<Link
                                    aria-label="View products"
                                    href={{ pathname: '/login', query: { source: 'coinw' } }}
                                    className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub2 font-semibold py-3 px-2 mt-10 transition-colors duration-150 inline-block"
                                >
                                    Register Here {'>'}
                                </Link>)}

                                <h1 className="text-left text-sub3 font-bold mt-10">April 17th 2024 - May 28th 2024</h1>
                            </div>
                        </div>

                        {/* Mobile View */}
                        <div className="block sm:hidden absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden" style={{
                            backgroundImage: `url('https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1712784128/Mobile_hero_banner_dsow8z.jpg')`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                        }}>
                            <div className="my-2 px-6 pt-[25rem] text-white ">
                                <h1 className="text-center text-b2 sm:text-h2 font-bold mt-[5rem] ">CoinW Trading Competition</h1>

                                <ImageShortcut
                                    src={sponsoredByCoinW}
                                    width={40}
                                    height={20}
                                    alt="thumbnail"
                                    className="object-cover h-[3rem] w-auto my-6"
                                    priority={true}
                                />
                            </div>
                        </div>
                    </div>
                </section>
                <section className="block sm:hidden px-12 pb-12 pt-4 text-white bg-[#000610]">
                    <div className="sm:mx-[9rem] flex flex-col items-center">
                        <h1 className="text-center text-b2 font-bold text-white">Win up to <span className="text-[#FCC229]">$1,000,000</span> in prizes!</h1>
                        <h1 className="text-center text-sub3  mt-4 mb-6 text-white">Open to all CU traders, no matter your experience.</h1>

                        {user ? (<Link
                            aria-label="View products"
                            target="_blank"
                            href={'https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform?pli=1'}
                            className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub3 sm:text-sub2 font-semibold py-3 px-8 mt-10 transition-colors duration-150 inline-block"
                        >
                            Register Here {'>'}
                        </Link>) : (<Link
                            aria-label="View products"
                            href={{ pathname: '/login', query: { source: 'coinw' } }}
                            className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub3 sm:text-sub2 font-semibold py-3 px-8 mt-10 transition-colors duration-150 inline-block"
                        >
                            Register Here {'>'}
                        </Link>)}

                        <h1 className="text-center text-sub3 font-bold mt-10 text-white">April 17th 2024 - May 28th 2024</h1>
                    </div>
                </section>

                <section className="px-12 py-6 sm:py-12">
                    <div className="sm:mx-[9rem]">
                        <h1 className="text-b2 mb-4 text-white text-center">About the Competition</h1>
                        <p className="text-[#BCBCBC] text-center">
                            The trading promotion is offered by a world-class cryptocurrency exchange CoinW. The CoinW Trading competition is for clients who meet the eligibility criteria listed below. By entering into this competition, you acknowledge that you understand the risks involved with crypto trading.
                        </p>

                    </div>
                </section>
                <section className="px-12 pt-10">
                    <div className="sm:mx-[9rem]">
                        <hr className="h-px  bg-[#5C4E39] border-0"></hr>
                    </div>
                </section>
                <section className="px-12 py-2 sm:py-12">
                    <div className="text-center sm:mx-[9rem]">
                        <h1 className="text-b2  text-center sm:text-left font-bold my-8 sm:my-16 text-white">Competition Timeline</h1>
                        <div className="block sm:hidden">

                            {timelines.map((timeline) => (
                                <div key={timeline.step}
                                    className="block rounded-lg  mt-6 border border-[#5C4E39] text-center text-surface shadow-secondary-1 dark:bg-surface-dark ">
                                    <div
                                        className="bg-[#5C4E39]  px-6 py-3 text-white">
                                        {timeline.step}
                                    </div>
                                    <div className="p-6">
                                        <h5 className="mb-2 text-xl font-medium leading-tight text-white">
                                            <div dangerouslySetInnerHTML={{ __html: timeline.description }} />
                                        </h5>
                                        <h4>Due Dates</h4>
                                        <p className="mb-4 text-base text-white">
                                            {timeline.dueDates}
                                        </p>
                                    </div>
                                </div>
                            ))}

                        </div>

                        <div className="relative overflow-x-auto hidden sm:block">
                            <table className="w-full text-sm text-left rtl:text-right text-white">
                                <thead className="bg-[#5C4E39] text-xs border border-[#5C4E39] text-white  uppercase">
                                    <tr>
                                        <th scope="col" className="px-6 py-3 text-white">
                                            Steps
                                        </th>
                                        <th scope="col" className="px-6 py-3  text-white">
                                            Description
                                        </th>
                                        <th scope="col" className="px-6 py-3  text-white">
                                            Due Dates
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {timelines.map((timeline) => (
                                        <tr key={timeline.step} className="bg-[#222121] border border-[#5C4E39]">
                                            <th scope="row" className="px-6 py-4 font-medium text-sub4  text-white whitespace-nowrap">
                                                {timeline.step}
                                            </th>
                                            <td className="px-6 py-4 text-sub4  text-white">
                                                <div dangerouslySetInnerHTML={{ __html: timeline.description }} />
                                            </td>
                                            <td className="px-6 py-4 text-cap1  text-white">
                                                {timeline.dueDates}
                                            </td>

                                        </tr>
                                    ))}


                                </tbody>
                            </table>
                        </div>

                    </div>
                </section>

                <section className="hidden sm:block px-12 py-2 sm:pb-12 pt-6">
                    <div className="sm:mx-[9rem]">
                        <h1 className="text-center sm:text-left text-b2  font-bold my-8 sm:my-16 text-white">
                            Prizes to be Won
                        </h1>

                        <div className="">
                            {prizes.map((prize) => (
                                <div key={prize.title} className="flex items-center p-4 sm:p-6">
                                    <div className="mr-4 flex-shrink-0">
                                        <ImageShortcut
                                            src={prize.icon}
                                            width={150}
                                            height={150}
                                            alt="thumbnail"
                                            className="object-cover h-150 w-150 rounded-full"
                                            priority={true}
                                        />
                                    </div>
                                    <div>
                                        <h1 className="text-white text-callout p-6">{prize.title}</h1>
                                        <p className="text-[#BCBCBC] pl-6 text-sub3"> <div dangerouslySetInnerHTML={{ __html: prize.description }} /></p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                <section className="block sm:hidden px-12 py-2 sm:pb-12 pt-6">
                    <div className="sm:mx-[9rem]">
                        <h1 className="text-center sm:text-left text-b2 font-bold my-8 sm:my-16 text-white">
                            Prizes to be Won
                        </h1>

                        <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4">
                            {prizes.map((prize) => (
                                <div key={prize.title} className="flex flex-col items-center p-4 sm:p-6">
                                    <div className="mb-4 sm:mb-6">
                                        <ImageShortcut
                                            src={prize.icon}
                                            width={150}
                                            height={150}
                                            alt="thumbnail"
                                            className="object-cover h-150 w-150 rounded-full"
                                            priority={true}
                                        />
                                    </div>
                                    <div className="text-center sm:text-left">
                                        <h1 className="text-white text-callout p-6">{prize.title}</h1>
                                        <p className="text-[#BCBCBC] pl-0 sm:pl-6 text-sub3">
                                            <div dangerouslySetInnerHTML={{ __html: prize.description }} />
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>


                <section className="px-12 py-2 sm:pb-12 pt-6">
                    <div className="sm:mx-[9rem]">
                        <p className="text-[#8B8B8B] text-sub4 text-center">
                            Crypto University may withhold or void prize amounts without prior notice if
                            it reasonably believes it necessary. By engaging in this Competition, you
                            agree to the collection, processing, and utilization of your personal data
                            by Crypto University for Competition-related purposes as well as for
                            marketing and promotional endeavors on social media platforms.
                        </p>
                    </div>
                </section>

            </section>
        </>
    )
}

export default CoinWCompetition
