import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { capitalizeWords } from '@/lib/capitalize'
import { removeMarkdown } from '@/lib/removeMarkdown'
import { CryptoGuide, CryptoGuideCategoryPost, CryptoGuidePost } from '@/types/CryptoGuideCategoryPostModel'
import CryptoguideComponent from '@/components/Pages/CryptoGuide/CryptoguideComponent'

interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}

// Fetch categories with posts
async function GetCategories() {
    try {
        const res = await fetch(`${process.env.API_URL}/cryptoguide/categories-with-posts`, {
            next: { revalidate: 10 }, // Revalidate data every 10 seconds
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.categories
    } catch (error) {
        console.error('Error fetching categories:', error)
        return null
    }
}

// Fetch default crypto guide post (e.g., the first post or a specific default post)
async function GetCryptoGuidePost(slug: string) {
    try {
        const res = await fetch(`${process.env.API_URL}/cryptoguide/post-by-slug/${slug}`, {
            next: { revalidate: 10 }, // Revalidate data every 10 seconds
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.cryptoguide
    } catch (error) {
        console.error('Error fetching default crypto guide:', error)
        return null
    }
}

//get crypto guide titles
async function GetCryptoGuideTitles() {
    try {
        const res = await fetch(`${process.env.API_URL}/cryptoguide/titles`, {
            next: { revalidate: 10 }, // Revalidate data every 10 seconds
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.cryptoguides
    } catch (error) {
        console.error('Error fetching default crypto guide:', error)
        return null
    }
}


export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug as string
    if (slug !== undefined)
        try {
            const cryptoGuide: CryptoGuide = await GetCryptoGuidePost(slug)
            if (cryptoGuide === null) return {}

            return {
                title: capitalizeWords(cryptoGuide.name),
                description: removeMarkdown("Cryptoguide for " + cryptoGuide.name),
                openGraph: {
                    title: capitalizeWords(cryptoGuide.name),
                    description: removeMarkdown("Cryptoguide for " + cryptoGuide.name),
                    type: 'website',
                    url: cryptoGuide.slug,
                },
                twitter: {
                    card: 'summary_large_image',
                    title: capitalizeWords(cryptoGuide.name),
                    description: removeMarkdown("Cryptoguide for " + cryptoGuide.name),
                },

            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const CryptoguidePostPage = async ({ params }: { params: PageProps }) => {
    const { slug } = params

    const categories: CryptoGuideCategoryPost[] = await GetCategories()
    const cryptoGuide: CryptoGuide = await GetCryptoGuidePost(slug)
    const categoriesTitles: CryptoGuidePost[] = await GetCryptoGuideTitles()

    if (cryptoGuide === null) {
        console.error("cryptoGuide not found for slug:", slug);
        notFound();
    }

    return (
        <>
            <div className="w-full">
                {/* Pass categories and cryptoGuide as props to the Hero component */}
                <CryptoguideComponent categoriesTitles={categoriesTitles} categories={categories} cryptoGuide={cryptoGuide} />
            </div>
        </>
    )
}

export default CryptoguidePostPage
