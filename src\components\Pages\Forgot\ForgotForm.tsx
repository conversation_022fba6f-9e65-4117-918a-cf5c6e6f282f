'use client'
import { cn } from '@/lib/cn'
import Link from 'next/link'
import { Close } from '@public/home'
import useAuth from '@/hooks/useAuth'
import { Email } from '@public/global'
import Label from '@/components/Ui/Label'
import { Fragment, useState } from 'react'
import Button from '@/components/Ui/Button'
import Mailbox from '@public/forgot/mailbox.png'
import ImageShortcut from '@/components/Ui/Image'
import { Dialog, Transition } from '@/lib/headlessui'
import ButtonSpinner from '@/components/Ui/buttonSpinner'

interface TextBoxProps {
    email: any
    setEmail: any
    placeholder: string
    isError?: string
}

const TextBox = ({ placeholder, email, isError, setEmail }: TextBoxProps) => {
    return (
        <div className="flex w-full flex-col gap-3">
            <Label required uppercase={false}>
                Email Address
            </Label>

            <div className="flex flex-col gap-2 max-md:w-full">
                <div className="relative flex select-none items-center">
                    <input
                        type={'email'}
                        name={'email'}
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                        className={cn(
                            'w-full rounded-full border border-gray-700 px-4 py-[17.5px] focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue max-md:py-3',
                            isError && 'border-red focus:ring-red'
                        )}
                        placeholder={placeholder}
                    />
                    <div className="absolute right-4">
                        <Email />
                    </div>
                </div>
                {isError && (
                    <div className="rounded-lg bg-red/20 p-3">
                        <p className="text-cap1 text-red">{isError}</p>
                    </div>
                )}
            </div>
        </div>
    )
}

const ForgotForm = () => {
    const { isLoading, error, forgotPassword } = useAuth()

    const [email, setEmail] = useState('')
    const [isOpen, setIsOpen] = useState(false)

    const handleSubmit = async (e: any) => {
        e.preventDefault()
        await forgotPassword({ email }).then(res => {
            res?.data?.success && setIsOpen(true)
        })
    }

    return (
        <>
            <form onSubmit={handleSubmit} className="flex flex-col space-y-6 max-xl:items-center">
                <TextBox placeholder="e.g <EMAIL>" email={email} setEmail={setEmail} isError={error} />
                <div className="max-xs:w-full max-xs:max-w-full max-xl:flex max-xl:w-[360px] max-xl:justify-center max-md:max-w-[310px] max-sm:px-4">
                    <Button type="submit" variant="primary" rounded disabled={isLoading || email == ''}>
                        {isLoading && <ButtonSpinner />} Send reset instruction
                    </Button>
                </div>
            </form>
            <Transition show={isOpen || false} as={Fragment}>
                <Dialog
                    as="div"
                    onClose={() => setIsOpen(false)}
                    className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                    <div className="fixed inset-0 flex items-center justify-center p-4 max-md:block max-md:p-0">
                        <Transition.Child
                            as={Fragment}
                            enter="transition duration-100 ease-out"
                            enterFrom="transform scale-95 opacity-0"
                            enterTo="transform scale-100 opacity-100"
                            leave="transition duration-75 ease-out"
                            leaveFrom="transform scale-100 opacity-100"
                            leaveTo="transform scale-95 opacity-0">
                            <Dialog.Panel className="flex h-full items-center justify-center max-md:block">
                                <div className="relative flex flex-col gap-4 rounded-[1.5rem] bg-white p-10 pt-[3.25rem] font-sans max-md:h-full max-md:justify-center max-md:gap-0 max-md:rounded-none max-md:px-8 max-md:py-12">
                                    <button
                                        type="button"
                                        onClick={() => setIsOpen(false)}
                                        className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                        <Close />
                                    </button>

                                    <ImageShortcut
                                        src={Mailbox}
                                        width={203}
                                        height={158}
                                        alt="mailbox"
                                        className="maxmd:h-[188px] self-center max-md:w-[241]"
                                    />

                                    <div className="flex max-w-[334px] flex-col items-center gap-4 self-center max-md:mt-6 max-md:max-w-full max-md:gap-1">
                                        <Dialog.Title className="font-manrope text-headline font-medium max-md:text-b3">
                                            Check your mail
                                        </Dialog.Title>
                                        <Dialog.Description className="text-center text-sub2 text-gray-900 max-md:max-w-[228px] max-md:text-black">
                                            We have sent a password reset link to your email
                                        </Dialog.Description>
                                    </div>

                                    <div className="flex w-[373px] flex-col gap-5 max-md:mt-14 max-md:w-full">
                                        <Link aria-label="Go back home" href="/" className="w-full rounded-full">
                                            <Button variant="primary" rounded>
                                                Back to Home
                                            </Button>
                                        </Link>
                                        <Link aria-label="Confirm later" href="/" className="w-full rounded-full">
                                            <Button variant="default" rounded>
                                                Skip, I will confirm later
                                            </Button>
                                        </Link>
                                    </div>

                                    <p className="max-w-[373px] text-center max-md:mt-14 max-md:max-w-[261px] max-md:self-center">
                                        Don&apos;t receive the email? Check your spam folder, or{' '}
                                        <span className="cursor-pointer text-blue" onClick={() => setIsOpen(false)}>
                                            try email again
                                        </span>
                                    </p>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
        </>
    )
}

export default ForgotForm
