export interface CustomDetailsModel {
    id: number | null
    hero_id: number | null
    title: string | null
    created_at: string | null
    updated_at: string | null
}

export interface CustomHeroModel {
    id: number
    page_id: number
    image: string
    title: string
    description: string | null
    ctaTitle: string | null
    ctaLink: string | null
    price: string | null
    footer: string | null
    created_at: string
    updated_at: string
    benefits: CustomDetailsModel[]
    additionalDetails: CustomDetailsModel[]
}

export interface CustomSectionModel {
    id: number
    page_id: number
    order: number
    image: string | null
    title: string | null
    description: string | null
    body: string | null
    ctaTitle: string | null
    ctaLink: string | null
    dark: boolean
    created_at: string
    updated_at: string
}

export interface CustomPageModel {
    id: number
    slug: string
    video: string
    hero_id: number
    expires_at: any | null
    created_at: string
    updated_at: string
    page_heros: CustomHeroModel[]
    page_sections: CustomSectionModel[]
}
