import Link from 'next/link'
import { cn } from '@/lib/cn'
import { Success } from '@public/global'

export const Headline = ({ step, onStep6 }: { step: number; onStep6: string }) => {
    return (
        <div
            className={cn(
                'flex flex-col gap-6 font-sans max-lg:gap-4 ',
                step === -1
                    ? 'w-full items-center text-center max-md:gap-9 '
                    : 'max-w-[465px] text-start max-lg:text-center',
                step === 6 ? onStep6 : ''
            )}>
            {step == 6 && (
                <p className="flex items-center gap-3 text-callout max-md:hidden">
                    <Success /> Thank you
                </p>
            )}

            <h1
                className={cn(
                    'text-h3 font-medium text-black max-sm:text-b3 ',
                    step === -1 && 'max-w-[925px] text-h3'
                )}>
                {step == -1 ? (
                    <span className="max-sm:text-b1">
                        Discover the biggest <span className="text-blue">Crypto</span> opportunities today
                    </span>
                ) : step == 0 ? (
                    'What’s your Username?'
                ) : step == 1 ? (
                    'What’s your Full Name?'
                ) : step == 2 ? (
                    'What’s your Email?'
                ) : step == 3 ? (
                    'Where are you from?'
                ) : step == 4 ? (
                    'Your Phone Number?'
                ) : step == 5 ? (
                    'Create a Password'
                ) : (
                    'Your Crypto Journey Begins here!'
                )}
            </h1>

            <p
                className={cn(
                    'text-callout font-normal max-sm:text-[0.875rem]',
                    step === -1 && 'max-w-[878px] text-sub2'
                )}>
                {step === -1 ? (
                    <span className="">
                        Crypto University is{' '}
                        <b className="font-semibold"> the {'world’s'} #1 Cryptocurrency education platform. </b>Our team
                        of experts offers customized courses designed to guide you on becoming the next Crypto experts
                        and millionaire
                    </span>
                ) : step == 0 ? (
                    'What shall we call you?'
                ) : step == 1 ? (
                    'What is your real name?'
                ) : step == 2 ? (
                    'Please tell us your @.'
                ) : step == 3 ? (
                    'Please select your country.'
                ) : step == 4 ? (
                    ''
                ) : step == 5 ? (
                    'Minimum 8 characters, mix of letters, numbers, and symbols'
                ) : (
                    <span className="text-start">
                        Your registration has been successful. Please login{' '}
                        <Link aria-label="Login" href="/dashboard" className="text-blue underline">
                            here
                        </Link>{' '}
                    </span>
                )}
            </p>
        </div>
    )
}
