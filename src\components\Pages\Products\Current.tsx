'use client'

import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import { usePathname } from 'next/navigation'

const Current = (call: any) => {
    const router = usePathname()

    return (
        <Link
            aria-label="Start Now"
            href={'/checkout/mentorship/' + call.slug + '?previous=' + router}
            className="w-[200px] max-md:w-[188px]">
            <Button variant="primary" rounded className="px-12 leading-none">
                Start Now &gt;
            </Button>
        </Link>
    )
}

export default Current
