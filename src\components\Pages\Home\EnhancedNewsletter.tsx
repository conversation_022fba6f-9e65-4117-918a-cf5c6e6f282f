"use client";
import { useState } from 'react'

export default function EnhancedNewsletter() {
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsSubmitted(true)
    setIsSubmitting(false)
  }

  if (isSubmitted) {
    return (
      <section className="bg-gradient-to-r from-green-600 to-green-700 py-16">
        <div className="container mx-auto text-center">
          <div className="bg-white rounded-3xl p-8 max-w-2xl mx-auto border-4 border-yellow-400">
            <div className="text-6xl mb-4">🎉</div>
            <h3 className="text-3xl font-black text-green-800 mb-4">SUCCESS! Check Your Email!</h3>
            <p className="text-lg text-gray-700 font-semibold">
              Your <strong className="text-green-600">$2,847 Crypto Profit Blueprint</strong> is on its way to <strong>{email}</strong>!
            </p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="bg-gradient-to-br from-red-900 via-black to-red-900 py-20 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-yellow-400/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-green-400/20 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-purple-400/20 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="text-white">
            {/* Urgency Banner */}
            <div className="bg-red-600 text-white px-6 py-3 rounded-2xl mb-6 border-4 border-yellow-400 animate-pulse">
              <div className="text-center">
                <div className="text-xl font-black">🚨 LAST CHANCE: FREE $2,847 BLUEPRINT!</div>
                <div className="text-sm font-bold">Join 47,832+ Profitable Students Today!</div>
              </div>
            </div>

            <h3 className="text-4xl md:text-5xl font-black mb-6 leading-tight">
              🔥 Get Our <span className="text-yellow-400">SECRET PROFIT SYSTEM</span> That Made Students <span className="text-green-400">$10K+/Month</span>
            </h3>

            <div className="bg-green-600 text-white p-6 rounded-2xl mb-8 border-2 border-yellow-400">
              <p className="text-xl font-bold text-center">
                The EXACT blueprint that helped <span className="text-yellow-400">Sarah make $89K in 6 months</span> and <span className="text-yellow-400">Mike quit his job</span> with crypto profits!
              </p>
            </div>

            {/* Value Stack */}
            <div className="bg-black/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border-2 border-yellow-400">
              <h4 className="text-2xl font-black text-yellow-400 mb-4 text-center">
                🎯 WHAT YOU GET (Worth $2,847):
              </h4>
              <div className="grid md:grid-cols-2 gap-3">
                {[
                  { text: 'Secret 5-Coin Portfolio', value: '$497' },
                  { text: 'Risk-Free Trading System', value: '$397' },
                  { text: 'Market Crash Protection', value: '$297' },
                  { text: 'Daily Profit Opportunities', value: '$397' },
                  { text: 'VIP Discord Access', value: '$497' },
                  { text: 'Personal Success Coach', value: '$297' },
                  { text: 'Lifetime Updates', value: '$297' },
                  { text: 'Mobile Trading App', value: '$197' }
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between bg-green-600/20 p-3 rounded-lg border border-green-400">
                    <div className="flex items-center">
                      <span className="text-green-400 mr-2">💎</span>
                      <span className="text-white font-semibold text-sm">{item.text}</span>
                    </div>
                    <span className="text-yellow-400 font-bold text-sm">{item.value}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Social Proof */}
            <div className="bg-purple-600 text-white p-4 rounded-2xl border-2 border-yellow-400">
              <div className="text-center">
                <div className="text-lg font-black mb-2">🏆 RECENT SUCCESS STORIES:</div>
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="bg-black/30 p-2 rounded">
                    <div className="text-lg font-black text-green-400">$47K</div>
                    <div className="text-xs">This month</div>
                  </div>
                  <div className="bg-black/30 p-2 rounded">
                    <div className="text-lg font-black text-green-400">$156K</div>
                    <div className="text-xs">First year</div>
                  </div>
                  <div className="bg-black/30 p-2 rounded">
                    <div className="text-lg font-black text-green-400">$89K</div>
                    <div className="text-xs">6 months</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Ultra-Aggressive Form */}
          <div className="bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-3xl p-8 border-4 border-red-600 relative">
            <div className="absolute top-4 right-4 animate-bounce">
              <div className="text-3xl">🚀</div>
            </div>

            <div className="text-center mb-6">
              <div className="bg-red-600 text-white px-4 py-2 rounded-2xl mb-4 animate-pulse">
                <div className="text-xl font-black">⚡ INSTANT ACCESS ⚡</div>
              </div>

              <h4 className="text-2xl font-black text-black mb-4">
                🔥 CLAIM YOUR $2,847 BLUEPRINT NOW!
              </h4>

              <div className="bg-black text-yellow-400 p-3 rounded-xl">
                <p className="font-bold">
                  Join 47,832+ students making $10K+/month!
                </p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="bg-red-600 text-white p-4 rounded-2xl">
                <div className="text-center mb-3">
                  <div className="text-lg font-black">⏰ LIMITED TIME OFFER!</div>
                  <div className="text-sm font-bold">Only 47 copies left today!</div>
                </div>

                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter Your Best Email Address"
                  className="w-full px-4 py-3 text-black text-lg font-semibold rounded-xl border-4 border-yellow-400 focus:ring-4 focus:ring-yellow-300 mb-4"
                  required
                />

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-4 bg-green-600 hover:bg-green-700 text-white text-xl font-black rounded-xl hover:scale-105 transform transition-all shadow-2xl"
                >
                  {isSubmitting ? '🔄 SENDING YOUR BLUEPRINT...' : '🚀 YES! SEND ME THE $2,847 BLUEPRINT FREE!'}
                </button>
              </div>
            </form>

            <div className="mt-4 text-center">
              <div className="bg-black text-yellow-400 p-3 rounded-xl mb-3">
                <div className="font-bold">🔒 100% FREE - NO CREDIT CARD REQUIRED</div>
                <div className="text-sm">Instant download. No spam. 47,832+ members trust us.</div>
              </div>

              <div className="flex justify-center gap-4 text-xs text-black font-semibold">
                <div className="flex items-center">
                  <span className="text-green-600 mr-1">🔒</span>
                  <span>SSL Secured</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-600 mr-1">⚡</span>
                  <span>Instant Access</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-600 mr-1">👥</span>
                  <span>47K+ Members</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}