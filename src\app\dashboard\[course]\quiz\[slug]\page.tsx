import QuestionTemplate from '@/components/Pages/Dashboard/Quiz/QuestionTemplate'
import {
    GetCourses,
    GetOneCourse,
    GetQuizzesCourse,
    getCourseById,
} from '@/config/validationCourseDashboard'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { CourseModelV2 } from '@/types/CourseModel'
import { redirect } from 'next/navigation'
import fetch from '@/lib/fetch'

type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
        lesson: string
        slug: string
    }
}
interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

const CourseQuiz = async ({ searchParams, params }: Props) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })
    if (courses === undefined) {
        redirect('/dashboard')
    }

    const courseId = getCourseById({ courses, params })
    const course = await GetOneCourse(user, courseId?.slug ?? '')

    if (courseId === null || course === undefined) {
        redirect('/dashboard')
    }

    const Quizzes :QuizModel[]  = await GetQuizzesCourse(user, courseId?.slug ?? '')
    if (Quizzes === undefined) {
        redirect('/dashboard')
    }
    //check if the quizzes id is the same as the params.slug
    const checkQuiz = Quizzes.find((quiz) => quiz.id == parseInt(params.slug))
    if (checkQuiz === undefined) {  
        redirect('/dashboard/'+courseId?.slug)
    }
    const IsAnswered = async () => {
        try {
            const response = await fetch('/user-quizzes/quiz/' + params.slug, {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
    const Answered: Answered = await IsAnswered()
    return (
        <div className="container mx-auto max-h-screen">
            <div className="flex h-full items-center justify-center max-md:py-20 max-md:items-start max-md:justify-start ">
                {/* find the quiz for the same id */}
                <QuestionTemplate quiz={Quizzes.find((quiz) => quiz.id == parseInt(params.slug))} user={user} Answered={Answered} />
            </div>
        </div>
    )
}

export default CourseQuiz
