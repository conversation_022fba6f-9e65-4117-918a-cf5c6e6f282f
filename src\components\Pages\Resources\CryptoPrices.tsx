"use client"

import Image from 'next/image'
import bitcoin from '../../../../public/bitcoin.svg'
import './CryptoPrices.css'
import { useEffect, useState } from 'react';
import { fetchCryptoData } from '@/lib/cryptoData';

interface CryptoPrice {
    name: string;
    Abr: string;
    value: number;
    change: number;
}


const CryptoPrices = () => {

    const [cryptoData, setCryptoData] = useState<CryptoPrice[]>([]);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        const getData = async () => {
            setLoading(true);
            const data = await fetchCryptoData();
            setCryptoData(data);
            setLoading(false);
        };

        getData();
    }, []);

    if (loading) {
        return <p>Loading...</p>;
    }

    return (
        <div className="grid grid-cols-2 justify-center gap-4 text-white sm:grid-cols-4">
            {cryptoData.map((crypto: any) => (
                <div
                    key={crypto.name}
                    className={`max-w-80 grid w-full items-center justify-between rounded-lg border p-4 sm:flex ${crypto.change > 0 ? 'green-inner-shadow border-[#00BF77]' : 'red-inner-shadow border-[#FC2424]'
                        }`}>
                    <section className='flex gap-2'>
                        <Image src={crypto.image} alt={'coin-image'} width={32} height={32} className='sm:w-[48px] sm:h-[48px]' />
                        <div className="grid">
                            <span className="text-[16px] font-bold text-white sm:text-[24px]">{crypto.Abr}</span>
                            <span className="text-[12px] text-[#959595] sm:text-[14px]">{crypto.name}</span>
                        </div>
                    </section>

                    <div className="grid">
                        <span className="mt-2 text-[16px] text-left sm:text-[18px] sm:hidden">${crypto.value.toLocaleString()}</span>
                        <span
                            className={`mt-2 flex  justify-start sm:justify-end text-[16px] font-[600] sm:text-[18px] ${crypto.change > 0 ? 'text-[#00B83F]' : 'text-[#FC2424]'
                                }`}>
                            {crypto.change > 0 ? '+' : ''}
                            {crypto.change}%
                        </span>
                        <span className="mt-2 text-[16px] sm:text-[18px] hidden sm:block">${crypto.value.toLocaleString()}</span>
                    </div>
                </div>
            ))}
        </div>
    )
}

export default CryptoPrices
