"use client"
import { Search } from "@public/global";
import ImageShortcut from "@/components/Ui/Image";
import Link from "next/link";
import { Disclosure, Transition } from '@/lib/headlessui'
import PortableText from '@/components/Ui/portable'
import { cn } from "@/lib/cn";

const VisualAssistance = () => {
    const headerDetails = [
        {
            image: "/cgu/man-climbing-stairs2x.png",
            title: 'Beginner Friendly',
            description: 'I have learned so much and refined so much of my knowledge about Bitcoin and Altcoins…and just being a more intentional investor than others'
        },
        {
            image: "/cgu/reading-book2x.png",
            title: 'Learn at your pace',
            description: 'We have endless amount of content. You pick and choose which area to start with based on your goals or prior experience. You can also refer back to our lessons whenever faced with a challenge.'
        },
        {
            image: "/cgu/chat-bubble2x.png",
            title: 'Beginner Friendly',
            description: 'You’ll be part of our <a href="https://discord.gg/M9cwwCP49c" className="text-yellow">Discord</a> server where you can ask questions to experts and other learners.'
        }
    ]
    const headerItems = [
        {
            title: 'This indicator draws horizontal and dynamic support and resistance'
        },
        {
            title: 'Green = Support'
        },
        {
            title: 'Red = Resistance'
        }
        ,
        {
            title: 'Multiple inputs allow for different measurements and color customization'
        }
        ,
        {
            title: 'Works on all cryptocurrency, stock, and forex pairs'
        }
        ,
        {
            title: 'Runs in REAL TIME on any time frame'
        }

    ]


    const courseOutline = [
        {
            title: "VISUALS",
            content: "<ul class=\"m-4 flex flex-wrap gap-4\"><li><strong>Sloping Solid Lines (Resistance)</strong> – Above current price, displayed in red.</li><li><strong>Sloping Solid Lines (Support)</strong> – Below current price, displayed in green.</li><li><strong>Horizontal Dotted Lines (Resistance)</strong> – Above current price, red. Editable in “Style” tab.</li><li><strong>Horizontal Dotted Lines (Support)</strong> – Below current price, green. Editable in “Style” tab.</li></ul>"
        },
        {
            title: "INPUTS",
            content: "<ul class=\"m-4 flex flex-wrap gap-4\"><li><strong>Number of Candles Observed</strong> – Represents how many sessions (candlesticks) are being observed.</li><li><strong>Horizontal Support/Resistance Source</strong> – Delegates where the horizontal support/resistance source comes from.</li><li><strong>Horizontal Support/Resistance Left</strong> – Represents how many sessions are observed left of the pivot high/low.</li><li><strong>Horizontal Support/Resistance Right</strong> – Represents how many sessions are observed right of the pivot high/low.</li><li><strong>Horizontal Support/Resistance Precision</strong> – Defines the precision of the range of values being observed.</li><li><strong>Dynamic Support/Resistance Resolution</strong> – Defines how many dynamic pivot highs/lows are being observed.</li></ul>"
        },
        {
            title: "STYLE",
            content: "<ul class=\"m-4 flex flex-wrap gap-4\"><li><strong>H S/R 1</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 2</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 3</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 4</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 5</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 6</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 7</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>H S/R 8</strong> – Depicts a horizontal line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>Lines</strong> – Enable/disable dynamic support/resistance lines by checking this box.</li><li><strong>Precision</strong> – N/A value.</li><li><strong>Labels On Price Scale</strong> – N/A value.</li><li><strong>Values In The Status Line</strong> – Remove values from the status line of the indicator by checking this box.</li><li><strong>Note</strong> – *Order on screen may differ from numerical value based on support/resistance values. Confirm plot number for alerts by deselecting unwanted values.*</li></ul>"
        }
        ,
        {
            title: "VISIBILITY",
            content: "<ul className=\"list-disc\"><li>Set the time frames you would like the strategy back tester to be visible on.</li></ul>"

        }
    ]

    const faqs = [
        {
            title: "How does the Visual Assistance Indicator work?",
            content: "The Visual Assistance Indicator makes life a breeze by plotting support and resistance for you."
        },
        {
            title: "How do I set up the Trend Momentum Strategy in TradingView?",
            content: "It comes with default values but is fully customizable. Please watch this video to understand your new indicator fully. Tutorial video"

        },
        {
            title: "Do I have to pay for TradingView to use the Visual Assistance Indicator?",
            content: "With a free TradingView account you can use up to three indicators simultaneously at no cost."

        },
        {
            title: "I bought the Visual Assistance Indicator but I don’t have access to it. What now?",
            content: "Access is typically granted within 1-24 hours after purchase. If it has been longer than 24 hours since your purchase, Please join our discord server here and visit our #get-support channel to get a ticket, or send an <NAME_EMAIL>"

        }
        ,
        {
            title: "I provided the wrong TradingView username at checkout. How can I change this?",
            content: "Please join our discord server here and visit our #get-support channel to get a ticket, or send an <NAME_EMAIL>"

        }
        ,
        {
            title: "How do I open the Visual Assistance Indicator in TradingView?",
            content: "Once logged into your TradingView account, select “charts” > “indicators” > “invite only scripts” > add your new indicator to your chart. (Press the star icon to favorite the indicator and make it appear in your favorites section.)"

        }
        ,
        {
            title: "What if I change my TradingView username or lose. access to my TradingView account that has the indicator?",
            content: "Please join our discord server here and visit our #get-support channel to get a ticket, or send an <NAME_EMAIL>"

        }
    ]
    const followers = [
        {
            url: "https://twitter.com/deadlycrypto",
            icon: "/cgu/icons8-twitterx-48.png",
            name: "X"
        },
        {
            url: "https://www.youtube.com/c/DeadlyCrypto",
            icon: "/cgu/icons8-youtube-48.png",
            name: "YouTube"
        }
    ]
    const followers2 = [
        {
            url: "https://www.instagram.com/greybtc/",
            icon: "/cgu/icons8-instagram-48.png",
            name: "Instagram"
        },
        {
            url: "http://deadlycrypto.com/",
            icon: "/indicators/Logo-v2.webp",
            name: "DeadlyCrypto"
        }
    ]
    return (
        <>
            <section className="bg-slate-900 p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 pt-12">
                    <div className="mt-10">
                        <ImageShortcut
                            src={'/indicators/visualassistance.png'}
                            width={400}
                            height={700}
                            alt="thumbnail"
                            className=" object-contain h-700 w-400"
                            priority
                        />
                    </div>

                    <div>
                        <h1 className="text-white text-h2 font-bold mb-4 mt-4 ">Visual Assistance Indicator</h1>
                        <p className="text-white pt-4 mb-4">Ideal for both new and experienced traders, the “Visual Assistance” indicator gives a visual representation of high and low values that would be considered horizontal and dynamic support/resistance. </p>
                        <ul className="text-white ml-4">
                            <li>
                                {
                                    headerItems.map(item => (
                                        <div key={item.title} className="flex">
                                            <div className="flex-none w-9 h-14">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                    <path fill="#00BF77"
                                                        d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                                                    </path>
                                                    <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                    <path stroke="#fff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5"
                                                        d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                </svg>
                                            </div>
                                            <div className="flex-initial w-90">
                                                {item.title}
                                            </div>
                                        </div>
                                    ))
                                }

                            </li>
                        </ul>
                        <p className="text-white text-headline my-6">$150</p>
                        <Link
                            aria-label="View products"
                            href={"/checkout/indicator/visual-assistance-indicator?previous=/indicators"}
                            className="bg-yellow hover:bg-[#00a043]  active:bg-[#017933] rounded-lg text-sub3 font-semibold py-3 px-6 mt-6 transition-colors duration-150"
                        >
                            Buy Now
                        </Link>
                    </div>
                </div>

            </section>
            <section className="mb-4">
                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold"> Introduction Video </h1>
                </div>
                <div className="flex justify-center rounded mt-10 p-6">
                    <iframe className="rounded" width="720" height="500" src="https://www.youtube.com/embed/HdGCRcU1hhk" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                </div>
            </section>
            <section className="mb-4 p-6">
                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold">Your Instructor</h1>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 m-10">
                    <div>
                        <ImageShortcut
                            src={'/indicators/deadly-crypto.webp'}
                            width={1280}
                            height={720}
                            alt="thumbnail"
                            className=" object-contain h-80 w-96 pt-8"
                            priority
                        />

                    </div>
                    <div>
                        <h1 className="text-headline">Edward Gonzales</h1>
                        <p className="mt-10 text-lg">Is a technical analyst, professional day trader and content creator that has been involved in cryptocurrency since April 2017.
                            Through his passion for cryptocurrency education, he was able to connect with Crypto University and join the team in January 2021. He works closely with the students in our private group to assist in learning and strategy development.
                            Edward also writes blogs for the university website. Specializing in passive income methods, Edward aims to bring a deeper understanding of algorithmic trading to Crypto University and our students. He’s also the creator of <a href="https://cryptouniversity.network/courses/crypto-automated-trading-course">Crypto Automated Trading Course.</a></p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 mt-4 gap-8 ">
                            {followers.map(follower => (
                                <Link key={follower.icon}
                                    aria-label="View products"
                                    href={follower.url}
                                    className=""
                                >
                                    <div className="border-gray-600 border rounded-full p-2">
                                        <div className="flex justify-center">
                                            <div className="flex flex-row ...">
                                                <div> <ImageShortcut
                                                    src={follower.icon}
                                                    width={5}
                                                    height={5}
                                                    alt="thumbnail"
                                                    className=" object-contain h-5 w-5"
                                                    priority
                                                /></div>
                                                <div className="ml-2">{follower.name}</div>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            ))}
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 mt-4 gap-8 ">
                            {followers2.map(follower => (
                                <Link key={follower.icon}
                                    aria-label="View products"
                                    href={follower.url}
                                    className=""
                                >
                                    <div className="border-gray-600 border rounded-full p-2">
                                        <div className="flex justify-center">
                                            <div className="flex flex-row ...">
                                                <div> <ImageShortcut
                                                    src={follower.icon}
                                                    width={5}
                                                    height={5}
                                                    alt="thumbnail"
                                                    className=" object-contain h-5 w-5"
                                                    priority
                                                /></div>
                                                <div className="ml-2">{follower.name}</div>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </section>
            <section className="mb-4 p-6">
                <h1 className="text-center mt-10 text-h3 font-bold">About the Indicator</h1>
                <div className="grid grid-cols-1 sm:grid-cols-1 m-10 gap-8">
                    <p className="mt-10 text-lg">Ideal for both new and experienced traders, the “Visual Assistance” indicator gives a visual representation of high and low values that would be considered horizontal and dynamic support/resistance.
                        High/low points in price are projected by horizontal lines (horizontal support/resistance) and sloping lines (dynamic support/resistance).The user has several editable inputs and options explained below.</p>
                </div>

                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold">How it Functions</h1>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-1 m-10 gap-8">
                    {courseOutline.map((question, index) => (
                        <Disclosure key={index}>
                            {({ open }) => (
                                <div
                                    key={index}
                                    className={cn(
                                        'px-6 py-5 max-md:min-w-full max-md:px-[10px] max-md:py-4',
                                        index + 1 !== question.title.length && 'border-b border-gray-700',
                                        true ? 'max-lg:min-w-full' : ''
                                    )}>
                                    <div
                                        key={index}
                                        className="flex w-full flex-col gap-4 bg-white transition-all duration-300 px-6">
                                        <Disclosure.Button
                                            className={
                                                'relative flex w-full cursor-pointer select-none items-center justify-between gap-4 text-left text-sub3 max-md:text-sub4'
                                            }>
                                            <div
                                                className={cn(
                                                    'max-w-[700px] font-medium text-black transition-[color] duration-300',
                                                    open && 'text-blue'
                                                )}>
                                                {question.title}
                                            </div>
                                            <span
                                                className={cn(
                                                    'flex min-h-[24px] min-w-[24px] flex-col items-center justify-center rounded-full text-white transition-[transform_background] duration-300 ',
                                                    open ? 'bg-blue' : 'rotate-180 bg-black'
                                                )}>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="10"
                                                    height="7"
                                                    fill="none"
                                                    viewBox="0 0 10 7">
                                                    <path
                                                        stroke="#fff"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth="1.5"
                                                        d="M9.242 5.5l-4.12-4.083L1 5.5"
                                                    />
                                                </svg>
                                            </span>
                                        </Disclosure.Button>
                                        <Transition
                                            show={open}
                                            enter="transition duration-100 ease-out"
                                            enterFrom="transform scale-95 opacity-0"
                                            enterTo="transform scale-100 opacity-100"
                                            leave="transition duration-75 ease-out"
                                            leaveFrom="transform scale-100 opacity-100"
                                            leaveTo="transform scale-95 opacity-0">
                                            <div className="font-sans text-sub3 transition-[max-height_opacity] duration-300 max-md:text-sub4">
                                                <div dangerouslySetInnerHTML={{ __html: question.content }} />
                                            </div>
                                        </Transition>
                                    </div>
                                </div>
                            )}
                        </Disclosure>
                    ))}
                </div>
            </section>
            <section className="mb-4 p-6">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mx-10 pb-4 mt-10">
                    {
                        headerDetails.map(header => (
                            <div key={header.title} className="h-100 w-100 bg-gray-750 text-white rounded-md">
                                <ImageShortcut
                                    src={header.image}
                                    width={20}
                                    height={20}
                                    alt="thumbnail"
                                    className="mx-2  object-scale-down  h-20 w-20"
                                    priority
                                />
                                <h1 className="py-4 mx-2 text-2xl font-bold">{header.title}</h1>
                                <div className="p-2" dangerouslySetInnerHTML={{ __html: header.description }} />
                            </div>
                        ))
                    }


                </div>
            </section>

            <section className="mb-4 p-6">
                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold">FAQs</h1>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-1 m-10 gap-8">
                    {faqs.map((question, index) => (
                        <Disclosure key={index}>
                            {({ open }) => (
                                <div
                                    key={index}
                                    className={cn(
                                        'px-6 py-5 max-md:min-w-full max-md:px-[10px] max-md:py-4',
                                        index + 1 !== question.title.length && 'border-b border-gray-700',
                                        true ? 'max-lg:min-w-full' : ''
                                    )}>
                                    <div
                                        key={index}
                                        className="flex w-full flex-col gap-4 bg-white transition-all duration-300 px-6">
                                        <Disclosure.Button
                                            className={
                                                'relative flex w-full cursor-pointer select-none items-center justify-between gap-4 text-left text-sub3 max-md:text-sub4'
                                            }>
                                            <div
                                                className={cn(
                                                    'max-w-[700px] font-medium text-black transition-[color] duration-300',
                                                    open && 'text-blue'
                                                )}>
                                                {question.title}
                                            </div>
                                            <span
                                                className={cn(
                                                    'flex min-h-[24px] min-w-[24px] flex-col items-center justify-center rounded-full text-white transition-[transform_background] duration-300 ',
                                                    open ? 'bg-blue' : 'rotate-180 bg-black'
                                                )}>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="10"
                                                    height="7"
                                                    fill="none"
                                                    viewBox="0 0 10 7">
                                                    <path
                                                        stroke="#fff"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth="1.5"
                                                        d="M9.242 5.5l-4.12-4.083L1 5.5"
                                                    />
                                                </svg>
                                            </span>
                                        </Disclosure.Button>
                                        <Transition
                                            show={open}
                                            enter="transition duration-100 ease-out"
                                            enterFrom="transform scale-95 opacity-0"
                                            enterTo="transform scale-100 opacity-100"
                                            leave="transition duration-75 ease-out"
                                            leaveFrom="transform scale-100 opacity-100"
                                            leaveTo="transform scale-95 opacity-0">
                                            <div className="font-sans text-sub3 transition-[max-height_opacity] duration-300 max-md:text-sub4">
                                                <div dangerouslySetInnerHTML={{ __html: question.content }} />
                                            </div>
                                        </Transition>
                                    </div>
                                </div>
                            )}
                        </Disclosure>
                    ))}
                </div>
            </section>
        </>
    );
};

export default VisualAssistance;