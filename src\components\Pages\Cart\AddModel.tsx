import Link from 'next/link'
import { Fragment } from 'react'
import { Close } from '@public/home'
import WishlistCard from './WishlistCard'
import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { CourseModel } from '@/types/CourseModel'
import { Dialog, Transition } from '@headlessui/react'

interface Props {
    product: CourseModel
    openModel: boolean
    setOpenModel: any
}

const AddModel = ({ product, openModel, setOpenModel }: Props) => {
    return (
        <Transition show={openModel || false} as={Fragment}>
            <Dialog
                as="div"
                onClose={() => setOpenModel(false)}
                className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                <div className="fixed inset-0 flex items-center justify-center max-md:block max-md:p-0">
                    <Transition.Child
                        as={Fragment}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0">
                        <Dialog.Panel className="flex h-full max-w-[520px] items-center justify-center max-md:block max-md:max-w-full">
                            <div className="relative flex flex-col items-center gap-9 rounded-[1.5rem] bg-white px-[52px] pb-10 pt-[100px] font-sans max-md:h-full max-md:justify-center max-md:gap-0 max-md:rounded-none max-md:px-5 max-md:pt-[60px]">
                                <button
                                    type="button"
                                    onClick={() => setOpenModel(false)}
                                    className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                    <Close />
                                </button>

                                <ImageShortcut
                                    src={'/cart/module.png'}
                                    alt="module"
                                    width={250}
                                    height={200}
                                    className="max-md:w-[254px]"
                                />

                                <div className="flex flex-col items-center gap-9 self-center max-md:mt-6 max-md:max-w-full max-md:gap-10">
                                    <Dialog.Title className="font-manrope text-headline font-medium max-md:text-b3">
                                        Added to Cart!
                                    </Dialog.Title>
                                    <div className="flex w-full items-center max-sm:flex-col">
                                        <WishlistCard course={product} removeBorder removeButton />
                                        <div className="max-md:hidden">
                                            <Link aria-label="Cart" href={`/cart`}>
                                                <Button
                                                    variant="dashed"
                                                    className="whitespace-nowrap !border-none !text-blue">
                                                    Go To Cart
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex w-full flex-col gap-5 max-md:mt-14 max-md:w-full">
                                    <Link aria-label="Continue Shopping" href="/" className="w-full rounded-full">
                                        <Button variant="primary" rounded>
                                            Back to Home
                                        </Button>
                                    </Link>
                                    <Link aria-label="Cart" href="/cart" className="w-full rounded-full md:hidden">
                                        <Button variant="default" rounded>
                                            Go To Cart
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    )
}

export default AddModel
