import { FaBitcoin, FaWallet } from 'react-icons/fa';  // Icons for crypto
import {  SiCoinmarketcap, SiTradingview } from 'react-icons/si';
import { FiArrowRightCircle } from 'react-icons/fi';
import { RiRemixiconFill,  } from 'react-icons/ri';
import { FaFacebookF, FaTwitter, FaLinkedinIn, FaRedditAlien } from 'react-icons/fa';
// import { SiUnstoppabledomains } from '@icons-pack/react-simple-icons';
import {  } from 'react-icons/bs';

const RightSide = () => {
  return (
      <div className="w-full md:w-64 bg-white p-10 shadow-xl">
          {/* <h2 className="text-lg mb-4 font-bold">Crypto Links</h2> */}

          {/* Recommended Products */}
          <div className="mb-6">
              <h3 className="text-blue-500 mb-2 font-semibold"> RECOMMENDED PRODUCTS</h3>
              <ul>
                  <li className="mb-2 flex items-center">
                      <RiRemixiconFill className="mr-2 text-purple-600" />
                      <a href="#" className="text-[#424242]">
                          Remitano
                      </a>
                  </li>
              </ul>
          </div>

          {/* Trade Bitcoin */}
          <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[#424242]">Trade Bitcoin</h3>
              <ul>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-yellow-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Binance
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Crypto.com
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Coinbase
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Luno
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FiArrowRightCircle className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          Okex
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FiArrowRightCircle className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          Paxful
                      </a>
                  </li>
              </ul>
          </div>

          {/* Buy Bitcoin */}
          <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[#424242]">Buy Bitcoin</h3>
              <ul>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-yellow-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Binance
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="mr-2 text-orange-500" />
                      <a href="#" className="text-[#424242]">
                          BlockFi
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Coinbase
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Crypto.com
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FiArrowRightCircle className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          Huobi
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Luno
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FiArrowRightCircle className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          Okex
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FiArrowRightCircle className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          Paxful
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <FiArrowRightCircle className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          Valr
                      </a>
                  </li>
              </ul>
          </div>

          {/* Best Crypto Wallet */}
          <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[#424242]">Best Crypto Wallet</h3>
              <ul>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="mr-2 text-gray-800" />
                      <a href="#" className="text-[#424242]">
                          Ledger
                      </a>
                  </li>
              </ul>
          </div>

          {/* Crypto Data */}
          <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[#424242]">Crypto Data</h3>
              <ul>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-green-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Coingecko
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <SiCoinmarketcap className="mr-2 text-[#424242]" />
                      <a href="#" className="text-[#424242]">
                          CoinMarketCap
                      </a>
                  </li>
                  <li className="mb-2 flex items-center">
                      <SiTradingview className="text-blue-500 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Tradingview
                      </a>
                  </li>
              </ul>
          </div>

          {/* Crypto Domains */}
          <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[#424242]">Crypto Domains</h3>
              <ul>
                  <li className="mb-2 flex items-center">
                      <FaBitcoin className="text-blue-600 mr-2" />
                      <a href="#" className="text-[#424242]">
                          Unstoppable domains
                      </a>
                  </li>
              </ul>
          </div>

          {/* Share Post */}
          <div className="mb-6">
              <h3 className="mb-2 font-semibold text-[#424242]">Share Post</h3>
              <div className="flex space-x-2">
                  <a href="#" className="text-blue-600">
                      <FaFacebookF />
                  </a>
                  <a href="#" className="text-blue-400">
                      <FaTwitter />
                  </a>
                  <a href="#" className="text-blue-700">
                      <FaLinkedinIn />
                  </a>
                  <a href="#" className="text-orange-600">
                      <FaRedditAlien />
                  </a>
              </div>
              <button className="bg-yellow-400 text-sm mt-2 rounded-full px-3 py-1 text-gray-800">Copy link</button>
          </div>
      </div>
  )
};

export default RightSide;
