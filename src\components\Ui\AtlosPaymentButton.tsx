"use client"
import { FormikProps } from 'formik';
import React, { useEffect, useState } from 'react';

interface atlosProps {
    orderId: string,
    orderAmount: number,
    userEmail: string,
    userName: string,
    subscriptionId: string,
}

const AtlosPaymentButton = ({ atlosProps,formik, disabled }: { formik: FormikProps<any>; disabled: boolean; atlosProps:atlosProps }) => {
  const [isAtlosAvailable, setIsAtlosAvailable] = useState(false);

  useEffect(() => {
    const checkAtlosAvailability = setInterval(() => {
      if (window.atlos) {
        setIsAtlosAvailable(true);
        clearInterval(checkAtlosAvailability);
      }
    }, 100);
    return () => clearInterval(checkAtlosAvailability);
  }, []);

  const handleAtlosSuccess = () => {
    console.log('Payment Successful');
  };

  const handleAtlosCanceled = () => {
    console.log('Payment Canceled');
  };

  const handleAtlosCompleted = () => {
    console.log('Payment Completed');
  };

  const handleAtlosClick = () => {
    if (isAtlosAvailable) {
      window.atlos.Pay({
        merchantId: process.env.NEXT_PUBLIC_ATLOS_MERCHANT_ID,
        orderId: atlosProps.orderId,
        orderAmount: atlosProps.orderAmount,
        orderCurrency: 'USD',
        recurrence: window.atlos.RECURRENCE_MONTH,
        userEmail: atlosProps.userEmail,
        userName: atlosProps.userName,
        onSuccess: handleAtlosSuccess,
        onCanceled: handleAtlosCanceled,
        onCompleted: handleAtlosCompleted,
        subscriptionId: atlosProps.subscriptionId,
        theme: 'light',
      });
    } else {
      console.log('Atlos is not loaded yet');
    }
  };

  return (
    <button onClick={handleAtlosClick} disabled={!isAtlosAvailable}>
      Complete Payment
    </button>
  );
};

export default AtlosPaymentButton;
