import axios from "@/lib/axios"
import { useState } from "react"

const useReferral = (token: string) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const reset = () => {
    setError("");
    setIsLoading(false);
  };

  const registerAffiliate = async (data: any) => {
    setError("");
    setIsLoading(true);

    try {
      const res = await axios.post("/affiliate", data, {
        headers: {
          authorization: `Bearer ${token}`,
        },
      });

      return res;
    } catch (error: any) {
      console.error({ error });
      setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateReferralCode = async (data: any) => {
    setError("");
    setIsLoading(true);

    try {
      const res = await axios.post("/affiliate/referral-code", data, {
        headers: {
          authorization: `Bear<PERSON> ${token}`,
        },
      });

      return res;
    } catch (error: any) {
      console.error({ error });
      setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    error,
    reset,
    isLoading,
    registerAffiliate,
    updateReferralCode
  };
};

export default useReferral;
