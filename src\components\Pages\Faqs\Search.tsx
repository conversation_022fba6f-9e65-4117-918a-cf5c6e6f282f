'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { Faq } from '@/types/FaqModel'
import { StringToID } from '@/lib/stringToId'
import React, { useState, FormEvent } from 'react'

interface props {
    faq: Faq
}
const Search = ({ faq }: props) => {
    const [search, setSearch] = useState('')
    const [showSuggestion, setShowSuggestion] = useState(false)
    const handleScroll = (e: FormEvent<HTMLFormElement> | React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        e.preventDefault()
        const href = StringToID(searchQuestionTitle(search).length ? searchQuestionTitle(search)[0] : '')
        const elem = document.getElementById(href)
        if (elem) {
            window.scrollTo({
                top: elem.getBoundingClientRect().top + window.scrollY - 100,
                behavior: 'smooth',
            })
        }
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
        if (e.key === 'Enter') {
            handleScroll(e)
        }
    }

    function searchQuestionTitle(questionTitle: string): string[] {
        const matchedCategories: string[] = []
        if (questionTitle === '') {
            return []
        }
        for (const faqs of faq) {
            for (const category of faqs.categories) {
                for (const question of category.questions) {
                    if (question.title.toLowerCase().includes(questionTitle.toLowerCase())) {
                        matchedCategories.push(category.title)
                        break
                    }
                }
            }
        }
        return matchedCategories
    }
    function SuggestionQuestionTitle(questionTitle: string): string[] {
        const matchedCategories: string[] = []
        if (questionTitle === '') {
            return []
        }
        for (const faqs of faq) {
            for (const category of faqs.categories) {
                for (const question of category.questions) {
                    if (question.title.toLowerCase().includes(questionTitle.toLowerCase())) {
                        matchedCategories.push(question.title)
                        break
                    }
                }
            }
        }
        return matchedCategories.slice(0, 4)
    }
    const handlerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearch(e.target.value)
        setShowSuggestion(true)
    }
    return (
        <div className="relative flex flex-col items-start">
            <form
                action=""
                onSubmit={handleScroll}
                onKeyDown={handleKeyDown}
                className="flex w-[430px] flex-col items-center gap-1 max-md:w-full">
                <div className="relative w-full">
                    <div className="relative w-full">
                        <input
                            onChange={handlerChange}
                            className="h-14 w-full rounded-lg border border-gray-700 px-4 pr-12 outline-none drop-shadow-lg placeholder:select-none placeholder:text-black focus:border-blue focus:ring-0 "
                            type="text"
                            placeholder="Search FAQs"
                            value={search}
                        />
                        <button className="absolute inset-0 left-[90%] flex cursor-pointer appearance-none items-center">
                            <Link
                                aria-label="Search"
                                href={
                                    '/faqs/#' +
                                    StringToID(searchQuestionTitle(search).length ? searchQuestionTitle(search)[0] : '')
                                }
                                onClick={handleScroll}>
                                <svg
                                    width="18"
                                    height="19"
                                    viewBox="0 0 18 19"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7.66667 14.8333C11.3486 14.8333 14.3333 11.8486 14.3333 8.16667C14.3333 4.48477 11.3486 1.5 7.66667 1.5C3.98477 1.5 1 4.48477 1 8.16667C1 11.8486 3.98477 14.8333 7.66667 14.8333Z"
                                        stroke="#060E1E"
                                        strokeWidth="1.5"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <path
                                        d="M16.774 17.274L12.375 12.875"
                                        stroke="#060E1E"
                                        strokeWidth="1.5"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </Link>
                        </button>
                    </div>
                    {SuggestionQuestionTitle(search).length > 0 && search.length > 0 && showSuggestion ? (
                        <div className="absolute z-10 w-full rounded-lg border bg-gray-50">
                            {SuggestionQuestionTitle(search).map((questionTitle, index) => (
                                <button
                                    key={index}
                                    className={cn(
                                        'w-full cursor-pointer select-none border-b border-gray-200 px-4 py-2 text-left transition-colors duration-150 hover:text-blue ',
                                        index == SuggestionQuestionTitle(search).length - 1 ? 'border-none' : ''
                                    )}>
                                    <Link
                                        aria-label="Search"
                                        href={'/faqs/#' + StringToID(searchQuestionTitle(questionTitle)[0])}
                                        onClick={e => {
                                            handleScroll(e), setShowSuggestion(false), setSearch(questionTitle)
                                        }}
                                        key={index}>
                                        {questionTitle}
                                    </Link>
                                </button>
                            ))}
                        </div>
                    ) : !showSuggestion ? null : search.length > 0 ? (
                        <div className="absolute z-10 w-full rounded-lg border bg-gray-100 text-black">
                            <div className="select-none px-4 py-2">No results found</div>
                        </div>
                    ) : null}
                </div>
            </form>
        </div>
    )
}

export default Search
