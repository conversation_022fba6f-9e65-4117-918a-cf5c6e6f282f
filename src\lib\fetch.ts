const BASE_URL = process.env.API_URL as string;

const fetchInstance = async (url: string, options: RequestInit = {}): Promise<any> => {
    const fetchOptions: RequestInit = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers,
        },
        ...options,
    };

    // try {
        const response = await fetch(BASE_URL + url, fetchOptions);
    //     if (!response.ok) {
    //         throw new Error('Request failed');
    //     }
        const responseData = await response.json();
        return responseData;
};

export default fetchInstance;
