
'use client'
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation'

const CoinWTrading = () => {

    const pathname = usePathname();
    const [showBanner, setShowBanner] = useState(true);

    useEffect(() => {
        const closeButton = document.getElementById('close-banner-button');
        const closeBanner = () => {
            setShowBanner(false);
        };
        if (closeButton) {
            closeButton.addEventListener('click', closeBanner);
        }
        return () => {
            if (closeButton) {
                closeButton.removeEventListener('click', closeBanner);
            }
        };
    }, [pathname]);

    return (
        <div>
            {showBanner && pathname !== '/' && pathname !== '/alpha-group' && pathname !== '/memberships' && pathname !== '/alpha-group-monthly-promotion' && pathname !== '/coinw-trading-competition' && pathname !== '/noones-p2p-academy' && showBanner && pathname !== '/dashboard' && !pathname.startsWith('/dashboard/') && (
                <div id="sticky-banner" className="fixed top-[80px] left-auto sm:left-1/2 transform -translate-x-auto sm:-translate-x-1/2 z-50 flex justify-between w-full  sm:w-40vw md:w-[40%] p-2 border-b border-[#32cd32] bg-[#32cd32]">
                    <div className="flex items-center pl-4">
                        <p className="flex items-center text-cap1 font-normal text-gray-500">
                            <span className='text-black'>Claim <strong>$30</strong> trading bonus on CoinW <a target="_blank" href="https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US" className="inline text-blue-600 underline underline-offset-2 decoration-600 font-bold decoration-solid hover:no-underline ml-6 sm:ml-8 mr-4">Claim Now {'>'}</a></span>
                        </p>
                    </div>
                    <div className="flex items-center">
                        <button id="close-banner-button" data-dismiss-target="#sticky-banner" type="button" className="p-2 flex-shrink-0 inline-flex justify-center w-7 h-7 items-center text-black hover:bg-green hover:text-gray-900 rounded-lg text-cap1 sm:text-sm">
                            <svg className="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span className="sr-only">Close banner</span>
                        </button>
                    </div>
                </div>
            )}
        </div>
    )
}

export default CoinWTrading