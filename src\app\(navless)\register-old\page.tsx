import getCountryInfo from '@/lib/countries'
import { Country } from '@/types/CountryModel'
import RegisterGamefied from '@/components/Pages/RegisterGamefied'

export const metadata = {
    title: 'Register',
    description:
        'Register for a Crypto University account and start exploring all the different products that we offer.',
    keywords: ['Register', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const RegisterPage = async () => {
    const countries: Country[] = await getCountryInfo()

    return (
        <section className="flex flex-grow">
            <RegisterGamefied countries={countries} />
        </section>
    )
}

export default RegisterPage
