"use client"
import AdditionalDetails from '@/components/Pages/new-register/AdditionalDetails'
import SignUpForm from '@/components/Pages/new-register/SignupForm'
import SuccessPage from '@/components/Pages/new-register/SuccessPage'
import Head from 'next/head'
import React, { useState } from 'react'
import { MdOutlineKeyboardArrowRight, MdClose } from 'react-icons/md'
import axios from "@/lib/axios";
import { signIn } from "next-auth/react";
import useAuth from "@/hooks/useAuth"

const Page = () => {
    const [showAdditionalDetails, setShowAdditionalDetails] = useState(false)
    const [showSuccessPage, setShowSuccessPage] = useState(false)
    const [userData, setUserData] = useState({
        name: '',
        email: '',
        password: '',
        country: '',
        phoneNumber: ''
    })
    console.log('userDatails,', userData)
    const [errors, setErrors] = useState({
        name: '',
        email: '',
        password: '',
        country: '',
        phoneNumber: ''
    })
    const [passwordStrength, setPasswordStrength] = useState(0)

    const validateEmail = (email: string) => {
        return /\S+@\S+\.\S+/.test(email)
    }


    const validatePassword = (password: string) => {
        let strength = 0
        if (password.length >= 8) strength++
        if (password.match(/[a-z]+/)) strength++
        if (password.match(/[A-Z]+/)) strength++
        if (password.match(/[0-9]+/)) strength++
        if (password.match(/[$@#&!]+/)) strength++
        return strength
    }

    const handleInputChange = (name: string, value: string) => {
        setUserData({ ...userData, [name]: value })

        // Clear error when user starts typing
        if (errors[name as keyof typeof errors]) {
            setErrors({ ...errors, [name]: '' })
        }

        // Update password strength
        if (name === 'password') {
            setPasswordStrength(validatePassword(value))
        }
    }

    const handleContinue = () => {
        const newErrors = { ...errors }
        let isValid = true

        if (!userData.name) {
            newErrors.name = 'Name is required'
            isValid = false
        }
        if (!userData.email) {
            newErrors.email = 'Email is required'
            isValid = false
        }

        if (!validateEmail(userData.email)) {
            newErrors.email = 'Invalid email address'
            isValid = false
        } else {
            // Simulating a check for existing email
            if (userData.email === '<EMAIL>') {
                newErrors.email = 'Email is Already Registered'
                isValid = false
            }
        }

        if (userData.password.length < 8) {
            newErrors.password = 'Password must be at least 8 characters long. Password can contain letters, numbers and punctuation'
            isValid = false
        }

        setErrors(newErrors)

        if (isValid) {
            setShowAdditionalDetails(true)
        }
    }

    const handleSignUpComplete = async () => {
        const newErrors = { ...errors };
        let isValid = true;

        if (!userData.country) {
            newErrors.country = 'Please select a country';
            isValid = false;
        }

        const match = userData.phoneNumber.match(/^(\+\d{1,3})(\d{6,})$/);
        let countryCode = "";
        let phoneNumber = "";

        if (match) {
             countryCode = match[1];  // "+265"
             phoneNumber = match[2];       // "999534173"
          
          } else {
            console.log("Invalid phone number format.");
          }

        const data = {
            display_name: userData.name,
            first_name: userData.name.split(" ")[0],
            last_name: userData.name.split(" ").slice(1).join(" "),
            email: userData.email,
            password: userData.password,
            country_code: countryCode,
            country: userData.country,
            phone: phoneNumber + '',
        };

          try {
            await axios.post("/auth/register", data);
            const res = await signIn("credentials", {
              email: userData.email,
              password: userData.password,
              redirect: false,
              // callbackUrl: "/dashboard",
            });


                if (res?.ok && res?.error === null){
                    setShowSuccessPage(true);
                }


            //   if (res?.error) {
            //     if (res?.error === "Request failed with status code 404")
            //       setError("We are having server issues. Please try again later.");
            //     else
            //       setError("Incorrect password.");
            //   }


            return { ok: res?.ok };
          } catch (error: any) {
            if (error.response.data.message === "email_already_exists")

            console.error(error.response.data.message);
          }

        setErrors(newErrors);

        if (isValid) {
            console.log('User registration data:', userData);
            setShowSuccessPage(true);
        }
    };


    const handleClose = () => {
        console.log('Close button clicked')
    }

    return (
        <div
            className="flex min-h-screen flex-col md:bg-[url('/icons/crypto-university-bg.png')] bg-no-repeat bg-cover bg-center"
        >
            <Head>
                <title>Sign Up - The Crypto University</title>
                <link rel="icon" href="/favicon.ico" />
            </Head>
            <div className="flex justify-end p-3">
                <button className="hidden md:flex w-[159px] items-center text-[14px] justify-center rounded-full border-[#FFFFFF] border bg-white bg-opacity-10 py-2 font-[600] text-[#FFFFFF] transition duration-300">
                    Go Home
                    <MdOutlineKeyboardArrowRight className="font-[600] w-4 h-4" />
                </button>
                <button
                    className="md:hidden text-black"
                    onClick={handleClose}
                >
                    <MdClose size={24} />
                </button>
            </div>

            <main className="flex w-full justify-start md:flex-row">
                <div className="w-full p-5 md:p-10 md:ml-[-15px] md:w-1/2">
                    {!showAdditionalDetails && !showSuccessPage ? (
                        <SignUpForm
                            userData={userData}
                            errors={errors}
                            passwordStrength={passwordStrength}
                            onInputChange={handleInputChange}
                            onContinue={handleContinue}
                        />
                    ) : showAdditionalDetails && !showSuccessPage ? (
                        <AdditionalDetails
                            userData={userData}
                            errors={errors}
                            onInputChange={handleInputChange}
                            onSignUpComplete={handleSignUpComplete}
                        />
                    ) : (
                        <SuccessPage />
                    )}
                </div>
            </main>
        </div>
    )
}

export default Page
