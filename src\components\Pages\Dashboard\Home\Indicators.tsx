import { IndicatorModel } from "@/types/IndicatorModel";
import CardIndicator from "./CardIndicator";

interface props {
  indicators: IndicatorModel[];
}

const Indicators = ({ indicators }: props) => {
  return (
    <div className="w-full flex gap-8 px-8 max-md:px-5 items-start flex-wrap">
      {indicators && indicators.map((item, index) => (
        <CardIndicator key={index} indicator={item} />
      ))}
    </div>
  );
};

export default Indicators;
