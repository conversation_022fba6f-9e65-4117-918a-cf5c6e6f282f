import Header from '@/components/Nav/Header'
import Footer from '@/components/Footer/Footer'
import HeaderMobile from '@/components/Nav/HeaderMobile'
import WeeklyDigest from '@/components/Ui/WeeklyDigest'
import MirrorText from '@/components/Pages/Custom/MirrorText'

interface CustomLayoutProps {
    children: React.ReactNode
}

export default async function CustomLayout({ children }: CustomLayoutProps) {
    return (
        <>
            <Header />
            <HeaderMobile />
            <div className="flex min-h-screen flex-col pt-20">
                <main className="flex flex-grow">{children}</main>
            </div>
            <MirrorText />
            <WeeklyDigest />
            <Footer />
        </>
    )
}
