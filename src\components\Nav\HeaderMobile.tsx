'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import Burger from '../Ui/Burger'
import GuestMobile from './GuestMobile'
import { Logo } from '../../../public/marketing'
import { GetCurrentUserClient } from '@/lib/session'

const HeaderMobile = () => {
    const user = GetCurrentUserClient()
    const [isOpen, setIsOpen] = useState(false)

    const handleOpen = async () => {
        setIsOpen(!isOpen)
        if (!isOpen) document.body.style.overflow = 'hidden'
        else if (isOpen) document.body.style.overflow = 'auto'
    }

    return (
        <header
            className={cn(
                'fixed z-50 hidden h-[80px] w-full select-none border-b border-gray-700 bg-white max-lg:flex',
                isOpen ? 'border-none' : ''
            )}>
            <nav className={cn('container mx-auto flex items-center justify-between', isOpen ? 'justify-end' : '')}>
                <Link
                    aria-label="Home page"
                    href="/"
                    className={cn('flex items-center gap-1 pr-2', isOpen ? 'hidden' : '')}>
                    <Logo />
                    <span className="font-sans text-cap2 font-semibold">Crypto University</span>
                </Link>

                {!user && !isOpen && (
                    <Link
                        aria-label="Register Page"
                        className="fit-content flex select-none items-center justify-center rounded-full bg-blue px-5 py-2 font-sans 
                text-cap2 font-medium leading-none text-white transition-[background-color] duration-150 active:bg-[#1E44CC]"
                        href="/register">
                        Register
                    </Link>
                )}
                <div onClick={handleOpen} className={'z-[51]'}>
                    <Burger isOpen={isOpen} handleOpen={handleOpen} />
                </div>
            </nav>
            {/* Dropdown */}
            <div
                className={cn(
                    'no-scrollbar fixed top-[0px] z-50 h-[calc(100vh-0rem)]  w-full overflow-y-scroll bg-white px-4 transition-all duration-500 lg:hidden ',
                    isOpen ? ' translate-y-[80px] ' : ' translate-y-[-130%]'
                )}>
                <div className="flex min-h-screen flex-col gap-6 ">
                    <div className="flex flex-col">
                        <GuestMobile user={user} handleOpen={handleOpen} />
                    </div>
                </div>
            </div>
        </header>
    )
}

export default HeaderMobile
