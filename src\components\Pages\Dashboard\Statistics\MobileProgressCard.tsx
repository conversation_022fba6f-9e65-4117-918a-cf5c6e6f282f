import Link from "next/link";
import ProgressBar from "./ProgressBar";
import ImageShortcut from "@/components/Ui/Image";

interface CoursesStat {
    name: string
    image: string
    isEnrolled: boolean
    notStarted: boolean
    videosCount: number
    lessonsCount: number
    userWatchedVideosCount: number
    certificate: string
    validForCertificate: boolean
}

const MobileProgressCard = ({ courseStat }: { courseStat: CoursesStat }) => {
    const percentage = Math.round((courseStat.userWatchedVideosCount / courseStat.videosCount) * 100);
    return (
        <div className="block sm:hidden">
            <div className=" border shadow-md w-[343px] h-[238px] px-6 py-6 bg-white rounded-lg flex-col justify-center items-center gap-5 inline-flex">
                <div className="bg-white flex-col justify-start items-start gap-4 flex">
                    <div className="justify-center items-center gap-3 inline-flex">
                        <img className="w-[75px] h-[75px] rounded-lg" src={courseStat.image} />
                        <div className="flex-col justify-start items-start gap-[9px] inline-flex">
                            <div className="text-slate-900 text-sm font-normal  leading-[18.20px]">{courseStat.name}</div>
                            <div className="px-[13px] py-0.5 bg-green-600 rounded justify-center items-center gap-2.5 inline-flex">
                                <div className="text-white text-xs font-medium  leading-tight tracking-tight">Completed</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="w-full h-[34px] flex-col justify-center items-center gap-[13px] flex">
                    <ProgressBar percent={(courseStat.userWatchedVideosCount / courseStat.videosCount) * 100} />
                    <div className="text-slate-900 text-base font-medium  leading-tight">{courseStat.userWatchedVideosCount}/{courseStat.videosCount} ({percentage}%)</div>
                </div>
                <div className="justify-center items-center gap-[11px] inline-flex">
                    <div className="justify-start items-center gap-1 flex">
                        <div className="w-6 h-6 pl-[2.30px] pr-[2.37px] py-[5px] justify-center items-center flex">
                            <div className="w-[19.33px] h-3.5 relative">
                            </div>
                        </div>
                        <div className="text-neutral-600 text-xs font-medium  leading-3">{courseStat.lessonsCount} Lessons</div>
                    </div>
                </div>
                <div className="h-[21px] px-6 py-3 rounded-[50px] justify-center items-center gap-2 inline-flex">
                    <Link
                        aria-label="Learn More"
                        href={`/mentorships/`}
                        className="text-sm font-semibol px-2 py-3 underline text-blue leading-[18.20px]">
                        Go To Course &gt;
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default MobileProgressCard;