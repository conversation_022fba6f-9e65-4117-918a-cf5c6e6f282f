'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import Image from '../Ui/Image'
import SignoutBtn from './SignoutBtn'
import { usePathname } from 'next/navigation'
import { Menu, Transition } from '@/lib/headlessui'
import { Activity, Bag, Calendar, Category, Danger, Faq, Heart, Setting } from '@public/global'

interface LinkItemProps {
    href: string
    children: React.ReactNode
    icon: (active: boolean) => JSX.Element
    route: string
}
interface ProfileProps {
    user: any
}

const LinkItem = ({ href, children, icon, route }: LinkItemProps) => {
    const active1 = route === href
    return (
        <Menu.Item>
            {({ active }) => (
                <Link
                    aria-label={children as string}
                    href={href}
                    className={cn(
                        'flex select-none items-center gap-4 rounded-[0.5rem] px-3 py-2 transition-[background] duration-150 ' +
                            (active1 && 'bg-gray-300'),
                        active && 'bg-gray-300'
                    )}>
                    {icon(active1)}
                    <span className={'flex-1 text-sub3 ' + (active1 && 'text-blue')}>{children}</span>
                </Link>
            )}
        </Menu.Item>
    )
}

const ProfileDropdown = ({ user }: ProfileProps) => {
    const currentRoute = usePathname()

    return (
        <Menu as="div">
            {({ open }) => (
                <>
                    <Menu.Button className="flex cursor-pointer items-center gap-4">
                        <Image
                            src={user?.image ? user?.image : 'https://i.ibb.co/tzrYqNX/default.png'}
                            width={36}
                            height={36}
                            className="h-[36px] w-[36px] rounded-full object-cover"
                            alt={user?.display_name + ' profile picture'}
                        />
                        <span className="text-sub3 font-semibold capitalize">
                            {user?.display_name.length > 10
                                ? user?.display_name.slice(0, 10) + '...'
                                : user?.display_name}
                        </span>
                    </Menu.Button>
                    <Transition
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95">
                        <Menu.Items className="absolute right-0 mt-8 flex min-w-[250px] select-none flex-col gap-3 rounded-[0.5rem] bg-white px-4 py-6 font-sans shadow-[0px_40px_80px_rgba(0,0,0,0.1)]">
                            <LinkItem href="/dashboard" icon={Category} route={currentRoute}>
                                My Courses
                            </LinkItem>

                            <hr className="h-[1px] w-full text-gray-700" />

                            <LinkItem href="consultation" icon={Calendar} route={currentRoute}>
                                1 ON 1 Consultation
                            </LinkItem>
                            <LinkItem href="/dashboard/affiliate" icon={Activity} route={currentRoute}>
                                Affiliates
                            </LinkItem>

                            <hr className="h-[1px] w-full text-gray-700" />

                            <LinkItem href="/cart" icon={Bag} route={currentRoute}>
                                My Cart
                            </LinkItem>
                            <LinkItem href="/wishlist" icon={active => <Heart active={active} />} route={currentRoute}>
                                Wishlist
                            </LinkItem>

                            <hr className="h-[1px] w-full text-gray-700" />

                            <LinkItem href="/dashboard/settings" icon={Setting} route={currentRoute}>
                                Settings
                            </LinkItem>
                            <LinkItem href="/help" icon={Danger} route={currentRoute}>
                                Help
                            </LinkItem>
                            <LinkItem href="/faqs" icon={Faq} route={currentRoute}>
                                FAQs
                            </LinkItem>

                            <Menu.Item>
                                {({ active }) => (
                                    <SignoutBtn className={active ? 'bg-red/10' : ''} currentRoute={currentRoute} />
                                )}
                            </Menu.Item>
                        </Menu.Items>
                    </Transition>
                </>
            )}
        </Menu>
    )
}

export default ProfileDropdown
