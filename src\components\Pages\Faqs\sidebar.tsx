import Link from 'next/link'
import SidebarLinks from './SidebarLinks'
import Button from '@/components/Ui/Button'

const sidebar = ({ categories }: any) => {
    const Links = categories.categories

    return (
        <div className="bg-yellow-light py-12 pl-16 pr-11 max-md:hidden">
            <div className="sticky top-[128px] flex flex-col gap-10">
                <h1 className="text-h3">FAQ&apos;s</h1>
                <div className="flex flex-col items-start gap-4">
                    <SidebarLinks Links={Links} />
                </div>
                <div className="flex flex-col gap-14">
                    <div className="border border-gray-700" />
                    <div className="flex flex-col items-start gap-7">
                        <div className="flex flex-col gap-3">
                            <p className="font-sans text-sub2 font-semibold">Have more questions?</p>
                            <p className="w-[282px] font-sans text-sub3 text-gray-900">
                                Contact us and we’ll get in touch with you in just a jiffy!
                            </p>
                        </div>
                        <div className="flex">
                            <Link
                                aria-label="Contact Us"
                                href="mailto:<EMAIL>"
                                className="rounded-full">
                                <Button variant="primary" rounded className="px-12">
                                    Contact Us &gt;
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default sidebar
