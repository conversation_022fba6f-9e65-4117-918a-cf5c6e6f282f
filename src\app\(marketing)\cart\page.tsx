import CTA from '@/components/Ui/CTA'
import Cart from '@/components/Pages/Cart/Cart'
import Whishlist from '@/components/Pages/Cart/Wishlist'

export const metadata = {
    title: 'Cart',
    description: 'Your Crypto University products cart.',
    keywords: ['Cart', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const CartPage = async () => {
    return (
        <section className="flex w-full flex-col text-black">
            <Cart />
            <Whishlist />
            <CTA border />
        </section>
    )
}

export default CartPage
