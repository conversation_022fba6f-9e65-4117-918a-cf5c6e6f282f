import ImageShortcut from '@/components/Ui/Image'

const Benefits = () => {
    const data = [
        { title: 'Earn cryptocurrency', image: '/affiliate/b1.png' },
        { title: 'Flexible working hours', image: '/affiliate/b2.png' },
        { title: 'Work from the comfort of your own home', image: '/affiliate/b3.png' },
        { title: 'Join an active Discord Community here', image: '/affiliate/b4.png' },
    ]
    return (
        <section id='benefits' className='container mx-auto pb-[110px] max-md:pb-[50px]'>
            <div className="flex flex-col gap-14 max-md:gap-8 items-center">
            <h1 className="text-headline font-medium max-md:text-b3">Benefits</h1>
            <div className="flex flex-wrap gap-6 max-md:gap-3 ">
                {data.map((item, index) => (
                    <div key={index} className="flex w-[310px] max-md:w-full max-md:flex-row px-5  max-md:px-3 max-md:justify-start max-md:gap-2 pb-8 max-md:pb-1 flex-col items-center justify-center gap-2 border shadow-[2.6594090461730957px_2.6594090461730957px_0px_0px_#0000000D] rounded-lg ">
                        <ImageShortcut width={205} height={205} src={item.image} alt={item.title} className="object-contain max-md:w-20 max-md:h-20" />
                        <h3 className="text-sub1 max-md:text-cap1 font-medium text-center max-md:text-left">{item.title}</h3>
                    </div>
                ))}
            </div>
        </div>
        </section>
    )
}

export default Benefits
