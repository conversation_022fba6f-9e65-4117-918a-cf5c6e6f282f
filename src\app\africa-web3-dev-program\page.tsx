import SecondView from './SecondView'
import FirstView from './FirstView'


export const metadata = {
  title: 'Africa Web3 Dev Program | Convert Web2 Devs to Web3 | $1.3M Salaries Paid',
  description:
      'Join the Africa Web3 Dev Program led by <PERSON><PERSON>. Convert from Web2 to Web3 development, earn competitive salaries, and gain access to workshops, training, networking events, and funding opportunities. Collaborate with top tech companies and be part of a thriving blockchain community.',
  keywords: ['Africa Web3 Dev Program', 'Web3 development', 'Web2 to Web3', 'blockchain training', '<PERSON><PERSON>', 'Ethereum', 'ICP', 'Celo', 'XRP', 'blockchain ecosystem', 'tech workshops', 'developer networking', 'blockchain hackathons', 'tech community', 'blockchain funding opportunities', 'Web3 projects', 'developer support'],
  openGraph: {
      images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1717089125/The_Africa_Web3_Dev_Program_thumbnail_afdgjw.jpg',
    },
}


const DevProgram = () => {
  return (
      <>
         <FirstView />
         <SecondView />
      </>
  )
}

export default DevProgram
