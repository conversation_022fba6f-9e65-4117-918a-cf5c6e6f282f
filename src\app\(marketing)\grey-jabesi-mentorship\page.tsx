
import Link from 'next/link'
// import OneOnOneView from '@/components/Pages/Products/OneOnOneView'
// import OneOnOneCourse from '@/components/Pages/Products/OneOnOneCourse'
// import WhyOneOnOne from '@/components/Pages/Products/WhyOneOnOne'
// import OneOnOneGain from '@/components/Pages/Products/OneOnOneGain'
// import OneOnOneAdmissionProcess from '@/components/Pages/Products/OneOnOneAdmissionProcess'
import OurStudents from '@/components/Pages/Memberships/OurStudents'
// import OneOnOneFoooter from '@/components/Pages/Products/OneOnOneFoooter'
import OneOnOneView from '@/components/Pages/Products/OneOnOneView'
import WhyOneOnOne from '@/components/Pages/Products/WhyOneOnOne'
import OneOnOneFoooter from '@/components/Pages/Products/OneOnOneFoooter'
import OneOnOneCourse from '@/components/Pages/Products/OneOnOneCourse'
import OneOnOneGain from '@/components/Pages/Products/OneOnOneGain'
import OneOnOneAdmissionProcess from '@/components/Pages/Products/OneOnOneAdmissionProcess'

export const metadata = {
    title: 'Exclusive One-on-One Crypto Mentorship with Grey Jabesi - Apply Now!',
    description:
        'Join an exclusive one-on-one mentorship with Grey Jabesi, a self-made crypto millionaire. Experience tailored guidance, daily interactions, and guaranteed transformation in the crypto world. Apply now for dedicated mentorship, hands-on trading experience, and complimentary masterclasses. Limited spots available',
    keywords: ['Crypto mentorship', 'Grey Jabesi', 'Cryptocurrency trading', 'Blockchain training', 'One-on-one mentorship', 'Crypto masterclass', 'Crypto University'],
    openGraph: {
        images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1716016181/One-on-One_Mentorship_with_Grey_Jabesi_-_Webpage_Thumbnail_Op2_gdkmxy.jpg',
      },
}


const NewConsultation = () => {
    return (
        <div className="w-full">
            <OneOnOneView />
            <OneOnOneCourse />
            <WhyOneOnOne />
            <OneOnOneGain />
            <OneOnOneAdmissionProcess />
            <OurStudents/>
            <OneOnOneFoooter />
        </div>
    )
}

export default NewConsultation