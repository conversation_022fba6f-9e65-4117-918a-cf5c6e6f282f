import { FC, ReactNode } from 'react'

interface Props {
    children: ReactNode,
    onClick?: () => void,
    fill?: boolean
}

const commonClasses = `py-3 px-6 text-sub3 font-sans font-medium rounded-full flex items-center justify-center select-none gap-2 w-full min-w-fit transition-[background-color] duration-150 capitalize`;
const normalClasses = `md:hidden border border-blue text-blue hover:border-blue/90 ${commonClasses}`;
const filledClasses = `bg-blue hover:bg-blue/90 text-white ${commonClasses}`;

const ButtonMobile: FC<Props> = ({ children, fill, onClick }) => {
    const className1 = fill ? filledClasses : normalClasses;

    return (
        <button
            type="button"
            onClick={onClick}
            className={className1}
        >
            {children}
        </button>
    )
}

export default ButtonMobile