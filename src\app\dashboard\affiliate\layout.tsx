import { siteConfig } from '@/config/site'
import { getCurrentUser } from '@/lib/session'
import fetchInstance from '@/lib/fetch'
import Nav from '@/components/Pages/Dashboard/Affiliate/Nav'

export const metadata = {
    title: {
        default: siteConfig.name,
        template: `%s | ${siteConfig.name}`,
    },
    description: 'Affilaite page for your Crypto University account.',
}

interface DashboardLayotuProps {
    children: React.ReactNode
}

// ? Dynamic forcing or it will give deployment error
export const dynamic = 'force-dynamic'

const affiliateLink = async (user: any) => {
    try {
        const res = await fetchInstance('/auth/profile/affiliate', {
            headers: {
                Authorization: `Bearer ${user?.access_token}`,
            },
            next: {
                revalidate: 10,
            },
        })
        return res.affiliateCode
    } catch (error) {
        console.error(error)
    }
}

export default async function DashboardLayotu({ children }: DashboardLayotuProps) {
    const user = await getCurrentUser()
    if (!user) return null

    let link = ''
    let finalLink = ''
    if (user?.is_affiliate) {
        link = await affiliateLink(user)
        finalLink = (process.env.NEXT_PUBLIC_BASE_URL + 'ref/' + link) as string
    }


    return (
        <>
            <main className="flex flex-1 overflow-y-auto overflow-x-hidden">
                <section className="flex flex-col h-screen flex-grow pt-6 max-lg:h-auto max-lg:flex-col ">
                    {user?.is_affiliate ? (
                        // <div className="flex flex-col gap-3">
                        // </div>
                        <>
                            <Nav />
                        </>
                    ) : (
                        <>
                        </>
                    )}
                    {children}
                </section>
            </main>
        </>
    )
}
