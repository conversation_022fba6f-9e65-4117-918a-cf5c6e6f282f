"use client"
import React from 'react';
import html2canvas from 'html2canvas';

interface DownloadButtonProps {
    studentName: string;
    courseName: string;
    completionDate: string;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({ studentName, courseName, completionDate }) => {
    const handleDownload = () => {
        // Select the element containing your content
        const element: any = document.getElementById('content');

        // Use html2canvas to convert the content to an image
        html2canvas(element).then(canvas => {
            // Convert the canvas to a base64 image data
            const imgData = canvas.toDataURL('image/png');

            // Create a temporary link element
            const link = document.createElement('a');
            link.href = imgData;
            link.download = 'certificate.png'; // Set the filename here

            // Programmatically click the link to trigger download
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);
        });
    };

    // This component doesn't render anything itself, it just triggers the download
    // when the button is clicked.
    return (
        <button onClick={handleDownload}>Download Certificate</button>
    );
};

export default DownloadButton;
