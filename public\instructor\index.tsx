const Facebook = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fill="#AAA"
        d="M12 0C5.373 0 0 5.373 0 12c0 6.016 4.432 10.984 10.207 11.852V15.18h-2.97v-3.155h2.97V9.927c0-3.475 1.693-5 4.58-5 1.384 0 2.115.102 2.462.149v2.753h-1.97c-1.226 0-1.655 1.163-1.655 2.473v1.724h3.594l-.488 3.155h-3.106v8.696C19.481 23.083 24 18.075 24 12c0-6.627-5.373-12-12-12z"
      ></path>
    </svg>
  );
};

const Instagram = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fill="#AAA"
        d="M7.09 0C3.18 0 0 3.18 0 7.09v9.82C0 20.82 3.18 24 7.09 24h9.82c3.91 0 7.09-3.18 7.09-7.09V7.09C24 3.18 20.82 0 16.91 0H7.09zm11.456 4.364c.6 0 1.09.49 1.09 1.09 0 .6-.49 1.091-1.09 1.091-.6 0-1.091-.49-1.091-1.09 0-.6.49-1.091 1.09-1.091zM12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6 2.69-6 6-6zm0 1.09A4.916 4.916 0 007.09 12 4.916 4.916 0 0012 16.91 4.916 4.916 0 0016.91 12 4.916 4.916 0 0012 7.09z"
      ></path>
    </svg>
  );
};

const LinkedIn = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      fill="none"
      viewBox="0 0 25 25"
    >
      <path
        fill="#AAA"
        d="M3.227 5.955a2.727 2.727 0 100-5.455 2.727 2.727 0 000 5.455zM4.864 24.5H1.59C.936 24.5.5 24.064.5 23.41V9.226c0-.654.436-1.09 1.09-1.09h3.274c.654 0 1.09.436 1.09 1.09V23.41c0 .655-.436 1.091-1.09 1.091zM17.954 8.136c-1.636 0-3.163.655-4.363 1.637v-.546c0-.654-.436-1.09-1.091-1.09H9.227c-.654 0-1.09.436-1.09 1.09V23.41c0 .655.436 1.091 1.09 1.091H12.5c.655 0 1.09-.436 1.09-1.09v-8.183a2.7 2.7 0 012.728-2.727 2.7 2.7 0 012.727 2.727v8.182c0 .655.437 1.091 1.091 1.091h3.273c.655 0 1.091-.436 1.091-1.09v-8.728c0-3.6-2.945-6.546-6.546-6.546z"
      ></path>
    </svg>
  );
};

const Twitter = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="20"
      fill="none"
      viewBox="0 0 24 20"
    >
      <path
        fill="#AAA"
        d="M24 2.308a9.85 9.85 0 01-2.83.776A4.936 4.936 0 0023.336.361a9.887 9.887 0 01-3.127 1.193A4.912 4.912 0 0016.615 0a4.924 4.924 0 00-4.795 6.045A13.981 13.981 0 011.67.9a4.905 4.905 0 00-.666 2.476c0 1.708.868 3.216 2.19 4.1a4.92 4.92 0 01-2.23-.617v.062a4.93 4.93 0 003.948 4.83 4.928 4.928 0 01-2.224.082 4.935 4.935 0 004.6 3.42 9.87 9.87 0 01-6.115 2.107c-.399 0-.79-.023-1.173-.07a13.912 13.912 0 007.547 2.213c9.057 0 14.01-7.502 14.01-14.008 0-.213-.005-.428-.013-.637A10.01 10.01 0 0024 2.308z"
      ></path>
    </svg>
  );
};
const Youtube = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="18"
      fill="none"
      viewBox="0 0 24 18"
    >
      <path
        fill="#AAA"
        d="M23.33 3.063C23.108 1.838 22.051.946 20.824.668 18.99.278 15.591 0 11.915 0 8.241 0 4.79.278 2.952.668 1.727.946.668 1.78.446 3.063.222 4.455 0 6.403 0 8.909c0 2.506.222 4.455.5 5.847.224 1.224 1.281 2.116 2.506 2.395 1.949.389 5.29.667 8.966.667 3.676 0 7.017-.278 8.966-.667 1.224-.279 2.281-1.114 2.505-2.395.222-1.392.5-3.398.557-5.847-.113-2.506-.392-4.454-.67-5.846zM8.91 12.807V5.011l6.792 3.898-6.793 3.898z"
      ></path>
    </svg>
  );
};
export { Facebook, Instagram, LinkedIn, Twitter, Youtube };
