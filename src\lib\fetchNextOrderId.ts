// lib/fetchNextOrderId.ts

export interface NextOrderIdResponse {
    nextOrderId: string;
    success: boolean;
    statusCode: number;
}

export const fetchNextOrderId = async (): Promise<string | null> => {
    try {
        const response = await fetch(`${process.env.API_URL}/user-subscription/get-next-order-id`);
        const data: NextOrderIdResponse = await response.json();

        if (data.success && data.statusCode === 200) {
            return data.nextOrderId;
        } else {
            throw new Error('Failed to fetch nextOrderId');
        }
    } catch (error) {
        console.error('Error fetching nextOrderId:', error);
        return null;
    }
};
