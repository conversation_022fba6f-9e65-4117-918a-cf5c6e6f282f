import fetch from '@/lib/fetch'
import { Metadata } from 'next'
import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'
import { notFound } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import { capitalizeWords } from '@/lib/capitalize'
import InstructorModel from '@/types/InstructorModel'
import Contact from '@/components/Pages/Course-details/Contact'
import Content from '@/components/Pages/Course-details/Content'
import { CourseDetailsModel, CourseModel } from '@/types/CourseModel'

interface MetaProps {
    params: {
        slug: any
    }
}
type Props = {
    params: {
        slug: string
    }
}

const GetUserCount = async () => {
    try {
        const response = await fetch('/website-home-page', {
            next: { revalidate: 60 },
        })
        return response.usersCount
    } catch (error) {
        console.error('Error:', error)
        return error
    }
}
const GetOneCourse = async (slug: string) => {
    if (slug !== undefined) {
        try {
            const response: { course: CourseModel } = await fetch('/course/slug/' + slug)
            const course: CourseModel = response.course

            const res: { instructor: InstructorModel } = await fetch('/instructor/' + course.instructor_id, {
                next: { revalidate: 60 },
            })
            const instructor: InstructorModel = res.instructor

            return { course, instructor }
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
}
const getStats = async (slug:string) => {
    try {
        const response1 :{ response: CourseDetailsModel } = await fetch('/course/course-details/'+slug, {
            next: { revalidate: 60 },
        })
        const response: CourseDetailsModel = response1.response
        return response
    } catch (error) {
        console.error('Error:', error)
        return error
    }
}
async function checkIfUserHasCourse(courseSlug: string, user: any) {
    const res = await fetchInstance(`/user-course/have-course?slug=${courseSlug}`, {
        headers: {
            Authorization: `Bearer ${user?.access_token}`,
        },
        next: { revalidate: 60 },
    })
    return res.status
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await GetOneCourse(slug)
            if (response === null || response === undefined) return {}
            else {
                return {
                    title: capitalizeWords(response.course.name),
                    description: response.course.short_description as string,
                    authors: [{ name: response.instructor.name }],
                    keywords: [
                        response.course.name as string,
                        response.course.slug as string,
                        'Crypto',
                        'Crypto University',
                        'Education',
                        'Crypto Courses',
                        'Customized Courses',
                    ],
                    openGraph: {
                        title: response.course.name,
                        description: response.course.short_description,
                        type: 'website',
                        url: response.course.slug,
                        images: [
                            {
                                url: response.course.bannerImage as string,
                                width: 1200,
                                height: 630,
                                alt: response.course.name,
                            },
                        ],
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: response.course.name,
                        description: 'Checkout page for ' + response.course.name,
                        images: [response.course.bannerImage as string],
                    },
                }
            }
        } catch (error: any) {
            console.error('Error:', error)
            return error
        }
    else return {}
}

const CoursePage = async ({ params }: Props) => {
    const user = await getCurrentUser()
    const slug = params.slug
    const stats: CourseDetailsModel = await getStats(slug) as CourseDetailsModel;

    let isEnrolled = false
    if (user) isEnrolled = await checkIfUserHasCourse(slug, user)

    const count = await GetUserCount()

    const response = await GetOneCourse(slug)
    const data = response as {
        course: CourseModel
        instructor: InstructorModel
    }
    if (data === null || data === undefined) notFound()

    const { course, instructor } = data

    return (
        <div className="relative flex w-full flex-col">
            <Content stats={stats} product={course} isEnrolled={isEnrolled} user={user} slug={slug} instructor={instructor} />
            <div className="lg:hidden">
                <Contact />
            </div>
            <div>
                <CTA border count={count} />
            </div>
        </div>
    )
}

export default CoursePage
