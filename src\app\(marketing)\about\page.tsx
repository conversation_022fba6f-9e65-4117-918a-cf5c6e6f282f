import Hero from '@/components/Pages/About/Hero'
import Team from '@/components/Pages/About/Team'
import Crypto from '@/components/Pages/About/Crypto'
// import AtlosPaymentButton from '@/components/Ui/AtlosPaymentButton'

export const metadata = {
    title: 'About',
    description:
        'Crypto University (Crypto U) is an online university that offers various courses on Cryptocurrency and Blockchain. We have a community of over 40000 Crypto enthusiasts from around the world. We believe in financial freedom, especially through Cryptocurrency, because it does not discriminate.',
    keywords: ['About', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const AboutPage = () => {
    return (
        <section className="w-full">
            <Hero />
            <Crypto />
            <Team />
            {/* <AtlosPaymentButton /> */}
        </section>
    )
}

export default AboutPage
