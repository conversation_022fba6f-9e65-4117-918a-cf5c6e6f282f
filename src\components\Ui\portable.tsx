import { PortableText, PortableTextComponents } from "@/lib/portabletext";
import { PortableTextBlock } from "@/lib/sanity";

interface props {
  value: PortableTextBlock[];
}

const components: PortableTextComponents = {
  marks: {
    link: ({ value, children }) => {
      const target = (value?.href || "").startsWith("http")
        ? "_blank"
        : undefined;
      return (
        <a
          className="text-blue hover:underline"
          href={value?.href}
          target={target}
          rel={target === "_blank" ? "noindex nofollow" : undefined}
        >
          {children}
        </a>
      );
    },
  },
  block: {
    h1: ({ children }) => <h1 className="text-h1">{children}</h1>,
    h2: ({ children }) => <h2 className="text-h3">{children}</h2>,
    h3: ({ children }) => <h3 className="text-h3">{children}</h3>,
    h4: ({ children }) => <h4 className="text-headline">{children}</h4>,
    h5: ({ children }) => <h5 className="text-b1">{children}</h5>,
    h6: ({ children }) => <h6 className="text-b2">{children}</h6>,
    normal: ({ children }) => <p className="text-sub3 max-md:text-sub4 py-1">{children}</p>,
    blockquote: ({ children }) => (
      <blockquote style={{ fontStyle: "italic" }}>
        <span className="text-gray-700 font-semibold">|</span> {children}
      </blockquote>
    ),
  },
  list: {
    bullet: ({ children }) => <p style={{ listStyle: "disc" }}>{children}</p>,
    number: ({ children }) => (
      <p style={{ listStyle: "decimal" }}>{children}</p>
    ),
    checkmarks: ({ children }) => (
      <ol className="m-auto text-b3">{children}</ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="list-disc">{children}</li>,
    checkmarks: ({ children }) => <li>✅ {children}</li>,
  },
};

const PortableTextComponent = ({ value }: props) => {
  return <PortableText value={value} components={components} />;
};
export default PortableTextComponent;
