'use client'
import { useState, useEffect } from 'react'
import <PERSON><PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import useWishlist from '@/hooks/useWishlist'
import { CourseModel } from '@/types/CourseModel'
import { cn } from '@/lib/cn'
import useCart from '@/hooks/useCart'

interface Props {
    course: CourseModel
    removeBorder?: boolean
    removeButton?: boolean
}

const Delete = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            fill="#AAA"
            fillRule="evenodd"
            d="M20.287 5.243c.39 0 .713.323.713.734v.38a.73.73 0 01-.713.734H3.714A.73.73 0 013 6.357v-.38c0-.411.324-.734.714-.734H6.63c.592 0 1.107-.421 1.24-1.015l.153-.682C8.261 2.617 9.041 2 9.935 2h4.13c.884 0 1.674.617 1.902 1.497l.163.73a1.28 1.28 0 001.241 1.016h2.916zm-1.481 13.891c.304-2.837.837-9.577.837-9.645a.746.746 0 00-.18-.558.726.726 0 00-.524-.234H5.07c-.2 0-.391.087-.524.234a.79.79 0 00-.19.558l.053.647c.142 1.763.537 6.674.793 8.998.18 1.712 1.304 2.788 2.931 2.827 1.256.029 2.55.039 3.872.039 1.246 0 2.51-.01 3.805-.039 1.684-.029 2.806-1.086 2.997-2.827z"
            clipRule="evenodd"></path>
    </svg>
)

const WishlistCard = ({ course, removeBorder, removeButton }: Props) => {
    const wishlist = useWishlist()
    const cart = useCart()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])
    if (!isMounted) return null

    const OnRemoveToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.removeproduct(course.id)
    }
    const OnAddToCart = (e: any) => {
        e.preventDefault()
        cart.addproduct(course)
        wishlist.removeproduct(course.id)
    }
    //check if id of product in wishlist equal to id of product in cart
    const isProductInCart = cart.products.some(product => product.id === course.id)
    return (
        <div
            className={cn(
                'flex max-w-[400px] flex-col justify-between gap-4 rounded-lg border border-gray-700 p-6 max-md:w-full max-md:max-w-full',
                removeBorder ? 'border-none p-0' : ''
            )}>
            <div className="flex items-center gap-3">
                <ImageShortcut
                    src={course.image}
                    alt="course"
                    className="h-20 w-20 rounded-lg object-contain"
                    width={80}
                    height={80}
                />
                <div className="flex flex-col gap-[6px] text-sub3 capitalize">
                    <h1>{course.name}</h1>
                    <p className="font-semibold">${course.priceWithoutTax}</p>
                </div>
            </div>
            <div className={removeButton ? 'hidden' : 'flex items-center gap-2'}>
                <div
                    onClick={OnRemoveToWishlist}
                    className="rounded-lg border border-gray-700 p-2 transition-colors duration-150 hover:cursor-pointer hover:bg-gray-400">
                    <Delete />
                </div>
                {!isProductInCart ? (
                    <Button onClick={OnAddToCart} className="!py-2" rounded>
                        {' '}
                        <span className="text-sub1">+</span> Add to Cart
                    </Button>
                ) : (
                    <Button disabled className="!py-2" rounded>
                        {' '}
                        <span className="text-sub1">+</span> In Cart
                    </Button>
                )}
            </div>
        </div>
    )
}

export default WishlistCard
