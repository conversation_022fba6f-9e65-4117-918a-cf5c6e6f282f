
type StatisticProps = {
    title: string
    value: string
    subValue: string | null
}

const StatisticsCard = ({ statistic }: {statistic:StatisticProps}) => {
    return (
        <div className="my-1 sm:my-4">
        <div className="block max-w-xl p-6 shadow-md bg-white border rounded-md  h-[8rem] sm:h-auto">
                <h5 className="mb-2 text-cap2 sm:text-2xl tracking-tight  text-gray-800">{statistic.title}</h5>
                <div className="flex justify-between gap-8 mt-6">
                    {statistic.subValue !== null ? (
                    <div>
                        <h5 className="mb-2 text-sub3 sm:text-callout  tracking-tight  text-green">{statistic.value}</h5>
                    </div>
                    ) : (
                    <div>
                        <h5 className="mb-2 text-cap2 sm:text-callout  tracking-tight  text-black">{statistic.value}</h5>
                    </div>
                    )}
                    <div>
                        <h5 className="mb-2 text-cap1  tracking-tight  text-black ">{statistic.subValue}</h5>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default StatisticsCard
