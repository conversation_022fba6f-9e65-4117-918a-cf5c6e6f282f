import Sections from '@/components/Pages/Blog/Sections'
import CardSkeleton from '@/components/Pages/Blog/CardSkeleton'
import HeroSkeleton from '@/components/Pages/Blog/HeroSkeleton'

const Loading = () => {
    return (
        <main>
            <HeroSkeleton />

            <section className="container mx-auto mb-8 max-lg:mb-20 max-lg:space-y-14">
                <Sections>
                    <div className="grid grid-cols-3 gap-6 max-lg:grid-cols-2 max-md:grid-cols-1">
                        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((_: any, index: any) => (
                            <span key={index}>
                                <CardSkeleton />
                            </span>
                        ))}
                    </div>
                </Sections>
            </section>
        </main>
    )
}

export default Loading
