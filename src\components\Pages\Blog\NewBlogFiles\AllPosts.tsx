'use client'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { LuDot } from 'react-icons/lu'

async function GetPagePosts(page = 1) {
    try {
        const res = await fetch(process.env.NEXT_PUBLIC_BASE_URL + '/api/blog?page=' + page, {
            next: { revalidate: 10 },
        })
        if (res.headers.get('content-type') !== 'application/json') return null
        const data = await res.json()
        return data.response.nodes
    } catch (error) {
        console.error(error)
    }
}

const AllPosts = () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [posts, setPosts] = useState<any[]>([])
    const postsPerPage = 6 // Number of posts per page

    useEffect(() => {
        const fetchPosts = async () => {
            const fetchedPosts = await GetPagePosts(currentPage)
            setPosts(fetchedPosts)
        }
        fetchPosts()
    }, [currentPage])

    // Calculate the indices of posts to display on the current page
    const indexOfLastPost = currentPage * postsPerPage
    const indexOfFirstPost = indexOfLastPost - postsPerPage
    const currentPosts = posts.slice(indexOfFirstPost, indexOfLastPost)

    // Calculate total pages
    const totalPages = Math.ceil(posts.length / postsPerPage)

    // Change page
    const paginate = (pageNumber: any) => setCurrentPage(pageNumber)

    return (
        <div className="overflow-hidden">
            <div className="mx-auto max-w-[400px] sm:max-w-[700px] md:max-w-[1400px]">
                <h2 className="text-[36px] font-[600]">All Blog Posts</h2>
                <section className="mt-5 grid md:grid-cols-2">
                    {currentPosts.map((post, index) => (
                        <div
                            key={index}
                            className="grid justify-start justify-items-start border-t p-3 align-top md:flex">
                            <Image
                                src={post.featuredImage.node.sourceUrl}
                                alt={post.featuredImage.node.altText || 'post'}
                                width={249}
                                height={140}
                                className="self-start"
                            />
                            <section className="mt-3 md:mx-4 md:mt-0">
                                <div className="space-y-2">
                                    <ol className="flex items-center gap-[8px] text-[14px] font-[600] text-[#2655FF]">
                                        {post.categories.nodes.map((category: any, idx: number) => (
                                            <React.Fragment key={idx}>
                                                <li className="underline">{category.name}</li>
                                                {idx < post.categories.nodes.length - 1 && (
                                                    <LuDot className="mx-[1px] h-6 w-6 text-[#2655FF]" />
                                                )}
                                            </React.Fragment>
                                        ))}
                                    </ol>
                                    <h2 className="text-[18px] font-[600] md:text-[24px]">{post.title}</h2>
                                    <p
                                        className="text-[12px] font-[400] text-[#97989F] md:text-[14px]"
                                        dangerouslySetInnerHTML={{ __html: post.excerpt }}></p>
                                    <div className="flex items-center gap-2 text-center">
                                        <Image
                                            src={post.featuredImage.node.sourceUrl}
                                            alt={post.featuredImage.node.altText}
                                            width={36.88}
                                            height={3.07}
                                            className="rounded-full"
                                        />
                                        <h2 className="text-[12px] text-[#2655FF]">{post.author.node.name}</h2>
                                        <h3 className="text-[12px] font-[500] text-[#97989F]">
                                            {new Date(post.date).toLocaleDateString()}
                                        </h3>
                                    </div>
                                </div>
                            </section>
                        </div>
                    ))}
                </section>
                <div className="mt-4 flex ">
                    {Array.from({ length: totalPages }, (_, index) => (
                        <button
                            key={index + 1}
                            onClick={() => paginate(index + 1)}
                            className={`mx-1 px-4 py-2 text-[14px] hover:rounded-full hover:bg-[#2655FF1A] hover:text-[#2655FF] md:text-[18px] ${
                                currentPage === index + 1 ? 'rounded-full bg-[#2655FF1A] text-[#2655FF]' : ''
                            }`}>
                            {index + 1}
                        </button>
                    ))}
                </div>
            </div>
        </div>
    )
}

export default AllPosts
