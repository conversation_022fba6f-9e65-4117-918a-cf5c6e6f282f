export default function Loading() {
    return (
        <section className="w-full py-8 container mx-auto flex flex-col gap-9 items-start">
            <div className="flex flex-wrap gap-4 items-center justify-between w-full max-md:flex-col max-md:items-start">
                <div className="w-80 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                <div className="max-md:hidden flex gap-8">
                    <div className="w-20 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                    <div className="w-20 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                </div>
            </div>
            <div className="flex flex-col gap-6 w-full">
                <div className="w-32 h-5 animate-pulse bg-gray-500 rounded-3xl " />
                <div className="w-full animate-pulse h-[600px] max-md:h-60 bg-gray-500 rounded-3xl" />
            </div>
            <div className="w-full flex justify-center md:hidden">
                <div className="w-40 animate-pulse h-5 bg-gray-500 rounded-3xl" />
            </div>
            <div className=" border-b border-gray-500 w-full md:hidden" />
            <div className="w-20 animate-pulse h-5 bg-gray-500 rounded-3xl" />
            <div className="flex flex-col gap-6 w-full">
                <div className="flex flex-col gap-3">
                    <div className="w-40 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                    <div className="w-full animate-pulse h-20 bg-gray-500 rounded-3xl " />
                </div>
                <div className=" border-b border-gray-500" />
                <div className="grid grid-cols-3 max-md:grid-cols-1 gap-3">
                    {[...Array(3)].map((_, i) => (
                        <div key={i} className="flex flex-col gap-2">
                            <div className="w-32 h-5 animate-pulse bg-gray-500 rounded-3xl" />
                            <div className="flex gap-4 items-center">
                                <div className="p-4 border border-gray-500 rounded-full">
                                    <div className="w-6 h-6 animate-pulse bg-gray-500 rounded-full" />
                                </div>
                                <div className="flex flex-col">
                                    <div className="w-32 h-10 animate-pulse bg-gray-500 rounded-3xl" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
                <div className=" pr-11 max-md:px-4 ">
                    <div className="flex flex-col gap-6 border-gray-500 max-md:pb-6 pb-16 ">
                        <div className="w-40 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                        <div className="flex flex-col items-start gap-6">
                            <div className="flex items-center gap-4 ">
                                <div className="min-w-[48px] min-h-[48px] animate-pulse h-5 bg-gray-500 rounded-3xl" />

                                <div className="flex flex-col gap-1">
                                    <div className="w-40 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                                    <div className="w-40 animate-pulse h-5 bg-gray-500 rounded-3xl" />
                                </div>
                            </div>
                            <div className="flex items-start gap-3 max-md:flex-col-reverse max-md:gap-5 w-full">
                                <div className="flex flex-col gap-5 max-md:flex-row max-md:items-end px-[14px]">
                                    {[...Array(3)].map((_, i) => (
                                        <div key={i} className="p-2 border border-gray-500 rounded-full" />
                                    ))}
                                </div>
                                <div className="w-full animate-pulse h-32 bg-gray-500 rounded-3xl" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}