"use client"
import axios, { AxiosError } from 'axios'
import Head from 'next/head'
import { useMutation } from 'react-query'
import {
  PayPalScriptProvider,
  PayPalButtons,
  FUNDING,
} from '@paypal/react-paypal-js'

export default function Home() {
  const createMutation = useMutation<{ data: any }, AxiosError, any, Response>(
    (data): any => axios.post('http://localhost:3001/paypal/order/create2',data),
  )
  const captureMutation = useMutation<string, AxiosError, any, Response>(
    (data): any => axios.post('http://localhost:3001/paypal/order/capture2', data),
  )
  const createPayPalOrder = async (): Promise<string> => {
    const response = await createMutation.mutateAsync({ orderPrice: 23 })
    return response.data.orderID
  }

  const onApprove = async (data: OnApproveData): Promise<void> => {
    return captureMutation.mutate({ orderID: data.orderID })
  }
  return (
    <div >
      <Head>
        <title>Create Next App</title>
        <meta name="description" content="Generated by create next app" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main >
        {captureMutation.data && (
          <div>{JSON.stringify(captureMutation.data)}</div>
        )}
        <PayPalScriptProvider
          options={{
            clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID as string,
            currency: 'USD',
          }}
        >
          <PayPalButtons
            style={{
              color: 'gold',
              shape: 'rect',
              label: 'pay',
              height: 50,
            }}
            fundingSource={FUNDING.PAYPAL}
            createOrder={createPayPalOrder}
            onApprove={onApprove}
          />
        </PayPalScriptProvider>
      </main>

      <footer >
        <a
          href="https://vercel.com?utm_source=create-next-app&utm_medium=default-template&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          Powered by{' '}
          <span>
          </span>
        </a>
      </footer>
    </div>
  )
}

interface OnApproveData {
  billingToken?: string | null
  facilitatorAccessToken: string
  orderID: string
  payerID?: string | null
  paymentID?: string | null
  subscriptionID?: string | null
  authCode?: string | null
}
