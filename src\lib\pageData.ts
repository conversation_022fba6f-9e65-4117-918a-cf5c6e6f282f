import fs from 'fs/promises';
import path from 'path';

const DATA_DIR = path.join(process.cwd(), 'public', 'data', 'generated-pages');

export async function getPageData(slug: string) {
  const filePath = path.join(DATA_DIR, `${slug}.json`);
  
  try {
    const fileContent = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(fileContent);
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      return null;
    }
    throw error;
  }
}

export async function savePageData(slug: string, data: any) {
  try {
    await fs.mkdir(DATA_DIR, { recursive: true });

    const filePath = path.join(DATA_DIR, `${slug}.json`);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error saving page data:', error);
    throw error;
  }
}
