import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'
import { CourseModel } from '@/types/CourseModel'
import Hero from '@/components/Pages/Products/Hero'
import Courses from '@/components/Pages/Products/Courses'
import Mentorship from '@/components/Pages/Products/Mentorship'
import { MentorshipModel } from '@/types/MentorshipModel'

export const metadata = {
    title: 'Courses & Products',
    description:
        'Crypto University is the world’s #1 Cryptocurrency education platform. Our team of experts offers customized courses and coachings designed to guide you on becoming the next Crypto experts and millionaire',
    keywords: ['Courses', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const GetAllCourses = async () => {
    try {
        const response = await fetchInstance('/course/all/public', {
            next: { revalidate: 10 },
        })
        return response.courses
    } catch (error) {
        console.error('Error Courses:', error)
        return error
    }
}
const GetCalls = async () => {
    try {
        const response = await fetchInstance('/mentorship/all/published/coaching', {
            next: { revalidate: 10 },
        })
        return response.mentorships
    } catch (error) {
        console.error('Error Calls:', error)
        return error
    }
}
const GetConsultation = async () => {
    try {
        const response = await fetchInstance('/mentorship/all/published/not-coaching', {
            next: { revalidate: 10 },
        })
        return response.mentorships
    } catch (error) {
        console.error('Error Consultation:', error)
        return error
    }
}

const ProductsPage = async () => {
    const calls: MentorshipModel[] = await GetCalls()
    const courses: CourseModel[] = await GetAllCourses()
    const consultation: MentorshipModel[] = await GetConsultation()

    return (
        <section className="flex w-full flex-col text-black">
            <Hero />
            <Courses courses={courses} />
            <Mentorship calls={calls} consultation={consultation} />
            <CTA />
        </section>
    )
}

export default ProductsPage
