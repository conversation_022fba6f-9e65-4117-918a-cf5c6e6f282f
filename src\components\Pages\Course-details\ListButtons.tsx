'use client'
import Link from 'next/link'
import Current from '../Home/Current'
import { StringToID } from '@/lib/stringToId'
import { CourseModel } from '@/types/CourseModel'
import handleScroll from '@/config/ScrollSmooth'

interface Props {
    user: any
    slug: string
    course: CourseModel
    isEnrolled: boolean
}

const ListButtons = ({ user, slug, course, isEnrolled }: Props) => {
    const List = [
        { name: 'Syllabus', link: '#syllabus' },
        ...(course.key_points.length !== 0 ? [{ name: 'Key Point', link: '#keypoint' }] : []),
        { name: 'About', link: '#about' },
    ]

    return (
        <div className="container mx-auto pt-16 max-md:pt-9">
            <div className="no-scrollbar flex items-center justify-between overflow-hidden py-4 max-md:overflow-x-scroll max-md:py-2">
                <div className="flex items-center gap-3 ">
                    {List.map((item, index) => (
                        <Link
                            aria-label={item.name}
                            onClick={e => handleScroll(e)}
                            href={`courses/${slug}/` + StringToID(item.link)}
                            key={index}
                            className="w-full rounded-lg bg-gray-300 px-6 py-3 font-sans text-sub2 text-black hover:bg-gray-300/70 hover:text-black/70  max-md:px-4 max-md:py-2 max-md:text-sub3">
                            <p className="whitespace-nowrap">{item.name}</p>
                        </Link>
                    ))}
                </div>
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2 font-sans max-md:hidden">
                        {course.sale && course?.sale > 0 ? (
                            <p className=" text-cap1 text-gray-700">${course.priceWithoutTax}</p>
                        ) : (
                            ''
                        )}

                        <p className="text-callout font-semibold">
                            ${course.finalPrice - (parseInt(course.price ?? '0') - course.priceWithoutTax)}
                        </p>
                        {course.sale && course?.sale > 0 ? (
                            <p className="text-cap1 text-green-dark">(-{course.sale}%)</p>
                        ) : (
                            ''
                        )}
                    </div>
                    <div className="w-[240px] max-md:hidden">
                        <Current isEnrolled={isEnrolled} user={user} slug={slug} />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ListButtons
