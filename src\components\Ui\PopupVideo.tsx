import { Modal } from '@/lib/videopopup'
import mix from '@/config/videoLink'
import Iframe from 'react-iframe'
interface Props {
    isOpen: boolean
    closeModal: () => void
    link: string
}

const PopupVideo = ({ isOpen, closeModal, link }: Props) => {
    return (
        <Modal classNames={{
          modal: 'customModal',
        }} center open={isOpen} onClose={closeModal}>
            <Iframe
                url={mix(link)}
                width="100%"
                height="100%"
                id=""
                className="w-[640px] h-[320px] max-md:w-auto max-md:h-auto max-[320px]:w-[230px] "
                allowFullScreen
            />
        </Modal>
    )
}

export default PopupVideo
