'use client'
import { cn } from '@/lib/cn'
import { PortableTextBlock } from '@/lib/sanity'
import PortableText from '@/components/Ui/portable'
import { Disclosure, Transition } from '@/lib/headlessui'

type DisclosureProps = {
    category: {
        title: string
        questions: {
            title: string
            answer: string
        }[]
    }
    notmain?: boolean
}
// border-[#EFB77C]
const TextDisclosureDark = ({ category, notmain }: DisclosureProps) => {
    return (
        <div className="flex flex-col rounded-lg border border-[#222121] bg-[#222121]">
            {category.questions.map((question, index) => (
                <Disclosure key={index}>
                    {({ open }) => (
                        <div
                            key={index}
                            className={cn(
                                'min-w-[829px] max-w-[829px] px-6 py-5 max-md:min-w-full max-md:px-[10px] max-md:py-4',
                                index + 1 !== category.questions.length && 'border-b border-[#222121]',
                                notmain ? 'max-lg:min-w-full' : ''
                            )}>
                            <div
                                key={index}
                                className="flex w-full flex-col gap-4 bg-[#222121] transition-all duration-300">
                                <Disclosure.Button
                                    className={
                                        'relative flex w-full cursor-pointer select-none items-center justify-between gap-4 text-left text-sub3 max-md:text-sub4'
                                    }>
                                    <div
                                        className={cn(
                                            'max-w-[700px] font-medium text-[#EFB77C] transition-[color] duration-300',
                                            open && 'text-[#b1814d]'
                                        )}>
                                        {question.title}
                                    </div>
                                    <span
                                        className={cn(
                                            'flex min-h-[24px] min-w-[24px] flex-col items-center justify-center rounded-full text-[#b1814d] transition-[transform_background] duration-300 ',
                                            open ? 'bg-[#b1814d]' : 'rotate-180 bg-[#EFB77C]'
                                        )}>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="10"
                                            height="7"
                                            fill="#6E4C31"
                                            viewBox="0 0 10 7">
                                            <path
                                                stroke="#000"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth="1.5"
                                                d="M9.242 5.5l-4.12-4.083L1 5.5"
                                            />
                                        </svg>
                                    </span>
                                </Disclosure.Button>
                                <Transition
                                    show={open}
                                    enter="transition duration-100 ease-out"
                                    enterFrom="transform scale-95 opacity-0"
                                    enterTo="transform scale-100 opacity-100"
                                    leave="transition duration-75 ease-out"
                                    leaveFrom="transform scale-100 opacity-100"
                                    leaveTo="transform scale-95 opacity-0">
                                    <div className="font-sans text-sub3 text-gray-500 transition-[max-height_opacity] duration-300 max-md:text-sub4">
                                    <div dangerouslySetInnerHTML={{ __html: question.answer }} />
                                    </div>
                                </Transition>
                            </div>
                        </div>
                    )}
                </Disclosure>
            ))}
        </div>
    )
}
export default TextDisclosureDark
