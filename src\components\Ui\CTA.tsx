'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import Button from '@/components/Ui/Button'
import { useState, useEffect } from 'react'

interface props {
    count?: number
    border?: boolean
}

const CTA = ({ count, border = false }: props) => {
    const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes
    const [urgencyLevel, setUrgencyLevel] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    clearInterval(timer);
                    return 0;
                }

                // Increase urgency as time runs out
                if (prev <= 300) setUrgencyLevel(2); // Last 5 minutes
                else if (prev <= 900) setUrgencyLevel(1); // Last 15 minutes

                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    const getUrgencyColor = () => {
        if (urgencyLevel >= 2) return 'from-red-600 to-red-800';
        if (urgencyLevel >= 1) return 'from-orange-600 to-orange-800';
        return 'from-red-600 to-red-700';
    };

    return (
        <section
            id="cta"
            className={cn(
                'w-full py-20 bg-gradient-to-br from-black via-gray-900 to-black text-white relative overflow-hidden',
                border && 'border-t border-gray-700'
            )}>

            {/* Animated Background */}
            <div className="absolute inset-0">
                <div className="absolute top-10 left-10 w-32 h-32 bg-primary/10 rounded-full animate-pulse"></div>
                <div className="absolute bottom-10 right-10 w-40 h-40 bg-green-400/10 rounded-full animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-red-600/10 rounded-full animate-pulse delay-500"></div>
            </div>

            <div className="container mx-auto relative z-10 px-4">
                <div className="max-w-6xl mx-auto">
                    {/* Countdown Timer */}
                    <div className={`bg-gradient-to-r ${getUrgencyColor()} text-white text-center py-4 rounded-2xl mb-8 border-4 border-yellow-400 animate-pulse`}>
                        <div className="flex items-center justify-center gap-2 font-black text-2xl">
                            <span className="animate-pulse">⚡</span>
                            <span>LIMITED TIME: {formatTime(timeLeft)} LEFT FOR 67% OFF!</span>
                            <span className="animate-pulse">⚡</span>
                        </div>
                    </div>

                    <div className="text-center mb-12">
                        <h1 className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight">
                            🚨 <span className="text-red-400">FINAL WARNING:</span> Don&apos;t Miss The <span className="text-primary">CRYPTO BULL RUN!</span>
                        </h1>

                        <div className="bg-green-600 text-white p-8 rounded-3xl mb-8 border-4 border-yellow-400 max-w-4xl mx-auto">
                            <p className="text-2xl md:text-3xl font-bold leading-relaxed">
                                While you&apos;re reading this, <span className="text-yellow-400">Sarah just made $12,847</span> and <span className="text-yellow-400">Mike earned $8,932</span> using our system. <span className="text-red-300">How much longer will you wait?</span>
                            </p>
                        </div>

                        {/* Scarcity Indicators */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
                            <div className="bg-red-600 p-4 rounded-2xl border-2 border-yellow-400">
                                <div className="text-3xl font-black text-yellow-400 animate-pulse">23</div>
                                <div className="text-sm font-bold">Spots Left Today</div>
                            </div>
                            <div className="bg-purple-600 p-4 rounded-2xl border-2 border-yellow-400">
                                <div className="text-3xl font-black text-yellow-400">147</div>
                                <div className="text-sm font-bold">People Viewing</div>
                            </div>
                            <div className="bg-green-600 p-4 rounded-2xl border-2 border-yellow-400">
                                <div className="text-3xl font-black text-yellow-400">$47K</div>
                                <div className="text-sm font-bold">Made This Hour</div>
                            </div>
                            <div className="bg-blue-600 p-4 rounded-2xl border-2 border-yellow-400">
                                <div className="text-3xl font-black text-yellow-400 animate-pulse">LIVE</div>
                                <div className="text-sm font-bold">Profit Alerts</div>
                            </div>
                        </div>
                    </div>

                    {/* Massive CTA Section */}
                    <div className="bg-gradient-to-r from-red-600 via-red-700 to-red-800 p-12 rounded-3xl border-4 border-yellow-400 text-center relative overflow-hidden">
                        {/* Animated elements */}
                        <div className="absolute top-4 left-4 animate-bounce">
                            <div className="text-4xl">💰</div>
                        </div>
                        <div className="absolute top-4 right-4 animate-bounce delay-500">
                            <div className="text-4xl">🚀</div>
                        </div>
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-pulse">
                            <div className="text-4xl">⚡</div>
                        </div>

                        <div className="relative z-10">
                            <h2 className="text-3xl md:text-4xl font-black text-white mb-6">
                                🔥 LAST CHANCE: JOIN 47,832+ CRYPTO MILLIONAIRES!
                            </h2>

                            <div className="bg-yellow-400 text-black p-6 rounded-2xl mb-8 inline-block">
                                <div className="text-2xl font-black">GET $3,276 WORTH OF TRAINING FOR JUST $97!</div>
                                <div className="text-lg font-bold">That&apos;s 97% OFF - But Only For The Next {formatTime(timeLeft)}!</div>
                            </div>

                            <p className="text-2xl text-yellow-100 mb-8 font-bold max-w-4xl mx-auto">
                                Stop being a spectator while others get rich! Our students are making <span className="text-yellow-400">$10K+/month</span> with crypto. Your financial freedom is just one click away!
                            </p>

                            {/* Dual CTA Buttons */}
                            <div className="flex flex-col lg:flex-row gap-6 justify-center items-center">
                                <Link href="/register" className="w-full lg:w-auto">
                                    <Button
                                        variant="primary"
                                        className="w-full lg:w-auto px-12 py-6 text-2xl font-black bg-green-600 hover:bg-green-700 hover:scale-110 transform transition-all shadow-2xl rounded-2xl"
                                    >
                                        🚀 YES! MAKE ME RICH WITH CRYPTO!
                                    </Button>
                                </Link>

                                <div className="text-white font-bold text-xl">OR</div>

                                <Link href="/courses" className="w-full lg:w-auto">
                                    <Button
                                        variant="secondary"
                                        className="w-full lg:w-auto px-12 py-6 text-2xl font-black bg-yellow-400 hover:bg-yellow-500 text-black hover:scale-110 transform transition-all shadow-2xl rounded-2xl"
                                    >
                                        💎 GET FREE $2,847 BLUEPRINT
                                    </Button>
                                </Link>
                            </div>

                            {/* Final Push */}
                            <div className="mt-8">
                                <div className="bg-black/50 backdrop-blur-sm text-white p-6 rounded-2xl border-2 border-yellow-400">
                                    <div className="text-xl font-bold mb-2">🔒 100% RISK-FREE GUARANTEE</div>
                                    <div className="text-lg">
                                        Not making profits within 30 days? Get your money back. No questions asked.
                                    </div>
                                </div>
                            </div>

                            {/* Urgency Footer */}
                            <div className="mt-6 text-yellow-200 font-bold text-lg animate-pulse">
                                ⚠️ This offer expires when the timer hits 00:00 - Don&apos;t miss out!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default CTA
