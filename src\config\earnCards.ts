import {
  CU,
  Nexo,
  Bybit,
  Brave,
  Pa<PERSON>ful,
  Crypto,
  Blockfi,
  Celcius,
  Binance,
  Coinbase,
  Compound,
  SuperRare,
} from "@public/earn";

export const EarnList = [
  {
    img: Binance,
    title: "Binance",
    description: "Buy, trade, and hold 600+ cryptocurrencies on Binance",
    background: "binance rounded-[12px] p-7 flex flex-col gap-10",
    button: "Upto $50 free on sign up >",
    link: "https://bit.ly/3JVH7Zq",
  },
  {
    img: Coinbase,
    title: "Coinbase",
    description: "Buy and Sell Bitcoin, Ethereum, and more with trust.",
    background: "coinbase",
    button: "Free $10 after sign up >",
    link: "https://bit.ly/3a5oMZr",
  },
  {
    img: Bybit,
    title: "ByBit",
    description: "Cryptocurrency Trading Platform.",
    background: "bybit",
    button: "Free $10 after sign up >",
    link: "https://bit.ly/3y2wOgg",
  },
  {
    img: <PERSON><PERSON><PERSON>,
    title: "<PERSON><PERSON><PERSON>",
    description: "1.7 million people call <PERSON><PERSON><PERSON> their home for crypto.",
    background: "celcius",
    button: "Earn $50 on sign up >",
    link: "https://bit.ly/3sllv2v",
  },
  {
    img: Crypto,
    title: "Crypto.com",
    description: "The Best Place to Buy, Sell, and Pay with Crypto.",
    background: "crypto",
    button: "Earn up to 18% on your assets >",
    link: "https://bit.ly/3rmLgPc",
  },
  {
    img: Brave,
    title: "Brave Browser",
    description: "Secure, Fast & Private Web Browser with Crypto.",
    background: "brave",
    button: "Earn by Browsing >",
    link: "https://brave.com/?ref=fbo424/",
  },
  {
    img: SuperRare,
    title: "SuperRare",
    description: "Make money selling art.",
    background: "superrare",
    button: "",
    link: "https://superrare.co/artwork/jobsworth-2029-928",
  },
  {
    img: Nexo,
    title: "Nexo",
    description: "$50 free & Earn 10% Compounding.",
    background: "nexo",
    button: "",
    link: "https://bit.ly/3a5oMZr",
  },
  {
    img: Blockfi,
    title: "BlockFi",
    description: "Earn up to 8.6 APY.",
    background: "blockfi",
    button: "",
    link: "https://blockfi.mxuy67.net/hardcorecrypto",
  },
  {
    img: CU,
    title: "Crypto University Affiliates",
    description: "Earn up to $1000 per referral.",
    background: "cu",
    button: "",
    link: "https://cryptouniversity.network/affiliates/",
  },
  {
    img: Paxful,
    title: "Paxful",
    description: "Start a Bitcoin Hustle.",
    background: "paxful",
    button: "",
    link: "https://paxful.com/?r=zGMkybbRwQW",
  },
  {
    img: Binance,
    title: "Binance",
    description: "$20,000 in Bitcoin to be Won in Binance Video Competition.",
    background: "binance2",
    button: "",
    link: "https://www.binance.com/en/blog/all/$20000-in-bitcoin-to-be-won-in-binance-futures-1st-anniversary-video-competition-421499824684900891",
  },
  {
    img: Compound,
    title: "Compound",
    description: "Decentralized Lending.",
    background: "compound",
    button: "",
    link: "https://compound.finance/",
  },
];
