'use client'
import Link from 'next/link'
import fetch from '@/lib/fetch'
import { useState } from 'react'
import Button from '@/components/Ui/Button'
import { useRouter } from 'next/navigation'
import { usePathname } from 'next/navigation'
import { CourseModel } from '@/types/CourseModel'
import useFinishTopic from '@/hooks/useFinishTopic'
import { isLastLesson, isLastTopic, nextTopic } from '@/config/nextAndPreviousFunc'
import ButtonSpinner from '@/components/Ui/buttonSpinner'

interface Props {
    slug: string
    TopicSlug?: any
    user?: any
    course?: CourseModel | undefined
}

const Buttons = ({ slug, TopicSlug, user, course }: Props) => {
    const router = useRouter()
    const currentRoute = usePathname()
    const [isLoading, setIsLoading] = useState(false)
    const { addFinishLess, lessExists } = useFinishTopic()
    const isDone = async () => {
        setIsLoading(true)
        try {
            await fetch('/topic/finish/topic/' + TopicSlug.id, {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            addFinishLess({ id: TopicSlug.id, finish: true })
            setIsLoading(false)

            if (isLastLesson({ course, currentRoute }) && isLastTopic({ course, currentRoute })) {
                router.push(currentRoute)
            } else {
                const nextTopicRoute = nextTopic({ course, currentRoute })
                router.push(nextTopicRoute)
            }
        } catch (error) {
            setIsLoading(false)
            console.error(error)
        }
    }

    return (
        <div className="flex w-full justify-between">
            <div className="flex items-start gap-3">
                <Link
                    aria-label="Overview"
                    href={slug + '#overview'}
                    className="w-full rounded-lg bg-blue px-6 py-3 font-sans text-sub2 text-white hover:bg-blue/70 hover:text-white/70  max-md:px-4 max-md:py-2 max-md:text-sub3">
                    <p className="whitespace-nowrap">Overview</p>
                </Link>
                {TopicSlug?.material && (
                    <Link
                        aria-label="Course Materials"
                        href={TopicSlug.material}
                        target="_blank"
                        className="w-full rounded-lg bg-gray-300 px-6 py-3 font-sans text-sub2 text-black hover:bg-gray-300/70 hover:text-black/70  max-md:px-4 max-md:py-2 max-md:text-sub3">
                        <p className="whitespace-nowrap">Course Materials</p>
                    </Link>
                )}
            </div>
            {TopicSlug && (
                <div>
                    <Button
                        disabled={TopicSlug.isCompleted || lessExists({ id: TopicSlug.id, finish: true }) || isLoading}
                        onClick={isDone}
                        className="!py-3"
                        variant="primary">
                        {isLoading && <ButtonSpinner />} Complete This Video
                    </Button>
                </div>
            )}
        </div>
    )
}

export default Buttons
