import OrderTemplate from '@/components/Pages/Dashboard/Settings/OrderTemplate'
import { authOptions } from '@/lib/auth'
import fetchInstance from '@/lib/fetch'
import { getCurrentUser } from '@/lib/session'
import TransactionModel from '@/types/TransactionModel'
import { redirect } from 'next/navigation'

interface Model {
    transactions: TransactionModel[]
    tax: number
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

const OrderHistory = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const { pageNumber } = searchParams
    const GetTransaction = async () => {
        try {
            const pageNumber1 = typeof pageNumber === 'string' && pageNumber !== undefined ? pageNumber : '1'
            const response = await fetchInstance('/auth/profile/transactions?pageSize=10&pageNumber=' + pageNumber1, {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response
        } catch (error) {
            console.error('Error Users: ', error)
            return error
        }
    }

    const transactions: Model = await GetTransaction()
    return (
        <div className="container mx-auto py-12">
            <div className="grid grid-cols-12 gap-5">
                <div className="max-md:hidden col-span-12 grid grid-cols-12 place-items-start place-content-center rounded-xl bg-gray-300 px-6 py-4 text-cap2 font-semibold text-gray-700">
                    <p className="col-span-3">PRODUCT </p>
                    <p className="col-span-2">ORDER ID</p>
                    <p className="col-span-2">DATE</p>
                    <p className="col-span-2">STATUS</p>
                    <p className="col-span-2">TOTAL AMOUNT</p>
                    <p className="col-span-1">ACTIONS</p>
                </div>
                {transactions.transactions.map((transaction, index) => (
                    <div key={index} className="col-span-12">
                        <OrderTemplate tax={transactions.tax} transaction={transaction} />
                    </div>
                ))}
            </div>
        </div>
    )
}

export default OrderHistory
