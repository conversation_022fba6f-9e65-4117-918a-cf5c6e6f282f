"use client"
import Link from 'next/link'
import <PERSON><PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import HeroImage from '@public/affiliate/hero.png'
import { signIn, signOut, useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import DiscordSuccessModel from '@/components/Pages/Checkout/DiscordSuccessModel'


const DiscordLoginPage = () => {
  const { data: session } = useSession()
  const [isDiscordUser, setIsDiscordUser] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (session && session.user.provider === 'discord') {
        try {
          const response = await fetch(`${process.env.API_URL}/discord/add-user`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: session.user.id,
            }),
          });

          if (response.ok) {
            setIsDiscordUser(true)
            // Redirect to the home page on success
            // router.push('/');
          } else {
            // Handle other responses or errors if needed
            console.error('Error fetching data:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      }
    };

    fetchData();
  }, [session]);
  return (
    <section className="flex w-full flex-col">


      <section id='hero' className="w-full bg-yellow-light text-black">
        <div className="- container mx-auto flex flex-wrap max-md:flex-col-reverse max-md:items-center justify-between">
          <div className="flex flex-col items-start gap-9 max-md:gap-7 pb-40 max-md:pb-20 pt-24 max-md:pt-0 ">
            <div className="flex flex-col items-start justify-center gap-4 max-md:gap-2  max-md:items-center  max-md:pt-0">
              <h1 className="text-h3 font-semibold max-md:text-center max-md:text-b1 max-md:font-medium">
                Please link your discord account <br className="max-md:hidden" />
                to access our Discord
              </h1>
              <p className="max-w-[575px] text-sub3 max-md:px-4 max-md:text-center max-md:text-cap2 max-md:leading-[18px]">
                Welcome to our Crypto University Discord channel! Join the No.1 crypto community and start your crypto journey with us. As a member, you will have access to valuable insights, discussions, and opportunities. Feel free to explore and engage with fellow crypto enthusiasts. If you have any questions, our community is here to help you navigate the exciting world of cryptocurrency. Lets grow together!
              </p>

            </div>
            <div className=" w-full max-w-[300px] max-md:max-w-full">
              {!isDiscordUser ? (
                <button
                  className="flex items-center bg-white border border-gray-300 rounded-lg shadow-md px-6 py-2 text-sm font-medium text-gray-800 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">

                  <svg className="h-6 w-6 mr-2" xmlns="http://www.w3.org/2000/svg"
                    width="800px" height="800px" viewBox="0 -28.5 256 256" version="1.1" preserveAspectRatio="xMidYMid">
                    <g>
                      <path
                        d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z"
                        fill="#5865F2" fill-rule="nonzero">

                      </path>
                    </g>
                  </svg>

                  <button onClick={() => signIn('discord')}>Join Web3 Dev Channel</button>
                </button>) : (<DiscordSuccessModel success={true} type='course' />)}
            </div>
          </div>
          <ImageShortcut
            src={HeroImage}
            alt="Hero Image"
            priority
            className="object-contain max-md:h-auto max-md:w-[191px]"
          />
        </div>
      </section>

      <section id='question' className="container mx-auto pb-[20px] max-md:py-9">
        <div className="flex- flex items-center gap-10 max-md:flex-col max-md:items-start max-md:gap-0">
          <ImageShortcut
            src={'/join/contact.png'}
            width={280}
            height={280}
            className={'max-md:hidden max-md:h-auto max-md:w-[190px]'}
            alt={'Contact Icon'}
          />
          <div className="flex flex-col gap-8 max-md:gap-4">
            <div className="flex flex-col gap-2 max-md:gap-[0.375rem]">
              <p className="text-headline font-semibold max-md:text-b3">Have Any Questions?</p>
            </div>
            <div className="flex flex-col gap-5">
              <div className="w-[310px] max-md:w-[220px]">
                <Button gradient variant="primary" rounded>
                  <Link
                    href={'mailto:<EMAIL>'}
                    aria-label="Email crypto university">
                    Help {'>'}
                  </Link>
                </Button>
              </div>
              <div className="flex flex-wrap gap-1 text-callout max-md:flex-col max-md:gap-2 max-md:text-sub3">
                <p>OR Shoot us an email on:</p>{' '}
                <span className="text-blue">
                  {' '}
                  <Link
                    aria-label="Email crypto university"
                    href={'mailto:<EMAIL>'}>
                    <EMAIL>
                  </Link>
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  )
}

export default DiscordLoginPage
