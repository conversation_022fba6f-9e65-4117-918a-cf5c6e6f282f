const Arrow = () => (
    <svg width="24" height="28" viewBox="0 0 24 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M7 21L17 11M17 11H7M17 11V21"
            stroke="#c4c4c499"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const CardSkeleton = () => {
    return (
        <div className="animate-pulse space-y-8 max-md:space-y-6">
            <div className="h-[240px] w-full bg-gray-500/60 max-md:h-[228px]" />

            <div className="space-y-6">
                <div className="space-y-3 max-md:space-y-2">
                    <div className="h-[7px] w-[100px] bg-gray-500/60" />

                    <div className="flex justify-between gap-4">
                        <span className="h-[10px] w-[140px] bg-gray-500/60" />
                        <div>
                            <Arrow />
                        </div>
                    </div>

                    <div className="space-y-2">
                        {Array.from({ length: 3 }).map((_, index) => (
                            <div
                                key={index}
                                className="h-[20px] w-full max-w-[100%] overflow-hidden bg-gray-500/60 text-sub3 text-[#667085]"
                            />
                        ))}
                    </div>
                </div>

                <div className="flex gap-2">
                    <div className="select-none rounded-full bg-gray-300 px-3 py-1 transition-colors duration-150">
                        <div className="h-[12px] w-[50px] rounded-xl bg-gray-500/60" />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CardSkeleton
