import { authOptions } from '@/lib/auth'
import fetchInstance from '@/lib/fetch'
import { getCurrentUser } from '@/lib/session'
import TransactionModel from '@/types/TransactionModel'
import { redirect } from 'next/navigation'
import Payouts from '@/components/Pages/Dashboard/Affiliate/Payouts'

interface Model {
    amount: number,
    created_at : string,
}

const PayoutsPage = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const GetPayouts = async () => {
        try {
            const response:any = await fetchInstance('/withdrwned-earnings', {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response.withdrawned_earnings
        } catch (error) {
            console.error('Error Users: ', error)
            return error
        }
    }

    const payouts: Model[] = await GetPayouts()
    return (
        <div className="container mx-auto py-12">
            <div className="grid grid-cols-12 gap-5">
                <div className="max-md:hidden col-span-12 grid grid-cols-12 place-items-start place-content-center rounded-xl bg-gray-300 px-6 py-4 text-cap2 font-semibold text-gray-700">
                    <p className="col-span-3">DATE </p>
                    <p className="col-span-3">AMOUNT</p>
                    <p className="col-span-2">PAYMENT METHOD</p>
                    <p className="col-span-2">STATUS</p>
                </div>
                {payouts.map((payout, index) => (
                    <div key={index} className="col-span-12">
                        <Payouts payout={payout} />
                    </div>
                ))}
            </div>
        </div>
    )
}

export default PayoutsPage
