import SubscriptionCard from './Card'
import { SubscriptionModel } from '@/types/SubscriptionModel'

interface SubscriptiosProps {
    subscriptions: SubscriptionModel[],
}
const Subscriptions = ({ subscriptions }: SubscriptiosProps) => {
    const links = [
        "nitros-bull",
        "visual-assistance",
        "ema-trading",
        "trend-momentum"
    ];

    return (
        <section className="container mx-auto" id="courses">
            <div className="space-y-8 pb-16 pt-11 max-md:space-y-3 max-md:border-b max-md:border-gray-700 max-md:pb-14 max-md:pt-9">
                <h3 className="text-headline font-medium max-md:text-callout">Membership Subscriptions</h3>

                <div className="grid grid-cols-3 gap-9 max-lg:grid-cols-2 max-md:grid-cols-1 max-md:gap-[1.125rem]">
                    {subscriptions != undefined ? (
                        subscriptions.map((subscription, index) => (
                            <SubscriptionCard
                                key={subscription.id}
                                subscription={subscription}
                                link={links[index % links.length]} // Assuming the links array corresponds to the indicators
                            />
                        ))
                    ) : (
                        <p>No Subscriptions available</p>
                    )}
                </div>
            </div>
        </section>
    )
}

export default Subscriptions
