"use client"

import Provider from '@/components/provider';
import { QueryClient, QueryClientProvider } from 'react-query'
import Previous from "@/components/Pages/Checkout/Previous"
interface ChckoutLayoutProps {
  children: React.ReactNode
}
const queryClient = new QueryClient()
export default async function CheckoutLayout({ children }: ChckoutLayoutProps) {
  return (
    <>
      <main className="container mx-auto pt-14 ">
        <QueryClientProvider client={queryClient}>
          <Provider>
            <Previous />
            {children}
          </Provider>
        </QueryClientProvider>
      </main>
    </>
  )
}