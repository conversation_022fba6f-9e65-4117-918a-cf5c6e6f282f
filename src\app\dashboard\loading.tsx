import React from 'react'

const loading = () => {
    const filters = [
        {
            name: 'All Course',
            filter: 'all',
            href: '/dashboard?filter=',
            pageNumbers: 1,
        },
        {
            name: 'In Progress',
            href: '/dashboard?filter=',
            filter: 'inProgress',
            pageNumbers: 1,
        },
    ]
    return (
        <section className="w-full text-black flex flex-col gap-14">
            <div className="w-full px-8 py-[1.6875rem] border-b border-gray-500 flex justify-between max-md:flex-col max-md:gap-4 max-md:px-5">
                <div className="font-manrope flex flex-col gap-2 max-md:flex-row max-md:justify-between">
                    <div className="w-40 h-5 bg-gray-500 animate-pulse  rounded-3xl" />
                    <div className="w-40 h-5 bg-gray-500 animate-pulse  rounded-3xl" />
                </div>
                <div className="flex space-x-4">
                    {filters.map((item, index) => (
                        <div key={index} className={'px-6 py-[0.844rem] rounded-lg '}>
                            {item.name}
                        </div>
                    ))}
                </div>
            </div>
            <div className="w-full flex gap-8 px-8 max-md:px-5 items-start flex-wrap">
                {[...Array(4)].map((_, i) => (
                    <div
                        key={i}
                        className={`flex flex-col gap-6 border h-[300px] transition-all overflow-hidden border-gray-500 rounded-lg cursor-pointer w-[260px] max-w-[260px] min-w-[260px] max-md:w-full max-md:max-w-[100%] max-md:min-w-full`}>
                        <div className={`flex flex-col gap-3 `}>
                            <div className="w-full h-[135px] max-md:h-[154px] bg-gray-500 animate-pulse rounded-lg" />
                        </div>
                        <div className="flex flex-col px-3 gap-3">
                            <div className="w-40 h-8 bg-gray-500 animate-pulse  rounded-3xl" />
                            <div className="w-full h-3 bg-gray-500 animate-pulse rounded-3xl" />
                        </div>
                        <div className="flex justify-between items-center px-2">
                            <div className="w-16 h-3 bg-gray-500 animate-pulse rounded-3xl" />
                            <div className="w-16 h-3 bg-gray-500 animate-pulse rounded-3xl" />
                        </div>
                    </div>
                ))}
            </div>
        </section>
    )
}

export default loading
