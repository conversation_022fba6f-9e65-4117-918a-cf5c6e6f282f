import Link from 'next/link'
import Image from '@/components/Ui/Image'
import Button from '@/components/Ui/Button'
import { CourseModel } from '@/types/CourseModel'
import { Certificate, CourseVideo } from '@public/home'
import WishlistButton from '@/components/Ui/WishlistButton'

interface CourseCardProps {
    course: CourseModel
}

const CourseCard = ({ course }: CourseCardProps) => {
    return (
        <div className="flex max-w-[400px] flex-grow flex-col justify-between space-y-6 rounded-[0.5rem] border border-gray-700 p-4 max-md:max-w-full max-md:space-y-3 max-md:p-[0.875rem]">
            <div>
                <Image
                    src={course.image}
                    width={207}
                    height={240}
                    className="h-[240px] w-auto max-md:h-[154px]"
                    alt={course.name}
                />
            </div>

            <div className="flex flex-1 flex-col gap-4">
                {/* Content */}
                <div className="flex flex-grow flex-col justify-between gap-4 max-md:gap-2">
                    <div className="flex flex-col gap-4 max-md:gap-2">
                        <p className="text-b3 font-medium capitalize max-md:text-sub2">{course.name}</p>
                        <p className="text-cap text-sub3 max-md:text-cap1">{course.short_description}</p>
                    </div>
                    <div className="flex justify-between font-sans">
                        {/* Videos */}
                        <div className="flex items-center gap-3 max-md:gap-[0.5625rem]">
                            <div className="flex items-center gap-1">
                                <CourseVideo />
                                <span className="text-sub3 leading-none text-gray-900 max-md:text-cap1">
                                    {course.nbOfVideos} videos
                                </span>
                            </div>
                            {course.rewardable && <Certificate />}
                        </div>
                        {/* Wishlist */}
                        <WishlistButton item={course} />
                    </div>
                </div>
                {/* Price */}
                <div className="flex items-center justify-between">
                    <div className="flex cursor-default flex-col gap-[0.125rem] font-sans max-md:flex-row-reverse max-md:items-center max-md:gap-1">
                        {course.sale ? (
                            <>
                                <h5 className="flex items-center gap-1 text-callout font-medium max-md:text-sub2">
                                    ${course.finalPrice - course.tax}{' '}
                                    <span className="text-cap1 text-green-dark max-md:text-cap3">
                                        (-{course.sale}%)
                                    </span>
                                </h5>
                                <p className="text-sub2 text-gray-900 line-through max-md:text-sub3">
                                    ${parseInt(course.price || '0') - course.tax}
                                </p>
                            </>
                        ) : (
                            <span className="text-callout font-medium max-md:text-sub2">
                                ${course.finalPrice - course.tax}
                            </span>
                        )}
                    </div>

                    <Link
                        aria-label="Course page"
                        href={'/courses/' + course.slug}
                        className="w-[200px] max-md:w-[188px]">
                        <Button variant="primary" rounded>
                            Start Now &gt;
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default CourseCard
