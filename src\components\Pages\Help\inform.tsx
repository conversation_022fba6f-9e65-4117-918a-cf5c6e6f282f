"use client";
import { cn } from "@/lib/cn";
import fetch from "@/lib/fetch";
import { useRef, useState } from "react";
import Label from "@/components/Ui/Label";
import CustomSelect from "../GetInTouch/CustomSelect";
import Button from "@/components/Ui/Button";
import { FormikErrors } from "@/lib/formik";
import { Country } from "@/types/CountryModel";
import ImageShortcut from "@/components/Ui/Image";
import { Email } from "../../../../public/global";
import { ProfileBlack } from "../../../../public/home";
import ButtonSpinner from "@/components/Ui/buttonSpinner";
import { FormikProps, Yup, useFormik } from "@/lib/formik";

interface TextBoxProps {
  setEmail?: any;
  placeholder: string;
  error?:
    | string
    | undefined
    | string[]
    | FormikErrors<any>
    | FormikErrors<any>[];
  label: string;
  className?: string;
  Icon?: React.ReactNode;
  CodeSelect?: React.ReactNode;
  formik: FormikProps<any>;
  id: ValidationSchemaKeys;
  value: any;
  onChange: any;
  innerRef: React.RefObject<HTMLInputElement>;
  type?: string;
  isSelectFocus?: boolean;
}
type ValidationSchemaKeys =
  | "full_name"
  | "phone_number"
  | "country_code"
  | "expected_number_of_learners"
  | "email";

const showError = (
  ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>,
  key: ValidationSchemaKeys,
  formik: FormikProps<any>
) => {
  if (ref.current) {
    ref.current.onfocus = () => {
      formik.setFieldTouched(key, false);
    };
  }
  return (
    formik.touched[key] &&
    (formik.errors as { [key: string]: string })[key]?.length > 0
  );
};
const TextBox = ({
  placeholder,
  error,
  label,
  className,
  Icon,
  CodeSelect,
  id,
  value,
  innerRef,
  formik,
  type,
}: TextBoxProps) => {
  const [focus, setFocus] = useState(false);

  return (
    <div className={cn("flex flex-col gap-3 w-full h-fit", className)}>
      <Label className="" required uppercase={false}>
        {label}
      </Label>

      <div className="flex flex-col gap-2 max-md:w-full">
        <div
          onBlur={() => {
            setFocus(false);
          }}
          onClick={() => setFocus(true)}
          className={cn(
            "flex relative select-none items-center flex-row flex-1 border border-gray-700 rounded-[50px] ",
            focus && "border-blue",
            (id != "phone_number"
              ? showError(innerRef, id, formik)
              : showError(innerRef, "country_code", formik) ||
                showError(innerRef, id, formik)) && "border-red focus:ring-red"
          )}
        >
          {Icon ? <div className="absolute left-4">{Icon}</div> : ""}
          {CodeSelect ? (
            <div
              className={cn(
                "w-[120px]  rounded-full rounded-r-none border-r-0 flex items-center",
                (showError(innerRef, "country_code", formik) ||
                  (showError(innerRef, id, formik) && !focus)) &&
                  "border-red focus:ring-red"
              )}
            >
              {CodeSelect}
              <div
                className={cn(
                  "border-l h-full py-1.5 mx-1  border-gray-700 z-30",
                  (showError(innerRef, "country_code", formik) ||
                    (showError(innerRef, id, formik) && !focus)) &&
                    "border-red",
                  focus && "border-blue border-l"
                )}
              ></div>
            </div>
          ) : (
            ""
          )}
          <input
            autoComplete="new-password"
            ref={innerRef}
            type={
              id == "email"
                ? "email"
                : id == "phone_number"
                ? "number"
                : type ?? "text"
            }
            name={id}
            id={id}
            value={value}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            className={cn(
              "border border-gray-700 rounded-full px-4 pr-2 py-[17.5px]  w-full focus:outline-none ring-0 border-transparent max-md:text-cap1 max-md:py-3.5",

              Icon && "pl-12",
              CodeSelect &&
                "border-l-0 rounded-l-none !h-[62px] grow max-w-full min-w-0"
            )}
            placeholder={placeholder}
          />
        </div>
        {showError(innerRef, id, formik) && (
          <div className="bg-red/20 rounded-lg p-3">
            <p className="text-red text-cap1">{error?.toString()}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export const InfoForm = ({ countries }: { countries: Country[] }) => {
  const [isLoading, setIsLoading] = useState(false);
  const codes = countries.map((country: Country) => ({
    value: country.idd.root + country.idd.suffixes,
    label: (
      <span className="flex gap-2 max-md:gap0.5">
        <ImageShortcut
          src={country.flags.svg}
          height={24}
          alt={country.name.common}
          width={24}
          className="object-contain"
        />
        {country.idd.suffixes?.length == 1
          ? country.idd.root + country.idd.suffixes
          : country.idd.root}
      </span>
    ),
  }));

  const formik = useFormik({
    initialValues: {
      full_name: "",
      email: "",
      phone_number: "",
      country_code: codes[34].value,
      expected_number_of_learners: "",
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      full_name: Yup.string().required("Full name is required"),
      phone_number: Yup.string().required("Phone number is required"),
      country_code: Yup.string().required("Phone number code is required"),
      expected_number_of_learners: Yup.string().required("Issue is required"),
    }),
    onSubmit: async (values: any) => {
      setIsLoading(true);
      const data = await fetch("/mail/get-in-touch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });
      if (data.success == true) {
        setIsLoading(false);
        alert("Your form has been submitted successfully");
        formik.setValues({
          full_name: "",
          email: "",
          phone_number: "",
          country_code: codes[34].value,
          expected_number_of_learners: "",
        });
        formik.setTouched({
          full_name: false,
          email: false,
          phone_number: false,
          country_code: false,
          expected_number_of_learners: false,
        });
      } else {
        setIsLoading(false);
        alert("Your form has not been submitted successfully");
      }
    },
    initialErrors: {
      full_name: "",
      email: "",
      phone_number: "",
      country_code: "",
      expected_number_of_learners: "",
    },
  });

  const [isFocus, setIsFocus] = useState(false);
  const nameRef = useRef<HTMLInputElement>(null);
  const emailRef = useRef<HTMLInputElement>(null);
  const learnersRef = useRef<HTMLInputElement>(null);
  const phoneSuffixRef = useRef<HTMLInputElement>(null);

  return (
    <form
      className="grid grid-cols-2 place-content-start gap-6 font-sans w-full h-fit max-lg:flex max-lg:flex-col max-w-[1200px]"
      onSubmit={formik.handleSubmit}
    >
      <h1 className="font-semibold font-manrope text-b3 self-end col-span-2 text-start w-full">
        Fill this Information
      </h1>
      <TextBox
        error={formik.errors.full_name}
        innerRef={nameRef}
        formik={formik}
        value={formik.values.full_name}
        onChange={formik.handleChange}
        id="full_name"
        placeholder="e.g John Smith"
        label="Enter Full Name"
        Icon={<ProfileBlack />}
      />
      <TextBox
        error={formik.errors.email}
        innerRef={emailRef}
        formik={formik}
        value={formik.values.email}
        onChange={formik.handleChange}
        id="email"
        placeholder="e.g <EMAIL>"
        label="Email"
        Icon={<Email />}
      />
      <TextBox
        isSelectFocus={isFocus}
        error={`${formik.errors.country_code ?? ""} ${
          formik.errors.country_code ? "|" : ""
        } ${formik.errors.phone_number ?? ""}`}
        innerRef={phoneSuffixRef}
        formik={formik}
        id="phone_number"
        label="Phone Number"
        placeholder="Enter mobile no."
        value={formik.values.phone_number}
        onChange={formik.handleChange}
        CodeSelect={
          <CustomSelect
            setIsFocus={setIsFocus}
            style={{
              control: (provided, state) => ({
                width: "120px",
                borderWidth: "0px",
                height: "auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                paddingRight: "0px",
                paddingLeft: "1rem",
                borderRadius: "50px",
                borderTopRightRadius: "0px",
                borderBottomRightRadius: "0px",
              }),
              indicatorSeparator: (provided, state) => ({
                display: "none",
              }),
              dropdownIndicator: (provided, state) => ({
                padding: "0px",
              }),
              valueContainer: (provided, state) => ({
                padding: "0px",
                alignItemd: "center",
                alignSelf: "center",
                display: "flex",
                flex: "1",
                textAlign: "center",
                verticalAlign: "middle",
                flexWrap: "wrap",
                position: "relative",
                overflow: "hidden",
                boxSizing: "border-box",
              }),
              singleValue: (provided, state) => ({
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }),
            }}
            isSearchable={true}
            formik={formik}
            options={codes}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.country_code}
            name="country_code"
            id="country_code"
            className="rounded-[50px]=relative border-gray-700 px-0 w-full placeholder:text-gray-700 focus:outline-none focus:ring-2 max-md:text-cap1  max-md:max-h-max"
          />
        }
      />
      <TextBox
        type="text"
        className="col-span-2"
        error={formik.errors.expected_number_of_learners}
        innerRef={learnersRef}
        value={formik.values.expected_number_of_learners}
        onChange={formik.handleChange}
        formik={formik}
        id="expected_number_of_learners"
        label="Issue"
        placeholder="For eg: Not able to access the course i paid for.. etc."
      />

      <span className="pr-[50%]">
        <Button
          variant="primary"
          rounded
          type="submit"
          disabled={formik.isSubmitting || !formik.isValid}
        >
          {isLoading && <ButtonSpinner />}Submit
        </Button>
      </span>
    </form>
  );
};

export default InfoForm;
