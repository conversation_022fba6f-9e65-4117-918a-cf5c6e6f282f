"use client"
import StatisticsCardV2 from "./StatisticsCardV2"
import ProgressCard from "./ProgressCard"
import MobileProgressCard from "./MobileProgressCard"

type StatisticProps = {
    title: string
    value: string
    subValue: any
}

interface Model {
    statistics: StatisticsModel
}

interface StatisticsModel {
    totalCourses: number
    totalTopics: number
    totalQuizTaken: number
    averageQuizScore: number
    coursesStats: CoursesStat[]
}

interface CoursesStat {
    name: string
    image: string
    isEnrolled: boolean
    notStarted: boolean
    videosCount: number
    lessonsCount: number
    userWatchedVideosCount: number
    certificate: string
    validForCertificate: boolean
}

const StatisticsDashboard = ({ coursesStats, token }: { coursesStats: Model, token: string; }) => {
    const stats = coursesStats.statistics;
    const statistics: StatisticProps[] = [
        {
            title: "Total Courses",
            value: stats.totalCourses.toString(),
            subValue: null
        },
        {
            title: "Total Topics Watched",
            value: stats.totalTopics.toString(),
            subValue: null,
        },
        {
            title: "Total Quiz Taken",
            value: stats.totalQuizTaken.toString(),
            subValue: null
        },
        {
            title: "Average Quiz Score",
            value: stats.averageQuizScore.toString(),
            subValue: "out of 10"
        }
    ]

    return (
        <div className="mx-8">
            <h1 className="mb-4 sm:mb-auto text-b3 capitalize font-bold text-black mt-10">Overall Analytics</h1>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                {
                    statistics.map((statistic) => (
                        <StatisticsCardV2 key={`${statistic.title}-${Math.random()}`} statistic={statistic} />
                    ))
                }
            </div>

            <div className='border-b-4 border-[#E2E2E2] my-2 mr-4 pr-4' />
            <div className="mb-10">
                <h1 className="mb-4 sm:mb-auto text-b3 font-bold capitalize text-black mt-10">Courses Progress</h1>
                {stats.coursesStats.map((courseStat) => (
                    <ProgressCard key={`${courseStat.name}-${Math.random()}`} courseStat={courseStat} />
                ))}

            </div>
        </div>
    )
}

export default StatisticsDashboard
