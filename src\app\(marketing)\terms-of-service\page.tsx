import fs from 'fs'
import { Markdown, matter } from '@/lib/markdown'

export const metadata = {
    title: 'Terms of Service',
    description: 'Terms of Service for Crypto University',
    keywords: ['Terms of Service', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const getTermsContent = () => {
    const file = `src/content/terms/terms-mdx.md`
    const content = fs.readFileSync(file, 'utf8')
    const matterResult = matter(content)
    return matterResult.content
}
const getDisclaimer = () => {
    const file = `src/content/disclaimer/disclaimer-mdx.md`
    const content = fs.readFileSync(file, 'utf8')
    const matterResult = matter(content)
    return matterResult.content
}

const TermsPage = async () => {
    const TemrsContent = getTermsContent()
    const DisclaimerContent = getDisclaimer()

    return (
        <section className="w-full overflow-hidden font-manrope text-black">
            <div className="bg-yellow-light py-14 max-md:pb-[3.125rem] max-md:pt-10">
                <div className="container mx-auto space-y-3">
                    <h1 className="text-headline font-semibold max-md:text-b1">Terms of Service</h1>
                    <p>
                        <b className="text-sub3 font-medium max-md:text-cap1">Last updated:</b> May 5, 2023
                    </p>
                </div>
            </div>

            <div className="flex flex-col gap-[3.25rem] max-md:gap-[4.5rem]">
                <div className="container prose mx-auto my-[3.125rem] text-[14px] leading-5 prose-a:text-blue hover:prose-a:text-blue/80 max-md:mb-16 max-md:mt-6 max-md:text-cap2">
                    <Markdown>{TemrsContent}</Markdown>
                </div>
                
                <div className="container prose mx-auto mb-[52px] text-[14px] leading-5 prose-a:text-blue hover:prose-a:text-blue/80 max-md:text-cap2">
                    <hr className="w-full text-gray-700" />
                    <div className="pb-[3.125rem]">
                        <div className="space-y-3">
                            <h1 className="text-headline font-semibold max-md:text-b1">Disclaimer</h1>
                            <p>Last updated: May 5, 2023</p>
                        </div>
                    </div>
                    <Markdown>{DisclaimerContent}</Markdown>
                </div>
            </div>
        </section>
    )
}

export default TermsPage
