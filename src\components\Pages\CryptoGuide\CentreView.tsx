import { CryptoGuide } from "@/types/CryptoGuideCategoryPostModel";

export default function CentreView({ post }: { post: CryptoGuide }) {
    return (
        <div className="mx-auto max-w-7xl p-8">
            {/* Title Section */}

            <div>
                {post.content}
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-8 mb-12">
                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100">
                    ← Prev
                </button>
                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100">
                    Next →
                </button>
            </div>

            {/* Explore All Topics */}
            <div className="mt-12">
                <h2 className="text-xl font-bold mb-4">Explore All Topics</h2>
                <p className="text-[#424242] mb-4">
                    If this is your first semester at CryptoGuide, we recommend starting at the beginning and working your way through
                    this guide.
                </p>
                <ul className="space-y-2 text-[#0000FF]">
                    <li><a href="#" className="hover:underline">Introduction</a></li>
                    <li><a href="#" className="hover:underline">How To Register For Crypto University</a></li>
                </ul>
            </div>
        </div>
    )
}
