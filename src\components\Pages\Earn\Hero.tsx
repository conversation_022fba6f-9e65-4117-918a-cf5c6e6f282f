import HeroImage from "@public/earn/hero.png";
import ImageShortcut from "@/components/Ui/Image";

const Hero = () => {
  return (
    <section id="hero" className="bg-yellow-light w-full text-black">
      <div className="container mx-auto flex flex-wrap max-md:flex-col xl:px-32 max-md:items-center">
        <ImageShortcut
          src={HeroImage}
          alt="Hero Image"
          priority
          className="object-contain max-md:w-[191px] max-md:h-auto"
        />
        <div className="flex flex-col items-start max-md:items-center justify-center gap-4 pt-24 pb-28 max-md:pt-0 max-md:pb-11">
          <h1 className="text-h3 font-semibold max-md:text-center max-md:font-medium max-md:text-b1">
            Learn Crypto. <br className="md:hidden" />
            Earn Crypto
          </h1>
          <p className="text-sub3 max-w-[575px] max-md:text-cap2 max-md:text-center max-md:leading-[18px] max-md:px-4">
            There are several ways to earn cryptocurrencies without having to
            buy them with your own money. Here are some of the methods we
            recommend. Of course it requires you to take action.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Hero;
