'use client'
import Link from 'next/link'
import { Play, Pause } from '@public/dashboard'
import { cn } from '@/lib/cn'
import { useState, useEffect } from 'react'
import fetch from '@/lib/fetch'
import useFinishTopic from '@/hooks/useFinishTopic'

interface props {
    isPlay?: boolean
    title: string
    isDone?: boolean
    link: string
    isQuiz?: boolean
    isAss?: boolean
    user?: any
    quizID?: number
    assID?: number
    isLesson?: boolean
    topicID?: number
}

const Lesson = ({ isPlay, title, isDone, link, isQuiz, user, quizID, isLesson, topicID, assID, isAss }: props) => {
    const [isDoneState, setIsDoneState] = useState(false)
    const [isAssState, setIsAssState] = useState(false)
    const { lessExists } = useFinishTopic()
    useEffect(() => {
        const IsAnswered = async () => {
            try {
                const response = await fetch('/user-quizzes/quiz/' + quizID, {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                    next: {
                        revalidate: 20,
                    },
                })
                setIsDoneState(response?.success || false)
            } catch (error) {
                return undefined
            }
        }
        const IsAnsweredAss = async () => {
            try {
                const response = await fetch('/user-assignment/user/' + assID, {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                    next: {
                        revalidate: 20,
                    },
                })
                setIsAssState(response?.user_assignment?.status ? response?.user_assignment?.status == 'pass' : false)
            } catch (error) {
                console.error(error)
                return undefined
            }
        }
        if (isAss) IsAnsweredAss()
        if (isQuiz) IsAnswered()
    }, [isDoneState, quizID, user?.access_token, isAss, assID])
    return (
        <Link
            aria-label={title}
            href={link}
            className={cn(
                'flex max-w-[248px] cursor-pointer items-center justify-between gap-12 rounded-full bg-gray-300 px-[14px] py-3 transition-colors duration-300 hover:bg-gray-400',
                isPlay && 'bg-gray-400'
            )}>
            <div className="flex items-center gap-3">
                {isQuiz ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path
                            fillRule="evenodd"
                            stroke="#081228"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1.5"
                            d="M16.334 2.75H7.665c-3.021 0-4.915 2.139-4.915 5.166v8.168c0 3.027 1.885 5.166 4.915 5.166h8.668c3.031 0 4.917-2.139 4.917-5.166V7.916c0-3.027-1.886-5.166-4.916-5.166z"
                            clipRule="evenodd"></path>
                        <path
                            stroke="#081228"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1.5"
                            d="M8.44 12l2.373 2.373 4.746-4.746"></path>
                    </svg>
                ) : isAss ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path
                            stroke="#081228"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1.5"
                            d="M11.996 16.677V14.14"></path>
                        <path
                            fillRule="evenodd"
                            stroke="#081228"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1.5"
                            d="M18.19 5.33c1.69 0 3.05 1.37 3.05 3.06v3.44c-2.46 1.44-5.71 2.31-9.25 2.31s-6.78-.87-9.24-2.31V8.38c0-1.69 1.37-3.05 3.06-3.05h12.38z"
                            clipRule="evenodd"></path>
                        <path
                            stroke="#081228"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1.5"
                            d="M15.496 5.326V4.96c0-1.22-.99-2.21-2.21-2.21h-2.58c-1.22 0-2.21.99-2.21 2.21v.366M2.775 15.483l.19 2.509a3.242 3.242 0 003.231 2.998h11.6a3.242 3.242 0 003.231-2.998l.19-2.51"></path>
                    </svg>
                ) : (
                    <div>{isPlay ? <Pause /> : <Play />}</div>
                )}
                <div className="flex flex-col text-cap1 capitalize">
                    <p className="font-medium leading-none">{title}</p>
                </div>
            </div>
            {!isLesson && (
                <span className="rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
                        <circle
                            cx="8"
                            cy="8"
                            r="8"
                            fill={
                                isDoneState ||
                                lessExists({ id: topicID ? topicID : -1, finish: true }) ||
                                isDone ||
                                isAssState
                                    ? '#00BF77'
                                    : '#AAAAAA'
                            }></circle>
                        <path
                            fill="#fff"
                            d="M5.5 8.5l2 2 4-4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"></path>
                    </svg>
                </span>
            )}
        </Link>
    )
}

export default Lesson
