import Image from 'next/image'
import React from 'react'
import oneOnOne from '../../../../public/icons/oneOnOne.svg'


const OneOnOneCourse = () => {
  return (
      <div className="bg-[#40389F] md:h-[80vh]">
          <section className="mx-auto max-w-[400px] md:max-w-[700px] md:pt-20 lg:max-w-[1400px]">
              <div className="grid md:grid-cols-3 ">
                  <div className=" mt-5 items-center justify-center text-center md:mt-0">
                      <Image
                          src={oneOnOne}
                          alt="video"
                          width={188}
                          height={218}
                          className="h-[218px] w-[188px] items-center justify-center rounded-[4.0px] text-center md:float-right md:h-[442px] md:w-[381px]"
                      />
                  </div>
                  <div className="col-span-2 mx-auto mb-5 max-w-[340px] gap-5 p-1 pt-3 md:mx-10 md:mb-0 md:max-w-[640px] md:p-10">
                      <h3 className="flex h-[36px] max-w-[227px] items-center justify-center rounded-full bg-[#00FF9E] text-center text-[14px] font-[600] text-[#222222] md:h-[40px] md:max-w-[329px] md:text-[22px]">
                          One-On-One Mentorship
                      </h3>

                      <h2 className="my-4 w-[330px] text-[24px] font-[600] text-white md:w-[623px] md:text-[48px]">
                          Are you ready to conquer the crypto world?
                      </h2>
                      <p className="max-w-[623px] text-[12px] font-[500] text-gray-500 md:text-[20px]">
                          Dive into an exclusive, one-on-one mentorship with Grey Jabesi, a self-made crypto millionaire
                          who will guide you step-by-step into the lucrative realms of blockchain and cryptocurrencies
                      </p>
                  </div>
              </div>
          </section>
      </div>
  )
}

export default OneOnOneCourse
