'use client'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import Iframe from 'react-iframe'

const VideoPlayerIcon = () => (
    <svg
        fill="white"
        className="rounded-full bg-black/30 transition-all duration-300 group-hover:bg-black/70"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true">
        <path
            clipRule="evenodd"
            fillRule="evenodd"
            d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm14.024-.983a1.125 1.125 0 010 1.966l-5.603 3.113A1.125 1.125 0 019 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113z"
        />
    </svg>
)

const Video = () => {
    const [imageClicked, setImageClicked] = useState(false)
    return (
        <section id='video' className="container mx-auto pb-[55px] flex justify-center">
            <div className="group relative flex max-w-[768px] w-full  items-center justify-center  ">
                {!imageClicked ? (
                    <div className='relative flex items-center justify-center -mt-[60px] max-md:w-full'>
                        <div onClick={() => setImageClicked(true)}>
                            <ImageShortcut
                                src={'/affiliate/video.png'}
                                width={1280}
                                height={720}
                                alt="thumbnail"
                                className="rounded-card  z-[10] rounded-[1rem] aspect-video h-[400px]  w-full  max-w-[768px] cursor-pointer select-none object-cover max-md:h-[190px] max-md:min-h-[300px] max-sm:min-h-[220px] md:object-cover"
                                priority
                            />
                        </div>
                        <div
                            className={'absolute z-[10] h-[100px] w-[100px] cursor-pointer'}
                            onClick={() => setImageClicked(true)}>
                            <VideoPlayerIcon />
                        </div>
                    </div>
                ) : (
                   
                        <Iframe
                        url="https://www.youtube.com/embed/zvom5OVam9Y"
                        width="100%"
                        height="100%"
                        className=" rounded-card -mt-[60px] z-[10] rounded-[1rem] aspect-video h-[400px]  w-full  max-w-[768px] cursor-pointer select-none object-cover max-md:h-[190px] max-md:min-h-[300px] max-sm:min-h-[220px] md:object-cover"
                        display="initial"
                        position="relative"
                        allowFullScreen
                    />
                   
                )}
            </div>
        </section>
    )
}

export default Video
