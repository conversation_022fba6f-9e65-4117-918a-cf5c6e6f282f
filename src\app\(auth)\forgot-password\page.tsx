import ImageShortcut from "@/components/Ui/Image"
import Forgot from "../../../../public/forgot/illustration.png"
import ForgotForm from "@/components/Pages/Forgot/ForgotForm"

export const metadata = {
    title: 'Forgot Password',
    description: 'If you ever forgot the password of your Crypto University account, you can reset it here.',
    keywords: ['Forgot Password'],
}

export default function ForgotPassword() {
    return (
        <section className="flex flex-col text-black font-sans h-full flex-grow justify-center max-md:justify-start overflow-hidden">
            <div className="container mx-auto flex max-xl:flex-col-reverse items-center justify-between">
                <div className="flex flex-col gap-16 max-md:gap-8">
                    <div className="max-w-[647px] max-md:w-full flex flex-col gap-6 max-md:gap-3">
                        <h1 className="text-h3 max-md:text-headline max-xl:text-center">Forgot Password</h1>
                        <p className="text-sub1 max-md:text-sub3 text-gray-900 max-xl:text-center">Don&apos;t worry! Enter the email associated with your account and we&apos;ll send an email with instructions to reset password.</p>
                    </div>
                    <div className="max-w-[450px] max-xl:max-w-full">
                        <ForgotForm />
                    </div>
                </div>
                <ImageShortcut
                    src={Forgot}
                    width={649}
                    height={649}
                    className="max-xl:w-[210px] max-xl:h-auto"
                    alt="Forgot Password"
                    priority
                />
            </div>
        </section>
    )
}