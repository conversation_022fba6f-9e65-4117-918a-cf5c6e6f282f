import fetch from '@/lib/fetch'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { capitalizeWords } from '@/lib/capitalize'
import { MentorshipModel } from '@/types/MentorshipModel'
import Mentorship from '@/components/Pages/Mentorship/Mentorship'
import OneOnOneView from '@/components/Pages/Products/OneOnOneView'
import OneOnOneCourse from '@/components/Pages/Products/OneOnOneCourse'
import WhyOneOnOne from '@/components/Pages/Products/WhyOneOnOne'
import OneOnOneGain from '@/components/Pages/Products/OneOnOneGain'
import OneOnOneAdmissionProcess from '@/components/Pages/Products/OneOnOneAdmissionProcess'
import OurStudents from '@/components/Pages/Memberships/OurStudents'
import OneOnOneFoooter from '@/components/Pages/Products/OneOnOneFoooter'

interface MetaProps {
    params: {
        slug: any
    }
}
type Props = {
    params: {
        slug: string
    }
}

const GetOneMentorship = async (slug: string) => {
    if (slug !== undefined) {
        try {
            const response: { mentorship: MentorshipModel } = await fetch('/mentorship/slug/' + slug)
            const mentorship: MentorshipModel = response.mentorship
            return mentorship
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await GetOneMentorship(slug)
            if (response === null || response === undefined) return {}
            else {
                let keywords = [
                    response.name as string,
                    response.slug as string,
                    'Crypto',
                    'Crypto University',
                    'Education',
                    'Crypto Mentorship',
                    'Customized Mentorship',
                ]
                if (response.slug === '1-on-1-mentorship') {
                    response.name = 'Exclusive One-on-One Crypto Mentorship with Grey Jabesi - Apply Now!'
                    response.description = 'Join an exclusive one-on-one mentorship with Grey Jabesi, a self-made crypto millionaire. Experience tailored guidance, daily interactions, and guaranteed transformation in the crypto world. Apply now for dedicated mentorship, hands-on trading experience, and complimentary masterclasses. Limited spots available'
                    keywords = ['Crypto mentorship', 'Grey Jabesi', 'Cryptocurrency trading', 'Blockchain training', 'One-on-one mentorship', 'Crypto masterclass', 'Crypto University']
                    response.image = 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1716016181/One-on-One_Mentorship_with_Grey_Jabesi_-_Webpage_Thumbnail_Op2_gdkmxy.jpg'
                }
                return {
                    title: capitalizeWords(response.name),
                    description: response.description as string,
                    authors: [{ name: response.name }],
                    keywords: keywords,
                    openGraph: {
                        title: response.name,
                        description: response.description,
                        type: 'website',
                        url: response.slug,
                        images: [
                            {
                                url: response.image as string,
                                width: 1200,
                                height: 630,
                                alt: response.image,
                            },
                        ],
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: response.name,
                        description: 'Checkout page for ' + response.name,
                        images: [response.image as string],
                    },
                }
            }
        } catch (error: any) {
            console.error('Error:', error)
            return error
        }
    else return {}
}

const MentorshipPage = async ({ params }: Props) => {
    const slug = params.slug
    const response = await GetOneMentorship(slug)
    if (response === null || response === undefined) notFound()
    return (
        <>
            {response.slug === '1-on-1-mentorship' ? (<div className="w-full">
                <OneOnOneView />
                <OneOnOneCourse />
                <WhyOneOnOne />
                <OneOnOneGain />
                <OneOnOneAdmissionProcess />
                <OurStudents />
                <OneOnOneFoooter />
            </div>) : (<div className="relative flex w-full flex-col">
                <Mentorship mentorship={response} />
            </div>)}
        </>
    )
}

export default MentorshipPage

