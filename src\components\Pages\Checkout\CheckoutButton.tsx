'use client'
import React from 'react'
import { FormikProps } from 'formik'
import Button from '@/components/Ui/Button'
import ButtonSpinner from '@/components/Ui/buttonSpinner'

export const CheckoutButton = ({ formik, disabled }: { formik: FormikProps<any>; disabled: boolean }) => {
    return (
        <Button
            onClick={formik.handleSubmit}
            type="button"
            variant="primary"
            disabled={formik.isSubmitting || disabled}
            rounded>
            {formik.isSubmitting && <ButtonSpinner />}{' '}
            {formik.isSubmitting ? 'Loading' : disabled ? 'Already owned' : 'Checkout'}
        </Button>
    )
}
