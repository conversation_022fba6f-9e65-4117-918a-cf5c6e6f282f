import { CourseModel } from '@/types/CourseModel'
import CourseCard from './CourseCard'

interface CoursesProps {
    courses: CourseModel[]
}

const Courses = ({ courses }: CoursesProps) => {
    return (
        <section
            id="courses"
            className="w-full border-b border-gray-700 py-16 text-black max-md:border-none max-md:py-0 max-md:pb-[4.7rem] max-md:pt-0">
            <div className="container mx-auto">
                <div className="flex flex-col gap-[2.625rem] max-md:gap-8">
                    <div className="flex flex-col gap-3 max-md:gap-2">
                        <h1 className="text-h3 font-medium max-md:text-callout max-md:font-semibold">
                            Discover Top Course
                        </h1>
                        <p className="text-callout max-md:text-cap1">
                            Crypto University gives you the tools And strategies you need to analyze any Cryptocurrency
                            projects and become a highly profitable Cryptocurrency investor and trader. Join our
                            community of over 15,000+ students
                        </p>
                    </div>
                    <div className="grid grid-cols-3 gap-9 max-lg:grid-cols-2 max-md:grid-cols-1 max-md:gap-[1.125rem]">
                        {courses.map((item: CourseModel, index: number) => (
                            <CourseCard key={index} course={item} />
                        ))}
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Courses
