import { NextResponse } from 'next/server'

export async function GET() {
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.WHOP_BEARER}`,
        },
    }

    const response = await (
        await fetch((process.env.WHOP_PRODUCT_API as string) + 'prod_BaUOMN9EsXukr?expand=plans', options)
        //THIS BELOW IS FOR THE REAL ADVANCED PRODUCT
        //await fetch((process.env.WHOP_PRODUCT_API as string) + 'prod_4sEMAgHmcct8Z?expand=plans', options)
    ).json()

    return NextResponse.json(
        {
            title: {
                text: 'The Superior Learning Experience.',
                color: 'white',
            },
            sub: {
                text: response.name,
                color: 'yellow',
            },
            price: {
                //text: response.plans[0].renewal_price,
                text: "500",
                color: 'white',
            },
            icon: {
                src: '/memberships/card2.png',
                alt: 'Advanced Membership Icon',
            },
            cards: {
                text: [
                    'Web3, AI, Content Creation Masterclass',
                    'Weekly Live Session',
                    'Live Support',
                    'Alpha Trading',
                    'Alpha Stream',
                    'Trading indicators',
                    // 'Day-to-day Live Session',                    
                    // 'Unlimited Live Support',
                    // 'VIP Stream',                    
                    // 'VIP Trading',                    
                    // 'VIP Q&A',
                ],
                color: 'white',
            },
            plans: response.plans[0].direct_link,
        },
        { status: 200 }
    )
}
