import Skins from '@/components/Pages/Resources/Skins'
import { Commas, coin, coinGecko, tradingview } from '@public/resources/trading'

export const metadata = {
    title: 'Trading Tools',
    description: 'Trading tools that Crypto University recommends to help you succeed in crypto.',
    keywords: ['Trading Tools', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const TradingPage = () => {
    const data = [
        {
            category: 'Technical Analysis',
            items: [
                {
                    name: 'TradingView',
                    img: tradingview,
                    link: 'https://www.tradingview.com/gopro/?share_your_love=ninja265',
                },
            ],
        },
        {
            category: 'Automated Trading',
            items: [
                {
                    name: '3Commas',
                    img: Commas,
                    link: 'https://3commas.io/?c=MOON2025',
                },
            ],
        },
        {
            category: 'Crypto Data Platforms',
            items: [
                {
                    name: 'CoinMarketCap',
                    img: coin,
                    link: 'https://coinmarketcap.com/',
                },
                {
                    name: 'CoinGecko',
                    img: coinGecko,
                    link: 'https://www.coingecko.com/?__cf_chl_rt_tk=toDpoV5xnM1aoyQxwPs1NaJw2txt23NqIBysbW1s.l0-1687863199-0-gaNycGzNDXs',
                },
            ],
        },
    ]

    return (
        <section className="w-full">
            <Skins data={data} />
        </section>
    )
}

export default TradingPage
