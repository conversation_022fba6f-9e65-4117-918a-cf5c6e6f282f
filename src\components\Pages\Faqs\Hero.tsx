import Search from "./Search";
import { Faq } from "@/types/FaqModel";
import ImageShortcut from "@/components/Ui/Image";

interface props {
  faq: Faq;
}

const Hero = ({ faq }: props) => {
  return (
    <>
      <div className="relative container max-md:hidden">
        <Search faq={faq} />
        <ImageShortcut
          src={"/faqs.png"}
          width={212}
          height={212}
          alt="Hero Image"
          className="absolute inset-0 z-10 w-[212px] h-[212px] object-cover left-[80%] top-[-20px]"
        />
      </div>
      {/* Mobile */}
      <div className="w-full hidden max-md:block">
        <div className="w-full pt-9 bg-gray-300 px-4 pb-14 relative">
          <h1 className="text-b1 font-medium">FAQ&apos;s</h1>
          <ImageShortcut
            src={"/faqs.png"}
            width={212}
            height={212}
            alt="Hero Image"
            className="absolute inset-0 z-10 w-[100px] h-[100px] object-cover left-[80%] max-sm:w-[65px] "
          />
        </div>
        <div className="px-4 mt-[-30px]">
          <Search faq={faq} />
        </div>
      </div>
    </>
  );
};

export default Hero;
