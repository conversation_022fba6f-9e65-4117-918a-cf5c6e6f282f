'use client'
import { useRef, useState } from 'react'
import { cn } from '@/lib/cn'
import MarkdownComponent from '@/components/Ui/MarkdownComponent'
import Button from '@/components/Ui/Button'
import axios from '@/lib/axios'

interface Props {
    description: string
    setPdf: any
    pdf: any
    id: string
    user: any
    Ans: {
        id: number
        user_id: number
        assignment_id: number
        status: string
        pdf: string
        grade: number
        comment: string
    }
}

const ListButtons = ({ description, setPdf, Ans, id, pdf, user }: Props) => {
    const [activeTabIndex, setActiveTabIndex] = useState<'description' | 'submission' | 'score'>('description')
    const [isLoading, setIsLoading] = useState(false)
    const [selectedFile, setSelectedFile] = useState(null)
    const handleFileInputChange = (event: any) => {
        const fileInput = event.target
        if (fileInput.files.length > 0) {
            const fileName = fileInput.files[0].name
            setSelectedFile(fileName)
            setPdf(fileInput.files[0])
        }
    }
    const handleSubmit = async () => {
        const formData = new FormData()
        formData.append('assignment_id', id)
        formData.append('pdf', pdf)
        setIsLoading(true)
        try {
            await axios.post('/user-assignment', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            setIsLoading(false)
            window.location.reload()
        } catch (error) {
            console.error(error)
            setIsLoading(false)
        }
    }
    const fileRef = useRef<HTMLInputElement>(null)
    return (
        <div className={'flex h-fit flex-col items-start gap-6 overflow-hidden p-1 max-md:w-full '}>
            <div className="relative flex items-start gap-8 border-b-4 border-[#E2E2E2] text-sub3 transition-colors max-md:grid max-md:w-full max-md:grid-cols-3 max-md:text-[14px] max-sm:gap-0">
                <label className={cn('flex w-full cursor-pointer items-center justify-normal gap-2 pb-4', '')}>
                    <span className="select-none font-semibold">Description</span>
                    <input
                        hidden
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setActiveTabIndex(e.target.value as 'description' | 'submission' | 'score')
                        }}
                        defaultChecked={activeTabIndex == 'description'}
                        type="radio"
                        name="payment"
                        value="description"
                    />
                </label>
                <label className="flex w-full cursor-pointer items-center justify-normal gap-2 pb-4 ">
                    <span className="select-none whitespace-nowrap font-semibold">Your Submission</span>
                    <input
                        hidden
                        defaultChecked={activeTabIndex == 'submission'}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setActiveTabIndex(e.target.value as 'description' | 'submission' | 'score')
                        }}
                        type="radio"
                        name="payment"
                        value="submission"
                    />
                </label>
                <label className="flex w-full cursor-pointer  items-center justify-normal gap-2 pb-4 ">
                    <span className="select-none font-semibold">Your Score</span>
                    <input
                        hidden
                        defaultChecked={activeTabIndex == 'score'}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setActiveTabIndex(e.target.value as 'description' | 'submission' | 'score')
                        }}
                        type="radio"
                        name="payment"
                        value="score"
                    />
                </label>
                <span
                    className={cn(
                        'absolute -bottom-1 left-0 h-1 w-1/3 bg-blue transition-all duration-300',
                        activeTabIndex == 'description'
                            ? 'w-[25%] translate-x-0'
                            : activeTabIndex == 'submission'
                            ? 'w-[35%] translate-x-[95%] max-md:translate-x-[85%]'
                            : activeTabIndex == 'score'
                            ? 'w-[25%] translate-x-[300%] max-md:w-[30%] max-md:translate-x-[220%]'
                            : ''
                    )}
                />
            </div>
            <div
                className={cn(
                    'relative flex w-[100%] justify-between gap-2 text-sub3 transition-all duration-300 max-md:text-[14px]'
                )}>
                <div
                    className={cn(
                        'relative flex w-full grid-flow-col flex-col gap-3 transition-all duration-300',
                        activeTabIndex == 'description'
                            ? ' opacity-100'
                            : activeTabIndex == 'submission'
                            ? 'hidden opacity-0 '
                            : activeTabIndex == 'score'
                            ? 'hidden  opacity-0'
                            : ''
                    )}>
                    <h1 className="text-sub1 font-medium">Description</h1>
                    <MarkdownComponent text={description} />
                </div>
                <div
                    className={cn(
                        'relative flex flex-col gap-3',
                        activeTabIndex == 'description'
                            ? 'hidden opacity-0'
                            : activeTabIndex == 'submission'
                            ? 'opacity-100'
                            : activeTabIndex == 'score'
                            ? 'hidden opacity-0'
                            : ''
                    )}>
                    <h1 className="text-sub1 font-medium">Your Submission</h1>
                    <p className="text-sub3 text-gray-900">You can submission 1 time task</p>
                    <div className="flex w-full items-center gap-3">
                        {selectedFile && (
                            <div className="flex w-full items-center gap-2 whitespace-nowrap rounded-full bg-gray-300 px-6 py-4">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    fill="none"
                                    viewBox="0 0 24 24">
                                    <path
                                        fill="#081228"
                                        fillRule="evenodd"
                                        d="M13.45 4.88h3.07c3.69 0 5.49 1.97 5.48 6.01v4.87c0 3.86-2.38 6.24-6.25 6.24H8.24C4.39 22 2 19.62 2 15.75V8.24C2 4.1 3.84 2 7.47 2h1.58c.931-.01 1.8.42 2.37 1.15l.88 1.17c.********** 1.15.56zM7.37 15.29h9.26c.41 0 .74-.34.74-.75a.74.74 0 00-.74-.75H7.37c-.42 0-.75.33-.75.75 0 .***********.75z"
                                        clipRule="evenodd"></path>
                                </svg>
                                <p className="text-sub3 font-semibold">{selectedFile}</p>
                            </div>
                        )}
                        <label
                            htmlFor="fileInput"
                            className={cn(
                                ' flex w-full min-w-fit cursor-pointer select-none items-center justify-center gap-2 rounded-full border-[1px] border-dashed border-black bg-gray-300 px-6 py-[17.5px] font-sans text-sub2 font-semibold text-black transition-[background-color] duration-150 hover:bg-gray-100 active:opacity-60',
                                selectedFile ? 'file-selected' : '',
                                Ans && Ans?.status != 'return'
                                    ? 'cursor-not-allowed opacity-60 hover:bg-gray-300 disabled:opacity-40'
                                    : ''
                            )}>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                fill="none"
                                viewBox="0 0 24 24">
                                <path
                                    stroke="#081228"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.5"
                                    d="M7.39 8.984h-.934a3.685 3.685 0 00-3.685 3.685v4.875a3.685 3.685 0 003.685 3.684h11.13a3.685 3.685 0 003.686-3.684v-4.885a3.675 3.675 0 00-3.674-3.675h-.944M12.021 2.19v12.041M9.105 5.119L12.02 2.19l2.917 2.928"></path>
                            </svg>
                            <span id="fileLabel">
                                {selectedFile ? 'change' : Ans != null ? 'Uploaded' : 'Upload PDF File'}
                            </span>
                            <input
                                ref={fileRef}
                                type="file"
                                name="pdf"
                                id="fileInput"
                                disabled={Ans && Ans?.status != 'return'}
                                accept="application/pdf"
                                className="hidden"
                                onChange={handleFileInputChange}
                            />
                        </label>
                    </div>
                    <Button
                        disabled={isLoading || (Ans && Ans?.status != 'return') || pdf === null}
                        onClick={handleSubmit}
                        className="flex items-center gap-2 md:hidden"
                        variant="primary"
                        rounded>
                        <span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="25"
                                fill="none"
                                viewBox="0 0 24 25">
                                <path
                                    stroke="#fff"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.5"
                                    d="M7.39 9.484h-.934a3.685 3.685 0 00-3.685 3.685v4.875a3.685 3.685 0 003.685 3.684h11.13a3.685 3.685 0 003.686-3.684v-4.885a3.675 3.675 0 00-3.674-3.675h-.944M12.021 2.69v12.041M9.105 5.619L12.02 2.69l2.917 2.928"></path>
                            </svg>
                        </span>
                        Upload Submission
                    </Button>
                </div>
                <div
                    className={cn(
                        'relative flex h-fit flex-col gap-3 ',
                        activeTabIndex == 'description'
                            ? 'hidden opacity-0'
                            : activeTabIndex == 'submission'
                            ? 'hidden  opacity-0'
                            : activeTabIndex == 'score'
                            ? ' opacity-100'
                            : ''
                    )}>
                    {Ans == null ? (
                        <p>Upload Your File Firstly</p>
                    ) : (
                        <div className="flex flex-col gap-3">
                            <div className="flex items-center gap-3">
                                <h1 className="text-b3 font-medium max-md:text-sub2">Status:</h1>
                                <p className="text-sub2 max-md:text-cap2 capitalize">
                                    {Ans.status == 'pass' ? 'Passed' : Ans.status == 'return' ? 'failed' : Ans.status}
                                </p>
                            </div>
                            <div className="flex items-center gap-3">
                                <h1 className="text-b3 font-medium max-md:text-sub2">Grade:</h1>
                                <p className="text-sub2 capitalize max-md:text-cap2">{Ans.grade}</p>
                            </div>
                            <div className="flex items-center gap-3">
                                <h1 className="text-b3 font-medium max-md:text-sub2">Comment:</h1>
                                {Ans.comment == null ? '' : <MarkdownComponent text={Ans.comment} />}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default ListButtons
