import ListButtons from "./ListButtons";
import ImageShortcut from "@/components/Ui/Image";
import HeaderImg from "@public/resources/header.png";
import CryptoPrices from "./CryptoPrices";
import TrendingCryptos from "./TrendingCryptos";

const Header = () => {
  
  const mockTrending = [
      { name: 'MAGA', Abr: 'MAGA Hot', value: 0.00026, change: -6.0 },
      { name: 'NOT', Abr: 'Notcoin', value: 0.02023, change: 3.1 },
      { name: 'TON', Abr: 'Toncoin', value: 7.97, change: -2.2 },
      { name: 'NOT', Abr: 'Notcoin', value: 0.02023, change: 3.1 },
  ]

  const currencyOptions = [
    { value: '$USD', label: '$USD' },
    { value: '€EUR', label: '€EUR' },
    { value: '¥JPY', label: '¥JPY' },
      // Add more currencies as needed
  ]
  return (
      <div className="relative w-full overflow-hidden pb-6 max-md:pb-[18px] max-md:pt-[30px]">
          <section className="bg-[#121519] pb-16 pt-[70px]">
              <div className="container mx-auto max-md:gap-[30px]">
                  <section className="mb-8 flex justify-between ">
                      <h1 className="text-[28px] font-bold text-white">👀 Crypto Prices</h1>
                      <select className="rounded-md border border-[#3E4550] bg-[#121519] px-3 py-2 text-[14px] font-[600] text-white">
                          {currencyOptions.map(option => (
                              <option className="gap-2" key={option.value} value={option.value}>
                                  {option.label}
                              </option>
                          ))}
                      </select>
                  </section>
                  <CryptoPrices/>
                  {/* <h1 className="mb-8 mt-12 text-[28px]  font-bold text-white">🔥 Trending Cryptos</h1> */}
                  {/* <TrendingCryptos trending={mockTrending} /> */}
              </div>
          </section>
          <section className="bg-yellow-ligh">
              <div className="container mx-auto flex flex-col gap-[72px] pt-[80px]  max-md:gap-[30px]">
                  <h1 className="text-h3 font-medium max-md:w-[225px] max-sm:text-b2">Crypto Resources</h1>
                  <ListButtons />
              </div>
          </section>

      </div>
  )
};

export default Header;
