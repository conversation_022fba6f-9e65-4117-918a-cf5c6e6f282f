import Skins from '@/components/Pages/Resources/Skins'
import {
    binance,
    blockfi,
    bybit,
    coinbase,
    crypto,
    ftx,
    huobi,
    kucoin,
    ledger,
    luno,
    okx,
    paxful,
    trezor,
    valr,
    noones,
    cryptou,
    bingx,
    coinw,
} from '@public/resources/wallets'

export const metadata = {
    title: 'Wallets',
    description: 'Wallets that Crypto University recommends to help you succeed in crypto.',
    keywords: ['Wallets', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const data = [
    {
        category: 'Hot Wallets',
        items: [
            {
                name: 'Binance',
                img: binance,
                link: 'https://www.binance.com/en/futures/ref?code=greybtc',
            },
            {
                name: 'Noones',
                img: noones,
                link: 'https://noones.com/en',
            },
            {
                name: 'Bybit',
                img: bybit,
                link: 'https://www.bybit.com/en/sign-up?affiliate_id=97446&group_id=0&group_type=1',
            },
            {
                name: 'Coinbase',
                img: coinbase,
                link: 'https://www.coinbase.com/join/580beab974462c332874e6f0',
            },
            {
                name: 'Crypto.com',
                img: crypto,
                link: 'http://platinum.crypto.com/r/hardcorecrypto?utm_campaign=Crypto.com',
            },
            // {
            //     name: 'CryptoU',
            //     img: cryptou,
            //     link: 'https://cryptouniversity.network/',
            // },
            {
                name: 'CoinW',
                img: coinw,
                link: 'https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US',
            },
            {
                name: 'Huobi',
                img: huobi,
                link: 'https://www.huobi.io/en-us/topic/invited/?invite_code=hpm35',
            },
            {
                name: 'Kucoin',
                img: kucoin,
                link: 'https://www.kucoin.com/ucenter/signup?rcode=2N2Qece&utm_source=kucoin',
            },
            {
                name: 'Luno',
                img: luno,
                link: 'https://www.luno.com/signup?irclickid=xfgUHwxj1xyPTg%3AV9Mwcc0e-UkF22LUfeUAz0E0&irgwc=1&utm_source=Impact&utm_medium=Referral_Link&utm_campaign=Affiliate&wpsrc=Impact%20Radius&wpsid=2430975&wpcrid=677833&wpsn=Crypto%20University&wpcid=10534&wpcrn=Online%20Tracking%20Link',
            },
            {
                name: 'OKX',
                img: okx,
                link: 'https://www.okx.com/join/2028981',
            },
            {
                name: 'BingX',
                img: bingx,
                link: 'https://bingx.com/en-us/',
            },
            {
                name: 'Valr',
                img: valr,
                link: 'https://www.valr.com/invite/VAURZWMT',
            },
        ],
    },
    {
        category: 'Cold Wallets',
        items: [
            {
                name: 'Ledger',
                img: ledger,
                link: 'https://shop.ledger.com/?r=c1ec',
            },
            {
                name: 'Trezor',
                img: trezor,
                link: 'https://trezor.io/trezor-model-t',
            },
        ],
    },
]

const WalletsPage = () => {
    return (
        <section className="w-full">
            <Skins data={data} />
        </section>
    )
}

export default WalletsPage
