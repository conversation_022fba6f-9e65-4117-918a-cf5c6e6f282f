import HeroImage from '@public/affiliate/hero.png'
import ImageShortcut from '@/components/Ui/Image'
import Button from '@/components/Ui/Button'

const Hero = () => {
    return (
        <section id='hero' className="w-full bg-yellow-light text-black">
            <div className="- container mx-auto flex flex-wrap max-md:flex-col-reverse max-md:items-center justify-between">
                <div className="flex flex-col items-start gap-9 max-md:gap-7 pb-40 max-md:pb-20 pt-24 max-md:pt-0 ">
                    <div className="flex flex-col items-start justify-center gap-4 max-md:gap-2  max-md:items-center  max-md:pt-0">
                        <h1 className="text-h3 font-semibold max-md:text-center max-md:text-b1 max-md:font-medium">
                            Get paid directly in <br className="max-md:hidden" />
                            cryptocurrency every week!
                        </h1>
                        <p className="max-w-[575px] text-sub3 max-md:px-4 max-md:text-center max-md:text-cap2 max-md:leading-[18px]">
                            Become a Crypto University affiliate and start earning today! Promote the No.#1 crypto
                            community in the world. Get paid for each referral sale. It&apos;s that easy.
                        </p>
                    </div>
                    <div className=" w-full max-w-[261px] max-md:max-w-full">
                        <Button className="" rounded variant="primary">
                            Start Earning {'>'}
                        </Button>
                    </div>
                </div>
                <ImageShortcut
                    src={HeroImage}
                    alt="Hero Image"
                    priority
                    className="object-contain max-md:h-auto max-md:w-[191px]"
                />
            </div>
        </section>
    )
}

export default Hero
