import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { MdKeyboardArrowRight } from 'react-icons/md'
import crown from '../../../../public/icons/crown.svg'
import daily from '../../../../public/icons/daily.svg'
import yellowbutton from '../../../../public/icons/yellowbutton.svg'
import guaranteed from '../../../../public/icons/guaranteed.svg'



const WhyOneOnOne = () => {
    const why = [
        {
            title: 'Tailored Guidance',
            description:
                '<PERSON> personally designs your learning experience, adapting to your pace, style, and schedule. You won’t just learn about crypto; you’ll live it.',
            image: crown,
        },
        {
            title: 'Daily Interactions',
            description:
                'Engage with <PERSON> every day for a month, ensuring you receive the focused attention and detailed insights needed to succeed.  ',
            image: daily,
        },
        {
            title: 'Guaranteed Transformation',
            description:
                'This mentorship doesn’t just teach; it transforms. Equip yourself with the knowledge and skills to master the crypto market.  ',
            image: guaranteed,
        },
    ]
  return (
      <div className="bg-white p-[32px] md:p-[64px]">
          <h2 className="mb-5 mt-4 text-[24px] font-[600] text-[#222222] md:text-center md:text-[48px]">
              Why This Mentorship?
          </h2>
          <div className="mx-auto grid max-w-[1150px] gap-10 md:grid-cols-3">
              {why.map((item, index) => (
                  <div key={index} className="mx-auto justify-center rounded-[8px] border text-center md:border-none p-2">
                      <div className="flex md:grid gap-3 md:gap-2 text-left md:text-center items-center my-2 md:my-0">
                          <section className="md:mx-auto">
                              <Image
                                  src={item.image}
                                  height={46.71}
                                  width={58}
                                  alt="crown"
                                  className="md:mx-auto md:my-3 h-[46.71px] w-[58px] md:h-[124px] md:w-[99.85px]" // Add mx-auto class here
                              />
                          </section>
                          <h2 className="md:my-2 text-[18px] md:text-[24px] md:items-center font-[600] w-full">{item.title}</h2>
                      </div>

                      <p className="md:justify-center md:text-center text-left text-[12px] md:text-[16px] font-[400] text-[#5B5B5B]">
                          {item.description}
                      </p>
                  </div>
              ))}
          </div>
          <Link aria-label="View products" target='_blank' href={'https://noteforms.com/forms/mentorship-request-d8avmm?notionforms=1'} className="my-3">
              <Image
                  src={yellowbutton}
                  height={54}
                  width={341}
                  alt="crown"
                  className="mx-auto my-5" // Add mx-auto class here
              />
          </Link>
      </div>
  )
}

export default WhyOneOnOne
