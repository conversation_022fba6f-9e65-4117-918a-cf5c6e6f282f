import fetchInstance from '@/lib/fetch'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import HeaderWithNoAction from '@/components/Pages/Dashboard/Home/HeaderWithNoAction'
import { MentorshipModel } from '@/types/MentorshipModel'
import NoMentorship from '@/components/Pages/Dashboard/Home/NoMentorship'
import Consultation from '@/components/Pages/Dashboard/Home/Consultation'

interface Model {
    mentorships: MentorshipModel[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

export const metadata = {
    title: 'My Mentorships',
    description: 'Mentorships page for your Crypto University account.',
}

const ConsultationPage = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

    const GetMentorships = async () => {
        try {
            const response = await fetchInstance(
                '/mentorship/all/pagination?pageNumber=1&pageSize=10',
                {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                }
            )
            return response
        } catch (error) {
            console.error('Error mentorships: ', error)
            return error
        }
    }

    const mentorships: Model = await GetMentorships()
    return (
        <section className="flex w-full flex-col gap-14 text-black">
            <HeaderWithNoAction user={user} />
            {mentorships.meta && mentorships.meta.totalCount == 0 ? (
                  <NoMentorship />
                  ) : (
                      <div className="flex h-full flex-col justify-between">
                          <Consultation mentorships={mentorships.mentorships} />
                      </div>
                  )}
        </section>
    )
}

export default ConsultationPage
