import '../styles/globals.scss'
import 'react-toastify/dist/ReactToastify.css'

import { cn } from '@/lib/cn'
import { siteConfig } from '@/config/site'
import { Poppins } from 'next/font/google'
import { ToastContainer } from 'react-toastify'
import { TailwindIndicator } from '@/components/tailwind'
import Head from "next/head";
import Provider from '@/components/provider'
import GoogleAnalytics from '@/components/google-analytics'
import TwitterPixel from '@/components/twitter-pixel'
import FacebookPixel from '@/components/facebook-pixel'
import { Suspense } from 'react'
import Analytics from '@/components/Analytics'
import SmartlookComponent from '@/components/smart-look'
import ClarityMicrosoft from '@/components/clarity-microsoft'
import Atlos from '@/components/atlos'
import ConvaiWidget from '@/components/ConvaiWidget '
import IntercomClientComponent from '@/components/Tools/Intercom/Intercom'
import ElfsightChatWidget from '@/components/Tools/Intercom/ElfsightChatWidget'
import Chatbase from '@/components/Tools/Chatbase/Chatbase'


const manrope = Poppins({
  weight: ['300', '400', '500', '600', '700'],
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-manrope',
})
const sans = Poppins({
  weight: ['300', '400', '500', '600', '700'],
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-sans',
})


export const metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL + ''),
  description: siteConfig.description,
  keywords: ['Crypto', 'Crypto University', 'Education', 'Crypto Courses', 'Customized Courses', 'Grey Jabesi'],
  authors: {
    name: siteConfig.creator,
    url: siteConfig.url,
  },
  creator: siteConfig.creator,
  openGraph: {
    fb: {
      app_id: '969315560269176',
    },
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: "https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1711984885/Crypto_University_Thumbnail_pkshxy.jpg",
        blurDataURL: "https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1711984885/Crypto_University_Thumbnail_pkshxy.jpg",
        width: 400, /// 1200,
        height: 400, // 630,
        alt: siteConfig.name,
      },
    ],
    // images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1711984885/Crypto_University_Thumbnail_pkshxy.jpg',
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    creator: '@cryptouniversity',
    images: [
      {
        url: "https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1711984885/Crypto_University_Thumbnail_pkshxy.jpg",
        blurDataURL: "https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1711984885/Crypto_University_Thumbnail_pkshxy.jpg",
        width: 400, /// 1200,
        height: 400, // 630,
        alt: siteConfig.name,
      },
    ],
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {

  const trackingId = process.env.GA_TRACKING_ID || '';
  const googleSEO = process.env.GOOGLE_SEO || '';
  const facebookPixelID = process.env.FB_PIXEL_ID || '';
  const twitterPixelID = process.env.TW_PIXEL_ID || '';
  const smartLookTrackingCode = process.env.SMART_LOOK_TRACKING_CODE || '';
  const smartLookRegion = process.env.SMART_LOOK_REGION || '';
  return (
    <html lang="en">
      <Head>
        <meta name="google-site-verification" content={googleSEO} />
      </Head>
      <Provider>
        {/* <ConvaiWidget /> */}
        {/* <IntercomClientComponent /> */}
        {/* <ElfsightChatWidget/> */}
        <Chatbase/>
        {/* <GoogleAnalytics GA_TRACKING_ID={trackingId} /> */}
        {/* <FacebookPixel FB_PIXEL_ID={facebookPixelID} /> */}
        {/* <TwitterPixel TW_PIXEL_ID={twitterPixelID} /> */}
        {/* <SmartlookComponent smartLookTrackingCode={smartLookTrackingCode} smartLookRegion={smartLookRegion} /> */}
        <Atlos />
        <body
          className={cn(
            'relative flex min-h-screen flex-col text-black antialiased',
            sans.variable,
            manrope.variable,
            manrope.className
          )}>
          <Suspense>
            <Analytics />
          </Suspense>
          {children}
          <TailwindIndicator />
          <ToastContainer
            position="bottom-right"
            autoClose={3000}
            hideProgressBar
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </body>
      </Provider>
    </html>
  )
}
