import React, { useState } from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { components } from 'react-select';
import Select from 'react-select';
import { StylesConfig } from 'react-select';
import { countryOptions as rawCountryOptions } from './countries'
import Button from '@/components/Ui/Button';
import ButtonSpinner from '@/components/Ui/buttonSpinner';

type CountryOption = { value: string; label: string; };

const CustomOption = (props: any) => {
    return (
        <components.Option {...props}>
            <span>{props.data.label}</span>
        </components.Option>
    );
};

interface AdditionalDetailsProps {
    userData: {
        country: string;
        phoneNumber: string;
    };
    errors: {
        country: string;
        phoneNumber: string;
    };
    onInputChange: (name: string, value: string) => void;
    onSignUpComplete: () => void;
}

const AdditionalDetails = ({ userData, errors, onInputChange, onSignUpComplete }: AdditionalDetailsProps) => {
    const [isSigningUp, setIsSigningUp] = useState(false);
    const handlePhoneChange = (value: string | undefined) => {
        onInputChange('phoneNumber', value || '');
    };

    const handleSubmit = (e: React.FormEvent) => {
        setIsSigningUp(true);
        e.preventDefault();
        onSignUpComplete();
        // setIsLoading(false);
    };

    const customStyles: StylesConfig<CountryOption, false> = {
        control: (provided) => ({
            ...provided,
            borderRadius: '9999px',
            minHeight: '40px',
            width: '316px',
        }),
        menu: (provided) => ({
            ...provided,
            width: '316px',
        }),
        container: (provided) => ({
            ...provided,
            width: '316px',
        }),
    };

    const countryOptions: CountryOption[] = rawCountryOptions.map(country => ({
        value: country.code.toLowerCase(),
        label: `${country.name} (${country.phoneCode})`
    }));

    return (
        <div className="mx-auto w-full max-w-md bg-white p-4 sm:rounded-[21.6px] sm:p-8 sm:shadow-md">
            <h1 className="mb-6 text-center text-[20px] font-bold text-[#081228]">Just a few more details!</h1>

            <form className="mt-20" onSubmit={handleSubmit}>
                <div className="mb-4">
                    <h2 className="mb-2 text-[14px] font-[400]">
                        Where are you from?<span className="text-red-500">*</span>
                    </h2>
                    <div className="w-[316px]">
                        {' '}
                        {/* Add this wrapper div */}
                        <Select
                            options={countryOptions}
                            value={countryOptions.find(option => option.value === userData.country)}
                            onChange={option => onInputChange('country', option?.value || '')}
                            placeholder="Select country"
                            components={{ Option: CustomOption }}
                            styles={customStyles} // Apply the custom styles here
                        />
                    </div>
                    {errors.country && <p className="text-xs text-red-500 mt-1">{errors.country}</p>}
                </div>

                <div className="mb-20">
                    <h2 className="mb-2 text-[14px] font-[400]">
                        Phone Number<span className="text-red-500">*</span>
                    </h2>
                    <div className="flex rounded-full border border-[#0812287e] bg-gray-100 p-2">
                        <PhoneInput
                            international
                            countryCallingCodeEditable={false}
                            defaultCountry={(userData.country as any) || undefined}
                            value={userData.phoneNumber}
                            onChange={handlePhoneChange}
                            className="w-full max-w-[456px] text-black"
                            inputStyle={{
                                background: 'transparent',
                                border: 'none',
                                outline: 'none',
                                width: '100%',
                            }}
                        />
                    </div>
                    {errors.phoneNumber && <p className="text-xs text-red-500 mt-1">{errors.phoneNumber}</p>}
                </div>

                <p className="mb-4 text-[10px] text-[#929292]">
                    By signing up, you agree to our{' '}
                    <a href="#" className="text-[#2655FF] hover:underline">
                        Terms of Use
                    </a>{' '}
                    and{' '}
                    <a href="#" className="text-[#2655FF] hover:underline">
                        Privacy Policy
                    </a>
                    .
                </p>

                <Button variant="primary" type="submit" rounded disabled={isSigningUp}>
                    {isSigningUp && <ButtonSpinner />}Sign Up
                </Button>

            </form>

            <p className="mt-6 text-center text-[12px] font-[500] text-[#929292]">
                Already have an account?{' '}
                <a href="#" className="font-[600] text-[#2655FF] hover:underline">
                    Sign in now
                </a>
            </p>
        </div>
    )
};

export default AdditionalDetails;