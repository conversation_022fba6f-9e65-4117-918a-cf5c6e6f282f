import Image from 'next/image'
import React from 'react'
import heartlike from './icons/heartlike.svg'
import clock from './icons/clock.svg'
import Certificate from './icons/Certificate.svg'
import video from './icons/video.svg'
import { MdKeyboardArrowRight } from 'react-icons/md'
import { CourseModel } from '@/types/CourseModel'
import Link from 'next/link'

interface TopCourseProps {
    courses: CourseModel[]
}


const TopCourse = ({ courses }:TopCourseProps ) => {
    return (
        <section className="relative w-full gap-[42px] p-[32px] md:p-[64px]">
            <section className="gap-[12px]">
                <h2 className="text-[22px] font-[500] md:text-[36px]">Discover Top Course</h2>
                <p className="mt-2 text-[12px] font-[400] md:mt-3 md:text-[18px]">
                    Crypto University gives you the tools And strategies you need to analyze any Cryptocurrency projects
                    and become a highly profitable Cryptocurrency investor and trader. Join our community of over
                    15,000+ students
                </p>
            </section>

            <section className="mt-10 grid md:grid-cols-3 gap-[37px]">
                {courses.map((course: CourseModel, index: number) => (
                    <div key={index} className="gap-[17.55px] rounded-[8.18px] border-[1.02px] p-[16.36px]">
                        <Image src={course.image} alt="video" width={211.7} height={245.45} />
                        <h2 className="my-3 text-[18px] font-[500] md:text-[22px]">{course.name}</h2>
                        <p className="text-[12px] font-[400] md:text-[14px]">{course.short_description}</p>
                        <div className="my-3 flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="flex justify-center gap-1">
                                    <Image
                                        src={video}
                                        alt="web3"
                                        width={17.55}
                                        height={17.55}
                                        className="h-[17.55px] w-[17.55px] md:h-[20.55px] md:w-[20.55px]"
                                    />
                                    <h3 className="text-[11px] md:text-[14.36px]">52 videos</h3>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Image
                                        src={clock}
                                        alt="web3"
                                        width={17.55}
                                        height={17.55}
                                        className="h-[17.55px] w-[17.55px] md:h-[20.55px] md:w-[20.55px]"
                                    />
                                    <h3 className="text-[11px] md:text-[14.36px]">24 hours</h3>
                                    <Image
                                        src={Certificate}
                                        alt="web3"
                                        width={17.55}
                                        height={17.55}
                                        className="h-[17.55px] w-[17.55px] md:h-[20.55px] md:w-[20.55px]"
                                    />
                                </div>
                            </div>
                            <Image
                                src={heartlike}
                                alt="web3"
                                width={17.55}
                                height={17.55}
                                className="h-[17.55px] w-[17.55px] md:h-[20.55px] md:w-[20.55px]"
                            />
                        </div>

                        <div className="flex justify-between">
                            <div className="grid">
                                <h2 className="text-[14px] font-[500] md:text-[18px]">
                                    ${course.finalPrice - course.tax}
                                </h2>
                                {course.sale ? (
                                    <>
                                        <h5 className="flex items-center gap-1 text-callout font-medium max-md:text-sub2">
                                            ${course.finalPrice - course.tax}{' '}
                                            <span className="text-cap1 text-green-dark max-md:text-cap3">
                                                (-{course.sale}%)
                                            </span>
                                        </h5>
                                        <p className="text-[12px]  md:text-[15px]">
                                            ${parseInt(course.price || '0') - course.tax}
                                        </p>
                                    </>
                                ) : (
                                    <span className="text-[12px] text-gray-900 line-through md:text-[15px]">
                                        ${course.finalPrice}
                                    </span>
                                )}
                            </div>
                            <Link aria-label="Course page"
                        href={'/courses/' + course.slug} className="flex h-[38px] w-[158px] items-center justify-center rounded-[50px] bg-blue-primary text-[12px] text-white md:h-[57.27px] md:w-[206.14px] md:rounded-[51.14px] md:text-[14.36px]">
                                START NOW <MdKeyboardArrowRight className="mb-[2px] h-5 w-5 md:mb-1 md:h-6 md:w-6" />
                            </Link>
                        </div>
                    </div>
                ))}

            </section>
        </section>
    )
}

export default TopCourse
