import { Metadata } from 'next'
import { Blogs } from '@/types/BlogModel'
import { notFound } from 'next/navigation'
import Card from '@/components/Pages/Blog/Card'
import { capitalizeWords } from '@/lib/capitalize'
import Sections from '@/components/Pages/Blog/Sections'
import Pagination from '@/components/Pages/Blog/Pagination'
// import AuthorHero from '@/components/Pages/Blog/author/AuthorHero'
import SearchBlog from '@/components/Pages/Blog/SearchBlog'
import { BlogPost } from '@/types/BlogPost'

interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}


async function getPostsByCategory(slug: string, page?: number) {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/category/${slug}?pageNumber=${page}&pageSize=9`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data
    } catch (error) {
        console.error(error)
        return null
    }
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug as string
    if (slug !== undefined)
        try {
            const getPosts = await getPostsByCategory(slug, 1)
            const posts: Blogs = getPosts.posts;
            if (posts.length === 0) return {}
            else {
                // Find the first matching category name
                const categoryName = posts
                    .flatMap((blog: BlogPost) =>
                        blog.categories
                            .filter((category: any) => category.category.slug === params.slug)
                            .map((category: any) => category.category.name)
                    )[0] || 'Uncategorized';

                return {
                    title: categoryName + ' - Blog',
                    description:
                        'Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
                    openGraph: {
                        title: categoryName,
                        description:
                            'Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
                        type: 'website',
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: categoryName,
                    },
                }
            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const CategoryPosts = async ({
    params,
    searchParams,
}: {
    params: PageProps
    searchParams: { [key: string]: string | string[] | undefined }
}) => {
    const page = searchParams.page
        ? parseInt(searchParams.page as string) <= 0
            ? 1
            : parseInt(searchParams.page as string)
        : 1
    console.log("Fetching post for slug 2:", params.slug);
    const posts = await getPostsByCategory(params.slug, page)
    console.log("Post Data qAL:", posts);

    const count = posts ? posts.meta.totalCount : 0
    const blogs: Blogs = posts ? posts.posts : [];

    console.log("blogs.length: ", blogs.length);

    if (blogs.length === 0) notFound()

    // Find the first matching category name
    const categoryName = blogs
        .flatMap((blog: BlogPost) =>
            blog.categories
                .filter((category: any) => category.category.slug === params.slug)
                .map((category: any) => category.category.name)
        )[0] || 'Uncategorized';

    console.log("categoryName: ", categoryName);

    return (
        <>
            <SearchBlog name={categoryName} />

            <main className="container mx-auto mb-8 max-lg:mb-20 max-lg:space-y-14">
                <Sections>
                    <h3 className="mb-8 text-b3 font-semibold">All Blog Posts</h3>
                    <div className="grid grid-cols-3 gap-6 max-lg:grid-cols-2 max-md:grid-cols-1">
                        {(blogs || []).map((blog, index) => (
                            <Card key={index} {...blog} />
                        ))}
                    </div>
                </Sections>

                <Pagination page={searchParams.page} count={count} />
            </main>
        </>
    )
}

export default CategoryPosts
