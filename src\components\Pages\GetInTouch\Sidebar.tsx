import ReachUs from "@/components/Ui/ReachUs";
import ImageShortcut from "@/components/Ui/Image";
import getInTouch from "@public/marketing/getintouch.png";

const Sidebar = () => {
  return (
    <div className="flex flex-col justify-between max-md:grow bg-yellow-light gap-[4vh] px-16 max-w-[621px] h-full max-md:h-fit max-md:py-7 max-lg:px-3.5 max-md:px-4">
      <div>
        <ImageShortcut
          src={getInTouch}
          priority
          alt="get in touch"
          width={250}
          height={250}
          className="max-md:h-52 max-md:w-52 max-md:absolute object-contain max-md:top-0 max-md:right-0"
        />
        <h1 className="text-black text-start text-h3 max-md:max-w-[240px] leading-[72px] font-semibold sm:max-w-full max-md:text-b1 max-md:font-medium">
          Partnership
          <br className="sm:hidden" /> or <br className="lg:block hidden" />
          Business Inquiries
        </h1>
        <p className="text-black text-start text-sub2 max-w-md max-md:text-[14px] mt-4 max-md:mt-8">
          If you’re an enterprise level organization interested in training your
          teams with up-to-date content in the field from Crypto University,
          contact us.
        </p>
      </div>

      <ReachUs />
    </div>
  );
};

export default Sidebar;
