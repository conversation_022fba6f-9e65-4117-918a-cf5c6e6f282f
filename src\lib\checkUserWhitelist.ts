async function checkUserWhitelisted(slug:string, email:string) {
  const postData = {
    slug: slug,
    email: email,
  };

  try {
    const response = await fetch(`${process.env.API_URL}/course-whitelist/user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(postData),
    });

    if (response.ok) {
      const data = await response.json();
      return data.whitelisted;
    } else {
      console.error('Whitelisted Error:', response.statusText);
      throw new Error('Error checking whitelisted status');
    }
  } catch (error) {
    console.error('Whitelisted Error:', error);
    throw error;
  }
}

export default checkUserWhitelisted;
