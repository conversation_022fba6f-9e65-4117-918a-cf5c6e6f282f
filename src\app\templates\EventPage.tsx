'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface EventPageProps {
    title: string
    subtitle: string
    stats: Array<{ value: string; label: string }>
    leadDev: { name: string; title: string; company: string; image: string }
    mission: string
    enrollLink: string
    videoUrl: string
    videoThumbnail: string
    partners: Array<{ name: string; logo: string }>
    readyToStartTitle: string
    readyToStartDescription: string
    readyToStartChecklist: Array<string>
    networkDescription: string
    collaborationDescription: string
}

const EventPage: React.FC<EventPageProps> = ({ 
    title, 
    subtitle, 
    stats, 
    leadDev, 
    mission, 
    enrollLink, 
    videoUrl,
    videoThumbnail,
    partners,
    readyToStartTitle,
    readyToStartDescription,
    readyToStartChecklist,
    networkDescription,
    collaborationDescription
}) => {
    const [imageClicked, setImageClicked] = useState(false)

    const getImageSrc = (src: string) => src.startsWith('http') ? src : `${src}`

    return (
        <>
            {/* First View */}
            <div className="w-full bg-[#262216] p-5">
                <div className="mx-auto grid max-w-[400px] py-16 sm:max-w-[700px] md:max-w-[1400px]">
                    <div>
                        <h2 className="w-[343px] text-left text-[30px] font-[600] text-white sm:text-center md:w-[776px] md:text-[50px]">
                            {title}
                        </h2>
                        <div className="justify-between md:flex">
                            <div className="my-2 sm:my-8">
                                {stats.map((stat, index) => (
                                    <h3
                                        key={index}
                                        className="text-[26px] font-[500] leading-[72px] text-green md:text-[48px]">
                                        {stat.value}{' '}
                                        <span className="text-[28px] font-[500] leading-[42px] text-white">
                                            {stat.label}
                                        </span>
                                    </h3>
                                ))}
                            </div>
                            <section className="flex items-center justify-end gap-2 px-2">
                                {/* Add your yellow arrow and watch video text images here */}
                            </section>
                        </div>
                    </div>

                    {/* web */}
                    <section className="hidden justify-between md:flex">
                        <div className="relative">
                            <section className="relative my-4 hidden w-[452px] rounded-[8px] border border-[#FCC229] p-3 md:block">
                                <Image
                                    src={leadDev.image}
                                    alt={leadDev.name}
                                    width={142}
                                    height={142}
                                    className="absolute top-[-3px] ml-[365px]"
                                />
                                <h3 className="text-[24px] font-[600] text-[#FCC229]">Lead Dev</h3>
                                <h3 className="text-[32px] font-[600] text-white">{leadDev.name}</h3>
                                <h3 className="text-[18px] font-[400] text-[#D0D0DD]">
                                    {leadDev.title}, {leadDev.company}
                                </h3>
                            </section>
                            <section className="my-6">
                                <h3 className="text-[24px] font-[600] text-[#FCC229]">Mission</h3>
                                <h3 className="text-[32px] font-[600] text-white">{mission}</h3>
                            </section>
                            <Link href={enrollLink} target="_blank" className="my-3 mt-8">
                                <button className="rounded bg-[#FCC229] px-4 py-2 font-bold text-black">Enroll Now</button>
                            </Link>
                        </div>
                        <div className="pt-3">
                            {!imageClicked ? (
                                <div
                                    onClick={() => setImageClicked(true)}
                                    className="cursor-pointer"
                                >
                                    <Image
                                        src={getImageSrc(videoThumbnail)}
                                        alt="Video Thumbnail"
                                        width={641}
                                        height={361}
                                        className="h-[208px] w-[348px] md:h-[361px] md:w-[641px] transition-transform duration-300 transform hover:scale-105"
                                    />
                                </div>
                            ) : (
                                <iframe
                                    width="560"
                                    height="315"
                                    src={videoUrl}
                                    title="Event Video"
                                    frameBorder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                    allowFullScreen
                                ></iframe>
                            )}
                        </div>
                    </section>

                    {/* mobile */}
                    <section className="justify-center md:hidden">
                        <div className="">
                            {!imageClicked ? (
                                <>
                                    <div
                                        onClick={() => setImageClicked(true)}
                                        className="cursor-pointer"
                                    >
                                        <Image
                                            src={getImageSrc(videoThumbnail)}
                                            alt="devProgram"
                                            width={348}
                                            height={208}
                                            className="mx-auto h-[208px] w-[348px] md:h-[361px] md:w-[641px] transition-transform duration-300 transform hover:scale-105"
                                        />
                                    </div>
                                </>
                            ) : (
                                <>
                                    <iframe
                                        src="https://www.youtube.com/embed/R8yOwjWeIRA?si=3qOkGdP_YS5eH2AD"
                                        title="YouTube video player"
                                        frameBorder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                        referrerPolicy="strict-origin-when-cross-origin"
                                        allowFullScreen
                                        className="h-64 w-full md:h-[315px] md:w-[560px]"
                                    ></iframe>
                                </>
                            )}
                        </div>
                        <section className="relative mx-auto my-4 w-[271px] justify-center rounded-[8px] border border-[#FCC229] p-3  md:block md:w-[452px]">
                            <Image
                                src={leadDev.image}
                                alt={leadDev.name}
                                width={100}
                                height={100}
                                className="absolute left-[-50px] top-[-3px] h-[100px] w-[100px]" // Adjusted the positioning
                            />
                            <div className="pl-6">
                                <h3 className="text-[13px] font-[600] text-[#FCC229] md:text-[24px]">Lead Dev</h3>
                                <h3 className="text-[17px] font-[600] text-white md:text-[32px]">{leadDev.name}</h3>
                                <h3 className="text-[11px] font-[400] text-[#D0D0DD] md:text-[18px]">
                                   {leadDev.title}, {leadDev.company}
                                </h3>
                            </div>
                        </section>

                        <section className="my-6 justify-center">
                            <h3 className="text-[14px] font-[600] text-[#FCC229] md:text-[24px]">Mission</h3>
                            <h3 className="text-[18px] font-[600] text-white md:text-[32px]">
                                {mission}
                            </h3>
                        </section>
                        
                        <Link href={enrollLink} target="_blank" className="my-3 mt-8">
                            <button className="rounded bg-[#FCC229] px-4 py-2 font-bold text-black">Enroll Now</button>
                        </Link>
                    </section>
                </div>
            </div>

            {/* Second View */}
            <div className="w-full border bg-white p-5">
                <section className="mx-auto grid max-w-[400px] grid-cols-2 items-center justify-center gap-3 md:gap-4 border-[#D2D2D2] py-3 sm:max-w-[700px] md:mx-auto md:max-w-[1400px] md:grid-cols-5 md:justify-between md:p-10 md:py-16">
                    {partners.map((partner, index) => (
                        <div key={index} className="flex items-center justify-center text-center">
                            <Image
                                src={getImageSrc(partner.logo)}
                                alt={partner.name}
                                width={100}
                                height={28}
                                className="h-[28px] w-[100px] md:h-[58.9px] md:w-[157px]"
                            />
                            {index < partners.length - 1 && (
                                <Image src="/icons/line.svg" alt="line" width={2} height={6} className="hidden px-2 md:block md:px-4" />
                            )}
                        </div>
                    ))}
                </section>
            </div>

            <div className="mx-auto grid max-w-[400px] p-5 py-10 sm:max-w-[700px] md:max-w-[1400px]">
                <h2 className="text-[24px] font-[600] md:text-[36px]">{readyToStartTitle}</h2>
                <p className="mt-4 max-w-[1198px] text-[14px] font-[400] text-[#222222] md:text-[20px]">
                    {readyToStartDescription}
                </p>
                <ul className="my-3 grid gap-2 text-left text-[13.62px] font-[600] text-[#222222] md:my-7 md:text-[18px]">
                    {readyToStartChecklist.map((item, index) => (
                        <li key={index} className="flex gap-2">
                            <Image src="/icons/yellowcheckbutton.svg" width={20} height={20} alt="Check" className="mb-1" />
                            {item}
                        </li>
                    ))}
                </ul>
                <h3 className="my-3 text-[16px] text-[#2655FF] md:text-[22px]">
                    {networkDescription}
                </h3>
                <Link href={enrollLink} target='_blank' className="my-2">
                    <button className="rounded bg-[#FCC229] px-4 py-2 font-bold text-black">Enroll Now</button>
                </Link>
                <p className="my-4 max-w-[1049px] text-[14px] font-[500] italic text-[#5B5B5B] md:text-[18px]">
                    {collaborationDescription}
                </p>
            </div>
        </>
    )
}

export default EventPage
