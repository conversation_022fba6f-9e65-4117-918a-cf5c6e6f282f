import Nav from '@/components/Pages/Dashboard/Settings/Nav'
import { siteConfig } from '@/config/site'
import { getCurrentUser } from '@/lib/session'

export const metadata = {
    title: {
        default: siteConfig.name,
        template: `%s | ${siteConfig.name}`,
    },
    description: 'Setting page for your Crypto University account.',
}

interface DashboardLayotuProps {
    children: React.ReactNode
}

// ? Dynamic forcing or it will give deployment error
export const dynamic = 'force-dynamic'

export default async function DashboardLayotu({ children }: DashboardLayotuProps) {
    const user = await getCurrentUser()
    if (!user) return null
    return (
        <>
            <main className="flex flex-1 overflow-y-auto overflow-x-hidden">
                <section className="flex flex-col h-screen flex-grow pt-6 max-lg:h-auto max-lg:flex-col ">
                    <Nav />
                    <div className='border-b-4 border-[#E2E2E2] -mt-1' />
                    {children}
                </section>
            </main>
        </>
    )
}
