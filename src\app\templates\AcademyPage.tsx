'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface AcademyPageProps {
    title: string
    subtitle: string
    headerItems: Array<{ title: string }>
    courseOffers: Array<{ title: string; description: string; icon: string }>
    whyChooseUs: Array<{ title: string; description: string; icon: string }>
    videoThumbnail: string
    discordChatImage: string
}

const AcademyPage: React.FC<AcademyPageProps> = ({
    title,
    subtitle,
    headerItems,
    courseOffers,
    whyChooseUs,
    videoThumbnail,
    discordChatImage,
}) => {
    const getImageSrc = (src: string) => src && (src.startsWith('http') ? src : `${src}`)

    return (
        <>
            <section className="bg-[#F6F6F6] px-12 py-6 sm:py-12">
                <div className="sm:mx-[9rem]">
                    <h1 className="text-b3 sm:text-headline font-bold mb-4">{title}</h1>
                    <p className="text-sub3 mb-8">{subtitle}</p>
                    <div className="flex flex-wrap gap-4">
                        {headerItems.map((item, index) => (
                            <button key={index} className="bg-white text-black px-4 py-2 rounded-full text-sub3">
                                {item.title}
                            </button>
                        ))}
                    </div>
                </div>
            </section>

            <section className="px-12 py-6 sm:py-12">
                <div className="sm:mx-[9rem]">
                    <h2 className="text-b3 sm:text-headline font-bold mb-8">What We Offer</h2>
                    <div className="flex flex-col sm:flex-row">
                        <div className="w-full sm:w-1/3 border border-[#DADADA] flex justify-center">
                            <Image
                                src={getImageSrc(videoThumbnail)}
                                width={300}
                                height={500}
                                alt="thumbnail"
                                className="object-cover h-80 w-60 p-6"
                                priority={true}
                            />
                        </div>
                        <div className="grid grid-cols-1 gap-2 w-full sm:w-2/3 divide-y space-y-4 py-4">
                            {courseOffers.map((offer, index) => (
                                <div key={index} className="flex flex-row items-start">
                                    <div className="mr-4">
                                        <Image
                                            src={getImageSrc(offer.icon)}
                                            width={50}
                                            height={50}
                                            alt="thumbnail"
                                            className="object-contain h-13 w-auto mx-3"
                                            priority={true}
                                        />
                                    </div>
                                    <div>
                                        <h1 className="text-b3 sm:text-sub3 pl-2 mb-2 font-bold text-left mt-2">{offer.title}</h1>
                                        <p className="text-left text-sub3 pl-2">{offer.description}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            <section className="px-12 py-6 sm:py-12 bg-[#F6F6F6]">
                <div className="sm:mx-[9rem]">
                    <h2 className="text-b3 sm:text-headline font-bold mb-8">Why Choose Us?</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                        {whyChooseUs.map((reason, index) => (
                            <div key={index} className="flex flex-col items-center text-center">
                                <Image
                                    src={getImageSrc(reason.icon)}
                                    width={80}
                                    height={80}
                                    alt={reason.title}
                                    className="mb-4"
                                />
                                <h3 className="text-b3 sm:text-sub3 font-bold mb-2">{reason.title}</h3>
                                <p className="text-sub3">{reason.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            <section className="px-12 py-6 sm:py-12">
                <div className="sm:mx-[9rem] flex flex-col sm:flex-row items-center justify-center sm:justify-between text-center sm:text-left">
                    <div className="max-w-md sm:mr-6">
                        <div className="flex justify-center sm:justify-start">
                            <Image
                                src={getImageSrc(discordChatImage)}
                                width={300}
                                height={500}
                                alt="Discord Chat"
                                className="object-contain h-80 w-auto p-2"
                                priority={true}
                            />
                        </div>
                    </div>
                    <div className="mt-6 sm:mt-0">
                        <h1 className="text-b3 sm:text-headline font-bold mb-4">Join the P2P Community Today!</h1>
                        <p className="mb-6">Shape the Future of Finance</p>
                        <Link
                            aria-label="Join Discord"
                            href="https://discord.com/invite/M9cwwCP49c"
                            className="bg-[#2655FF] text-white hover:bg-blue-700 active:bg-blue-800 rounded-lg text-sub1 sm:text-sub2 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150 inline-block"
                        >
                            Join CU Discord {'>'}
                        </Link>
                    </div>
                </div>
            </section>
        </>
    )
}

export default AcademyPage
