"use client"
import ImageShortcut from '@/components/Ui/Image'
import Socials from './Socials'
import CopyLink from './CopyLink'
import Link from 'next/link'
import { Ad } from '@/types/AdModel'
import { useEffect, useState } from 'react'
import axios from 'axios';


async function fetchAllAdverts(search: string) {
    try {
        const response = await axios.get(`${process.env.API_URL}/ads?search=${search}&pageNumber=1&pageSize=10`);
        console.error(response.data.data);

        return response.data.data;
    } catch (error) {
        console.error(error);
        return null;
    }
}

function filterAdsByLocationAndLevel(ads: any[], location: string, level: number) {
    return ads.filter(ad => ad.location === location && ad.level === level);
}

const GlobalSide = () => {

    const [ads, setAds] = useState([]);
    const [filteredAdvertRight, setFilteredAdvertRight] = useState<Ad>();
    const [filteredAdvertRight2, setFilteredAdvertRight2] = useState<Ad>();
    const [filteredAdvertRight3, setFilteredAdvertRight3] = useState<Ad>();


    useEffect(() => {
        async function fetchAds() {
            const adsData = await fetchAllAdverts('post');
            console.log(adsData)
            if (adsData) {
                setAds(adsData);
            }
        }

        fetchAds();
    }, []);

    useEffect(() => {
        if (ads.length > 0) {
            const filteredLocationRight1 = filterAdsByLocationAndLevel(ads, 'right', 1);
            const filteredLocationRight2 = filterAdsByLocationAndLevel(ads, 'right', 2);
            const filteredLocationRight3 = filterAdsByLocationAndLevel(ads, 'right', 3);

            setFilteredAdvertRight(filteredLocationRight1[0]);
            setFilteredAdvertRight2(filteredLocationRight2[0]);
            setFilteredAdvertRight3(filteredLocationRight3[0]);
        }

    }, [ads]);

    return (
        <div className="min-w-[420px] max-w-[420px] space-y-11 max-md:w-full max-sm:min-w-full max-sm:max-w-full">
            <Socials />
            <CopyLink />

            {filteredAdvertRight?.url ? (
                <Link className='pt-4' target="_blank" href={filteredAdvertRight.url}>
                    <ImageShortcut
                        src={filteredAdvertRight.image}
                        alt={filteredAdvertRight.name}
                        priority
                        className="bg-[hsl(0,0%,98.4%,0.2)] bg-fixed opacity-0 transition duration-300 ease-in-out hover:opacity-100 object-cover max-sm:min-w-full max-sm:max-w-full pt-4"
                    />
                </Link>
            ) : (<span>No Adverts are available</span>)}

            {filteredAdvertRight2?.url ? (
                <Link className='py-4' target="_blank" href={filteredAdvertRight2.url}>
                    <ImageShortcut
                        src={filteredAdvertRight2.image}
                        alt={filteredAdvertRight2.name}
                        priority
                        className="bg-[hsl(0,0%,98.4%,0.2)] bg-fixed opacity-0 transition duration-300 ease-in-out hover:opacity-100 object-cover max-sm:min-w-full max-sm:max-w-full py-4"
                    />
                </Link>
            ) : (<span>No Adverts are available</span>)}

            {filteredAdvertRight3?.url ? (
                <Link target="_blank" href={filteredAdvertRight3.url}>
                    <ImageShortcut
                        src={filteredAdvertRight3.image}
                        alt={filteredAdvertRight3.name}
                        priority
                        className="bg-[hsl(0,0%,98.4%,0.2)] bg-fixed opacity-0 transition duration-300 ease-in-out hover:opacity-100 object-cover max-sm:min-w-full max-sm:max-w-full"
                    />
                </Link>
            ) : (<span>No Adverts are available</span>)}

        </div>
    )
}

export default GlobalSide
