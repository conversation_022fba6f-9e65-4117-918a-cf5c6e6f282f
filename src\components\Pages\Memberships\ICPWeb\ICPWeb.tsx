import React from 'react'
import './ICPWeb.css'
import Image from 'next/image'
import mark from '../../../../../public/icons/mark.svg'
import { MdKeyboardArrowRight } from 'react-icons/md'
import Link from 'next/link'
const ICPWeb = () => {
  return (
      <div className="bg mt-5 ">
          <div className="mx-auto w-full justify-between pt-20 md:flex md:pt-10">
              <div className=" mx-auto hidden text-white opacity-0 md:block">
                  <h2 className="text-[54px] font-[600]">Join The ICP Web 3.0 Developers Program</h2>
                  <p className="text-[20px] font-[400]">
                      We offer a range of support, including workshops, training, networking events, and access to
                      funding opportunities.
                  </p>
                  <ul className="my-5 text-[20px] font-[500]">
                      <li>Multiply Your Salary by 5-10x</li>
                      <li>Access Global Job Opportunities</li>
                      <li>Stay Ahead in the Tech Game</li>
                  </ul>
                  <button className="rounded-[8px] bg-[#FCC229] p-5 text-[14px]">Enroll Now</button>
              </div>
              <div className="p-5 pt-40 text-white md:float-right md:p-[64px] md:pt-10">
                  <h2 className="text-[32px] font-[600] md:text-[54px]">Join The ICP Web 3.0 Developers Program</h2>
                  <p className="text-[14px] font-[400] md:text-[20px]">
                      We offer a range of support, including workshops, training, networking events, and access to
                      funding opportunities.
                  </p>
                  <ul className="my-5 text-[14px] font-[500] md:text-[18px]">
                      <li className="my-1 flex items-center gap-2 md:my-2">
                          <Image src={mark} width={20} height={20} alt="mark" />
                          Multiply Your Salary by 5-10x
                      </li>
                      <li className="my-1 flex items-center gap-2 md:my-2">
                          {' '}
                          <Image src={mark} width={20} height={20} alt="mark" />
                          Access Global Job Opportunities
                      </li>
                      <li className="my-1 flex items-center gap-2 md:my-2">
                          {' '}
                          <Image src={mark} width={20} height={20} alt="mark" />
                          Stay Ahead in the Tech Game
                      </li>
                  </ul>
                  <Link className="h-[48px] w-[342px] text-black rounded-[8px] bg-[#FCC229] text-[14px] md:text-[18px] flex items-center  justify-center my-4 font-[600]" href={'/zambia-web3-developer-programme'}>Enroll Now <MdKeyboardArrowRight className="mb-[2px] md:mb-0 h-5 w-5 md:h-6 md:w-6" /></Link>
              </div>
          </div>
      </div>
  )
}

export default ICPWeb
