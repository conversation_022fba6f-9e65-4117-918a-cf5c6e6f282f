'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { usePathname } from 'next/navigation'

const buttons = [
    { name: 'Wallets', slug: 'wallets' },
    { name: 'NFT Tools', slug: 'nft-tools' },
    { name: 'Exchanges', slug: 'exchanges' },
    { name: 'Trading Tools', slug: 'trading-tools' },
    {
        name: 'Extra Resources',
        slug: 'extra-resources',
    },
]

const ListButtons = () => {
    const pathname = usePathname()

    return (
        <div className="flex flex-col">
            <div className="no-scrollbar z-10 flex items-center gap-[70px] overflow-hidden border-b-2 border-[#E5E5E5] max-md:overflow-x-scroll max-sm:gap-[20px]">
                {buttons.map((item, index) => (
                    <Link
                        aria-label={item.name}
                        className={cn(
                            'border border-transparent px-[15px] py-[5px] text-sub1 font-medium text-[black] transition-all duration-300 max-md:text-cap2',
                            pathname === '/resources/' + item.slug &&
                                'border-b-4 border-b-[#2655FF] text-[#2655FF] font-[700]'
                        )}
                        href={'/resources/' + item.slug}
                        key={index}>
                        <button key={index}>
                            <span className="whitespace-nowrap">{item.name}</span>
                        </button>
                    </Link>
                ))}
            </div>
        </div>
    )
}

export default ListButtons
