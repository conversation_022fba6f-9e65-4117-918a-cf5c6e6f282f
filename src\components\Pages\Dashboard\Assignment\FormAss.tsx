'use client'
import { usePathname } from 'next/navigation'
import ListButtons from './ListButtons'
import Title from './Title'
import { useState } from 'react'

interface Props {
    name: string
    title: string
    description: string
    user: any
    isAnsweredCheck: {
        id: number
        user_id: number
        assignment_id: number
        status: string
        pdf: string
        grade: number
        comment: string
    }
}

const FormAss = ({ name, title, description, user, isAnsweredCheck }: Props) => {
    const currentPath = usePathname()
    const [pdf, setPdf] = useState(null)
    return (
        <>
            <div className="w-full">
                <Title isAns={isAnsweredCheck && isAnsweredCheck?.status != 'return' } user={user} id={currentPath.split('/')[4]} pdf={pdf} name={name} title={title} />
            </div>
            <div className="max-md:w-full">
                <ListButtons user={user} id={currentPath.split('/')[4]}  pdf={pdf} Ans={isAnsweredCheck} setPdf={setPdf} description={description} />
            </div>
        </>
    )
}

export default FormAss
