'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { Counters } from './Counters'
import { Headline } from './Headline'
import { Logo } from '@public/marketing'
import { useRef, useState } from 'react'
import CustomSelect from './CustomSelect'
import Label from '@/components/Ui/Label'
import { ProfileBlack } from '@public/home'
import { Country } from '@/types/CountryModel'
import ImageShortcut from '@/components/Ui/Image'
import BackButton from '@/components/Ui/BackButton'
import Step0 from '@public/registerGamefied/step0.png'
import Step1 from '@public/registerGamefied/step1.png'
import Step2 from '@public/registerGamefied/step2.png'
import Step3 from '@public/registerGamefied/step3.png'
import Step4 from '@public/registerGamefied/step4.png'
import Step5 from '@public/registerGamefied/step5.png'
import Step6 from '@public/registerGamefied/step6.png'
import useMultistepForm from '@/hooks/useMultistepFrom'
import ProgressBar, { ProgressBarMob } from './Progress'
import { useTransition, animated } from '@react-spring/web'
import { Email, Eye, EyeOff, Location, Lock } from '@public/global'

const RegisterGamefied = ({ countries }: { countries: Country[] }) => {
    const { step, goBack, formik, showError, isLoading, nbOfSteps, registerError, disableSubmit } = useMultistepForm()

    const [showPassword, setShowPassword] = useState(false)

    const countryNames = countries.map((country: Country) => ({
        value: country.name.common,
        label: country.name.common,
    }))

    const codes = countries.map((country: Country) => ({
        value: country.idd.root + country.idd.suffixes,
        label: (
            <span className="flex gap-1">
                <ImageShortcut src={country.flags.svg} height={32} alt={country.name.common} width={32} />
                {country.idd.suffixes?.length == 1 ? country.idd.root + country.idd.suffixes : country.idd.root}
            </span>
        ),
    }))

    const transitions = useTransition(step, {
        from: { opacity: 0 },
        enter: { opacity: 1 },
        config: { duration: 150 },
    })

    const nameRef = useRef<HTMLInputElement>(null)
    const fullnameref = useRef<HTMLInputElement>(null)
    const emailRef = useRef<HTMLInputElement>(null)
    const countryRef = useRef<HTMLInputElement>(null)
    const phoneRef = useRef<HTMLInputElement>(null)
    const passwordRef = useRef<HTMLInputElement>(null)

    return (
        <div className="flex w-full flex-col bg-[#f9f9f9]">
            <header className="relative z-50 flex h-[80px] w-full select-none items-center border-b border-gray-700 bg-white max-[869px]:hidden">
                <Link
                    aria-label="Home page"
                    href="/"
                    className="absolute left-0 mx-24 flex items-center gap-1 max-lg:mx-6">
                    <Logo />
                </Link>
                <nav className="container mx-auto flex w-full max-w-2xl items-center justify-between">
                    {step != 6 && step != -1 && <ProgressBar currentStep={step} steps={nbOfSteps - 1} />}
                </nav>
            </header>

            <header className="z-50 hidden h-[80px] w-full select-none items-center border-b  border-gray-700 bg-white px-8 max-[869px]:flex">
                <Link aria-label="Home page" href="/" className="absolute left-0 flex items-center gap-1  max-lg:mx-3">
                    <Logo />
                </Link>
                <nav className="container mx-auto flex items-center justify-between px-14 max-[869px]:ml-8 max-md:pr-4">
                    {step != 6 && step != -1 && <ProgressBarMob currentStep={step} steps={nbOfSteps - 1} />}
                </nav>
            </header>
            {transitions((style, index) => (
                <animated.div
                    key={index}
                    style={style}
                    className={cn(
                        'relative flex h-full flex-col items-center bg-[#f9f9f9] pb-4 pt-[16vh]  max-lg:pt-[3vh]',
                        step === -1 ? 'max-md:pt-[7vh]' : 'max-md:pt-[1vh]'
                    )}>
                    <div
                        className={cn(
                            'container mx-auto flex w-full justify-between gap-4 px-4 max-lg:flex-col max-lg:items-center max-lg:gap-4 max-lg:p-4',
                            step === -1 && 'flex-col items-center justify-center',
                            step == 6 && 'flex-row-reverse justify-evenly'
                        )}>
                        <div
                            className={cn(
                                'flex flex-col justify-center gap-6 max-lg:flex-col-reverse max-lg:items-center max-lg:gap-2 max-lg:text-center',
                                step !== -1 ? 'max-w-[465px]' : 'w-full'
                            )}>
                            {step > 0 && step < 6 && (
                                <BackButton className="absolute top-20 max-lg:left-4 max-lg:top-10" onClick={goBack}>
                                    Prev
                                </BackButton>
                            )}
                            <Headline step={step} onStep6="lg:hidden" />
                            <ImageShortcut
                                src={
                                    step === 0
                                        ? Step0
                                        : step === 1
                                        ? Step1
                                        : step === 2
                                        ? Step2
                                        : step === 3
                                        ? Step3
                                        : step === 4
                                        ? Step4
                                        : step === 5
                                        ? Step5
                                        : Step6
                                }
                                alt={`Image for page`}
                                width={240}
                                height={240}
                                className={cn(
                                    'h-[240px] w-[240px] object-contain max-lg:h-[250px] max-lg:w-[250px] max-md:h-[180px] max-md:w-[180px]',
                                    (step === -1 || step === 6) && 'hidden'
                                )}
                            />

                            <ImageShortcut
                                src={Step6}
                                alt={`Image for page`}
                                width={552}
                                height={552}
                                className={cn(
                                    'h-[552px] w-[552px] object-contain max-lg:h-[350px] max-lg:w-[350px] max-md:h-[250px] max-md:w-[250px]',
                                    step !== 6 && 'hidden'
                                )}
                            />
                            <Counters step={step} className={cn(step !== -1 ? 'max-lg:hidden' : 'hidden')} />
                        </div>

                        <form
                            className="flex flex-col items-center justify-start max-sm:w-full"
                            onSubmit={formik.handleSubmit}>
                            {step === 0 && (
                                <div className="flex w-[520px] flex-col gap-3 max-sm:w-full">
                                    <Label required uppercase={false}>
                                        Username
                                    </Label>
                                    <div className="relative flex items-center">
                                        <div className="absolute left-4">
                                            <ProfileBlack />
                                        </div>
                                        <input
                                            ref={nameRef}
                                            onChange={e =>
                                                formik.setFieldValue('name', e.target.value.replace(/\s/g, ''))
                                            }
                                            autoFocus
                                            onBlur={formik.handleBlur}
                                            value={formik.values.name}
                                            name="name"
                                            id="name"
                                            placeholder="e.g JohnSmith"
                                            className="w-full rounded-[50px] border border-gray-700 bg-white px-12 py-[1.125rem] outline-none transition-[border] duration-150  placeholder:text-gray-700 focus:border-blue"
                                        />
                                    </div>
                                    <div
                                        className={cn(
                                            'bg invisible mb-1 min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                                            showError(step, nameRef) && 'visible'
                                        )}>
                                        <p className="select-none text-cap1">{formik.errors.name}</p>
                                    </div>
                                </div>
                            )}

                            {step === 1 && (
                                <div className="flex w-[520px] flex-col gap-3 max-sm:w-full">
                                    <Label required uppercase={false}>
                                        Full Name
                                    </Label>
                                    <div className="relative flex items-center ">
                                        <div className="absolute left-4">
                                            <Email />
                                        </div>
                                        <input
                                            ref={fullnameref}
                                            required
                                            autoFocus
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            value={formik.values.fullname}
                                            name="fullname"
                                            id="fullname"
                                            type="text"
                                            placeholder="e.g John Smith"
                                            className="w-full rounded-[50px] border border-gray-700 bg-white px-12 py-[1.125rem] outline-none transition-[border] duration-150  placeholder:text-gray-700 focus:border-blue"
                                        />
                                    </div>
                                    <div
                                        className={cn(
                                            'bg invisible mb-1 min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                                            showError(step, fullnameref) && 'visible'
                                        )}>
                                        <p className="select-none text-cap1">{formik.errors.fullname}</p>
                                    </div>
                                </div>
                            )}

                            {step === 2 && (
                                <div className="flex w-[520px] flex-col gap-3 max-sm:w-full">
                                    <Label required uppercase={false}>
                                        Email
                                    </Label>
                                    <div className="relative flex items-center ">
                                        <div className="absolute left-4">
                                            <Email />
                                        </div>
                                        <input
                                            ref={emailRef}
                                            required
                                            autoFocus
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            value={formik.values.email}
                                            name="email"
                                            id="email"
                                            type="text"
                                            placeholder="e.g John Smith"
                                            className="w-full rounded-[50px] border border-gray-700 bg-white px-12 py-[1.125rem] outline-none transition-[border] duration-150  placeholder:text-gray-700 focus:border-blue"
                                        />
                                    </div>
                                    <div
                                        className={cn(
                                            'bg invisible mb-1 min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                                            showError(step, emailRef) && 'visible'
                                        )}>
                                        <p className="select-none text-cap1">{formik.errors.email}</p>
                                    </div>
                                </div>
                            )}

                            {step === 3 && (
                                <div className="flex w-[520px] flex-col gap-3 max-sm:w-full">
                                    <Label required uppercase={false}>
                                        Country
                                    </Label>
                                    <div className="relative flex items-center">
                                        <div className="absolute left-4">
                                            <Location />
                                        </div>
                                        <CustomSelect
                                            style={{
                                                control: (provided, state) => ({
                                                    width: '100%',
                                                    borderWidth: '0px',
                                                    height: '100%',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'space-between',
                                                    paddingRight: '1.625rem',
                                                    paddingLeft: '2.563rem',
                                                }),
                                            }}
                                            innerRef={countryRef}
                                            isSearchable={true}
                                            formik={formik}
                                            label="Country"
                                            options={countryNames}
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            value={formik.values.country}
                                            name="country"
                                            id="country"
                                            className="relative w-full rounded-[50px] border border-gray-700 bg-white px-0 py-3 outline-none transition-[border] duration-150 placeholder:text-gray-700 focus:border-blue"
                                        />
                                    </div>
                                    <div
                                        className={cn(
                                            'bg invisible mb-1 min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                                            showError(step, countryRef) && 'visible'
                                        )}>
                                        <p className="select-none text-cap1">{formik.errors.country}</p>
                                    </div>
                                </div>
                            )}

                            {step === 4 && (
                                <div className="flex w-[520px] flex-col gap-3 max-sm:w-full">
                                    <Label required uppercase={false}>
                                        Phone Number
                                    </Label>
                                    <div className="relative flex items-center gap-2.5">
                                        <CustomSelect
                                            formik={formik}
                                            required
                                            defaultValue={codes[5]}
                                            label="Code"
                                            options={codes}
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            value={formik.values.phonePrefix}
                                            name="phonePrefix"
                                            id="phonePrefix"
                                            className="relative rounded-[50px] border border-gray-700 bg-white px-0 outline-none transition-[border] duration-150 placeholder:text-gray-700 focus:border-blue"
                                            style={{
                                                control: (provided, state) => ({
                                                    width: '8.75rem',
                                                    borderWidth: '0px',
                                                    height: '100%',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    paddingRight: '0.5rem',
                                                    paddingLeft: '0.8rem',
                                                    paddingTop: '0.875rem',
                                                    paddingBottom: '0.875rem',
                                                }),
                                                valueContainer: () => ({
                                                    padding: '0px',
                                                    height: '100%',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }),
                                                dropdownIndicator: state => ({
                                                    color: '#292929',
                                                    padding: '0rem',
                                                }),
                                                indicatorSeparator: state => ({
                                                    display: 'none',
                                                }),
                                                singleValue: state => ({
                                                    color: '#292929',
                                                    padding: '0rem',
                                                    paddingRight: '2rem',
                                                    cursor: 'default',
                                                }),
                                            }}
                                        />
                                        <input
                                            required
                                            ref={phoneRef}
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            value={formik.values.phoneSuffix}
                                            name="phoneSuffix"
                                            id="phoneSuffix"
                                            type="number"
                                            autoFocus
                                            placeholder="Enter mobile no."
                                            className="w-full rounded-[50px] border border-gray-700 px-4 py-[1.125rem] outline-none transition-[border] duration-150 placeholder:text-gray-700 focus:border-blue"
                                        />
                                    </div>
                                    <div
                                        className={cn(
                                            'bg invisible mb-1 min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                                            showError(step, phoneRef) && 'visible'
                                        )}>
                                        <p className="select-none text-cap1">
                                            {formik.errors.phoneSuffix} {'|' && formik.errors.phonePrefix}
                                        </p>
                                    </div>
                                </div>
                            )}

                            {step === 5 && (
                                <div className="flex w-[520px] flex-col gap-3 max-sm:w-full">
                                    <Label required uppercase={false}>
                                        Password
                                    </Label>
                                    <div className="relative flex items-center">
                                        <div className="absolute left-4">
                                            <Lock />
                                        </div>
                                        <input
                                            onInvalid={(e: any) => e.preventDefault()}
                                            ref={passwordRef}
                                            onChange={formik.handleChange}
                                            required
                                            onBlur={formik.handleBlur}
                                            value={formik.values.password}
                                            name="password"
                                            placeholder="enter your password"
                                            autoFocus
                                            type={showPassword ? 'text' : 'password'}
                                            id="password"
                                            className="w-full rounded-[50px] border border-gray-700 px-12 py-[1.125rem] outline-none transition-[border] duration-150  placeholder:text-gray-700 focus:border-blue"
                                        />
                                        <div
                                            onClick={() => setShowPassword(!showPassword)}
                                            className="absolute right-4 z-10 cursor-pointer">
                                            {showPassword ? <EyeOff /> : <Eye />}
                                        </div>
                                    </div>
                                    <div
                                        className={cn(
                                            'bg invisible mb-1 min-h-[57.7px]  rounded-lg bg-red/20 p-3 text-red',
                                            (showError(step, passwordRef) || registerError) && 'visible'
                                        )}>
                                        <p className="select-none text-cap1">
                                            {formik.errors.password ?? registerError}
                                        </p>
                                    </div>
                                </div>
                            )}

                            <div className="flex w-fit flex-col gap-3 self-start pt-3 max-lg:self-center">
                                <button
                                    type="submit"
                                    disabled={disableSubmit(step) || isLoading}
                                    className={cn(
                                        'gradient gradient rounded-[50px] px-20 py-5 text-sub1 font-semibold leading-none text-white disabled:cursor-not-allowed disabled:opacity-50',
                                        step === 6 && 'hidden',
                                        step === -1 && 'my-12 max-md:my-4 max-md:w-full'
                                    )}>
                                    {step === -1 ? 'Register Now >' : step < 5 ? 'Next >' : 'Submit'}
                                </button>

                                <div className={cn('flex max-w-lg flex-col gap-28 sm:gap-12', step !== 6 && 'hidden')}>
                                    <Headline step={step} onStep6="max-lg:hidden" />
                                    <Link
                                        aria-label="Join CU Discord Community"
                                        className={cn(
                                            'gradient gradient flex w-fit gap-3 rounded-[50px] px-11 py-5 text-sub1 font-semibold text-white disabled:cursor-not-allowed disabled:opacity-50 max-lg:px-20'
                                        )}
                                        href="https://discord.com/invite/M9cwwCP49c"
                                        target="_blank">
                                        <svg
                                            width="25"
                                            height="25"
                                            viewBox="0 0 65 65"
                                            fill="white"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M50.7431 15.4338C47.2476 13.8297 43.51 12.6639 39.6025 12C39.1226 12.851 38.562 13.9956 38.1754 14.9062C34.0217 14.2934 29.906 14.2934 25.8286 14.9062C25.4421 13.9956 24.8688 12.851 24.3846 12C20.4729 12.6639 16.731 13.8339 13.2354 15.4422C6.18493 25.8922 4.27369 36.0827 5.22931 46.1284C9.90561 49.5535 14.4374 51.6342 18.8928 52.9958C19.9929 51.5108 20.9739 49.9323 21.8191 48.2686C20.2095 47.6687 18.6677 46.9283 17.2109 46.0689C17.5974 45.788 17.9755 45.4944 18.3407 45.1923C27.2259 49.2685 36.88 49.2685 45.6591 45.1923C46.0287 45.4944 46.4067 45.788 46.7889 46.0689C45.3279 46.9325 43.7818 47.6729 42.1722 48.2729C43.0174 49.9323 43.9943 51.5151 45.0985 53C49.5582 51.6385 54.0942 49.5579 58.7705 46.1284C59.8918 34.4828 56.855 24.3859 50.7431 15.4338ZM23.0296 39.9504C20.3624 39.9504 18.175 37.508 18.175 34.5338C18.175 31.5598 20.3156 29.1132 23.0296 29.1132C25.7437 29.1132 27.931 31.5554 27.8843 34.5338C27.8885 37.508 25.7437 39.9504 23.0296 39.9504ZM40.9702 39.9504C38.3029 39.9504 36.1156 37.508 36.1156 34.5338C36.1156 31.5598 38.2561 29.1132 40.9702 29.1132C43.6842 29.1132 45.8716 31.5554 45.8249 34.5338C45.8249 37.508 43.6842 39.9504 40.9702 39.9504Z"
                                                fill="white"
                                            />
                                        </svg>
                                        {'Join Our Discord Server >'}
                                    </Link>
                                </div>
                            </div>
                        </form>

                        <Counters step={step} className={step !== -1 ? 'lg:hidden' : ''} />
                    </div>
                </animated.div>
            ))}
        </div>
    )
}

export default RegisterGamefied
