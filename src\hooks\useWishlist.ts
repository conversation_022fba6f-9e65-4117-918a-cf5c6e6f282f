import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { CourseModel } from '@/types/CourseModel'

interface WishlistStore {
    products: CourseModel[]
    addproduct: (product: CourseModel) => void
    productExists: (product: CourseModel) => boolean
    removeproduct: (id: number) => void
    clearWishlist: () => void
}

const useWishlist = create(
    persist<WishlistStore>(
        (set, get) => ({
            products: [],
            productExists: (product: CourseModel) => {
                const currentproducts = get().products
                const productExists = currentproducts.find(c => c.id === product.id)
                return !!productExists
            },
            addproduct: (product: CourseModel) => {
                const currentproducts = get().products
                const productExists = currentproducts.find(c => c.id === product.id)
                if (productExists) {
                    return
                }
                set({ products: [...get().products, product] })
            },
            removeproduct: (id: number) => {
                set({ products: [...get().products.filter(c => c.id !== id)] })
            },
            clearWishlist: () => {
                set({ products: [] })
            },
        }),
        {
            name: 'wishlist-storage',
            storage: createJSONStorage<WishlistStore>(() => localStorage),
        }
    )
)

export default useWishlist
