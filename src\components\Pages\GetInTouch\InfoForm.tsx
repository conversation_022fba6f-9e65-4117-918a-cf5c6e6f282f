'use client'
import { cn } from '@/lib/cn'
import fetch from '@/lib/fetch'
import { toast } from 'react-toastify'
import { Email } from '@public/global'
import { useRef, useState } from 'react'
import Label from '@/components/Ui/Label'
import CustomSelect from './CustomSelect'
import { ProfileBlack } from '@public/home'
import Button from '@/components/Ui/Button'
import { Country } from '@/types/CountryModel'
import ImageShortcut from '@/components/Ui/Image'
import ButtonSpinner from '@/components/Ui/buttonSpinner'
import { FormikProps, Yup, useFormik, FormikErrors } from '@/lib/formik'

interface TextBoxProps {
    setEmail?: any
    placeholder: string
    error?: string | undefined | string[] | FormikErrors<any> | FormikErrors<any>[]
    label: string
    className?: string
    Icon?: React.ReactNode
    CodeSelect?: React.ReactNode
    formik: FormikProps<any>
    id: ValidationSchemaKeys
    value: any
    onChange: any
    innerRef: React.RefObject<HTMLInputElement>
    type?: string
    isSelectFocus?: boolean
}
type ValidationSchemaKeys =
    | 'full_name'
    | 'phone_number'
    | 'country_code'
    | 'institution_name'
    | 'job_title'
    | 'enquiry'
    | 'expected_number_of_learners'
    | 'email'

const showError = (
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>,
    key: ValidationSchemaKeys,
    formik: FormikProps<any>
) => {
    if (ref.current) {
        ref.current.onfocus = () => {
            formik.setFieldTouched(key, false)
        }
    }
    return formik.touched[key] && (formik.errors as { [key: string]: string })[key]?.length > 0
}
const TextBox = ({
    placeholder,
    error,
    label,
    className,
    Icon,
    CodeSelect,
    id,
    onChange,
    value,
    innerRef,
    formik,
    type,
}: TextBoxProps) => {
    const [focus, setFocus] = useState(false)

    return (
        <div className={cn('flex h-fit w-full flex-col gap-3', className)}>
            <Label className="" required uppercase={false}>
                {label}
            </Label>

            <div className="flex flex-col gap-2 max-md:w-full">
                <div
                    onBlur={() => {
                        setFocus(false)
                    }}
                    onClick={() => setFocus(true)}
                    className={cn(
                        'relative flex flex-1 select-none flex-row items-center rounded-[50px] border border-gray-700 ',
                        focus && 'border-blue',
                        (id != 'phone_number'
                            ? showError(innerRef, id, formik)
                            : showError(innerRef, 'country_code', formik) || showError(innerRef, id, formik)) &&
                            'border-red focus:ring-red'
                    )}>
                    {Icon ? <div className="absolute left-4">{Icon}</div> : ''}
                    {CodeSelect ? (
                        <div
                            className={cn(
                                'flex  w-[120px] items-center rounded-full rounded-r-none border-r-0',
                                (showError(innerRef, 'country_code', formik) ||
                                    (showError(innerRef, id, formik) && !focus)) &&
                                    'border-red focus:ring-red'
                            )}>
                            {CodeSelect}
                            <div
                                className={cn(
                                    'z-30 mx-1 h-full border-l  border-gray-700 py-1.5',
                                    (showError(innerRef, 'country_code', formik) ||
                                        (showError(innerRef, id, formik) && !focus)) &&
                                        'border-red',
                                    focus && 'border-l border-blue'
                                )}></div>
                        </div>
                    ) : (
                        ''
                    )}
                    <input
                        autoComplete="new-password"
                        ref={innerRef}
                        type={id == 'email' ? 'email' : id == 'phone_number' ? 'number' : type ?? 'text'}
                        name={id}
                        id={id}
                        value={value}
                        onBlur={formik.handleBlur}
                        onChange={formik.handleChange}
                        className={cn(
                            'w-full rounded-full border border-gray-700 border-transparent px-4  py-[17.5px] pr-2 ring-0 focus:outline-none max-md:py-3.5 max-md:text-cap1',

                            Icon && 'pl-12',
                            CodeSelect && '!h-[62px] min-w-0 max-w-full grow rounded-l-none border-l-0'
                        )}
                        placeholder={placeholder}
                    />
                </div>
                {showError(innerRef, id, formik) && (
                    <div className="rounded-lg bg-red/20 p-3">
                        <p className="text-cap1 text-red">{error?.toString()}</p>
                    </div>
                )}
            </div>
        </div>
    )
}

export const InfoForm = ({ countries }: { countries: Country[] }) => {
    const [isLoading, setIsLoading] = useState(false)
    const codes = countries.map((country: Country) => ({
        value: country.idd.root + country.idd.suffixes,
        label: (
            <span className="max-md:gap0.5 flex gap-2">
                <ImageShortcut
                    src={country.flags.svg}
                    height={24}
                    alt={country.name.common}
                    width={24}
                    className="object-contain"
                />
                {country.idd.suffixes?.length == 1 ? country.idd.root + country.idd.suffixes : country.idd.root}
            </span>
        ),
    }))

    const formik = useFormik({
        initialValues: {
            full_name: '',
            email: '',
            phone_number: '',
            country_code: codes[34].value,
            institution_name: '',
            job_title: '',
            enquiry: '',
            expected_number_of_learners: '',
        },
        validationSchema: Yup.object({
            email: Yup.string().email('Invalid email address').required('Work email is required'),
            full_name: Yup.string().required('Full name is required'),
            phone_number: Yup.string().required('Phone number is required'),
            country_code: Yup.string().required('Phone number code is required'),
            institution_name: Yup.string().required('Institution name is required'),
            job_title: Yup.string().required('Job title is required'),
            enquiry: Yup.string().required('Enquiry is required'),
            expected_number_of_learners: Yup.number().required('Expected number of learners is required'),
        }),
        onSubmit: async (values: any) => {
            setIsLoading(true)
            const data = await fetch('/mail/get-in-touch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(values),
            })
            if (data.success == true) {
                setIsLoading(false)
                toast('✅ Email Sent!', {
                    position: 'bottom-right',
                    progress: undefined,
                    theme: 'light',
                })
                formik.setValues({
                    full_name: '',
                    email: '',
                    phone_number: '',
                    country_code: codes[34].value,
                    institution_name: '',
                    job_title: '',
                    enquiry: '',
                    expected_number_of_learners: '',
                })
                formik.setTouched({
                    full_name: false,
                    email: false,
                    phone_number: false,
                    country_code: false,
                    institution_name: false,
                    job_title: false,
                    enquiry: false,
                    expected_number_of_learners: false,
                })
            } else {
                setIsLoading(false)
                toast('❌ Error submitting the form!', {
                    position: 'bottom-right',
                    progress: undefined,
                    theme: 'light',
                })
            }
        },
        initialErrors: {
            full_name: '',
            email: '',
            phone_number: '',
            country_code: '',
            institution_name: '',
            job_title: '',
            enquiry: '',
            expected_number_of_learners: '',
        },
    })

    const [isFocus, setIsFocus] = useState(false)
    const nameRef = useRef<HTMLInputElement>(null)
    const emailRef = useRef<HTMLInputElement>(null)
    const jobTitleRef = useRef<HTMLInputElement>(null)
    const learnersRef = useRef<HTMLInputElement>(null)
    const phoneSuffixRef = useRef<HTMLInputElement>(null)
    const institutionRef = useRef<HTMLInputElement>(null)
    const enquiryRef = useRef<HTMLTextAreaElement | HTMLTextAreaElement>(null)

    return (
        <form
            className="grid h-fit w-full max-w-[1200px] grid-cols-2 place-content-start gap-6 font-sans max-lg:flex max-lg:flex-col"
            onSubmit={formik.handleSubmit}>
            <h1 className="col-span-2 w-full self-end text-start font-manrope text-b3 font-semibold">
                Fill this Information
            </h1>
            <TextBox
                error={formik.errors.full_name}
                innerRef={nameRef}
                formik={formik}
                value={formik.values.full_name}
                onChange={formik.handleChange}
                id="full_name"
                placeholder="e.g John Smith"
                label="Enter Full Name"
                Icon={<ProfileBlack />}
            />
            <TextBox
                error={formik.errors.email}
                innerRef={emailRef}
                formik={formik}
                value={formik.values.email}
                onChange={formik.handleChange}
                id="email"
                placeholder="e.g <EMAIL>"
                label="Work Email"
                Icon={<Email />}
            />
            <TextBox
                isSelectFocus={isFocus}
                error={`${formik.errors.country_code ?? ''} ${formik.errors.country_code ? '|' : ''} ${
                    formik.errors.phone_number ?? ''
                }`}
                innerRef={phoneSuffixRef}
                formik={formik}
                id="phone_number"
                label="Phone Number"
                placeholder="Enter mobile no."
                value={formik.values.phone_number}
                onChange={formik.handleChange}
                CodeSelect={
                    <CustomSelect
                        setIsFocus={setIsFocus}
                        style={{
                            control: (provided, state) => ({
                                width: '120px',
                                borderWidth: '0px',
                                height: 'auto',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingRight: '0px',
                                paddingLeft: '1rem',
                                borderRadius: '50px',
                                borderTopRightRadius: '0px',
                                borderBottomRightRadius: '0px',
                            }),
                            indicatorSeparator: (provided, state) => ({
                                display: 'none',
                            }),
                            dropdownIndicator: (provided, state) => ({
                                padding: '0px',
                            }),
                            valueContainer: (provided, state) => ({
                                padding: '0px',
                                alignItemd: 'center',
                                alignSelf: 'center',
                                display: 'flex',
                                flex: '1',
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                flexWrap: 'wrap',
                                position: 'relative',
                                overflow: 'hidden',
                                boxSizing: 'border-box',
                            }),
                            singleValue: (provided, state) => ({
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }),
                        }}
                        isSearchable={true}
                        formik={formik}
                        options={codes}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.country_code}
                        name="country_code"
                        id="country_code"
                        className="rounded-[50px]=relative w-full border-gray-700 px-0 placeholder:text-gray-700 focus:outline-none focus:ring-2 max-md:max-h-max  max-md:text-cap1"
                    />
                }
            />
            <TextBox
                type="number"
                error={formik.errors.expected_number_of_learners}
                innerRef={learnersRef}
                value={formik.values.expected_number_of_learners}
                onChange={formik.handleChange}
                formik={formik}
                id="expected_number_of_learners"
                label="Expected Number Of learners"
                placeholder="0"
            />
            <TextBox
                error={formik.errors.institution_name}
                innerRef={institutionRef}
                value={formik.values.institution_name}
                onChange={formik.handleChange}
                formik={formik}
                id="institution_name"
                label="Institution Name"
                placeholder="Select"
            />
            <TextBox
                error={formik.errors.job_title}
                innerRef={jobTitleRef}
                value={formik.values.job_title}
                onChange={formik.handleChange}
                formik={formik}
                id="job_title"
                label="Job Title"
                placeholder="Enter your designation"
            />
            <div className="col-span-2 flex h-fit w-full flex-col gap-3">
                <Label className="" required uppercase={false}>
                    Enquiry
                </Label>
                <textarea
                    ref={enquiryRef}
                    placeholder="For eg: Employee Training, Job-hunting Partnership, Industry Partnership"
                    className={cn(
                        'rounded-[43.1818px] border border-gray-700 px-4 py-3.5',
                        'w-full focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue',
                        'max-md:py-3.5 max-md:text-cap1',
                        showError(enquiryRef, 'enquiry', formik) && 'border-red focus:ring-red'
                    )}
                    name="enquiry"
                    id="enquiry"
                    rows={1}
                    value={formik.values.enquiry}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}></textarea>
                {showError(enquiryRef, 'enquiry', formik) && (
                    <div className="rounded-lg bg-red/20 p-3">
                        <p className="text-cap1 text-red">{formik.errors.enquiry?.toString()}</p>
                    </div>
                )}
            </div>
            <span className="pr-[50%]">
                <Button variant="primary" rounded type="submit" disabled={formik.isSubmitting || !formik.isValid}>
                    {isLoading && <ButtonSpinner />}Submit
                </Button>
            </span>
        </form>
    )
}
