import MarkdownComponent from '@/components/Ui/MarkdownComponent'
import { Certificate, Translate, Video } from '@public/briefly'
import Instructors from '../../../Ui/Instructors'
import InstructorModel from '@/types/InstructorModel'

interface Props {
    instructor: InstructorModel
    topic?:
        | {
              id: number
              name: string
              rank: number
              video_link: string
              description: string
          }
        | undefined
    lesson?:
        | {
              description: string
              id: number
              name: string
              rank: number
              video_link: string
              quizzes: [
                  {
                      id: number
                      question: string
                      choices: string[]
                      answer: string
                      lesson_id: number | null
                      course_id: number | null
                      created_at: Date
                      updated_at: Date
                      archived_course_id: number | null
                  }
              ]
              course_topics: {
                  id: number
                  name: string
                  rank: number
                  video_link: string
                  description: string
              }[]
          }
        | undefined
    numberOflessons: number
}

const Overview = ({ topic, instructor, lesson, numberOflessons }: Props) => {
    const data = [
        {
            title: 'Language & Subtitle',
            beginner: 'English Language',
            description: 'Subtitle: English',
            icon: Translate,
        },
        {
            title: 'Lecture',
            beginner: `${numberOflessons} Topics`,
            description: '',
            icon: Video,
        },
        {
            title: 'Certificate',
            beginner: 'Certificate completion',
            description: 'Get a certificate after completing',
            icon: Certificate,
        },
    ]
    return (
        <div id="overview" className="flex w-full flex-col gap-6">
            <div className="flex flex-col gap-3">
                <h1 className="text-sub1 font-medium capitalize">About This Topics</h1>
                <MarkdownComponent text={topic?.description ?? (lesson?.description as string)} />
            </div>
            <div className=" border-b border-gray-700" />
            <div className="grid grid-cols-3 gap-3 max-md:grid-cols-1">
                {data.map((item, index) => (
                    <div key={index} className="flex flex-col gap-2">
                        <h1 className="text-sub3">{item.title}</h1>
                        <div className="flex items-center gap-4">
                            <div className="rounded-full border border-gray-700 p-4">
                                <item.icon />
                            </div>
                            <div className="flex flex-col">
                                <h1 className="text-sub2 font-medium">{item.beginner}</h1>
                                <p className="text-sub3 text-gray-700">{item.description}</p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            <div className=" border-b border-gray-700" />
            <Instructors instructor={instructor} />
        </div>
    )
}

export default Overview
