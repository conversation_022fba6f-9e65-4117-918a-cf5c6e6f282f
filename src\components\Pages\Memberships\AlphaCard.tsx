import Link from 'next/link';
import { cn } from '@/lib/cn';
import Button from '@/components/Ui/Button';
import ImageShortcut from '@/components/Ui/Image';
import { AlphaGroupModel } from '@/types/AlphaGroupModel';
import { discount50,discount30 } from '@public/alpha-group';

interface Props {
    item: AlphaGroupModel;
    index: number;
}

const AlphaCard = ({ item, index }: Props) => {
    return (
        <div
            className={cn(
                'flex flex-col gap-7 rounded-3xl border-2 !border-[#EFB77C] p-[38px] m-2 shadow-md max-sm:p-6 justify-center items-center',
                index == 1 && 'membershipcard2'
            )}
            style={{ width: 'calc(90% - 1rem)' }}
        >
            <div className={cn('flex flex-col gap-[21px] max-sm:gap-3', index == 1 ? 'gap-10 max-sm:gap-6' : '')}>
                <div className="flex flex-col items-center gap-1">
                    <div className="relative flex w-full max-sm:static">
                        <ImageShortcut
                            src={item.icon.src}
                            height={150}
                            width={150}
                            className="absolute left-[76%] mt-[-117px] h-auto object-cover max-sm:right-0 max-sm:mt-[-70px] max-sm:w-[95px]"
                            alt={item.icon.alt}
                        />
                    </div>

                    <div className="relative flex w-full max-sm:static">
                        {item.plan.text === '50% off' ? ( 
                        <ImageShortcut
                            src={discount50}
                            width={40}
                            height={40}
                            alt="thumbnail"
                            className="absolute left-[76%] sm:left-[96%] mt-[50px] object-contain h-[4rem] w-[4rem] ml-2"
                            priority
                        />): ''
                        }
                       
                    </div>


                    <p className={`text-b3 text-center mt-6 uppercase max-sm:text-cap2 text-${item.sub.color}`}>
                        {item.billed.text}
                    </p>
                </div>
                <p className={`font-sans text-center text-h2 max-sm:text-sub1 text-${item.price.color}`}>
                    ${parseInt(item.price.text)}{' '}
                    <span className="text-sub1 font-normal max-sm:text-cap2">/m</span>
                </p>
                <div className={cn('flex flex-col gap-3 max-sm:gap-2 ', index == 1 ? 'gap-6 max-sm:gap-3' : '')}>
                    <p
                        className={cn(
                            'font-sans text-sub2 text-[#CFCFCF] text-center max-sm:text-capz',
                            index == 1 ? 'text-white' : ''
                        )}>
                        <div dangerouslySetInnerHTML={{ __html: item.sub.text }} />
                    </p>
                </div>
            </div>
            <Link aria-label="Membership page" href={item.plans} className="w-full">
                <Button
                    className={cn(
                        'uppercase !border-[#EFB77C] border-2 hover:!bg-gray-400  !text-white !bg-gradient-to-r from-[#EFB77C] to-[#6E4C31]',
                        index === 1
                            ? '!bg-gradient-to-r !from-[#fff] !to-[#fff] !text-[#EFB77C] hover:!bg-white/80 border-2  !border-white hover:!text-[#EFB77C]/80 active:!bg-white/90 active:!text-[#EFB77C]/90'
                            : ''
                    )}
                    variant="primary"
                >
                    {item.button.text}
                </Button>
            </Link>

            <div className={cn('flex flex-col gap-3 max-sm:gap-2 ', index == 1 ? 'gap-6 max-sm:gap-3' : '')}>
                <p
                    className={cn(
                        'font-sans text-sub2 text-[#CFCFCF] uppercase text-center max-sm:text-cap2',
                        index == 1 ? 'text-white' : ''
                    )}>
                    {item.plan.text}
                </p>
            </div>
        </div>
    );
};

export default AlphaCard;
