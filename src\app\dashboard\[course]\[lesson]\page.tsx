import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import { CourseDetailsModel, CourseModelV2 } from '@/types/CourseModel'
import Video from '@/components/Pages/Dashboard/Course/Video'

import {
    GetCourses,
    GetFirstTopic,
    GetOneCourse,
    getCourseById,
    getFirstLesson,
    getCurrentLesson,
    getStats,
} from '../../../../config/validationCourseDashboard'
import BreadCrumb from '@/components/Pages/Dashboard/Course/BreadCrumb'
import NPButtons from '@/components/Pages/Dashboard/Course/NPButtons'
import Buttons from '@/components/Pages/Dashboard/Course/Buttons'
import Overview from '@/components/Pages/Dashboard/Course/Overview'

interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}
type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
        lesson: string
    }
}

const LessonPage = async ({ searchParams, params }: Props) => {
    // Check if user is logged in
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

    // Get page and filters
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })
    if (courses === undefined) {
        redirect('/dashboard')
    }

    const courseId = getCourseById({ courses, params })
    const course = await GetOneCourse(user, courseId?.slug ?? '')
    const stat: CourseDetailsModel = (await getStats(course?.course.slug ?? '')) as CourseDetailsModel
    if (courseId === null || course === undefined) {
        redirect('/dashboard')
    }

    const firstLesson = getFirstLesson({ course })
    if (firstLesson === undefined) {
        redirect('/dashboard')
    }
    const currentLesson = getCurrentLesson({ course, params })

    const curentTopic = getCurrentLesson({ course, params })
    if (
        curentTopic !== undefined &&
        curentTopic.video_link !== undefined &&
        curentTopic.video_link !== null &&
        curentTopic.video_link !== ''
    ) {
        return (
            <section className="container mx-auto flex w-full flex-col items-start gap-9 py-8">
                <div className="flex w-full flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
                    <BreadCrumb />
                    <div className="max-md:hidden">
                        <NPButtons course={course?.course} />
                    </div>
                </div>

                <Video bannerImage={course.course.bannerImage} topic={currentLesson} />

                <div className="flex w-full justify-center md:hidden">
                    <NPButtons course={course?.course} />
                </div>
                <div className=" w-full border-b border-gray-700 md:hidden" />
                <Buttons slug={'/dashboard/' + params.course + '/' + params.lesson} />
                <Overview numberOflessons={stat?.count.topics} instructor={course.instructor} topic={currentLesson} />
            </section>
        )
    }
    if (currentLesson !== undefined) {
        const currentTopic = currentLesson.course_topics.find(topic => topic.rank === 1)
        const lessonName = currentLesson.name.toLowerCase().replace(/\s/g, '-')
        const topicName = currentTopic?.name.toLowerCase().replace(/\s/g, '-')
        redirect(`/dashboard/${courseId.slug}/${lessonName}/${topicName}`)
    }

    const firstTopic = GetFirstTopic({ course })
    if (firstTopic !== undefined) {
        const lessonName = firstLesson.name.toLowerCase().replace(/\s/g, '-')
        const topicName = firstTopic.name.toLowerCase().replace(/\s/g, '-')
        redirect(`/dashboard/${courseId.slug}/${lessonName}/${topicName}`)
    }

    redirect('/dashboard')
}

export default LessonPage
