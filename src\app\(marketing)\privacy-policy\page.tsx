import fs from 'fs'
import { matter, Markdown } from '@/lib/markdown'

export const metadata = {
    title: 'Privacy Policy',
    description: 'Privacy Policy for Crypto University',
    keywords: ['Privacy Policy', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const getPrivacyContent = () => {
    const file = `src/content/privacy/privacy-mdx.md`
    const content = fs.readFileSync(file, 'utf8')
    const matterResult = matter(content)
    return matterResult.content
}

const PrivacyPage = async () => {
    const PrivacyContent = getPrivacyContent()

    return (
        <section className="w-full overflow-hidden font-manrope text-black">
            <div className="bg-yellow-light py-14 max-md:pb-[3.125rem] max-md:pt-10">
                <div className="container mx-auto space-y-3">
                    <h1 className="text-headline font-semibold max-md:text-b1">Privacy Policy</h1>
                    <p>
                        <b className="text-sub3 font-medium max-md:text-cap1">Last updated:</b> May 5, 2023
                    </p>
                </div>
            </div>

            <div className="container prose mx-auto my-[3.125rem] text-[14px] leading-5 prose-a:text-blue hover:prose-a:text-blue/80 max-md:mb-16 max-md:mt-6 max-md:text-cap2">
                <Markdown>{PrivacyContent}</Markdown>
            </div>
        </section>
    )
}

export default PrivacyPage
