import { Metada<PERSON> } from 'next'
import fetchInstance from '@/lib/fetch'
import { notFound } from 'next/navigation'
import getCountryInfo from '@/lib/countries'
import { getCurrentUser } from '@/lib/session'
import { Country } from '@/types/CountryModel'
import { capitalizeWords } from '@/lib/capitalize'
import SuccessModel from '@/components/Pages/Checkout/SuccessModel'
import { CheckoutFormNew } from '@/components/Pages/Checkout/CheckoutFormNew'
import SuccessModelNewUser from '@/components/Pages/Checkout/SuccessModelNewUser'

interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}
interface PageSlugProps {
    referral?: string
    hash?: string
    newUser?: string
}

async function checkIfUserHasCourse(courseSlug: string, user: any) {
    const res = await fetchInstance(`/user-course/have-course?slug=${courseSlug}`, {
        headers: {
            Authorization: `Bearer ${user?.access_token}`,
        },
    })
    return res.status
}
async function getCourseFromParams(params: PageProps) {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await fetchInstance(`/course/byslug/${slug}`, { next: { revalidate: 60 } })
            return response.course
        } catch (error: any) {
            console.error('Course Error:', error)
            return undefined
        }
}
async function isSuccessPayment(searchParams: PageSlugProps, user: any) {
    const { hash, newUser } = searchParams
    if (hash !== undefined)
        try {
            if (newUser === 'true' && !user) return { user_status: false }
            else {
                const check = await fetch(`${process.env.API_URL}/payment/check_hax?hash=${hash as string}`, {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                })
                const data = await check.json()
                return data
            }
        } catch (error: any) {
            console.error('Hash Error:', error)
            return error
        }
    else return { statusCode: 402 }
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await getCourseFromParams({ slug })
            if (response === null) return {}
            else {
                return {
                    title: 'Checkout ' + capitalizeWords(response.name),
                    description: 'Checkout page for ' + response.name,
                    openGraph: {
                        title: response.title,
                        description: response.description,
                        type: 'website',
                        url: response.slug,
                        images: [
                            {
                                url: response.bannerImage,
                                width: 1200,
                                height: 630,
                                alt: response.title,
                            },
                        ],
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: response.title,
                        description: 'Checkout page for ' + response.name,
                        images: [response.bannerImage],
                    },
                }
            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const CheckoutPage = async ({ params, searchParams }: { params: PageProps; searchParams: PageSlugProps }) => {
    const course = await getCourseFromParams(params)
    const countries: Country[] = await getCountryInfo()

    if (course === null || course === undefined) notFound()

    const user = await getCurrentUser()

    let isEnrolled = false
    if (user !== null && user !== undefined) isEnrolled = await checkIfUserHasCourse(course.slug, user)

    if (searchParams.hash) {
        // const data = await isSuccessPayment(searchParams, user)
        return (

            <section className="mx-auto pb-12">
                {/* <CheckoutFormNew
                    countries={countries}
                    course={course}
                    user={user}
                    disabled={isEnrolled}
                /> */}
                { searchParams.newUser == 'true' ? (
                        <SuccessModelNewUser success={true} />
                    ) : (
                        <SuccessModel success={true} type='course' />
                    )
                    }
            </section>
            // <section className="mx-auto pb-12">
            //     <CheckoutFormNew
            //         countries={countries}
            //         course={course}
            //         user={user}
            //         disabled={isEnrolled}
            //     />
            //     {data?.status === true ? (
            //         searchParams.newUser == 'true' ? (
            //             <SuccessModelNewUser success={true} />
            //         ) : (
            //             <SuccessModel success={true} />
            //         )
            //     ) : (
            //         data?.user_status === false && <SuccessModel success={false} />
            //     )}
            // </section>
        )
    }

    return (
        <section className="mx-auto pb-12">
            <CheckoutFormNew
                countries={countries}
                course={course}
                user={user}
                disabled={isEnrolled}
            />
        </section>
    )
}

export default CheckoutPage


// import { Metadata } from 'next'
// import fetchInstance from '@/lib/fetch'
// import { notFound } from 'next/navigation'
// import getCountryInfo from '@/lib/countries'
// import { getCurrentUser } from '@/lib/session'
// import { Country } from '@/types/CountryModel'
// import { capitalizeWords } from '@/lib/capitalize'
// import SuccessModel from '@/components/Pages/Checkout/SuccessModel'
// import { CheckoutFormNew } from '@/components/Pages/Checkout/CheckoutFormNew'
// import SuccessModelNewUser from '@/components/Pages/Checkout/SuccessModelNewUser'

// interface MetaProps {
//     params: {
//         slug: any
//     }
// }
// interface PageProps {
//     slug: any
// }
// interface PageSlugProps {
//     referral?: string
//     hash?: string
//     newUser?: string
// }

// async function checkIfUserHasCourse(courseSlug: string, user: any) {
//     const res = await fetchInstance(`/user-course/have-course?slug=${courseSlug}`, {
//         headers: {
//             Authorization: `Bearer ${user?.access_token}`,
//         },
//     })
//     return res.status
// }
// async function getCourseFromParams(params: PageProps) {
//     const slug = params?.slug
//     if (slug !== undefined)
//         try {
//             const response = await fetchInstance(`/course/byslug/${slug}`, { next: { revalidate: 60 } })
//             return response.course
//         } catch (error: any) {
//             console.error('Course Error:', error)
//             return undefined
//         }
// }



// async function isSuccessPayment(searchParams: PageSlugProps, user: any) {
//     const { hash, newUser } = searchParams
//     if (hash !== undefined)
//         try {
//             if (newUser === 'true' && !user) return { user_status: false }
//             else {
//                 const check = await fetch(`${process.env.API_URL}/payment/check_hax?hash=${hash as string}`, {
//                     headers: {
//                         Authorization: `Bearer ${user?.access_token}`,
//                     },
//                 })
//                 const data = await check.json()
//                 return data
//             }
//         } catch (error: any) {
//             console.error('Hash Error:', error)
//             return error
//         }
//     else return { statusCode: 402 }
// }

// export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
//     const slug = params?.slug
//     if (slug !== undefined)
//         try {
//             const response = await getCourseFromParams({ slug })
//             if (response === null) return {}
//             else {
//                 return {
//                     title: 'Checkout ' + capitalizeWords(response.name),
//                     description: 'Checkout page for ' + response.name,
//                     openGraph: {
//                         title: response.title,
//                         description: response.description,
//                         type: 'website',
//                         url: response.slug,
//                         images: [
//                             {
//                                 url: response.bannerImage,
//                                 width: 1200,
//                                 height: 630,
//                                 alt: response.title,
//                             },
//                         ],
//                     },
//                     twitter: {
//                         card: 'summary_large_image',
//                         title: response.title,
//                         description: 'Checkout page for ' + response.name,
//                         images: [response.bannerImage],
//                     },
//                 }
//             }
//         } catch (error: any) {
//             console.error('Metadata:', error)
//             return error
//         }
//     else return {}
// }

// const CheckoutPage = async ({ params, searchParams }: { params: PageProps; searchParams: PageSlugProps }) => {
//     const course = await getCourseFromParams(params)
   
//     const countries: Country[] = await getCountryInfo()

//     if (course === null || course === undefined) notFound()

//     const user = await getCurrentUser()


//     let isEnrolled = false
//     if (user !== null && user !== undefined) isEnrolled = await checkIfUserHasCourse(course.slug, user)

//     if (searchParams.hash) {
//         const data = await isSuccessPayment(searchParams, user)
//         return (
//             <section className="mx-auto pb-12">
//                 <CheckoutFormNew
//                     countries={countries}
//                     course={course}
//                     user={user}
//                     disabled={isEnrolled}
//                     slug={params.slug}
//                 />
//                 {data?.status === true ? (
//                     searchParams.newUser == 'true' ? (
//                         <SuccessModelNewUser success={true} />
//                     ) : (
//                         <SuccessModel success={true} />
//                     )
//                 ) : (
//                     data?.user_status === false && <SuccessModel success={false} />
//                 )}
//             </section>
//         )
//     }

//     return (
//         <section className="mx-auto pb-12">
//             <CheckoutFormNew
//                 countries={countries}
//                 course={course}
//                 user={user}
//                 disabled={isEnrolled}
//                 slug={params.slug}
//             />
//         </section>
//     )
// }

// export default CheckoutPage
