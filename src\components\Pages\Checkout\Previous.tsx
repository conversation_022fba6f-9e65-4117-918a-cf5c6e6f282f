'use client'
import Link from 'next/link'
import BackButton from '@/components/Ui/BackButton'
import { useSearchParams } from 'next/navigation'

const Previous = () => {
    const router = useSearchParams().get('previous')
    if (router == null)
        return (
            <Link aria-label="Back" href={`/`}>
                <BackButton>Back</BackButton>
            </Link>
        )

    return (
        <Link aria-label="Back" href={router}>
            <BackButton>Back</BackButton>
        </Link>
    )
}

export default Previous
