import Image from 'next/image';
import bitcoin from '../../../../public/bitcoin.svg';

const TrendingCryptos = ({ trending }: any) => {
  return (
      <div className="mt-8 grid gap-4 rounded-[8px] border border-[#3E4550] sm:flex sm:justify-center">
          {trending.map((crypto: any) => (
              <div
                  key={crypto.name}
                  className={`max-w-80 flex w-full items-center justify-between border-b border-[#3E4550] p-4 text-white sm:border-b-0 sm:border-r ${
                      crypto.change > 0 ? 'bg-green-100 ' : 'bg-red-100 '
                  }`}>
                    <section className='flex gap-2'>
                         <Image src={bitcoin} alt={'coin-image'} width={32} height={32} className='sm:w-[48px] sm:h-[48px]' />   
                  <div className="grid">
                      <span className="sm:text-[24px text-[14px] font-bold text-white ">{crypto.name}</span>
                      <span className="text-[12px] text-[#959595] sm:text-[14px]">{crypto.Abr}</span>
                  </div>
                    </section>
                
                  <div className="grid ">
                      <span
                          className={`mt-2 flex justify-end text-[16px] sm:text-[18px] ${
                              crypto.change > 0 ? 'text-[#00B83F]' : 'text-[#FC2424]'
                          }`}>
                          {crypto.change > 0 ? '+' : ''}
                          {crypto.change}%
                      </span>
                      <span className="mt-2 text-[16px] sm:text-[18px]">${crypto.value}</span>
                  </div>
              </div>
          ))}
      </div>
  )
};

export default TrendingCryptos;
