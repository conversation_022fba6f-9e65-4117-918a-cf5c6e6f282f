// video-utils.js

export function replaceVideoExtension(link: string): string {
    if (!/^https?:\/\/(media\.)?publit\.io\/.*$/.test(link)) {
      return link;
    }
  
    const newVideoUrl = link.replace(/\.[^.]*$/, '.html?player=PLAYER1');
  
    return newVideoUrl;
  }
  
  export default function replaceVideoExtensionOrConvertToEmbedLink(link: string): string {
    const newVideoUrl = replaceVideoExtension(link);
  
    if (newVideoUrl === link) {
      return replaceVideoEmbedLink(link);
    }
  
    return newVideoUrl;
  }
  
  function replaceVideoEmbedLink(link: string): string {
    const baseUrl = 'https://media.publit.io/file/'
    const transformedDomain = 'www.youtube-nocookie.com'
    const embedPath = '/embed/'
    const vimeoDomain = 'vimeo.com'
    const vimeoPlayerUrl = 'https://player.vimeo.com/video/'
  
    if (link.includes('youtu.be')) {
      const videoId = link.split('/').pop()
      return `https://${transformedDomain}${embedPath}${videoId}`
    }
  
    if (link.includes('youtube.com/embed/')) {
      const videoId = link.split('/embed/')[1]
      return `https://${transformedDomain}${embedPath}${videoId}`
    }
  
    if (link.includes('youtube.com')) {
      const videoId = link.split('v=')[1]
      return `https://${transformedDomain}${embedPath}${videoId}`
    }
  
    if (link.includes(vimeoDomain)) {
      const videoId = link.split('/').pop()
      return `${vimeoPlayerUrl}${videoId}?h=c3a102c9fe&title=0&byline=0&portrait=0`
    }
  
    return link
  }