const Arrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 6 10" fill="none">
        <path
            d="M5.0405 8.5722L0.959961 5.00045L5.0405 1.42871"
            stroke="#292929"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)
const HeroSlugSkeleton = () => {
    return (
        <div className="bg-yellow-light py-8 text-black animate-pulse">
            <div className="container mx-auto flex">
                <div className="flex items-center gap-[1.125rem]">
                    <div className="rounded-full bg-gray-500/60 px-[10px] py-[8px]">
                        <Arrow />
                    </div>
                    <div className="bg-gray-500/60 w-[300px] max-sm:w-[200px] h-[20px]" />
                </div>
            </div>
        </div>
    )
}

export default HeroSlugSkeleton
