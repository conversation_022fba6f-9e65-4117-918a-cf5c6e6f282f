import Link from 'next/link'
import { Blog } from '@/types/BlogModel'
import { Markdown } from '@/lib/markdown'
import formatDate from '@/lib/formatBlogDate'
import ImageShortcut from '@/components/Ui/Image'

const Arrow = () => (
    <svg width="24" height="28" viewBox="0 0 24 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M7 21L17 11M17 11H7M17 11V21"
            stroke="#1A1A1A"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const Card = ({ ...blog }: Blog) => {
    const author = blog.author

    return (
        <div className="space-y-8 max-md:space-y-6">
            <Link aria-label="Post page" href={`/blog/${blog.slug}`}>
                {blog.image !== null ? (
                    <ImageShortcut
                        src={blog.image || ''}
                        width={416}
                        height={240}
                        alt={blog.title || ''}
                        // className="h-[240px] w-full bg-blue/60 object-contain max-md:h-[228px]"
                        className="h-[240px] w-full bg-black object-contain max-md:h-[228px]"
                    />
                ) : (
                    <div className="h-[240px] w-full animate-pulse bg-blue/60 object-contain max-md:h-[228px]" />
                )}
            </Link>

            <div className="space-y-6">
                {/* Info */}
                <div className="space-y-3 max-md:space-y-2">
                    <span className="text-[14px] font-medium leading-[20px] text-blue">
                        <Link
                            aria-label={author.name + ' page'}
                            href={`/blog/author/${author.slug}`}
                            className="hover:underline capitalize">
                            {author.name}
                        </Link>{' '}
                        • {formatDate(blog.published_date)}
                    </span>

                    <h4 className="text-sub2 font-medium">
                        <Link aria-label="post page" href={`/blog/${blog.slug}`} className="flex justify-between gap-4">
                            {blog.title}
                            <div>
                                <Arrow />
                            </div>
                        </Link>
                    </h4>

                    <div className="text-sub3 text-[#667085] max-w-[100%] overflow-hidden">
                        <Markdown>
                            {blog.excerpt
                                ?.replace(/(<([^>]+)>)/gi, '')
                                .replace(/&hellip;/g, '...')
                                .replace(/[\[\]']+/g, '')
                                .replace(/&nbsp;/g, ' ')
                                .replace(/&#8217;/g, "'")
                                .replace(/&#8220;/g, '"')
                                .replace(/&#8221;/g, '"')
                                .replace(/&#8211;/g, '-')
                                .replace(/&#038;/g, '&')
                                .replace(/&#8230;/g, '...')
                                .replace(/&#8216;/g, "'")
                                .replace(/&#8218;/g, "'")
                                .slice(0, 230) + ''}
                        </Markdown>
                    </div>
                </div>
                {/* Categories */}
                <div className="flex gap-2">
                    {blog.categories.length > 0 &&
                        blog.categories.map((category, index) => (
                            <Link
                                aria-label={category.category.name + ' page'}
                                href={`/blog/category/${category.category.slug}`}
                                key={index}
                                className="select-none rounded-full bg-gray-300 px-3 py-1 text-[14px] font-semibold leading-[20px] transition-colors duration-150 hover:bg-gray-400">
                                {category.category.name}
                            </Link>
                        ))}
                </div>
            </div>
        </div>
    )
}

export default Card
