import ImageShortcut from '@/components/Ui/Image'
import React from 'react'

interface HeaderProps {
    image?: string
    title?: string
    step: number
    numberOfQuizzes: number
}

const Header = ({ image, title, step, numberOfQuizzes }: HeaderProps) => {
    const progress = (step+1) / numberOfQuizzes *100
    return (
        <>
            <div className="bg-gray-300  py-4">
                <div className="container mx-auto flex items-center gap-5">
                    <ImageShortcut
                        src={image}
                        alt={title}
                        width={80}
                        height={80}
                        className="object-contain max-md:h-[60px] max-md:w-[60px]"
                    />
                    <div className="flex items-center gap-4 max-md:flex-col max-md:items-start max-md:gap-1">
                        <h1 className="text-callout font-medium capitalize max-md:text-cap1">{title}</h1>
                        <p className="text-b2 font-medium text-blue max-md:text-sub2">{'> '} Quiz</p>
                    </div>
                </div>
            </div>
            <div
                style={{ width: progress + '%' }}
                className={`w-[ h-[10px] bg-blue transition-all duration-150 max-md:h-[6px]` + progress + '%]'}
            />
        </>
    )
}

export default Header
