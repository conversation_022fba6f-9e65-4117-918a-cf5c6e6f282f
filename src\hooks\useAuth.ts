import axios from "@/lib/axios";
import { useState } from "react";
import { signIn } from "next-auth/react";

interface UserRegistration {
  display_name: string;
  fullname: string;
  email: string;
  password: string;
  country?: string;
  phone?: string;
  code?: string;
}
interface ForgotPassword {
  email: string;
}
interface ResetPassword {
  email: string;
  password: string;
  token: string;
}

const   useAuth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const reset = () => {
    setError("");
    setIsLoading(false);
  };

  const login = async (email: string, password: string, source: string) => {
    setError("");
    setIsLoading(true);
    try {
      const res = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });
      if(source !== 'coinw'){
        if (res?.ok && res?.error === null) window.location.href = "/dashboard";
      } else {
        // if (res?.ok && res?.error === null) window.location.href = "https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US";
        if (res?.ok && res?.error === null) window.location.href = "https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform?pli=1";
      }
      
      if (res?.error) {
        if (res?.error === "Request failed with status code 404")
          setError("We are having server issues. Please try again later.");
        else
          setError("Incorrect password.");
      }
    } catch (error: any) {
      setError(error.response.data.message);
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (registration: UserRegistration, source: string) => {
    setIsLoading(true);
    const { display_name, fullname, email, password, country, phone, code } =
      registration;
    const data = {
      display_name,
      first_name: fullname.split(" ")[0],
      last_name: fullname.split(" ").slice(1).join(" "),
      email,
      password,
      country_code: code,
      country,
      phone: phone + '',
    };
    try {
      await axios.post("/auth/register", data);
      const res = await signIn("credentials", {
        email: registration.email,
        password: registration.password,
        redirect: false,
        // callbackUrl: "/dashboard",
      });

      if(source === 'coinw'){
        // if (res?.ok && res?.error === null) window.location.href = "https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US";
        if (res?.ok && res?.error === null) window.location.href = "https://docs.google.com/forms/d/e/1FAIpQLSfzDuMMQLGWnCYXt65gXrw07aRnQhW7G21ryJwHw8Sva3eFJQ/viewform?pli=1";
      }
      // if (res?.ok) window.location.href = "/dashboard";
      return { ok: res?.ok };
    } catch (error: any) {
      if (error.response.data.message === "email_already_exists")
        setError("Email already exists.");
      else console.error(error.response.data.message);
    } finally {
      setIsLoading(false);
    }
  };

  const forgotPassword = async (data: ForgotPassword) => {
    setError("");
    setIsLoading(true);
    try {
      const res = await axios.post("/auth/forgotPassword", data);
      return res;
    } catch (error: any) {
      setError(error.response.data.message);
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (data: ResetPassword) => {
    setError("");
    setIsLoading(true);
    try {
      const res = await axios.post("/auth/resetPassword", data);
      return res;
    } catch (error: any) {
      // setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyEmail = async (email: string,) => {
    setError("");
    setIsLoading(true);
    try {
      const res = await axios.post("/auth/sendVerifyEmail", { email });
      return res;
    } catch (error: any) {
      setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: any) => {
    setError("");
    setIsLoading(true);
    try {
      const res = await axios.post("/auth/profile", data);
      return res;
    } catch (error: any) {
      setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateImage = async (data: any) => {
    setError("");
    setIsLoading(true);
    try {
      const res = await axios.post("/auth/profile", data);
      return res;
    } catch (error: any) {
      setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    error,
    reset,
    login,
    register,
    isLoading,
    verifyEmail,
    updateImage,
    updateProfile,
    resetPassword,
    forgotPassword,
  };
};

export default useAuth;
