'use client'
import { usePathname } from 'next/navigation'
import { toast } from 'react-toastify'

function removeHttp(url: string) {
    return url.replace(/^https?:\/\//, '')
}
const CopySVG = () => (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="32" height="32" rx="16" fill="#FCC229" />
        <path
            d="M11.1272 10.4699L11.125 12.0618V19.9382C11.125 20.5845 11.3818 21.2044 11.8389 21.6614C12.296 22.1185 12.916 22.3752 13.5625 22.3752H20.0245C19.9081 22.7042 19.6925 22.989 19.4075 23.1904C19.1225 23.3918 18.782 23.5 18.433 23.5H13.5625C12.6177 23.5 11.7115 23.1247 11.0434 22.4568C10.3753 21.7888 10 20.8829 10 19.9382V12.0618C10 11.3269 10.4702 10.7008 11.1272 10.4699ZM20.3125 8.5C20.5341 8.5 20.7535 8.54364 20.9583 8.62843C21.163 8.71322 21.349 8.83749 21.5057 8.99416C21.6624 9.15083 21.7867 9.33682 21.8715 9.54151C21.9564 9.74621 22 9.9656 22 10.1872V19.9352C22 20.1568 21.9564 20.3762 21.8715 20.5809C21.7867 20.7856 21.6624 20.9715 21.5057 21.1282C21.349 21.2849 21.163 21.4092 20.9583 21.4939C20.7535 21.5787 20.5341 21.6224 20.3125 21.6224H13.5625C13.1149 21.6224 12.6857 21.4446 12.3693 21.1282C12.0528 20.8118 11.875 20.3827 11.875 19.9352V10.1872C11.875 9.7397 12.0528 9.31056 12.3693 8.99416C12.6857 8.67775 13.1149 8.5 13.5625 8.5H20.3125ZM20.3125 9.62478H13.5625C13.4133 9.62478 13.2702 9.68403 13.1648 9.78949C13.0593 9.89496 13 10.038 13 10.1872V19.9352C13 20.2457 13.252 20.4976 13.5625 20.4976H20.3125C20.4617 20.4976 20.6048 20.4383 20.7102 20.3329C20.8157 20.2274 20.875 20.0844 20.875 19.9352V10.1872C20.875 10.038 20.8157 9.89496 20.7102 9.78949C20.6048 9.68403 20.4617 9.62478 20.3125 9.62478Z"
            fill="white"
        />
    </svg>
)

const CopyLink = () => {
    const path = usePathname()

    return (
        <div className="flex items-center justify-between gap-[2px] rounded-lg bg-gray-300 py-3 pl-5 pr-4">
            <div className="space-y-3">
                <p className="text-sub3 font-semibold">Copy Link</p>
                <p className="text-sub3">
                    {(removeHttp(process.env.NEXT_PUBLIC_BASE_URL as string) + path.substring(1)).length > 38
                        ? (removeHttp(process.env.NEXT_PUBLIC_BASE_URL as string) + path.substring(1)).substring(0, 35) + '...'
                        : removeHttp(process.env.NEXT_PUBLIC_BASE_URL as string) + path.substring(1)}
                </p>
            </div>

            <button
                type="button"
                onClick={() => {
                    navigator.clipboard.writeText(process.env.NEXT_PUBLIC_BASE_URL + path.substring(1))
                    toast.success('Link copied!')
                }}
                className="cursor-pointer rounded-full shadow transition-[box-shadow] active:shadow-none">
                <CopySVG />
            </button>
        </div>
    )
}

export default CopyLink
