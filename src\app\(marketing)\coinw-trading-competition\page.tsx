import CoinWCompetition from '@/components/Pages/CoinWCompetition/Competition'
import FinalComponent from '@/components/Pages/CoinWCompetition/FinalComponent'
import CoinWFAQ from '@/components/Pages/CoinWCompetition/CoinWFAQ'

export const metadata = {
    title: 'CoinW Trading Competition',
    description:
        " The promotion is offered by a world-class cryptocurrency exchange CoinW. CoinW Trading competition - Apr is for clients entering into this competition, you acknowledge that you understand the risks involved in trading.",
    keywords: [
        'Memberships',
        'Crypto University',
        'Crypto U',
        'Crypto',
        'Blockchain',
        'Cryptocurrency',
        'Alpha Group',
        'Crypto Expert',
        'Crypto Investor',
        'Crypto Trader',
    ],
    openGraph: {
        images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1713295117/CoinWCompetitionThumbnail1200x628_1_tqanuj.jpg',
      },
}

const CoinWCompetitionPage = async () => {
    return (
        <div className="w-full bg-[#222121]">
            <CoinWCompetition/>
            <CoinWFAQ/>
            <FinalComponent/>
        </div>
    )
}

export default CoinWCompetitionPage
