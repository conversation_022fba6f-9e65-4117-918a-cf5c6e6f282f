"use client"
import React, { useState } from 'react';
import html2canvas from 'html2canvas';

interface CertificateProps {
    studentName: string;
    courseName: string;
    completionDate: string;
}

const CertificateGenerator: React.FC<CertificateProps> = ({ studentName, courseName, completionDate }) => {
    const [imageGenerated, setImageGenerated] = useState(false);

    const generateImage = () => {
        // Select the element containing your content
        const element:any = document.getElementById('content');

        // Use html2canvas to convert the content to an image
        html2canvas(element).then(canvas => {
            // Convert the canvas to a base64 image data
            const imgData = canvas.toDataURL('image/png');

            // Create a temporary link element
            const link = document.createElement('a');
            link.href = imgData;
            link.download = 'certificate.png'; // Set the filename here

            // Programmatically click the link to trigger download
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);

            // Update state to indicate image generation
            setImageGenerated(true);
        });
    };

    return (
        <div>
            <div id="content" style={{ backgroundImage: "url('/certificates/CryptoUCertificateTemplate.jpg')", width: '100%', height: '100vh', backgroundSize: 'cover' }}>
                <h1 style={{ color: '#3A56FA', fontWeight: 'bold', fontSize: '40px', textAlign: 'center', paddingTop: '36vh' }}>{studentName}</h1>
                <h1 style={{ color: '#000', fontWeight: 'bold', fontSize: '28px', textAlign: 'center', paddingTop: '10vh' }}>{courseName}</h1>
                <h1 style={{ color: '#000', fontWeight: 'bold', fontSize: '28px', textAlign: 'center', paddingTop: '10vh' }}>{completionDate}</h1>
            </div>
            {!imageGenerated && <button onClick={generateImage}>Generate Image</button>}
            {imageGenerated && <p>Image Generated!</p>}
        </div>
    );
};

export default CertificateGenerator;
