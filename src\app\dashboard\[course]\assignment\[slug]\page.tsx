import BreadCrumb from '@/components/Pages/Dashboard/Assignment/BreadCrumb'
import NPButtons from '@/components/Pages/Dashboard/Course/NPButtons'
import { GetAssignmentsCourse, GetCourses, GetOneAssignment, GetOneCourse, getCourseById } from '@/config/validationCourseDashboard'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { CourseModelV2 } from '@/types/CourseModel'
import { redirect } from 'next/navigation'
import Form from '@/components/Pages/Dashboard/Assignment/FormAss'
import fetchInstance from '@/lib/fetch'
type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
        lesson: string
        slug: string
    }
}
interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

const CourseAss = async ({ searchParams, params }: Props) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })
    if (courses === undefined) {
        redirect('/dashboard')
    }

    const courseId = getCourseById({ courses, params })
    const course = await GetOneCourse(user, courseId?.slug ?? '')

    if (courseId === null || course === undefined) {
        redirect('/dashboard')
    }
    const Assignments: AssignmentModel[] = await GetAssignmentsCourse(user, courseId?.slug ?? '')
    if (Assignments === undefined) {
        redirect('/dashboard')
    }
    //check if the assignments id is the same as the params.slug
    const checkAssignment = Assignments.find(assignment => assignment.id === parseInt(params.slug))
    if (checkAssignment === undefined) {
        redirect('/dashboard/' + courseId?.slug)
    }
    const isAnswered = async () => {
        try {
            const response = await fetchInstance('/user-assignment/user/' +params.slug, {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response.user_assignment
        } catch (error) {
            console.error(error)
            return undefined
        }
    }
    const isAnsweredCheck = await isAnswered()

    const OneAss : AssignmentModel = await GetOneAssignment(user, params.slug)
    return (
        <section className="container mx-auto flex w-full flex-col  items-start gap-9 py-8">
            <div className="flex w-full flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
                <BreadCrumb />
                <div className="max-md:hidden">
                    <NPButtons course={course?.course} />
                </div>
            </div>
            <Form isAnsweredCheck={isAnsweredCheck}  user={user} description={OneAss.description} name={OneAss.name} title={courseId?.slug.split('-').join(' ')+" assignment "+OneAss.id} />
        </section>
    )
}

export default CourseAss
