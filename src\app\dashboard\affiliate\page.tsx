import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import fetchInstance from '@/lib/fetch'
import Header from '@/components/Pages/Dashboard/Affiliate/Header'
import Guest from '@/components/Pages/Dashboard/Affiliate/Guest'
import AffiliateDashboard from '@/components/Pages/Dashboard/Affiliate/AffiliateDashboard'

export const metadata = {
    title: 'Affiliate',
    description: 'Affiliate page for your Crypto University account.',
}

interface Model {
    affiliate: {
        total_unpaid: number
        total_withdrawn: number
        referral_count: number
        totalPages: number
        commission_rate: number
        earnings: number
        affiliateCode: string
    }
}

const affiliateLink = async (user: any) => {
    try {
        const res = await fetchInstance('/auth/profile/affiliate', {
            headers: {
                Authorization: `Bearer ${user?.access_token}`,
            },
            next: {
                revalidate: 10,
            },
        })
        return res.affiliateCode
    } catch (error) {
        console.error(error)
    }
}

const AffiliatePage = async () => {
    const user = await getCurrentUser()
    if (!user) redirect(authOptions?.pages?.signIn || '/')

    let link = ''
    let finalLink = ''
    if (user?.is_affiliate) {
        link = await affiliateLink(user)
        finalLink = (process.env.NEXT_PUBLIC_BASE_URL + 'ref/' + link) as string
    }

    const GetAffiliateStats = async () => {
        try {
            const response = await fetchInstance(
                '/affiliate/statistics',
                {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                }
            )
            return response
        } catch (error) {
            console.error('Error Users: ', error)
            return error
        }
    }
    const accessToken = user?.access_token || ''
    const affiliateStats: Model = await GetAffiliateStats()
    return (
        <section className="flex w-full flex-col gap-14 text-black">
            {user?.is_affiliate ? (
                <AffiliateDashboard affiliateStats={affiliateStats} token={accessToken} />
            ) : (
                <>
                    <Header user={user} />
                    <div className="flex w-full items-center justify-center">
                        <Guest />
                    </div>
                </>
            )}

        </section>
    )
}

export default AffiliatePage
