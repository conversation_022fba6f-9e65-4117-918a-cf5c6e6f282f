import CourseCard from "../Home/CourseCard";

const Courses = ({ courses }: any) => {
  return (
    <section
      id="courses"
      className="border-b border-gray-700 max-md:border-none"
    >
      <div className="container mx-auto">
        <div className="max-md:border-b max-md:border-gray-700 space-y-8 max-md:space-y-3 pt-11 max-md:pt-9 pb-16 max-md:pb-14">
          <h3 className="text-headline max-md:text-callout font-medium">
            Top courses in Blockchain
          </h3>

          <div className="grid grid-cols-3 max-lg:grid-cols-2 max-md:grid-cols-1 gap-9 max-md:gap-[1.125rem]">
            {courses.length > 0 ? (
              courses.map((course: any) => (
                <CourseCard
                  key={course.id}
                  course = {course}
                />
              ))
            ) : (
              <p>No courses available</p>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Courses;
