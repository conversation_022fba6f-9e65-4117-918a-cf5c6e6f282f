import Card from "./Card";
import InformData from "./inform";
import Card1 from "@public/help/card1.png";
import Card2 from "@public/help/card2.png";
import { Country } from "@/types/CountryModel";

const Right = ({ countries }: { countries: Country[] }) => {
  const data = [
    {
      title: "FAQs",
      description: "Read our frequently asked questions which might help you.",
      link: "/faqs",
      img: Card1,
    },
    {
      title: "Contact Us",
      description: "Call us or text us on WhatsApp for faster assistance.",
      link: "/join-our-community",
      img: Card2,
    },
  ];

  return (
    <div className="flex flex-col py-14 pl-20 pr-16 w-full gap-14 max-md:py-[30px] max-md:px-4 max-w-[1200px]">
      {/* Cards */}
      <div className="flex justify-between gap-8 max-lg:flex-col">
        {data.map((item, index) => (
          <Card
            key={index}
            title={item.title}
            description={item.description}
            link={item.link}
            img={item.img}
          />
        ))}
      </div>

      {/* Divider */}
      <div className="flex items-center gap-7 w-full text-[#DDDDDD]">
        <div className="border-b border w-full text-b3" /> OR
        <div className="border-b border w-full" />
      </div>

      {/* Form */}
      <InformData countries={countries} />
    </div>
  );
};

export default Right;