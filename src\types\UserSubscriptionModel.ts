import { user } from "./UserModel"
import { SubscriptionModel } from "./SubscriptionModel"

 export default interface UserSubscriptionModel {
    id: number
    status: string
    affiliate_id: number | null
    auto_renewal_status: string
    start_date: string
    expiry_date: string
    created_at: string
    updated_at: string
    user_id:  number | null
    subscription_id:   number | null
    recurring_payment: string
    order_id: string,
    paymentMethod: string,
    users: user;
    subscriptions:SubscriptionModel;
}
