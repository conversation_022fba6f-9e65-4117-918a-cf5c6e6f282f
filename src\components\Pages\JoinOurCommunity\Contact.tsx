'use client'
import Link from 'next/link'
import React from 'react'
import <PERSON><PERSON> from '../../Ui/Button'
import ImageShortcut from '../../Ui/Image'

const Contact = () => {
    return (
        <section id="contact" className="container mx-auto overflow-hidden pb-20 font-sans max-md:pb-16">
            <div className="flex w-full flex-wrap items-center gap-6 border-gray-700 max-md:flex-col max-md:items-start max-md:gap-0 max-md:border-t max-md:pt-12">
                {/* Question */}
                <h3 className="max-w-[287px] font-manrope text-headline font-medium max-md:max-w-full max-md:text-b3">
                    Looking to partner with us?
                </h3>

                {/* CTA */}
                <div className="flex- flex items-center gap-6 max-md:flex-col max-md:items-start max-md:gap-0">
                    <ImageShortcut
                        src={'/join/contact.png'}
                        width={200}
                        height={200}
                        className={'max-md:h-auto max-md:w-[190px]'}
                        alt={'Contact Icon'}
                    />
                    <div className="flex flex-col gap-7">
                        <div className="flex flex-col gap-2 max-md:gap-[0.375rem]">
                            <p className="text-sub2 font-semibold">You can also reach us on:</p>
                            <div className="text-sub2 text-blue">
                                <Link aria-label="Contact Us" href={'mailto:<EMAIL>'}>
                                    E-mail: <EMAIL>
                                </Link>
                            </div>
                        </div>
                        <div className="w-[310px] max-md:w-[220px]">
                            <Button gradient variant="primary" rounded>
                                <Link aria-label="Contact Us" href={'mailto:<EMAIL>'}>
                                    Email Now
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Contact
