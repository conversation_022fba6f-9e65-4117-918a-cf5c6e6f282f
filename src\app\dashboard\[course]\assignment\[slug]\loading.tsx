import React from 'react'

const loading = () => {
    return (
        <section className="container mx-auto flex w-full flex-col  items-start gap-9 py-8">
            <div className="flex w-full flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
                <div className="h-5 w-80 animate-pulse rounded-3xl bg-gray-500" />
                <div className="flex gap-8 max-md:hidden">
                    <div className="h-5 w-20 animate-pulse rounded-3xl bg-gray-500" />
                    <div className="h-5 w-20 animate-pulse rounded-3xl bg-gray-500" />
                </div>
            </div>
            <div className="w-full">
                <div className="flex w-full items-center justify-between">
                    <div className="flex max-w-[360px] flex-col gap-2">
                        <div className="h-5 w-20 animate-pulse rounded-3xl bg-gray-500" />
                        <div className="h-16 w-96 animate-pulse rounded-3xl bg-gray-500" />
                    </div>
                    <div className="h-16 w-[280px] animate-pulse rounded-full bg-gray-500 max-md:hidden"></div>
                </div>
            </div>
            <div className="max-md:w-full">
                <div className={'flex h-fit max-h-max flex-col items-start gap-6 overflow-hidden p-1 max-md:w-full '}>
                    <div className="relative flex items-start gap-8 border-b-4 border-[#E2E2E2] text-sub3 transition-colors max-md:w-full max-md:text-[14px] max-sm:gap-0">
                        <label className={'flex w-full cursor-pointer items-center justify-normal gap-2 pb-4'}>
                            <span className="select-none font-semibold">Description</span>
                            <input hidden defaultChecked={true} type="radio" name="payment" value="description" />
                        </label>
                        <label className="flex w-full cursor-pointer items-center justify-normal gap-2 pb-4 ">
                            <span className="select-none whitespace-nowrap font-semibold">Your Submission</span>
                            <input hidden defaultChecked={false} type="radio" name="payment" value="submission" />
                        </label>
                        <label className="flex w-full cursor-pointer  items-center justify-normal gap-2 pb-4 ">
                            <span className="select-none font-semibold">Your Score</span>
                            <input hidden defaultChecked={false} type="radio" name="payment" value="score" />
                        </label>
                        <span
                            className={
                                'absolute -bottom-1 left-0 h-1 w-[25%] translate-x-0 bg-blue transition-all duration-300'
                            }
                        />
                    </div>
                    <div
                        className={
                            'relative flex w-[100%] justify-between gap-2 text-sub3 transition-all duration-300 max-md:text-[14px]'
                        }>
                        <div
                            className={
                                'relative flex w-full grid-flow-col flex-col gap-3 opacity-100 transition-all duration-300'
                            }>
                            <h1 className="text-sub1 font-medium">Description</h1>
                            <div className="h-16 w-full animate-pulse rounded-3xl bg-gray-500"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default loading
