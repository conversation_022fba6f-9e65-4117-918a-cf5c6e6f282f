'use client'
import { cn } from '@/lib/cn'
import { useState, useEffect } from 'react'
import numberWithCommas from '@/lib/formatNumber'

interface props {
    number: number
    unit: string
    icon: JSX.Element
    className?: string
}

const Counter = ({ number, unit, icon, className }: props) => {
    const [num, setNum] = useState(0)

    useEffect(() => {
        const interval = setInterval(() => {
            setNum(prevNumber => {
                if (prevNumber === number) {
                    clearInterval(interval)
                    return prevNumber
                }
                if (number < 100) return prevNumber + 10

                return prevNumber + 100
            })
        }, 1)

        return () => clearInterval(interval)
    }, [number])

    return (
        <div className={cn('flex items-center gap-4 border-r border-[#676A73] max-md:gap-2', className)}>
            {icon}
            <div className="flex flex-col pr-9 max-md:pr-0">
                <div className="text-b1 font-medium max-md:text-sub1">{numberWithCommas(num)}+</div>
                <p className="w-full text-sub2 max-md:text-cap2">{unit}</p>
            </div>
        </div>
    )
}
export default Counter
