import { AlphaGroupModel } from '@/types/AlphaGroupModel'
import AlphaCard from './AlphaCard2'
import AlphaSwiperCards from './AlphaSwiperCards2'

const AlphaGroupPricing2 = async ({ data }: { data: AlphaGroupModel[] }) => {


    return (
        <div id="plans" className="pb- container mx-auto flex flex-col mt-[3rem] gap-[106px] py-auto sm:pb-[5rem] max-sm:gap-10 mb-10">
            <div className="flex flex-col items-center gap-4">
                <h1 className="text-callout font-semibold uppercase text-[#4A3AFF] max-sm:text-cap2">One Membership. Unlimited Returns</h1>
                <p className="text-center text-[32px] font-medium leading-[142%] max-sm:text-sub3">
                    Pick Your Alpha Group Membership Plan
                </p>
            </div>
            <div id='plans' className="flex w-full items-center justify-between max-md:hidden mx-2 px-6">
                {data.map((item, index) => (
                    <AlphaCard key={index} item={item} index={index} />
                ))}
            </div>
            <AlphaSwiperCards data={data} />
        </div>
    )
}

export default AlphaGroupPricing2
