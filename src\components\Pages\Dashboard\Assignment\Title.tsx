'use client'
import axios from '@/lib/axios'
import <PERSON><PERSON> from '@/components/Ui/Button'
import { useState } from 'react'
interface Props {
    name: string
    title: string
    pdf: any
    id: string
    user: any
    isAns:boolean
}

const Title = ({ name, title, pdf, id, user, isAns }: Props) => {
    const [isLoading, setIsLoading] = useState(false)
    const handleSubmit = async () => {
        const formData = new FormData()
        formData.append('assignment_id', id)
        formData.append('pdf', pdf)
        setIsLoading(true)
        try {
            await axios.post('/user-assignment', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            setIsLoading(false)
            window.location.reload();
        } catch (error) {
            console.error(error)
            setIsLoading(false)
        }
    }
    return (
        <div className="flex w-full items-center justify-between">
            <div className="flex max-w-[360px] flex-col gap-2">
                <h1 className="text-sub2 text-gray-900">{name}</h1>
                <p className="text-b3 font-medium capitalize">{title}</p>
            </div>
            <div className="w-[280px] max-md:hidden">
                <Button
                    disabled={isLoading || isAns || pdf === null}
                    onClick={handleSubmit}
                    className="flex items-center gap-2"
                    variant="primary"
                    rounded>
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" fill="none" viewBox="0 0 24 25">
                            <path
                                stroke="#fff"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="1.5"
                                d="M7.39 9.484h-.934a3.685 3.685 0 00-3.685 3.685v4.875a3.685 3.685 0 003.685 3.684h11.13a3.685 3.685 0 003.686-3.684v-4.885a3.675 3.675 0 00-3.674-3.675h-.944M12.021 2.69v12.041M9.105 5.619L12.02 2.69l2.917 2.928"></path>
                        </svg>
                    </span>
                    Upload Submission
                </Button>
            </div>
        </div>
    )
}

export default Title
