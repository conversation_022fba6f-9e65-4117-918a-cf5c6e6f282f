const Category = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M3 6.5C3 3.875 3.028 3 6.5 3s3.5.875 3.5 3.5.011 3.5-3.5 3.5S3 9.125 3 6.5zM14 6.5c0-2.625.028-3.5 3.5-3.5s3.5.875 3.5 3.5.011 3.5-3.5 3.5S14 9.125 14 6.5zM3 17.5c0-2.625.028-3.5 3.5-3.5s3.5.875 3.5 3.5.011 3.5-3.5 3.5S3 20.125 3 17.5zM14 17.5c0-2.625.028-3.5 3.5-3.5s3.5.875 3.5 3.5.011 3.5-3.5 3.5-3.5-.875-3.5-3.5z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
const Calendar = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M3.092 9.404h17.824M16.443 13.31h.01M12.005 13.31h.01M7.558 13.31h.01M16.443 17.196h.01M12.005 17.196h.01M7.558 17.196h.01M16.043 2v3.29M7.965 2v3.29"
      ></path>
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M16.238 3.58H7.771C4.834 3.58 3 5.214 3 8.221v9.05C3 20.326 4.834 22 7.771 22h8.458C19.175 22 21 20.355 21 17.347V8.222c.01-3.007-1.816-4.643-4.762-4.643z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
const Activity = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M7.244 14.781l2.993-3.89 3.415 2.682 2.929-3.78"
      ></path>
      <circle
        cx="19.994"
        cy="4.2"
        r="1.922"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      ></circle>
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M14.924 3.12H7.656c-3.011 0-4.879 2.133-4.879 5.144v8.083c0 3.011 1.831 5.135 4.88 5.135h8.603c3.012 0 4.879-2.124 4.879-5.135v-7.04"
      ></path>
    </svg>
  );
};
const Image = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M21.21 7.9v8.15c0 3.02-1.89 5.15-4.91 5.15H7.65c-3.02 0-4.9-2.13-4.9-5.15V7.9c0-3.02 1.89-5.15 4.9-5.15h8.65c3.02 0 4.91 2.13 4.91 5.15z"
        clipRule="evenodd"
      ></path>
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M5.281 16.431l1.528-1.613a1.401 1.401 0 011.98-.06l.937.952a1.434 1.434 0 002.027.02c.037-.036 2.334-2.822 2.334-2.822a1.687 1.687 0 012.375-.229c.048.04 2.218 2.267 2.218 2.267"
      ></path>
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M10.313 9.133a1.754 1.754 0 11-3.508.001 1.754 1.754 0 013.508 0z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
const Bag = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M16.513 21.5H8.166c-3.067 0-5.42-1.108-4.751-5.565l.778-6.041c.412-2.225 1.83-3.076 3.076-3.076h10.178c1.263 0 2.6.915 3.076 3.076l.778 6.04c.567 3.955-1.721 5.566-4.788 5.566z"
        clipRule="evenodd"
      ></path>
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M16.651 6.598a4.32 4.32 0 00-4.32-4.32v0a4.32 4.32 0 00-4.339 4.32h0M15.297 11.102h-.046M9.465 11.102h-.046"
      ></path>
    </svg>
  );
};
const Heart = ({ active }: { active: boolean }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M2.872 11.598c-1.073-3.35.18-7.179 3.698-8.312a6.007 6.007 0 015.43.912c1.455-1.125 3.572-1.505 5.42-.912 3.517 1.133 4.779 4.962 3.707 8.312-1.67 5.31-9.127 9.4-9.127 9.4s-7.402-4.028-9.128-9.4z"
        clipRule="evenodd"
      ></path>
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M16 6.7a2.781 2.781 0 011.917 2.422"
      ></path>
    </svg>
  );
};
const Setting = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M20.807 7.624l-.622-1.08a1.913 1.913 0 00-2.609-.705v0a1.904 1.904 0 01-2.608-.678 1.83 1.83 0 01-.257-.915v0a1.914 1.914 0 00-1.913-1.968h-1.254A1.904 1.904 0 009.64 4.191v0a1.913 1.913 0 01-1.913 1.886 1.83 1.83 0 01-.915-.257v0a1.913 1.913 0 00-2.609.705l-.668 1.099a1.913 1.913 0 00.696 2.608v0a1.913 1.913 0 010 3.314v0a1.904 1.904 0 00-.696 2.6v0l.632 1.089a1.913 1.913 0 002.608.741v0a1.895 1.895 0 012.6.696c.164.277.253.593.256.915v0c0 1.056.857 1.913 1.913 1.913h1.254c1.053 0 1.908-.85 1.913-1.904v0a1.904 1.904 0 011.913-1.913c.322.009.636.097.916.256v0a1.913 1.913 0 002.608-.695v0l.66-1.099a1.904 1.904 0 00-.696-2.608v0a1.904 1.904 0 01-.696-2.609c.166-.29.406-.53.696-.696v0a1.913 1.913 0 00.695-2.6v0-.008z"
        clipRule="evenodd"
      ></path>
      <circle
        cx="12.175"
        cy="11.889"
        r="2.636"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      ></circle>
    </svg>
  );
};
const Danger = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M12 2.75a9.25 9.25 0 110 18.5 9.25 9.25 0 010-18.5z"
        clipRule="evenodd"
      ></path>
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M11.994 8.204v4.42"
      ></path>
      <path
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M11.995 15.796h.01"
      ></path>
    </svg>
  );
};
const Faq = (active: boolean) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        fill={active ? "#2655FF" : "#081228"}
        d="M11.6 14.22c-.01-.379.05-.755.177-1.107.09-.249.242-.465.438-.625.175-.129.364-.258.57-.387l.568-.344c.197-.117.357-.298.456-.519.117-.268.171-.564.16-.862a1.65 1.65 0 00-.49-1.25 1.677 1.677 0 00-1.224-.48 1.582 1.582 0 00-1.24.543c-.32.358-.486.849-.456 1.35H9.256c-.053-.873.245-1.726.814-2.338.286-.313.627-.56 1.003-.723.376-.163.778-.24 1.182-.226.787-.021 1.55.3 2.123.893.571.57.889 1.384.87 2.231.015.458-.067.914-.239 1.331-.125.315-.32.59-.569.8a6.327 6.327 0 01-.677.45 2.27 2.27 0 00-.569.469c-.16.184-.247.432-.239.687v.919h-1.354v-.813zm.08 3.749a1.025 1.025 0 01-.264-.69c0-.26.093-.51.26-.693a.85.85 0 01.63-.286.87.87 0 01.736.434c.165.27.196.612.083.912a.913.913 0 01-.644.584.837.837 0 01-.8-.261z"
      ></path>
      <path
        fillRule="evenodd"
        stroke={active ? "#2655FF" : "#081228"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M12.25 3a9.25 9.25 0 110 18.5 9.25 9.25 0 010-18.5z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
const Logout = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        stroke="#FC0019"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M15.015 7.39v-.934a3.685 3.685 0 00-3.684-3.685H6.455a3.685 3.685 0 00-3.684 3.685v11.13a3.685 3.685 0 003.684 3.686h4.886a3.675 3.675 0 003.675-3.674v-.944M21.809 12.021H9.768M18.88 9.106l2.929 2.915-2.928 2.916"
      ></path>
    </svg>
  );
};
const Call = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="19"
      height="18"
      fill="none"
      viewBox="0 0 19 18"
    >
      <path
        fillRule="evenodd"
        stroke="#2655FF"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.125"
        d="M9.149 9.354c2.992 2.991 3.67-.469 5.575 1.435 1.837 1.836 2.892 2.203.565 4.53-.291.234-2.143 3.052-8.65-3.454-6.51-6.507-3.693-8.36-3.459-8.652C5.513.88 5.874 1.942 7.71 3.778 9.617 5.682 6.158 6.363 9.15 9.354z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};

const Lock = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
  >
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M16.423 9.448V7.3a4.552 4.552 0 00-4.551-4.551 4.55 4.55 0 00-4.57 4.53v2.168"
    ></path>
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M15.683 21.25H8.042a3.792 3.792 0 01-3.792-3.792v-4.29a3.792 3.792 0 013.792-3.791h7.641a3.792 3.792 0 013.792 3.792v4.289a3.792 3.792 0 01-3.792 3.792z"
      clipRule="evenodd"
    ></path>
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M11.862 14.203v2.22"
    ></path>
  </svg>
);
const Eye = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
  >
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M15.162 12.053a3.162 3.162 0 11-6.323-.001 3.162 3.162 0 016.323.001z"
      clipRule="evenodd"
    ></path>
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M11.998 19.355c3.808 0 7.291-2.738 9.252-7.302-1.961-4.564-5.444-7.302-9.252-7.302h.004c-3.808 0-7.291 2.738-9.252 7.302 1.961 4.564 5.444 7.302 9.252 7.302h-.004z"
      clipRule="evenodd"
    ></path>
  </svg>
);
const EyeOff = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
  >
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M9.76 14.367a3.123 3.123 0 01-.924-2.23A3.16 3.16 0 0112 8.973c.867 0 1.665.35 2.23.925M15.105 12.699a3.158 3.158 0 01-2.537 2.542"
    ></path>
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M6.655 17.472c-1.587-1.246-2.931-3.066-3.905-5.335.984-2.279 2.337-4.109 3.934-5.365C8.271 5.516 10.102 4.834 12 4.834c1.909 0 3.739.692 5.336 1.957M19.449 8.99a15.358 15.358 0 011.802 3.147c-1.967 4.557-5.443 7.302-9.25 7.302-.863 0-1.714-.14-2.532-.413M19.887 4.25L4.113 20.023"
    ></path>
  </svg>
);

const Email = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
  >
    <path
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M17.903 8.851l-4.443 3.613c-.84.666-2.02.666-2.86 0L6.12 8.851"
    ></path>
    <path
      fillRule="evenodd"
      stroke="#081228"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M16.909 21C19.95 21.008 22 18.51 22 15.438V8.57C22 5.499 19.95 3 16.909 3H7.09C4.05 3 2 5.499 2 8.57v6.868C2 18.51 4.05 21.008 7.091 21h9.818z"
      clipRule="evenodd"
    ></path>
  </svg>
);

const Location = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="24"
    fill="none"
    viewBox="0 0 17 24"
  >
    <path
      fill="#000"
      d="M8.5 0C3.813 0 0 3.813 0 8.5c0 1.543.692 3.203.721 3.273.224.53.664 1.354.983 1.837l5.828 8.83c.238.362.591.57.968.57s.73-.208.969-.569l5.828-8.831a13.65 13.65 0 00.982-1.837c.03-.07.721-1.73.721-3.273C17 3.813 13.187 0 8.5 0zm6.857 11.384c-.199.476-.61 1.242-.895 1.675l-5.829 8.832c-.114.174-.151.174-.266 0L2.538 13.06c-.285-.434-.695-1.2-.895-1.676C1.635 11.364 1 9.836 1 8.5 1 4.364 4.364 1 8.5 1S16 4.364 16 8.5c0 1.338-.636 2.87-.643 2.884z"
    ></path>
    <path
      fill="#000"
      d="M8.5 4A4.505 4.505 0 004 8.5C4 10.983 6.019 13 8.5 13c2.482 0 4.5-2.018 4.5-4.5C13 6.02 10.982 4 8.5 4zm0 8C6.57 12 5 10.43 5 8.5S6.57 5 8.5 5 12 6.57 12 8.5 10.43 12 8.5 12z"
    ></path>
  </svg>
);
const Success = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    fill="none"
    viewBox="0 0 18 18"
  >
    <path
      fill="#20CC64"
      d="M8.842 17.947a8.842 8.842 0 100-17.684 8.842 8.842 0 000 17.684zm-4.59-8.609a.804.804 0 011.134 0l1.849 1.85 4.654-4.655a.804.804 0 011.133 1.134l-5.225 5.225a.804.804 0 01-1.133 0L4.252 10.48a.803.803 0 010-1.141z"
    ></path>
  </svg>
);

const Search = ({ height, width }: { height?: number; width?: number }) => (
  <svg
    width={width ? width : 24}
    height={height ? height : 24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.7666 20.7554C16.7309 20.7554 20.7552 16.7311 20.7552 11.7669C20.7552 6.80263 16.7309 2.77832 11.7666 2.77832C6.80239 2.77832 2.77808 6.80263 2.77808 11.7669C2.77808 16.7311 6.80239 20.7554 11.7666 20.7554Z"
      stroke="#AAAAAA"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.0183 18.4854L21.5423 22.0002"
      stroke="#AAAAAA"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export {
  Location,
  Success,
  Search,
  Category,
  Email,
  EyeOff,
  Eye,
  Lock,
  Call,
  Calendar,
  Activity,
  Image,
  Bag,
  Heart,
  Setting,
  Danger,
  Faq,
  Logout,
};
