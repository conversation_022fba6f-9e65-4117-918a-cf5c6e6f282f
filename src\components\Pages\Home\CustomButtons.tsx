import { useSwiper } from '@/lib/swiper'

const Arrow = () => (
    <svg width="8" height="11" viewBox="0 0 8 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M6.21031 9.27857L1.78973 5.40918L6.21031 1.53979"
            stroke="white"
            strokeWidth="1.625"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const CustomButtons = () => {
    const swiper = useSwiper()
    return (
        <div className="flex select-none items-center gap-6 pt-[28px]">
            <div
                onClick={() => swiper.slidePrev()}
                className="cursor-pointer rounded-[50%] bg-blue px-[14px] py-3 transition-colors duration-150 hover:bg-blue/90">
                <Arrow />
            </div>
            <div
                onClick={() => swiper.slideNext()}
                className="rotate-180 cursor-pointer rounded-[50%] bg-blue px-[14px] py-3 transition-colors duration-150 hover:bg-blue/90">
                <Arrow />
            </div>
        </div>
    )
}

export default CustomButtons
