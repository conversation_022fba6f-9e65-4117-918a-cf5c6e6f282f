'use client'
import { redirect } from 'next/navigation'
import useAffiliate from '@/hooks/useAffiliate'
import { useEffect, useState } from 'react'

const State = ({ slug }: { slug: string }) => {
    const store = useAffiliate()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        store.setReferral(slug)
        store.setStartDate(new Date())
        store.setEndDate(new Date)

        setIsMounted(true)
    }, [slug, store.setReferral])

    if (!isMounted) return <p className='container mx-auto flex h-screen text-3xl items-center justify-center'>Redirecting...</p>
    else redirect('/')
}

export default State
