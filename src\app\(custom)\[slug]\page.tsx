import fetch from '@/lib/fetch'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { capitalizeWords } from '@/lib/capitalize'
import { removeMarkdown } from '@/lib/removeMarkdown'
import Section from '@/components/Pages/Custom/Section'
import HeroSection from '@/components/Pages/Custom/Hero'
import { CustomPageModel } from '@/types/CustomPageModel'
import CountdownTimer from '@/components/Pages/Custom/CountdownTimer'
import IntroductionSection from '@/components/Pages/Custom/Introduction'

interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}

async function getPageBySlug(slug: string) {
    try {
        const res = await fetch('/custom-page/all/' + slug, {
            next: { revalidate: 60 },
        })
        return res
    } catch (error) {
        console.error('Error:', error)
        return error
    }
}

export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug as string
    if (slug !== undefined)
        try {
            const { page } = await getPageBySlug(slug)
            if (page === null || !page || page.success === false) return {}
            return {
                title: capitalizeWords(page.page_heros![0].title),
                description: removeMarkdown(page.page_heros![0].description),
                openGraph: {
                    title: capitalizeWords(page.page_heros![0].title),
                    description: removeMarkdown(page.page_heros![0].description),
                    type: 'website',
                    images: [
                        {
                            url: page.page_heros![0].image,
                            width: 1080,
                            height: 720,
                        },
                    ],
                },
                twitter: {
                    card: 'summary_large_image',
                    title: capitalizeWords(page.page_heros![0].title),
                    description: removeMarkdown(page.page_heros![0].description),
                    images: [page.page_heros![0].image],
                },
            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const CustomPage = async ({ params }: { params: PageProps }) => {
    const page = await getPageBySlug(params.slug)
    if (page.success === false) notFound()

    const data: CustomPageModel = page.page
    const { id, slug, video, expires_at, page_heros, page_sections } = data
    const heroData: Partial<CustomPageModel> = {
        id,
        slug,
        video,
        expires_at,
        page_heros,
    }

    return (
        <main className="w-full">
            <HeroSection data={heroData} />
            <IntroductionSection video={video} />
            {page_sections?.map((section, index) => (
                <Section section={section} key={index} />
            ))}
            {expires_at && <CountdownTimer targetDate={expires_at} />}
        </main>
    )
}

export default CustomPage
