import ImageShortcut from '@/components/Ui/Image'
import { TrustPilot } from '@public/home'

type UserProps = {
    name: string
    img: any | null
    description: string
    initials: string
}

const TestimonialCard = ({ user }: { user: UserProps }) => {
    return (
        <div className="h-full max-w-[531px] select-none space-y-[0.875rem] rounded-lg border border-gray-400 bg-white py-[1.125rem] pl-7 pr-[1.125rem] shadow-[4px_4px_0px_0px_#0000001A]">
            <div className="flex items-center justify-between">
                <div className='flex items-center gap-3'>
                    {user.img ? <ImageShortcut src={user.img} width={53} height={53} className='rounded-full w-[53px] h-[53px] object-cover object-top' alt="" /> : <div className='w-[53px] h-[53px] font-semibold bg-blue/50 rounded-full flex items-center justify-center'>{user.initials}</div>}
                    <p className="text-sub3 font-semibold leading-[36px]">{user.name}</p>
                </div>

                <TrustPilot width='62.86' height='15.63' />
            </div>
            <p className="max-w-[486px] text-cap1">{user.description}</p>
        </div>
    )
}

export default TestimonialCard
