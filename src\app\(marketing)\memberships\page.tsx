import Membership from '@/components/Pages/Memberships/membership/Membership';
import AlphaGroupPricing from '@/components/Pages/Memberships/AlphaGroupPricing';
import Discord from '@/components/Pages/Memberships/Discord';
import CUMembershipFaqsStaticDark from '@/components/Ui/CUMembershipFaqsStaticDark';
import { AlphaGroupModel } from '@/types/AlphaGroupModel';
import { ComingSoon } from '@/components/Pages/Memberships/ComingSoon/ComingSoon';

export const metadata = {
    title: 'Memberships - Join the Elite in Crypto Intelligence',
    description:
        "Join the world of high-stakes crypto trading with our Alpha Group. We're more than just a community; we're a consortium of forward-thinking investors and experts dedicated to catching every single opportunity in crypto.",
    keywords: [
        'Memberships',
        'Crypto University',
        'Crypto U',
        'Crypto',
        'Blockchain',
        'Cryptocurrency',
        'Alpha Group',
        'Crypto Expert',
        'Crypto Investor',
        'Crypto Trader',
    ],
    openGraph: {
        images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1717966317/Alpha_Group_Webpage_Thumbnail_1200x628_1_ie2zok.jpg',
    },
};

// Static membership plans data in AlphaGroupModel[] format
const fetchMembershipPlans = async (): Promise<AlphaGroupModel[]> => {
    const monthlyPlan: AlphaGroupModel = {
        title: {
            text: 'Monthly Special',
            color: 'white',
        },
        cards: {
            text: ['Flexible Plan'],
            color: 'white',
        },
        icon: {
            src: '/memberships/card1.png',
            alt: 'Alpha Group Monthly Plan',
        },
        billed: {
            text: 'Monthly',
            color: 'white',
        },
        price: {
            text: "500",
            color: 'white',
        },
        sub: {
            text: "Billed <strong>Monthly</strong>",
            color: 'white',
        },
        button: {
            text: "Get 1 Month",
            color: 'white',
        },
        plan: {
            text: 'Flexible Plan',
            color: 'white',
        },
        plans: '/checkout/subscription/' + 'alpha-group-monthly' + '?previous=' + '/subscriptions',
    };

    const semiAnnualPlan: AlphaGroupModel = {
        title: {
            text: 'Alpha Group 6 Months Special',
            color: 'white',
        },
        cards: {
            text: ['Discounted Plan'],
            color: 'white',
        },
        icon: {
            src: '/memberships/card3.png',
            alt: 'Alpha Group 6 Months Plan Icon',
        },
        billed: {
            text: '6 Months',
            color: 'white',
        },
        price: {
            text: "349",
            color: 'white',
        },
        sub: {
            text: "Billed <strong>$2,099</strong> at once",
            color: 'white',
        },
        button: {
            text: "Get 6 Months",
            color: 'white',
        },
        plan: {
            text: '30% off',
            color: 'white',
        },
        plans: '/checkout/subscription/' + 'alpha-group-6-months' + '?previous=' + '/subscriptions',
    };

    const annualPlan: AlphaGroupModel = {
        title: {
            text: 'Alpha Group Annual Plan',
            color: 'white',
        },
        cards: {
            text: ['Best Value Plan'],
            color: 'white',
        },
        icon: {
            src: '/memberships/card2.png',
            alt: 'Alpha Group 12 Months Plan Icon',
        },
        billed: {
            text: '12 Months',
            color: 'white',
        },
        price: {
            text: "249",
            color: 'white',
        },
        sub: {
            text: "Billed <strong>$2,999</strong> at once",
            color: 'white',
        },
        button: {
            text: "Get One Year",
            color: 'white',
        },
        plan: {
            text: '50% off',
            color: 'white',
        },
        plans: '/checkout/subscription/' + 'alpha-group-yearly' + '?previous=' + '/subscriptions'
    };

    // Return all plans as an array
    return [monthlyPlan, semiAnnualPlan, annualPlan];
};

const AlphaMembershipPage = async () => {
    const data = await fetchMembershipPlans();

    return (
        <div className="w-full bg-[#222121]">
              {/* <ComingSoon/> */}
            <Membership />
            {data.length > 0 ? <AlphaGroupPricing data={data} /> : <p>No available plans</p>}
            <CUMembershipFaqsStaticDark />
            <Discord />
        </div>
    );
};

export default AlphaMembershipPage;