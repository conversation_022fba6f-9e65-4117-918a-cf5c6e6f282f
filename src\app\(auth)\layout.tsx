import Header from "@/components/Nav/Header"
import HeaderMobile from "@/components/Nav/HeaderMobile"

interface MarketingLayoutProps {
    children: React.ReactNode
}

export default async function MarketingLayout({
    children,
}: MarketingLayoutProps) {
    return (
        <>
            <Header />
            <HeaderMobile />
            <div className="flex pt-20 min-h-screen flex-col">
                <main className="flex-grow flex flex-col">{children}</main>
            </div>
        </>
    )
}