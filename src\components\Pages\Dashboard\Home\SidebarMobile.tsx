'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import fetch from '@/lib/fetch'
import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { CourseModel } from '@/types/CourseModel'
import DisclosureLesson from '@/components/Ui/DisclosureLesson'
import { Coaching, Affiliate, Statistic, FAQ, Help, Setting, Myclass, Indicators } from '@public/dashboard'

interface LinkItemProps {
    href: string
    icon: (active: boolean) => React.ReactNode
    active: boolean
    text: string
    setActive: React.Dispatch<React.SetStateAction<boolean>>
}

const LinkItem = ({ active, href, icon, text, setActive }: LinkItemProps) => {
    return (
        <Link
            aria-label={text}
            onClick={() => setActive(false)}
            href={href}
            className={cn(
                'flex cursor-pointer gap-[1.375rem] rounded-xl py-[1.1rem] pl-8 ',
                active
                    ? 'bg-blue text-white'
                    : 'bg-transparent text-black transition-[background] duration-150 hover:bg-gray-200'
            )}>
            {icon(active)}
            <span>{text}</span>
        </Link>
    )
}
const navItems = [
    {
        href: '/dashboard',
        text: 'My Courses',
        icon: (active: boolean) => Myclass({ active }),
    },
    {
        href: '/dashboard/indicators',
        icon: (active: boolean) => Indicators({ active }),
        text: 'My Indicators',
    },
    {
        href: '/dashboard/consultation',
        icon: (active: boolean) => Coaching({ active }),
        text: 'My Mentorships',
    },
    {
        href: '/dashboard/affiliate',
        icon: (active: boolean) => Affiliate({ active }),
        text: 'Affiliate',
    },
    {
        href: '/dashboard/statistics',
        icon: (active: boolean) => Statistic({ active }),
        disabled: false,
        text: 'My Statistics',
    },
]
const navItems2 = [
    {
        href: '/dashboard/settings',
        icon: (active: boolean) => Setting({ active }),
        text: 'Setting',
    },
    {
        href: '/help',
        text: 'Help',
        icon: (active: boolean) => Help({ active }),
    },
    {
        href: '/faqs',
        icon: (active: boolean) => FAQ({ active }),
        text: 'FAQs',
    },
]

const MenuSVG = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            stroke="#222"
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M19 12h-8.908M19 17.091h-8.91m8.91-10.182h-8.91"></path>
        <path
            fill="#222"
            fillRule="evenodd"
            d="M6.271 8.182c.637 0 1.273-.636 1.273-1.273 0-.636-.636-1.273-1.273-1.273C5.635 5.636 5 6.273 5 6.91c0 .637.635 1.273 1.271 1.273zm0 5.09c.637 0 1.273-.636 1.273-1.272s-.636-1.273-1.273-1.273C5.635 10.727 5 11.364 5 12c0 .636.635 1.273 1.271 1.273zm0 5.092c.637 0 1.273-.637 1.273-1.273 0-.636-.636-1.273-1.273-1.273-.636 0-1.271.637-1.271 1.273 0 .636.635 1.273 1.271 1.273z"
            clipRule="evenodd"></path>
    </svg>
)
const CloseMenuSVG = () => (
    <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" fill="none" viewBox="0 0 27 27">
            <path
                stroke="#081228"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M7.873 19.125l11.25-11.25M19.123 19.125L7.873 7.875"></path>
        </svg>
    </span>
)

const SidebarMobile = ({ user }: { user: any }) => {
    const currentRoute = usePathname()
    const [data, setData] = useState<CourseModel>()
    const [active, setActive] = useState(false)
    const [quizzes, setQuizzes] = useState<QuizModel[]>([])
    const [isLoaded, setIsLoaded] = useState(true)

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch('/user-course/slug/' + currentRoute.split('/')[2], {
                    headers: {
                        Authorization: `Bearer ${user.access_token}`,
                    },
                })
                const data = await response.course
                setIsLoaded(false)
                setData(data)
            } catch (error) {
                setIsLoaded(false)
                console.error(error)
            }
        }
        const fetchQuizzes = async () => {
            try {
                const response = await fetch('/quiz/courses/' + currentRoute.split('/')[2], {
                    headers: {
                        Authorization: `Bearer ${user.access_token}`,
                    },
                })
                const data = await response.quiz
                setIsLoaded(false)
                setQuizzes(data)
            } catch (error) {
                setIsLoaded(false)
                console.error(error)
            }
        }
        fetchData()
        fetchQuizzes()
    }, [currentRoute, user.access_token])

    if (
        // '/dashboard' === currentRoute ||
        // '/dashboard/affiliate' === currentRoute ||
        // // '/dashboard/indicators' === currentRoute ||
        // '/dashboard/statitsics' === currentRoute ||
        // '/dashboard/settings' === currentRoute || 
        // '/dashboard/settings/order-history' === currentRoute 

        '/dashboard' === currentRoute ||
        '/dashboard/affiliate' === currentRoute ||
        '/dashboard/statitsics' === currentRoute ||
        '/dashboard/settings' === currentRoute ||
        '/dashboard/settings/order-history' === currentRoute ||
        '/dashboard/settings/memberships' === currentRoute ||
        '/dashboard/statistics' === currentRoute ||
        '/dashboard/affiliate' === currentRoute ||
        '/dashboard/affiliate/payouts' === currentRoute ||
        '/dashboard/affiliate/referrals' === currentRoute ||
        '/dashboard/affiliate/registration' === currentRoute ||
        '/dashboard/consultation' === currentRoute ||
        '/dashboard/indicators' === currentRoute
    ) {
        return (
            <>
                <div
                    className={cn(
                        'flex items-center justify-between px-4 pt-8 transition-all duration-500 lg:hidden',
                        active ? 'translate-y-[-130%] opacity-0' : 'translate-y-[0] opacity-100'
                    )}>
                    <button onClick={() => setActive(true)} className="flex w-full items-center justify-end gap-2">
                        <span className="text-sub3 ">Menu</span>
                        <MenuSVG />
                    </button>
                </div>
                <div
                    className={cn(
                        'no-scrollbar fixed top-0 z-50 h-[calc(100vh-0rem)] w-full overflow-y-scroll bg-white px-4 pt-[26px] transition-[transform] duration-500 lg:hidden',
                        active ? 'translate-y-[80px]  ' : 'translate-y-[-130%]'
                    )}>
                    <div className="flex min-h-screen flex-col gap-6">
                        <ul className="z-20 flex items-center justify-between">
                            <h1 className="text-sub3 font-semibold">Menu</h1>
                            <button onClick={() => setActive(false)} className="text-sub3 font-semibold">
                                <CloseMenuSVG />
                            </button>
                        </ul>
                        <ul className="flex flex-col">
                            {navItems.map((item, index) => (
                                <li key={index}>
                                    <LinkItem
                                        setActive={setActive}
                                        href={item.href}
                                        active={item.href === currentRoute}
                                        icon={item.icon}
                                        text={item.text}
                                    />
                                </li>
                            ))}
                        </ul>
                        <div className="px-9">
                            <hr className="text-gray" />
                        </div>
                        <ul className="flex flex-col pb-16">
                            {navItems2.map((item, index) => (
                                <li key={index}>
                                    <LinkItem
                                        setActive={setActive}
                                        href={item.href}
                                        active={item.href === currentRoute}
                                        icon={(active: boolean) => {
                                            return item.icon(active)
                                        }}
                                        text={item.text}
                                    />
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            </>
        )
    }
    if ('/dashboard/quizzes' === currentRoute.split('/').slice(0, 3).join('/')) {
        return null
    }
    return (
        <>
            <div
                className={cn(
                    'flex items-center justify-between px-4 pt-8 transition-all duration-500 lg:hidden',
                    active ? 'translate-y-[-130%] opacity-0' : 'translate-y-[0] opacity-100'
                )}>
                <Link aria-label="Go Back" href="/dashboard">
                    <button className="flex gap-2">
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="6"
                                height="10"
                                fill="none"
                                viewBox="0 0 6 10">
                                <path
                                    stroke="#292929"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.5"
                                    d="M5.04 8.572L.959 5l4.08-3.572"></path>
                            </svg>
                        </span>
                        <span className="test-sub3 ">Go Back</span>
                    </button>
                </Link>
                <button onClick={() => setActive(true)} className="flex gap-2">
                    <span className="test-sub3 ">Lessons / Topics</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path
                            stroke="#222"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M19 12h-8.908M19 17.091h-8.91m8.91-10.182h-8.91"></path>
                        <path
                            fill="#222"
                            fillRule="evenodd"
                            d="M6.271 8.182c.637 0 1.273-.636 1.273-1.273 0-.636-.636-1.273-1.273-1.273C5.635 5.636 5 6.273 5 6.91c0 .637.635 1.273 1.271 1.273zm0 5.09c.637 0 1.273-.636 1.273-1.272s-.636-1.273-1.273-1.273C5.635 10.727 5 11.364 5 12c0 .636.635 1.273 1.271 1.273zm0 5.092c.637 0 1.273-.637 1.273-1.273 0-.636-.636-1.273-1.273-1.273-.636 0-1.271.637-1.271 1.273 0 .636.635 1.273 1.271 1.273z"
                            clipRule="evenodd"></path>
                    </svg>
                </button>
            </div>
            <div
                className={cn(
                    'no-scrollbar fixed top-[0px] z-50 h-[calc(100vh-0rem)]  w-full overflow-y-scroll bg-white pb-10 transition-all duration-500 lg:hidden ',
                    active ? ' translate-y-[80px] ' : ' translate-y-[-130%]'
                )}>
                <div className="flex min-h-screen flex-col gap-6 ">
                    <ul className=" z-20 flex justify-between p-4 ">
                        <h1 className="text-sub3 font-semibold">Lessons/Topics</h1>
                        <button onClick={() => setActive(false)} className="text-sub3 font-semibold">
                            <span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="27"
                                    height="27"
                                    fill="none"
                                    viewBox="0 0 27 27">
                                    <path
                                        stroke="#081228"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="1.5"
                                        d="M7.873 19.125l11.25-11.25M19.123 19.125L7.873 7.875"></path>
                                </svg>
                            </span>
                        </button>
                    </ul>
                    <ul className="flex flex-col pb-16">
                        {isLoaded ? (
                            <div className="flex items-center justify-center"></div>
                        ) : (
                            <DisclosureLesson
                                quizzes={quizzes}
                                handelClose={() => setActive(false)}
                                isPurchasing={true}
                                course={data}
                            />
                        )}
                    </ul>
                </div>
            </div>
        </>
    )
}

export default SidebarMobile
