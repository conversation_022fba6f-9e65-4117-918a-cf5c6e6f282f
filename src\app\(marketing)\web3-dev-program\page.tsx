import SecondView from './SecondView'
import FirstView from './FirstView'
import { capitalizeWords } from '@/lib/capitalize'
import { Metadata } from 'next'

interface MetaProps {
  params: {
    slug: any
  }
}


export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {

  const pageMetadata = {
    title: 'Web3 Dev Program | Convert Web2 Devs to Web3 | $1.3M Salaries Paid',
    description:
      'Join the Web3 Dev Program by Crypto University led by <PERSON><PERSON>. Convert from Web2 to Web3 development, earn competitive salaries, and gain access to workshops, training, networking events, and funding opportunities. Collaborate with top tech companies and be part of a thriving blockchain community.',
    keywords: ['Africa Web3 Dev Program', 'Web3 development', 'Web2 to Web3', 'blockchain training', '<PERSON><PERSON> Akinwande', 'Ethereum', 'ICP', 'Celo', 'XRP', 'blockchain ecosystem', 'tech workshops', 'developer networking', 'blockchain hackathons', 'tech community', 'blockchain funding opportunities', 'Web3 projects', 'developer support'],
    image: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1717450591/The_Africa_Web3_Dev_Program_thumbnail_1_yoi3vv.jpg',
  }

  return {
    title: capitalizeWords(pageMetadata.title),
    description: pageMetadata.description as string,
    authors: [{ name: pageMetadata.title }],
    keywords: pageMetadata.keywords,
    openGraph: {
      title: pageMetadata.title,
      description: pageMetadata.description,
      type: 'website',
      url: 'web3-dev-program',
      images: [
        {
          url: pageMetadata.image as string,
          width: 1200,
          height: 630,
          alt: pageMetadata.image,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: pageMetadata.title,
      description: pageMetadata.description,
      images: [pageMetadata.image as string],
    },
  }
}

const DevProgram = () => {
  return (
    <section className="flex w-full flex-col">
      <FirstView />
      <SecondView />
    </section>
  )
}

export default DevProgram
