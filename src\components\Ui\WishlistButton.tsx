'use client'
import useWishlist from '@/hooks/useWishlist'
import { CourseModel } from '@/types/CourseModel'
import { Whishlist } from '@public/home'
import { useEffect, useState } from 'react'

interface Props {
    item: CourseModel
}

const WishlistButton = ({ item }: Props) => {
    const wishlist = useWishlist()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])
    if (!isMounted) return null

    const OnAddToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.addproduct(item)
    }
    const OnRemoveToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.removeproduct(item.id)
    }
    return (
        <div
            onClick={e => (wishlist.productExists(item) ? OnRemoveToWishlist(e) : OnAddToWishlist(e))}
            data-popover-target="popover-image"
            className="cursor-pointer select-none">
            <Whishlist active={wishlist.productExists(item)} />
        </div>
    )
}

export default WishlistButton
