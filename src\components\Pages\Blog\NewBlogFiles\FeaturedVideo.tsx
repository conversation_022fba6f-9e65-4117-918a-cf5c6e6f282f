"use client"
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import Noones from '../../../../../public/icons/Noones.jpg'
import p2p from '../../../../../public/icons/p2p.svg'
import line from '../../../../../public/icons/line.svg'
import Link from 'next/link'
import { MdChevronRight } from 'react-icons/md'
import ImageShortcut from '@/components/Ui/Image'
import axios from 'axios';
import { Ad } from '@/types/AdModel'


async function fetchAllAdverts(search: string) {
    try {
        const response = await axios.get(`${process.env.API_URL}/ads?search=${search}&pageNumber=1&pageSize=10`);
        console.error(response.data.data);

        return response.data.data;
    } catch (error) {
        console.error(error);
        return null;
    }
}

function filterAdsByLocationAndLevel(ads: any[], location: string, level: number) {
    return ads.filter(ad => ad.location === location && ad.level === level);
}


const FeaturedVideo = () => {

    const [ads, setAds] = useState([]);
    const [filteredAdvert, setFilteredAdvert] = useState<Ad>();

    const featuredVideos = [
        {
            logo: Noones,
            image: Noones,
            title: 'P2P trading - All you need to know',
            description:
                'NoOnes: Transforming financial communication globally. Trade, transact, and store wealth easily.',
            videoUrl: 'https://www.youtube.com/watch?v=Yn8tj59yWkg&ab_channel=CryptoHustle',
        },
    ]

    useEffect(() => {
        async function fetchAds() {
            const adsData = await fetchAllAdverts('blog');
            console.log(adsData)
            if (adsData) {
                setAds(adsData);
            }
        }

        fetchAds();
    }, []);

    useEffect(() => {
        if (ads.length > 0) {
            const filteredLocation1 = filterAdsByLocationAndLevel(ads, 'center', 1);
            if (filteredLocation1.length > 0) {
                setFilteredAdvert(filteredLocation1[0]);
            } else {
                // Set filteredAdvert to null or some default value to handle the case where no matching ad is found
                setFilteredAdvert(undefined);
            }
        }
    }, [ads]);

    return (
        <div className="mx-auto my-5 max-w-[400px] overflow-hidden p-[32px] sm:max-w-[700px] md:my-[60px] md:max-w-[1400px] md:p-[4px]">
            <div className="mb-6 block h-full justify-center  md:hidden">
                {filteredAdvert?.url ? (
                    <Link
                        className="pt-4"
                        target="_blank"
                        aria-label="Cart"
                        href={filteredAdvert.url}>
                        <ImageShortcut
                        width={980}
                        height={120}
                            src={filteredAdvert.image}
                            alt={filteredAdvert.name}
                            className="bg-[hsl(0,0%,98.4%,0.2)] bg-fixed object-cover pt-4 opacity-0 transition duration-300 ease-in-out hover:opacity-100 max-sm:min-w-full max-sm:max-w-full"
                        />
                    </Link>
                ) : (<span>No Adverts are available</span>)}
            </div>

            <section className="flex gap-[148px]">
                <div className="grid items-center justify-center md:flex md:gap-3">
                    <h2 className=" text-[18px] font-[600] md:mb-5 md:text-[36px]">Featured Videos</h2>
                    <Image src={line} alt="line" width={2} height={5} className="mx-2 mb-4 hidden md:block" />
                    <h3 className=" mb-3 text-[12px] font-[600] text-[#1A1A1A] md:text-[16px]">By Grey Jabesi, CEO</h3>
                </div>
                <h2 className="mb-5  pl-[2rem] ml-8 hidden text-[22px] font-[600] md:mb-5 md:block md:text-[36px]">Sponsored</h2>
            </section>

            <div className="grid justify-center gap-6 p-4 md:grid-cols-2">
                <div className="grid gap-6 md:mt-3">
                    {featuredVideos.map(featuredVideo => (
                        <section
                            key={featuredVideo.title}
                            className="grid w-full grid-cols-1 items-center space-y-4 rounded-[8px] border-[1px] border-[#FCC229] bg-[#FEEB8810] p-4 md:max-w-[580px] md:grid-cols-2 md:space-y-0">
                            <div>
                                <Image
                                    src={featuredVideo.logo}
                                    alt="video"
                                    width={260}
                                    height={146.65}
                                    className="hidden rounded-[4.0px] p-2 md:block"
                                />
                            </div>
                            <div className="space-y-3">
                                <div className="flex items-center gap-2 text-center">
                                    <Image
                                        src={p2p}
                                        alt="author"
                                        width={36.88}
                                        height={3.07}
                                        className="rounded-full"
                                    />
                                    <h2 className="text-[12px] text-[#CA9B21]">IsaacBandie</h2>
                                    <h3 className="text-[12px] font-[500] text-[#97989F]">August 20, 2022</h3>
                                </div>
                                <h2 className="mt-3 text-[18px] font-[600]">{featuredVideo.title}</h2>
                                <Image
                                    src={featuredVideo.image}
                                    alt="video"
                                    width={260}
                                    height={146.65}
                                    className="mt-3 block rounded-[4.0px] md:hidden"
                                />
                                <p className="my-3 text-[14px] font-[400] text-[#5B5B5B]">
                                    {featuredVideo.description}
                                </p>
                                <Link
                                    target="_blank"
                                    aria-label="Watch Video"
                                    href={featuredVideo.videoUrl}
                                    className="flex items-center text-[14px] text-[#2655FF]">
                                    Watch Video <MdChevronRight className="text-lg " />
                                </Link>
                            </div>
                        </section>
                    ))}
                </div>
                <div className="hidden items-center justify-center rounded-lg md:flex">
                    {filteredAdvert?.url ? (
                        <Link
                            className=""
                            target="_blank"
                            aria-label="Cart"
                            href={filteredAdvert.url}>
                            <ImageShortcut
                            width={980}
                            height={120}
                                src={filteredAdvert.image}
                                alt={filteredAdvert.name}
                                className=" h-[214px]  object-cover max-sm:min-w-full max-sm:max-w-full"
                            />
                        </Link>) : (<span>No Adverts are available</span>)}

                </div>
            </div>
        </div>
    )
}

export default FeaturedVideo
