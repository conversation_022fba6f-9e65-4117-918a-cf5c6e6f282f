import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface finishLess {
    id: number
    finish: boolean
}

interface FinishTopicStore {
    finishLess: finishLess[]
    addFinishLess: (finishLess: finishLess) => void
    lessExists: (finishLess: finishLess) => boolean
}

const useFinishTopic = create(
    persist<FinishTopicStore>(
        (set, get) => ({
            finishLess: [{id: 0, finish: false}],
            lessExists: (finishLess: finishLess) => {
                const currentFinishLess = get().finishLess;
                const lessExists = currentFinishLess.find((c) => c.id === finishLess.id);
                return !!lessExists;
              },
            addFinishLess: (finishLess: finishLess) => {
                const currentFinishLess = get().finishLess
                const lessExists = currentFinishLess.find(c => c.id === finishLess.id)
                if (lessExists) {
                    return
                }
                set({ finishLess: [...get().finishLess, finishLess] })
            },
        }),
        {
            name: 'finishLesss-storage',
            storage: createJSONStorage<FinishTopicStore>(() => localStorage),
        }
    )
)

export default useFinishTopic
