import HeroImage from '@public/affiliate/hero.png'
import ImageShortcut from '@/components/Ui/Image'
import Button from '@/components/Ui/Button'
import Link from 'next/link'

const HelloZambia = () => {
    return (
        <section id='hero' className="w-full bg-yellow-light text-black ">
            <div className="- container flex flex-wrap max-md:flex-col-reverse max-md:items-center justify-between px-auto sm:px-14">
                <div className="flex flex-col items-start gap-9 max-md:gap-7 pb-20 max-md:pb-20 pt-24 max-md:pt-0 ">
                    <div className="flex flex-col items-start justify-center gap-4 max-md:gap-2  max-md:items-center  max-md:pt-0">
                        <h1 className="text-h3 font-semibold max-md:text-center max-md:text-b1 max-md:font-medium">
                            The ICP Web 3.0 <br className="max-md:hidden" /> Developers Program
                        </h1>
                        <p className="max-w-[575px] text-sub3 max-md:px-4 max-md:text-center max-md:text-cap2 max-md:leading-[18px]">
                            <p>
                                🌟 Multiply Your Salary 5-10X
                            </p>
                            <p>
                                🌍 Access Global Job Opportunities
                            </p>
                            <p>
                                💡 Stay Ahead in the Tech Game
                            </p>
                            <p>
                                Embrace the Future of Blockchain Technology
                            </p> 
                            <p className='mt-2'>
                                Learn, Innovate, and Lead in the Web3 Space
                            </p>
                        </p>
                    </div>
                    <div className=" w-full max-w-[261px] max-md:max-w-full">
                        <Button className="" rounded variant="primary">
                            {/* <Link href={'https://cdn.forms-content.sg-form.com/b7c37a74-13e3-11ee-81a4-d673f9b62626'}> */}
                            <Link href={'courses/web3-dev-program'}>
                            Enroll Now{'>'}
                            </Link>
                        </Button>
                    </div>
                </div>
                <ImageShortcut
                    src={'/zambia/PoweredBy.png'}
                    alt="Zambia Powered by"
                    height={300}
                    width={500}
                    priority
                    className="object-contain max-md:h-auto max-md:w-[191px] my-8 sm:my-auto"
                />
            </div>

        </section>
    )
}

export default HelloZambia
