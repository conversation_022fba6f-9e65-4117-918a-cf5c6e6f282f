import Image from 'next/image'
import React from 'react'
import Noones from '../../../components/Pages/Memberships/icons/Noones.jpg'
import Runestone from '../../../components/Pages/Memberships/icons/Runestone.jpg'
import Link from 'next/link'


const FeaturedVideos = () => {

    const featuredVideos = [
        {
            logo: Runestone,
            image: Runestone,
            title: "Bitcoin Runes - All You Need to Know",
            description: "Learn about Bitcoin Runes, a new token protocol on the Bitcoin network, based on social consensus. Join the Bitcoin army!",
            videoUrl: "https://www.youtube.com/watch?v=rGHZXDS5h80&ab_channel=CryptoUniversity",
        },
        {
            logo: Noones,
            image: Noones,
            title: "P2P trading - All you need to know",
            description: "NoOnes: Transforming financial communication globally. Trade, transact, and store wealth easily.",
            videoUrl: "https://www.youtube.com/watch?v=Yn8tj59yWkg&ab_channel=CryptoHustle",
        }
    ]

    return (
        <div className="border-t-[1px] p-[32px] md:p-[64px]">
            <section className="mx-auto w-full ">
                <h2 className=" mb-5 text-[22px] font-[600] md:mb-5 md:text-center md:text-[36px]">Featured Videos</h2>
            </section>
            <div className="flex justify-center">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:mt-3 md:flex">
                    {featuredVideos.map((featuredVideo) => (
                        <section key={featuredVideo.title} className="max-w-[344px] grid-cols-2  items-center rounded-[8px] border-[1px] border-[#FCC229] bg-[#FEEB8810] p-4 md:grid md:max-w-[580px] md:text-left">
                            <div>
                                <Image
                                    src={featuredVideo.logo}
                                    alt="video"
                                    width={260}
                                    height={146.65}
                                    className="hidden rounded-[4.0px] md:block p-2"
                                />
                            </div>
                            <div className="">
                                <span className="h-[28px] w-[121px] rounded-[64px] border-[1px] border-[#FCC229] bg-[#FCC22933] p-2 text-[12px] text-[#BD921F]">
                                    Featured Video
                                </span>
                                <h2 className="mt-3 text-[18px] font-[600]">{featuredVideo.title}</h2>
                                <Image
                                    src={featuredVideo.image}
                                    alt="video"
                                    width={260}
                                    height={146.65}
                                    className="mt-3 block rounded-[4.0px] md:hidden"
                                />
                                <p className="my-3 text-[14px] font-[400] text-[#5B5B5B]">
                                    {featuredVideo.description}
                                </p>
                                <Link
                                    target='_blank'
                                    aria-label="Watch Video"
                                    href={featuredVideo.videoUrl}
                                    className="float-right text-[#2655FF] md:float-none">
                                    Watch Video
                                </Link>
                            </div>
                        </section>
                    ))}
                </div>
            </div>
        </div>
    )
}

export default FeaturedVideos
