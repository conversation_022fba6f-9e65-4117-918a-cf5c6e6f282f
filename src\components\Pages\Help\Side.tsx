import SideImg from "@public/help/side.png";
import ReachUs from "@/components/Ui/ReachUs";
import ImageShortcut from "@/components/Ui/Image";

const Side = () => {
  return (
    <div className="flex flex-col justify-between max-md:grow bg-yellow-light px-16 h-full gap-[4vh] max-md:max-w-full max-md:w-full max-w-[621px] max-md:h-fit max-md:py-7 max-lg:px-3.5 max-md:px-4">
      <div>
        <ImageShortcut
          src={SideImg}
          priority
          alt="get in touch"
          className="max-md:h-52 max-md:w-52 max-md:hidden"
        />

        <div className="flex flex-col gap-1 max-md:gap-4">
          <h1 className="text-black text-start text-h3 leading-[150%] font-semibold sm:max-w-full max-md:text-headline max-md:font-medium">
            Help!
          </h1>
          <p className="text-black text-start text-sub3 max-md:text-cap2 max-w-[466px]">
            Having troubles navigating through our system or having a hard time
            figuring our how *this thing* works?!
          </p>
        </div>

        <p className="text-b3 font-medium max-md:text-sub3 mt-14">
          Don’t worry! We’re here and online 24x7 to help you.
        </p>
      </div>

      <ReachUs />
    </div>
  );
};

export default Side;
