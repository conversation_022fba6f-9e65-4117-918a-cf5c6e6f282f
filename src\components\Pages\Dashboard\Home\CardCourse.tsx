'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { Video } from '@public/home'
import React, { useState } from 'react'
import Image from '@/components/Ui/Image'
import Button from '@/components/Ui/Button'
import { CourseModelV2 } from '@/types/CourseModel'
import ProgressBar from '@/components/Ui/ProgressBar'

interface props {
    course: CourseModelV2
}

const CardCourse = ({ course }: props) => {
    const [onCLick, setOnClick] = useState(false)
    function capitalizeCourseName(courseName: string) {
        return courseName.replace(/\b\w/g, function (match) {
            return match.toUpperCase()
        })
    }
    return (
        <div
            className={`flex h-[300px] w-[260px] min-w-[260px] max-w-[260px] cursor-pointer flex-col gap-6 overflow-hidden rounded-lg border border-gray-700 transition-all max-md:w-full max-md:min-w-full max-md:max-w-[100%]  ${onCLick ? 'h-[350px] justify-between pb-2 ' : ''
                }`}>
            <div
                onClick={() => setOnClick(!onCLick)}
                className={cn(`flex flex-col gap-3 `, !onCLick ? 'pb-2' : 'pb-0')}>
                <div className="relative">
                    <Image
                        src={course.bannerImage}
                        width={207}
                        height={240}
                        className="h-[135px] w-full select-none rounded-lg object-cover  max-md:h-[154px]"
                        alt={course.name}
                    />
                    <div className="absolute inset-0 left-2 top-2 flex select-none items-start font-sans text-cap3">
                        <p
                            className={cn(
                                `rounded-full px-2 py-1`,
                                course.isEnrolled && course.notStarted
                                    ? ' bg-[#E9EEFF] text-blue'
                                    : course.isEnrolled &&
                                        !course.notStarted &&
                                        course.userWatchedVideosCount !== course.videosCount
                                        ? 'bg-[#FFF6DF] text-yellow'
                                        : course.userWatchedVideosCount === course.videosCount
                                            ? 'bg-[#E6FFF5] text-green-dark'
                                            : 'bg-[#E6FFF5] text-green-light'
                            )}>

                            {course.notStarted && course.isEnrolled && course.userWatchedVideosCount === 0
                                ? 'Not Started'
                                : course.isEnrolled &&
                                    course.userWatchedVideosCount !== course.videosCount
                                    ? 'In Progress'
                                    : course.userWatchedVideosCount === course.videosCount
                                        ? 'Finished'
                                        : 'Finished'}
                        </p>
                    </div>
                </div>
                <div className="flex flex-col gap-3 px-3">
                    <h2 className="select-none text-sub3 font-medium">
                        {capitalizeCourseName(course.name).length > 42 ? (
                            <span>{capitalizeCourseName(course.name).substring(0, 42)}...</span>
                        ) : (
                            <span>{capitalizeCourseName(course.name)}</span>
                        )}
                    </h2>
                    <div className="flex items-center gap-2">
                        <ProgressBar percent={(course.userWatchedVideosCount / course.videosCount) * 100} />
                        <p className="select-none font-sans text-cap1">
                            {course.userWatchedVideosCount}/{course.videosCount}
                        </p>
                    </div>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1">
                            <Video strok="#081228" />
                            <p className="select-none font-sans text-cap1 leading-none text-gray-900">
                                {course.videosCount} Topics
                            </p>
                        </div>
                        <div className="flex items-center gap-1">
                            <Video strok="#081228" />
                            <p className="select-none font-sans text-cap1 leading-none text-gray-900">
                                {course.lessonsCount} Lessons
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            {onCLick ? (
                <div className="px-2">
                    <Link aria-label="Continue Course" href={'/dashboard/' + course.slug}>
                        <Button disabled={course.videosCount === 0} rounded variant="primary">
                            Continue Course
                        </Button>
                    </Link>
                </div>
            ) : (
                ''
            )}
        </div>
    )
}

export default CardCourse
