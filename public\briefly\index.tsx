const Global = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"
            ></path>
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M8 3h1a28.424 28.424 0 000 18H8M15 3a28.424 28.424 0 010 18"
            ></path>
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M3 16v-1a28.424 28.424 0 0018 0v1M3 9a28.424 28.424 0 0118 0"
            ></path>
        </svg>
    );
};
const Certificate = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                fill="#2655FF"
                fillRule="evenodd"
                d="M5.25 12a.75.75 0 01.75-.75h8a.75.75 0 010 1.5H6a.75.75 0 01-.75-.75zM5.25 16a.75.75 0 01.75-.75h4a.75.75 0 010 1.5H6a.75.75 0 01-.75-.75z"
                clipRule="evenodd"
            ></path>
            <path
                fill="#2655FF"
                d="M7.945 3.25c-1.367 0-2.47 0-3.337.117-.9.12-1.658.38-2.26.981-.602.602-.86 1.36-.981 2.26-.117.867-.117 1.97-.117 3.337v4.11c0 1.367 0 2.47.117 3.337.12.9.38 1.658.981 2.26.602.602 1.36.86 2.26.982.867.116 1.97.116 3.337.116h7.594a5.934 5.934 0 01-1.496-1.5H8c-1.435 0-2.437-.002-3.192-.103-.734-.099-1.122-.28-1.399-.556-.277-.277-.457-.665-.556-1.4-.101-.755-.103-1.756-.103-3.191v-4c0-1.435.002-2.437.103-3.192.099-.734.28-1.122.556-1.399.277-.277.665-.457 1.4-.556C5.562 4.752 6.564 4.75 8 4.75h8c1.435 0 2.436.002 3.192.103.734.099 1.122.28 1.399.556.277.277.457.665.556 1.4.101.754.103 1.756.103 3.191v.487a5.901 5.901 0 011.5.942V9.945c0-1.367 0-2.47-.116-3.337-.122-.9-.38-1.658-.982-2.26-.602-.602-1.36-.86-2.26-.981-.867-.117-1.97-.117-3.337-.117h-8.11z"
            ></path>
            <path
                fill="#2655FF"
                fillRule="evenodd"
                d="M19.25 11.25a4 4 0 00-3 6.646V22a.75.75 0 001.097.665l1.953-1.02 1.953 1.02A.75.75 0 0022.35 22v-4.222a4 4 0 00-3.1-6.528zm-2.5 4a2.5 2.5 0 115 0 2.5 2.5 0 01-5 0zm4.1 3.667a3.988 3.988 0 01-1.6.333c-.53 0-1.037-.103-1.5-.29v1.803l1.203-.628a.75.75 0 01.694 0l1.203.628v-1.846z"
                clipRule="evenodd"
            ></path>
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeWidth="1.5"
                d="M6 12h8M6 16h4"
            ></path>
        </svg>
    )
}
const Mobile = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M20 7v10c0 4-1 5-5 5H9c-4 0-5-1-5-5V7c0-4 1-5 5-5h6c4 0 5 1 5 5zM14 5.5h-4"
            ></path>
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M12 19.1a1.55 1.55 0 100-3.1 1.55 1.55 0 000 3.1z"
            ></path>
        </svg>
    )
}
const Translate = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M19.06 18.67l-2.14-4.27-2.14 4.27M15.17 17.91h3.52"
            ></path>
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M16.92 22a5.08 5.08 0 11.001-10.162A5.08 5.08 0 0116.92 22zM5.02 2h3.92c2.07 0 3.07 1 3.02 3.02v3.92c.05 2.07-.95 3.07-3.02 3.02H5.02C3 12 2 11 2 8.93V5.01C2 3 3 2 5.02 2zM9.01 5.85H4.95M6.969 5.17v.68M7.99 5.84c0 1.75-1.37 3.17-3.05 3.17M9.01 9.01c-.73 0-1.39-.39-1.85-1.01M2 15c0 3.87 3.13 7 7 7l-1.05-1.75M22 9c0-3.87-3.13-7-7-7l1.05 1.75"
            ></path>
        </svg>
    )
}
const Unlimited = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
        >
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M10.18 9.32a4.474 4.474 0 00-3.64-1.87c-2.51 0-4.55 2.04-4.55 4.55s2.04 4.55 4.55 4.55c1.69 0 3.26-.89 4.13-2.34L12 12l1.32-2.21a4.821 4.821 0 014.13-2.34C19.96 7.45 22 9.49 22 12s-2.04 4.55-4.55 4.55c-1.5 0-2.81-.74-3.64-1.87"
            ></path>
        </svg>
    )
}
const Video = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        stroke="#2655FF"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M16.298 15.538c.08 1.832-1.398 3.382-3.303 3.46-.14.005-6.979-.008-6.979-.008-1.895.144-3.554-1.218-3.704-3.044-.01-.136-.008-7.474-.008-7.474-.084-1.834 1.393-3.387 3.298-3.468.143-.007 6.973.006 6.973.006 1.904-.142 3.568 1.23 3.715 ************.008 7.464.008 7.464z"
        clipRule="evenodd"
      ></path>
      <path
        stroke="#2655FF"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M16.3 9.98l3.294-2.695c.816-.668 2.04-.086 2.039.967L21.62 15.6c-.001 1.053-1.226 1.63-2.04.962l-3.28-2.695"
      ></path>
    </svg>
)
export { Global, Certificate, Mobile, Translate, Unlimited, Video };