import IndicatorsCard from './Card'
import { IndicatorModel } from '@/types/IndicatorModel'

interface IndicatorsProps {
    indicators: IndicatorModel[],
}
const Indicators = ({ indicators }: IndicatorsProps) => {
    const links = [
        "nitros-bull",
        "visual-assistance",
        "ema-trading",
        "trend-momentum"
    ];

    return (
        <section className="container mx-auto" id="courses">
            <div className="space-y-8 pb-16 pt-11 max-md:space-y-3 max-md:border-b max-md:border-gray-700 max-md:pb-14 max-md:pt-9">
                <h3 className="text-headline font-medium max-md:text-callout">Trading Indicators</h3>

                <div className="grid grid-cols-3 gap-9 max-lg:grid-cols-2 max-md:grid-cols-1 max-md:gap-[1.125rem]">
                    {indicators != undefined ? (
                        indicators.map((indicator, index) => (
                            <IndicatorsCard
                                key={indicator.id}
                                indicator={indicator}
                                link={links[index % links.length]} // Assuming the links array corresponds to the indicators
                            />
                        ))
                    ) : (
                        <p>No Indicators available</p>
                    )}
                </div>
            </div>
        </section>
    )
}

export default Indicators



// import IndicatorsCard from './Card'
// import { IndicatorModel } from '@/types/IndicatorModel'

// interface IndicatorsProps {
//     indicators: IndicatorModel[]
// }

// const Indicators = ({ indicators }: IndicatorsProps) => {

//     const links = [
//         "nitros-bull",
//         "visual-assistance",
//         "ema-trading",
//         "trend-momentum"
//     ];
    


//     return (
//         <section className="container mx-auto" id="courses">
//             <div className="space-y-8 pb-16 pt-11 max-md:space-y-3 max-md:border-b max-md:border-gray-700 max-md:pb-14 max-md:pt-9">
//                 <h3 className="text-headline font-medium max-md:text-callout">Trading Indicators</h3>

//                 <div className="grid grid-cols-3 gap-9 max-lg:grid-cols-2 max-md:grid-cols-1 max-md:gap-[1.125rem]">
//                     {indicators != undefined ? (
//                         indicators.map((indicator: any) => <IndicatorsCard key={indicator.id} indicator={indicator} />)
//                     ) : (
//                         <p>No Indicators available</p>
//                     )}
//                 </div>
//             </div>
//         </section>
//     )
// }

// export default Indicators
