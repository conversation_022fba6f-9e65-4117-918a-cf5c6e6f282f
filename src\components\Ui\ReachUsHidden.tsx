import Link from 'next/link'
import ImageShortcut from './Image'
import getInTouch2 from '@public/marketing/getintouch24.png'

const ReachUsHidden = () => {
    return (
        <div className="mt-[8vh] flex w-full flex-col p-3 pb-7 max-lg:mt-0 md:hidden">
            <hr className="mx-auto w-full border-t-[0.5px] border-solid border-gray-700" />
            <ImageShortcut src={getInTouch2} alt="get in touch" width={190} height={190} className="pb-4" />
            <h6 className="flex flex-col justify-center gap-0.5">
                <span className="font- max-w-md text-start text-sub2 font-semibold text-black">
                    You can also reach us on:
                </span>
                <span className="text-start text-sub2 font-semibold text-black">
                    <Link
                        aria-label="Reach Us"
                        href="mailto:<EMAIL>"
                        className="font-normal text-blue ">
                        E-mail: <EMAIL>
                    </Link>
                </span>
            </h6>
        </div>
    )
}

export default ReachUsHidden
