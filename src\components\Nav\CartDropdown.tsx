'use client'

import { Menu, Transition } from '@/lib/headlessui'
import { Cart as WhishlistIcon } from '@public/home'
import WishlistCard from '../Pages/Cart/WishlistCard'
import Button from '../Ui/Button'
import { useEffect, useState } from 'react'
import { cn } from '@/lib/cn'
import Link from 'next/link'
import useCart from '@/hooks/useCart'

const Cart = () => {
    const cart = useCart()
    const [isMounted, setIsMounted] = useState(false)
    const [totalPrice, setTotalPrice] = useState(0)

    useEffect(() => {
        setIsMounted(true)
        setTotalPrice(cart.products.reduce((total, item) => total + item.priceWithoutTax, 0))
    }, [cart.products])
    if (!isMounted) return null
    return (
        <Menu className={'list-none'} as="li">
            {({ open, close }) => (
                <>
                    <Menu.Button
                        aria-label="Cart Button"
                        className={
                            'relative flex cursor-pointer items-center gap-[0.625rem] text-sub3 transition-[font-weight] duration-300 '
                        }>
                        <WhishlistIcon black={cart.products.length > 0} />
                        <div
                            className={cn(
                                'absolute inset-0 left-3 top-3 flex h-[20px] w-[20px] items-center justify-center rounded-full bg-blue ',
                                cart.products.length > 0 ? '' : 'hidden'
                            )}>
                            <p className="text-cap2 text-white"> {cart.products.length}</p>
                        </div>
                    </Menu.Button>
                    <Transition
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95">
                        <Menu.Items className="-px-[24px] absolute right-0 mt-10 max-w-[760px] select-none space-y-3 rounded-[0.5rem] bg-white p-6 font-sans text-sub3 shadow-[0px_40px_80px_rgba(0,0,0,0.1)]">
                            {cart.products.length > 0 ? (
                                <ul className="flex flex-col gap-4">
                                    {cart.products.slice(0, 3).map(course => (
                                        <WishlistCard key={course.id} course={course} removeBorder removeButton />
                                    ))}
                                </ul>
                            ) : (
                                <div className="flex flex-col items-center gap-4">
                                    <WhishlistIcon />
                                    <p className="text-sub2 capitalize">Your Cart is empty</p>
                                </div>
                            )}

                            <div
                                className={cn(
                                    'border-b border-gray-700 px-6',
                                    cart.products.length == 0 ? 'opacity-0' : ''
                                )}
                            />
                            {cart.products.length > 0 && <p className="text-sub2 font-medium">Total: ${totalPrice} </p>}
                            <div className="w-[343px] border-gray-700 pb-2" onClick={close}>
                                <Link aria-label="Cart" href={'/cart'}>
                                    <Button className="!py-2" rounded variant="primary">
                                        Go To Cart
                                    </Button>
                                </Link>
                            </div>
                        </Menu.Items>
                    </Transition>
                </>
            )}
        </Menu>
    )
}

export default Cart
