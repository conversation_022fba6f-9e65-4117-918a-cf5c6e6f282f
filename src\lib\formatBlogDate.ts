export default function formatDate(dateString: string) {
    const date = new Date(dateString)
    const options: Intl.DateTimeFormatOptions = {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
    }
    const formattedDate = new Intl.DateTimeFormat('en-US', options).format(date).replace(',', '')
    const [month, day, year] = formattedDate.split(' ')
    return `${day} ${month} ${year}`
}
