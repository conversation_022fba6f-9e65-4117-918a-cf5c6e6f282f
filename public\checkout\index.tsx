function Card() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                stroke="#081228"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeWidth="1.5"
                d="M2 8.505h20M6 16.505h2M10.5 16.505h4"></path>
            <path
                stroke="#081228"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M6.44 3.505h11.11c3.56 0 4.45.88 4.45 4.39v8.21c0 3.51-.89 4.39-4.44 4.39H6.44c-3.55.01-4.44-.87-4.44-4.38v-8.22c0-3.51.89-4.39 4.44-4.39z"></path>
        </svg>
    )
}
function Bitcoin() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" fill="none" viewBox="0 0 52 52">
            <path
                fill="#060E1E"
                d="M34.953 23.649c.375-2.522-1.532-3.878-4.138-4.783l.845-3.416-2.064-.518-.824 3.327c-.542-.137-1.098-.265-1.654-.392l.83-3.349L25.884 14l-.845 3.416c-.45-.104-.891-.205-1.319-.313l.003-.011-2.848-.716-.549 2.22s1.532.355 1.5.376c.836.21.987.768.962 1.21l-.963 3.893c.057.014.131.036.215.068l-.219-.054-1.35 5.452c-.102.255-.361.64-.946.494.021.03-1.5-.377-1.5-.377L17 32.038l2.687.675c.5.126.99.258 1.47.382l-.854 3.455 2.063.518.846-3.417c.563.153 1.11.295 1.645.43l-.843 3.402 2.064.517.854-3.448c3.52.671 6.167.4 7.282-2.807.898-2.582-.045-4.072-1.897-5.043 1.35-.313 2.365-1.207 2.636-3.053zm-4.718 6.662c-.636 2.583-4.954 1.186-6.353.836l1.134-4.577c1.4.352 5.887 1.049 5.22 3.741zm.64-6.7c-.582 2.35-4.175 1.155-5.34.863l1.028-4.15c1.164.292 4.918.837 4.311 3.287z"></path>
            <rect width="51" height="51" x="0.5" y="0.5" stroke="#AAA" rx="25.5"></rect>
        </svg>
    )
}
function Eth() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <g clipPath="url(#clip0_78_104942)">
                <path
                    fill="#627EEA"
                    d="M16 32c8.837 0 16-7.163 16-16S24.837 0 16 0 0 7.163 0 16s7.163 16 16 16z"></path>
                <path fill="#fff" fillOpacity="0.602" d="M16 4v8.87l7.497 3.35L16 4z"></path>
                <path fill="#fff" d="M16.498 4L9 16.22l7.498-3.35V4z"></path>
                <path fill="#fff" fillOpacity="0.602" d="M16 22.352v6.027L23.502 18 16 22.352z"></path>
                <path fill="#fff" d="M16.498 28.379v-6.028L9 18l7.498 10.379z"></path>
                <path fill="#fff" fillOpacity="0.2" d="M16 20.701l7.497-4.353L16 13v7.701z"></path>
                <path fill="#fff" fillOpacity="0.602" d="M9 16.348l7.498 4.353V13L9 16.348z"></path>
            </g>
            <defs>
                <clipPath id="clip0_78_104942">
                    <path fill="#fff" d="M0 0H32V32H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}
function Usdt() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <g clipPath="url(#clip0_78_104943)">
                <path
                    fill="#26A17B"
                    d="M16 32c8.837 0 16-7.163 16-16S24.837 0 16 0 0 7.163 0 16s7.163 16 16 16z"></path>
                <path
                    fill="#fff"
                    fillRule="evenodd"
                    d="M17.622 17.564v-.002c-.11.008-.677.042-1.942.042-1.01 0-1.721-.03-1.971-.042v.003c-3.888-.171-6.79-.848-6.79-1.658 0-.809 2.902-1.486 6.79-1.66v2.644c.254.018.982.061 1.988.061 1.207 0 1.812-.05 1.925-.06v-2.643c3.88.173 6.775.85 6.775 1.658 0 .81-2.895 1.485-6.775 1.657zm0-3.59v-2.366h5.414V8H8.295v3.608h5.414v2.365C9.309 14.175 6 15.047 6 16.091c0 1.044 3.309 1.915 7.709 2.118v7.582h3.913v-7.584c4.393-.202 7.694-1.073 7.694-2.116 0-1.043-3.301-1.914-7.694-2.117z"
                    clipRule="evenodd"></path>
            </g>
            <defs>
                <clipPath id="clip0_78_104943">
                    <path fill="#fff" d="M0 0H32V32H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}
function Shib() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <path
                fill="#F00500"
                d="M22.712 3.641a19.106 19.106 0 00-2.107 2.5l-.2-.064a14.43 14.43 0 00-3.53-.684 27.638 27.638 0 00-2.146 0c-1.326.088-2.343.287-3.499.689-.046.018-.087.032-.129.046a18.82 18.82 0 00-2.177-2.482 15.134 15.134 0 0113.603-.097l.185.092zM29.868 21.174a14.435 14.435 0 01-2.588 4.54 16.078 16.078 0 01-2.025 1.982 14.937 14.937 0 01-5.838 2.88 15.298 15.298 0 01-7.174 0 14.906 14.906 0 01-5.838-2.88c-.73-.6-1.41-1.262-2.025-1.983A14.266 14.266 0 011.792 11.81 15.1 15.1 0 012.97 9.267c.217-.37.485-.786.614-.957.444 1.91.99 3.587 1.193 4.193-.014.032-.032.069-.051.101-1.077 2.182-1.66 4.285-1.798 6.522 0 .028-.005.06-.005.088-.037.67-.018.99.093 1.41.346 1.317 1.414 2.769 3.087 4.193 2.742 2.33 6.458 4.012 9.273 4.206 2.935.199 7.266-1.516 10.15-4.022a14.5 14.5 0 001.276-1.28c.287-.333.712-.906.68-.906-.01 0 0-.01.018-.018.014-.01.028-.024.019-.033-.005-.004.005-.018.018-.023.014-.005.023-.014.019-.023-.005-.01 0-.018.018-.023.014-.005.019-.019.014-.028-.004-.01 0-.018.01-.018.009 0 .018-.014.018-.024 0-.009.01-.023.018-.023a.02.02 0 00.019-.018.488.488 0 01.06-.106c.07-.107.347-.601.384-.684.263-.597.43-1.128.508-1.66.037-.254.06-.721.042-.832-.005-.018-.005-.05-.01-.102-.009-.092-.013-.23-.023-.37-.009-.207-.027-.499-.041-.642-.199-2.098-.735-3.813-1.752-5.63a1.597 1.597 0 01-.102-.194c-.009-.014-.014-.028-.018-.037.143-.43.73-2.228 1.197-4.285l.01.01.069.092c.12.161.439.642.582.873a14.555 14.555 0 011.52 3.398c.837 2.87.768 5.93-.212 8.76z"></path>
            <path
                fill="#000"
                d="M23.868 15.65c-.014.074-.231.296-.449.458-.638.466-1.78.901-2.94 1.11-.66.12-1.322.138-1.52.04-.13-.064-.148-.115-.102-.281.097-.347.411-.726.901-1.082.25-.18 1.262-.795 1.734-1.054.776-.425 1.405-.68 1.83-.74.134-.018.296-.023.347 0 .083.033.175.278.212.574.019.157.01.85-.013.975zM12.978 17.18c-.028.055-.166.116-.323.139-.158.023-.638.009-.911-.023-.98-.13-2.02-.43-2.778-.8-.425-.208-.73-.416-.957-.643l-.13-.134-.013-.175c-.023-.306-.019-.786.018-.957.023-.143.074-.282.143-.407.037-.046.037-.046.204-.046.203 0 .374.032.652.12.568.176 1.414.6 2.445 1.22.85.509 1.17.758 1.414 1.1.167.213.278.513.236.606zM19.524 23.614c0 .014-.055.236-.125.5-.07.262-.125.475-.125.484-.05.005-.097.01-.148.005h-.147l-.204.48-.226.546-.028.065-.097-.157-.097-.157V24.1l-.037.008a15.21 15.21 0 01-.883.116c-1.012.102-2.03.079-3.037-.065a3.115 3.115 0 00-.305-.037c-.005.005 0 .3.01.666l.013.656-.078.12c-.042.065-.084.12-.084.125-.014.014-.06-.06-.166-.272a3.125 3.125 0 01-.259-.703l-.032-.134-.143.01-.144.013-.037-.171a4.921 4.921 0 01-.05-.314l-.014-.148-.12-.107c-.07-.06-.14-.12-.153-.129-.023-.019-.033-.05-.033-.079V23.6l.578.005.578.004.019.06.018.06.199.01.776.023.578.014.148-.232.152-.23h.195l-.005-.481-.005-.481-.254-.111c-.818-.36-1.299-.772-1.479-1.271-.037-.102-.037-.139-.046-.601-.005-.458-.005-.5.027-.601a.672.672 0 01.477-.471c.078-.024.286-.024 1.377-.024l1.285.005.143.07c.171.083.25.143.347.272.111.148.143.264.143.546 0 .402-.027.748-.069.887-.06.19-.152.37-.268.532-.231.296-.652.591-1.035.725l-.111.042.004.485.005.486.102.009.101.01.139.207.134.208h.62c.341 0 .642.005.67.01.046.009.055.004.101-.075l.056-.083h.531c.398-.018.518-.009.518.005z"></path>
            <path
                fill="#000"
                d="M17.082 24.94c-.088.052-.148.075-.162.066-.014-.005-.078-.06-.148-.111l-.124-.102-.13.139c-.282.305-.291.319-.374.323-.13.014-.157-.01-.315-.245-.083-.12-.148-.222-.148-.222s-.06-.009-.129-.014l-.13-.014-.06.125-.06.125-.106-.032a2.26 2.26 0 01-.245-.097l-.139-.07v-.263l1.23.005 1.23.004.004.125c.005.148.01.143-.194.259z"></path>
            <path
                fill="#fff"
                d="M28.638 19.302c-.005-.018-.005-.05-.01-.102-1.109-.12-4.275-.194-7.215 2.233 0 0-.948-4.345-5.385-4.345-4.437 0-6.083 4.345-6.083 4.345-2.487-2.676-5.815-2.482-7.017-2.307 0 .028-.004.06-.004.088-.037.67-.019.99.092 1.41.347 1.317 1.414 2.769 3.088 4.193 2.74 2.33 6.457 4.012 9.272 4.206 2.935.199 7.267-1.516 10.15-4.022.454-.402.88-.827 1.277-1.28.286-.333.712-.906.68-.906-.01 0 0-.01.018-.018.014-.01.027-.024.018-.033-.004-.005.005-.018.019-.023.014-.005.023-.014.018-.023-.005-.01 0-.019.019-.023.014-.005.018-.019.014-.028-.005-.01 0-.018.009-.018s.018-.014.018-.024c0-.009.01-.023.019-.023s.018-.009.018-.018a.483.483 0 01.06-.106c.07-.107.347-.601.384-.684.263-.597.43-1.128.508-1.66.028-.254.051-.721.033-.832zm-11.556 5.64c-.088.05-.148.073-.162.064-.014-.005-.079-.06-.148-.11l-.125-.103-.13.139c-.281.305-.29.319-.373.324-.13.014-.158-.01-.315-.245-.083-.12-.148-.222-.148-.222s-.06-.01-.13-.014l-.129-.014-.06.125-.06.125-.106-.033a2.261 2.261 0 01-.245-.097l-.139-.07v-.263l1.23.005 1.23.005.004.124c.005.148.01.144-.194.26zm2.311-.833c-.07.264-.125.476-.125.486-.05.004-.097.009-.148.004h-.148l-.198.486c-.111.263-.213.508-.227.545l-.028.065-.097-.157-.097-.158V24.1l-.037.01c-.078.018-.638.092-.883.115-1.012.101-2.029.078-3.037-.065a3.119 3.119 0 00-.305-.037c-.004.005 0 .3.01.666l.014.656-.079.12c-.042.065-.083.12-.083.125-.014.014-.06-.06-.167-.273a3.123 3.123 0 01-.258-.702l-.033-.134-.143.009-.143.014-.037-.171a4.837 4.837 0 01-.051-.314l-.014-.148-.12-.107a4.39 4.39 0 00-.153-.13c-.023-.018-.032-.05-.032-.078v-.055l.578.004.577.005.019.06.018.06.2.01.776.023.578.013.147-.23.153-.232h.194l-.005-.48-.004-.481-.254-.111c-.819-.36-1.3-.772-1.48-1.271-.037-.102-.037-.139-.046-.601-.004-.458-.004-.5.028-.601a.672.672 0 01.476-.472c.079-.023.287-.023 1.378-.023l1.285.005.143.07c.17.082.25.142.346.272.111.148.144.263.144.545 0 .402-.028.75-.07.888-.06.19-.152.37-.268.531-.23.296-.651.592-1.035.726l-.111.042.005.485.004.485.102.01.102.009.138.208.134.208h.62c.342 0 .642.005.67.01.046.008.056.004.102-.075l.055-.083h.532c.411 0 .531.005.531.018-.014-.004-.074.222-.143.481z"></path>
            <path
                fill="#FFA409"
                d="M28.602 18.83c-.01-.208-.028-.499-.042-.642-.199-2.099-.735-3.814-1.752-5.63a1.597 1.597 0 01-.101-.194c-.01-.014-.014-.028-.019-.038.143-.43.73-2.228 1.197-4.284.634-2.792 1.036-6.056-.194-7.036 0 0-2.126-.157-4.973 2.635a19.099 19.099 0 00-2.108 2.5l-.2-.064a14.428 14.428 0 00-3.53-.684 27.638 27.638 0 00-2.145 0c-1.327.088-2.344.287-3.5.689a2.28 2.28 0 01-.129.046A18.812 18.812 0 008.93 3.646C5.966.84 3.761 1.002 3.761 1.002c-1.303 1.012-.86 4.423-.185 7.308.444 1.909.99 3.587 1.193 4.192-.014.032-.032.07-.051.102-1.077 2.182-1.66 4.285-1.798 6.522 1.206-.176 4.53-.374 7.021 2.307 0 0 1.646-4.345 6.083-4.345 4.438 0 5.385 4.345 5.385 4.345a9.851 9.851 0 017.216-2.233l-.023-.37zm-22.96-8.07S3.914 6.437 4.357 3.97c.074-.417.213-.782.43-1.06 0 0 1.96.195 5.122 3.708 0 0-.601.291-1.405.91 0 0-.005.005-.01.005-.873.67-1.992 1.734-2.852 3.226zm7.336 6.42c-.028.055-.166.116-.323.139-.157.023-.638.009-.911-.023-.98-.13-2.02-.43-2.778-.8-.425-.208-.73-.416-.957-.643l-.13-.134-.013-.175c-.023-.305-.019-.786.018-.957.023-.143.074-.282.144-.407.037-.046.037-.046.203-.046.203 0 .374.032.652.12.568.176 1.414.6 2.445 1.22.85.509 1.17.758 1.414 1.1.167.213.278.514.236.606zm10.89-1.53c-.013.074-.23.296-.448.458-.638.466-1.78.901-2.94 1.109-.66.12-1.322.139-1.52.042-.13-.065-.148-.116-.102-.282.097-.347.411-.726.901-1.082.25-.18 1.262-.795 1.734-1.054.776-.425 1.405-.68 1.83-.74.134-.018.296-.023.347 0 .083.033.175.278.212.574.019.157.01.85-.014.975zm-.767-8.121l-.01-.005c-.776-.62-1.358-.91-1.358-.91 3.055-3.514 4.946-3.708 4.946-3.708.208.282.342.643.416 1.059.43 2.468-1.239 6.794-1.239 6.794a11.01 11.01 0 00-2.755-3.23z"></path>
            <path
                fill="#FF9300"
                d="M27.099 3.965c-.36-.046-2.066-.056-3.998 3.564l-.01-.005c-.776-.62-1.359-.91-1.359-.91 3.056-3.514 4.946-3.708 4.946-3.708.213.282.347.643.421 1.059z"></path>
            <path
                fill="#FF8300"
                d="M25.861 10.76a10.967 10.967 0 00-2.76-3.23c1.933-3.62 3.634-3.611 3.999-3.565.43 2.473-1.239 6.795-1.239 6.795zM27.169 3.979l-.07-.014.07.014z"></path>
            <path
                fill="#FF9300"
                d="M9.908 6.613s-.601.292-1.406.911c0 0-.004.005-.009.005-2.001-3.624-3.762-3.61-4.141-3.564.074-.416.212-.781.43-1.059.004 0 1.964.194 5.126 3.707z"></path>
            <path
                fill="#FF8300"
                d="M8.5 7.53c-.879.674-1.997 1.732-2.857 3.23 0 0-1.729-4.322-1.285-6.795.374-.046 2.14-.055 4.142 3.564zM4.357 3.965l-.074.014.074-.014z"></path>
            <path
                fill="#fff"
                d="M21.183 13.159s-1.479.092-1.294-1.064c.185-1.155 1.34-1.294 1.664-1.248.324.047 1.618.509 1.387 1.48-.231.97-.555.785-.74.832-.185.046-1.017 0-1.017 0zM10.273 13.159s-1.479.092-1.294-1.064c.185-1.155 1.34-1.294 1.664-1.248.323.047 1.618.509 1.387 1.48-.231.97-.555.785-.74.832-.185.046-1.017 0-1.017 0z"></path>
        </svg>
    )
}
function Sol() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <circle cx="16" cy="16" r="16" fill="#000"></circle>
            <g clipPath="url(#clip0_78_105362)">
                <path
                    fill="url(#paint0_linear_78_105362)"
                    d="M10.085 19.448a.62.62 0 01.44-.182h15.164c.277 0 .415.337.22.534l-2.996 3.017a.62.62 0 01-.44.183H7.31a.313.313 0 01-.22-.534l2.995-3.018z"></path>
                <path
                    fill="url(#paint1_linear_78_105362)"
                    d="M10.085 8.183a.636.636 0 01.44-.183h15.164c.277 0 .415.337.22.534l-2.996 3.018a.62.62 0 01-.44.182H7.31a.313.313 0 01-.22-.534l2.995-3.017z"></path>
                <path
                    fill="url(#paint2_linear_78_105362)"
                    d="M22.913 13.78a.62.62 0 00-.44-.183H7.31a.313.313 0 00-.22.534l2.995 3.017a.62.62 0 00.44.183h15.164a.313.313 0 00.22-.534l-2.996-3.018z"></path>
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_78_105362"
                    x1="24.24"
                    x2="13.625"
                    y1="6.198"
                    y2="26.382"
                    gradientUnits="userSpaceOnUse">
                    <stop stopColor="#00FFA3"></stop>
                    <stop offset="1" stopColor="#DC1FFF"></stop>
                </linearGradient>
                <linearGradient
                    id="paint1_linear_78_105362"
                    x1="19.651"
                    x2="9.036"
                    y1="3.784"
                    y2="23.969"
                    gradientUnits="userSpaceOnUse">
                    <stop stopColor="#00FFA3"></stop>
                    <stop offset="1" stopColor="#DC1FFF"></stop>
                </linearGradient>
                <linearGradient
                    id="paint2_linear_78_105362"
                    x1="21.931"
                    x2="11.316"
                    y1="4.983"
                    y2="25.168"
                    gradientUnits="userSpaceOnUse">
                    <stop stopColor="#00FFA3"></stop>
                    <stop offset="1" stopColor="#DC1FFF"></stop>
                </linearGradient>
                <clipPath id="clip0_78_105362">
                    <path fill="#fff" d="M0 0H19V15H0z" transform="translate(7 8)"></path>
                </clipPath>
            </defs>
        </svg>
    )
}
function Bch() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <g clipPath="url(#clip0_78_105361)">
                <path
                    fill="#58BE91"
                    d="M16 32c8.837 0 16-7.163 16-16S24.837 0 16 0 0 7.163 0 16s7.163 16 16 16z"></path>
                <path
                    fill="#fff"
                    d="M21.23 10.523c-.776-1.972-2.722-2.15-4.988-1.71L15.435 6l-1.712.491.786 2.74c-.45.128-.908.27-1.363.41l-.79-2.758-1.711.49.805 2.813c-.368.114-.73.226-1.085.328l-.003-.01L8 11.181l.525 1.83s1.258-.388 1.243-.358c.694-.199 1.035.139 1.2.468l.92 3.204c.047-.013.11-.029.184-.04l-.181.052 1.287 4.49c.032.227.004.612-.48.752.027.013-1.246.356-1.246.356l.247 2.143 2.228-.64c.415-.117.825-.227 1.226-.34l.817 2.845 1.71-.49-.807-2.815c.459-.122.916-.248 1.372-.38l.802 2.803 1.713-.491-.814-2.84c2.831-.991 4.638-2.294 4.113-5.07-.422-2.234-1.724-2.912-3.471-2.836.848-.79 1.213-1.858.642-3.3v-.001zm-.65 6.77c.61 2.127-3.1 2.929-4.26 3.263l-1.081-3.77c1.16-.333 4.704-1.71 5.34.508l.001-.001zm-2.322-5.09c.554 1.935-2.547 2.58-3.514 2.857l-.98-3.419c.966-.277 3.915-1.455 4.494.563v-.001z"></path>
            </g>
            <defs>
                <clipPath id="clip0_78_105361">
                    <path fill="#fff" d="M0 0H32V32H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}
function Bnb() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <g clipPath="url(#clip0_78_105360)">
                <path
                    fill="#F3BA2F"
                    d="M16 32c8.837 0 16-7.163 16-16S24.837 0 16 0 0 7.163 0 16s7.163 16 16 16z"></path>
                <path
                    fill="#fff"
                    d="M12.116 14.404L16 10.52l3.886 3.886 2.26-2.26L16 6l-6.144 6.144 2.26 2.26zM6 16l2.26-2.26L10.52 16l-2.26 2.26L6 16zm6.116 1.596L16 21.48l3.886-3.886 2.26 2.259L16 26l-6.144-6.144-.003-.003 2.263-2.257zM21.48 16l2.26-2.26L26 16l-2.26 2.26L21.48 16zm-3.188-.002h.002V16L16 18.294l-2.291-2.29-.004-.004.004-.003.401-.402.195-.195L16 13.706l2.293 2.293-.001-.001z"></path>
            </g>
            <defs>
                <clipPath id="clip0_78_105360">
                    <path fill="#fff" d="M0 0H32V32H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}
function Btc() {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 32 32">
            <g clipPath="url(#clip0_78_104944)">
                <path
                    fill="#F7931A"
                    d="M16 32c8.837 0 16-7.163 16-16S24.837 0 16 0 0 7.163 0 16s7.163 16 16 16z"></path>
                <path
                    fill="#fff"
                    d="M23.032 14.02c.314-2.096-1.283-3.223-3.465-3.975l.708-2.84-1.728-.43-.69 2.765c-.454-.114-.92-.22-1.385-.326l.695-2.783L15.439 6l-.708 2.839c-.376-.086-.746-.17-1.104-.26l.002-.009-2.384-.595-.46 1.846s1.283.294 1.256.312c.7.175.826.638.805 1.006l-.806 3.235c.*************.18.057l-.183-.045-1.13 4.532c-.086.212-.303.531-.793.41.018.025-1.256-.313-1.256-.313L8 20.993l2.25.561c.418.105.828.215 1.231.318l-.715 2.872 1.727.43.708-2.84c.472.127.93.245 1.378.357l-.706 2.828 1.728.43.715-2.866c2.948.558 5.164.333 6.097-2.333.752-2.146-.037-3.385-1.588-4.192 1.13-.26 1.98-1.003 2.207-2.538zm-3.95 5.538c-.533 2.147-4.148.986-5.32.695l.95-3.805c1.172.293 4.929.872 4.37 3.11zm.535-5.569c-.487 1.953-3.495.96-4.47.717l.86-3.45c.975.243 4.118.696 3.61 2.733z"></path>
            </g>
            <defs>
                <clipPath id="clip0_78_104944">
                    <path fill="#fff" d="M0 0H32V32H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}
function Coinbase() {
    return (
        <svg width="81" height="15" viewBox="0 0 81 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_61_89)">
                <path
                    d="M16.3479 4.18771C13.4054 4.18771 11.1062 6.50338 11.1062 9.60338C11.1062 12.7034 13.3473 14.9992 16.3479 14.9992C19.3486 14.9992 21.6286 12.6638 21.6286 9.58357C21.6286 6.5232 19.3875 4.18771 16.3479 4.18771ZM16.3678 12.7651C14.6921 12.7651 13.4642 11.416 13.4642 9.60415C13.4642 7.77174 14.6723 6.42334 16.3479 6.42334C18.0435 6.42334 19.2706 7.79232 19.2706 9.60415C19.2706 11.416 18.0435 12.7651 16.3678 12.7651ZM22.2712 6.54378H23.733V14.7988H26.0711V4.38894H22.2712V6.54378ZM5.22187 6.42258C6.44977 6.42258 7.42399 7.20768 7.79383 8.37543H10.2687C9.82023 5.87911 7.8328 4.18771 5.24172 4.18771C2.29918 4.18771 0 6.50338 0 9.60415C0 12.7049 2.2411 15 5.24172 15C7.77472 15 9.80111 13.3086 10.2496 10.7917H7.79383C7.44311 11.9594 6.46888 12.7651 5.24099 12.7651C3.54546 12.7651 2.35727 11.416 2.35727 9.60415C2.358 7.77174 3.52708 6.42258 5.22187 6.42258ZM66.6976 8.59724L64.9829 8.33579C64.1646 8.21536 63.58 7.93333 63.58 7.26866C63.58 6.54378 64.3403 6.18172 65.3726 6.18172C66.5027 6.18172 67.224 6.68479 67.3799 7.51029H69.6401C69.3865 5.41643 67.8277 4.18848 65.4314 4.18848C62.9565 4.18848 61.3198 5.49723 61.3198 7.34946C61.3198 9.12089 62.3918 10.1484 64.5543 10.47L66.2689 10.7315C67.1071 10.8519 67.5747 11.1949 67.5747 11.839C67.5747 12.6645 66.7564 13.0068 65.6263 13.0068C64.2425 13.0068 63.4631 12.4229 63.3462 11.5372H61.047C61.2617 13.5708 62.8007 15 65.6064 15C68.1593 15 69.8541 13.7919 69.8541 11.7178C69.8541 9.86559 68.6269 8.89908 66.6976 8.59724ZM24.902 0.100615C24.0447 0.100615 23.4014 0.744702 23.4014 1.63042C23.4014 2.51613 24.044 3.16022 24.902 3.16022C25.7594 3.16022 26.4027 2.51613 26.4027 1.63042C26.4027 0.744702 25.7594 0.100615 24.902 0.100615ZM59.2155 7.95315C59.2155 5.69846 57.8905 4.18848 55.0848 4.18848C52.4349 4.18848 50.954 5.57803 50.6614 7.71228H52.9804C53.0973 6.88678 53.7208 6.2023 55.0458 6.2023C56.2347 6.2023 56.8193 6.74577 56.8193 7.41044C56.8193 8.27634 55.7472 8.49738 54.4223 8.6384C52.6297 8.83963 50.4085 9.48371 50.4085 11.9C50.4085 13.7728 51.7533 14.9802 53.8966 14.9802C55.5722 14.9802 56.6244 14.2553 57.1509 13.1074C57.2288 14.1341 57.9692 14.7988 59.0023 14.7988H60.3662V12.6447H59.2162V7.95315H59.2155ZM56.9163 10.5707C56.9163 11.9602 55.7472 12.9869 54.3245 12.9869C53.4473 12.9869 52.7069 12.6043 52.7069 11.7994C52.7069 10.7727 53.8958 10.4906 54.987 10.3702C56.0391 10.2696 56.6237 10.0279 56.9163 9.56451V10.5707ZM44.5043 4.18771C43.1984 4.18771 42.1073 4.75177 41.3279 5.6977V0H38.9898V14.7988H41.2889V13.4298C42.0683 14.4161 43.1793 15 44.5043 15C47.31 15 49.4342 12.7049 49.4342 9.60415C49.4342 6.50338 47.2711 4.18771 44.5043 4.18771ZM44.1535 12.7651C42.4779 12.7651 41.25 11.416 41.25 9.60415C41.25 7.79232 42.497 6.42334 44.1727 6.42334C45.8682 6.42334 47.0564 7.7725 47.0564 9.60415C47.0564 11.416 45.8292 12.7651 44.1535 12.7651ZM33.3973 4.18771C31.8775 4.18771 30.8834 4.8318 30.2989 5.7381V4.38894H27.9799V14.798H30.318V9.14071C30.318 7.54993 31.2922 6.42258 32.7341 6.42258C34.0789 6.42258 34.9164 7.40891 34.9164 8.83886V14.7988H37.2545V8.65821C37.2553 6.03994 35.9502 4.18771 33.3973 4.18771ZM81 9.2619C81 6.28233 78.8957 4.18848 76.07 4.18848C73.0694 4.18848 70.8673 6.52396 70.8673 9.60415C70.8673 12.8459 73.2253 15 76.109 15C78.5449 15 80.4544 13.5098 80.9412 11.3962H78.5052C78.1545 12.3223 77.2972 12.8459 76.1472 12.8459C74.6466 12.8459 73.5164 11.8794 73.2635 10.188H80.9993V9.2619H81ZM73.4003 8.45622C73.7709 7.00645 74.823 6.30215 76.0311 6.30215C77.356 6.30215 78.3692 7.08725 78.603 8.45622H73.4003Z"
                    fill="#0052FF"
                />
            </g>
            <defs>
                <clipPath id="clip0_61_89">
                    <rect width="81" height="15" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}


function Atlos(){
    return (
        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
	 width="131.902px" height="33px" viewBox="0 0 131.902 33" enable-background="new 0 0 131.902 33">
<g>
	<path fill="#606060" d="M2.693,24.771c-0.315,0.453-0.707,0.681-1.171,0.681c-0.423,0-0.781-0.177-1.078-0.529
		C0.148,24.568,0,24.143,0,23.637c0-0.453,0.104-0.818,0.316-1.098l7.697-12.37c0.359-0.58,0.834-1.053,1.425-1.418
		c0.591-0.366,1.227-0.549,1.9-0.549c0.633,0,1.235,0.164,1.805,0.492c0.569,0.329,1.035,0.782,1.396,1.362l7.696,12.293
		c0.127,0.178,0.232,0.373,0.317,0.588c0.084,0.213,0.127,0.447,0.127,0.699c0,0.506-0.147,0.934-0.443,1.287
		c-0.295,0.354-0.654,0.529-1.075,0.529c-0.507,0-0.908-0.24-1.204-0.721l-7.38-11.763c-0.147-0.202-0.332-0.422-0.554-0.662
		c-0.221-0.24-0.448-0.359-0.681-0.359s-0.459,0.12-0.682,0.359c-0.222,0.241-0.396,0.46-0.522,0.662L2.693,24.771z"/>
	<path fill="#606060" d="M46.281,9.488c0.296,0.354,0.442,0.782,0.442,1.286s-0.147,0.933-0.442,1.286
		c-0.297,0.353-0.655,0.53-1.078,0.53h-9.376v11.046c0,0.505-0.147,0.935-0.443,1.287c-0.295,0.353-0.654,0.529-1.076,0.529
		c-0.423,0-0.782-0.177-1.078-0.529c-0.296-0.354-0.442-0.78-0.442-1.287V12.59h-9.503c-0.381-0.05-0.707-0.246-0.982-0.586
		c-0.274-0.34-0.411-0.75-0.411-1.229c0-0.503,0.147-0.932,0.443-1.286c0.295-0.354,0.653-0.53,1.076-0.53h21.793
		C45.626,8.958,45.985,9.135,46.281,9.488z"/>
	<path fill="#606060" d="M52.553,25.055c-0.529-0.265-0.987-0.623-1.378-1.078c-0.391-0.453-0.702-0.983-0.937-1.588
		c-0.232-0.605-0.349-1.262-0.349-1.968v-9.646c0-0.503,0.149-0.932,0.444-1.286c0.294-0.354,0.654-0.53,1.076-0.53
		c0.4,0,0.755,0.177,1.061,0.53s0.459,0.782,0.459,1.286v9.646c0,0.378,0.122,0.706,0.364,0.983
		c0.243,0.277,0.522,0.416,0.839,0.416H73.14c0.423,0,0.779,0.177,1.077,0.529c0.297,0.354,0.443,0.781,0.443,1.286
		c0,0.507-0.147,0.935-0.443,1.287c-0.298,0.353-0.654,0.529-1.077,0.529H54.232C53.639,25.452,53.08,25.318,52.553,25.055z"/>
	<path fill="#606060" d="M102.823,17.205c0,0.96-0.132,1.925-0.396,2.895c-0.265,0.971-0.648,1.854-1.156,2.647
		c-0.507,0.795-1.119,1.444-1.837,1.948s-1.531,0.757-2.438,0.757H82.552c-0.865-0.052-1.637-0.335-2.313-0.851
		c-0.677-0.519-1.252-1.168-1.728-1.949c-0.476-0.78-0.84-1.65-1.092-2.609c-0.254-0.958-0.38-1.904-0.38-2.838
		c0-0.958,0.131-1.923,0.396-2.894s0.648-1.854,1.157-2.648c0.506-0.794,1.119-1.444,1.837-1.948s1.53-0.757,2.438-0.757h14.444
		c0.865,0.052,1.637,0.334,2.313,0.852s1.25,1.166,1.728,1.948c0.475,0.782,0.84,1.652,1.093,2.61
		C102.699,15.327,102.823,16.272,102.823,17.205z M98.105,21.348c0.463-0.314,0.828-0.699,1.094-1.152
		c0.264-0.455,0.442-0.951,0.537-1.495c0.097-0.543,0.144-1.039,0.144-1.494c0-0.454-0.047-0.952-0.144-1.494
		c-0.095-0.542-0.273-1.041-0.537-1.495c-0.266-0.454-0.631-0.838-1.094-1.153c-0.465-0.316-1.056-0.473-1.774-0.473H83.532
		c-0.739,0-1.337,0.157-1.788,0.473c-0.455,0.315-0.814,0.7-1.078,1.153c-0.266,0.454-0.444,0.952-0.539,1.495
		c-0.095,0.542-0.142,1.04-0.142,1.494c0,0.455,0.047,0.952,0.142,1.494c0.095,0.544,0.273,1.04,0.539,1.495
		c0.264,0.453,0.624,0.838,1.078,1.152c0.452,0.316,1.049,0.473,1.788,0.473h12.797C97.049,21.82,97.64,21.664,98.105,21.348z"/>
	<path fill="#606060" d="M108.558,18.624c-0.506-0.265-0.95-0.625-1.33-1.079c-0.38-0.453-0.682-0.982-0.902-1.588
		c-0.222-0.606-0.332-1.261-0.332-1.967c0-0.705,0.109-1.361,0.332-1.967c0.222-0.605,0.522-1.135,0.902-1.589
		c0.378-0.454,0.823-0.813,1.33-1.078s1.058-0.397,1.646-0.397h19.229c0.422,0,0.779,0.177,1.075,0.53
		c0.297,0.354,0.444,0.782,0.444,1.286s-0.147,0.933-0.444,1.286c-0.296,0.353-0.653,0.53-1.075,0.53h-19.292
		c-0.316,0-0.597,0.132-0.841,0.397c-0.241,0.265-0.364,0.601-0.364,1.002c0,0.378,0.122,0.706,0.364,0.984
		c0.243,0.278,0.523,0.416,0.841,0.416h17.519c0.591,0,1.146,0.132,1.664,0.397c0.515,0.265,0.966,0.625,1.345,1.079
		c0.381,0.452,0.682,0.989,0.902,1.608c0.223,0.616,0.332,1.277,0.332,1.984c0,0.706-0.109,1.361-0.332,1.967
		c-0.222,0.606-0.521,1.135-0.902,1.59c-0.379,0.452-0.83,0.809-1.345,1.059c-0.519,0.253-1.073,0.379-1.664,0.379h-19.196
		c-0.422,0-0.782-0.178-1.077-0.529c-0.295-0.354-0.442-0.781-0.442-1.285c0-0.507,0.147-0.936,0.442-1.289
		c0.295-0.353,0.655-0.529,1.077-0.529h19.323c0.316,0,0.591-0.131,0.822-0.396c0.232-0.266,0.349-0.588,0.349-0.965
		c0-0.403-0.115-0.744-0.349-1.022c-0.232-0.276-0.506-0.414-0.822-0.414h-17.581C109.615,19.021,109.065,18.889,108.558,18.624z"/>
</g>
</svg>
    )
}
function Stripe() {
    return (
        <svg width="49" height="20" viewBox="0 0 37 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M36.2465 7.75018C36.2465 5.18676 34.998 3.16406 32.6118 3.16406C30.2155 3.16406 28.7656 5.18676 28.7656 7.73015C28.7656 10.7442 30.4773 12.2662 32.934 12.2662C34.1321 12.2662 35.0383 11.9958 35.7229 11.6153V9.61266C35.0383 9.95312 34.2529 10.1634 33.2561 10.1634C32.2795 10.1634 31.4136 9.82294 31.3029 8.64137H36.2263C36.2263 8.51119 36.2465 7.9905 36.2465 7.75018ZM31.2727 6.79891C31.2727 5.6674 31.9674 5.19677 32.6017 5.19677C33.2159 5.19677 33.8703 5.6674 33.8703 6.79891H31.2727Z"
                fill="#635BFF"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24.8819 3.16406C23.8952 3.16406 23.2608 3.62468 22.9084 3.9451L22.7776 3.32428H20.5625V14.9998L23.0796 14.4691L23.0897 11.6354C23.4521 11.8957 23.9858 12.2662 24.8718 12.2662C26.674 12.2662 28.3152 10.8243 28.3152 7.65004C28.3051 4.74617 26.6438 3.16406 24.8819 3.16406ZM24.2778 10.0633C23.6837 10.0633 23.3313 9.85298 23.0897 9.59263L23.0796 5.87768C23.3414 5.58729 23.7039 5.38703 24.2778 5.38703C25.194 5.38703 25.8283 6.40839 25.8283 7.72014C25.8283 9.06193 25.2041 10.0633 24.2778 10.0633Z"
                fill="#635BFF"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.0938 2.57343L19.6209 2.03271V0L17.0938 0.530708V2.57343Z"
                fill="#635BFF"
            />
            <path d="M19.6209 3.33594H17.0938V12.0976H19.6209V3.33594Z" fill="#635BFF" />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.3906 4.07887L14.2295 3.33788H12.0547V12.0996H14.5718V6.16164C15.1658 5.39062 16.1727 5.5308 16.4848 5.64095V3.33788C16.1626 3.21772 14.9846 2.99742 14.3906 4.07887Z"
                fill="#635BFF"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.34958 1.16406L6.89288 1.68476L6.88281 9.70545C6.88281 11.1874 8.00041 12.2789 9.49054 12.2789C10.3162 12.2789 10.9203 12.1287 11.2525 11.9484V9.91573C10.9303 10.0459 9.33951 10.5065 9.33951 9.02454V5.4698H11.2525V3.33696H9.33951L9.34958 1.16406Z"
                fill="#635BFF"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M2.54732 5.87768C2.54732 5.48716 2.86951 5.33696 3.40313 5.33696C4.16833 5.33696 5.1349 5.56727 5.90011 5.97781V3.62468C5.06442 3.29424 4.23881 3.16406 3.40313 3.16406C1.35924 3.16406 0 4.22548 0 5.99784C0 8.76153 3.82601 8.32094 3.82601 9.51253C3.82601 9.97314 3.42327 10.1233 2.85944 10.1233C2.02376 10.1233 0.956502 9.78289 0.110753 9.32227V11.7055C1.04712 12.106 1.99355 12.2762 2.85944 12.2762C4.95367 12.2762 6.39346 11.2448 6.39346 9.45245C6.38339 6.46847 2.54732 6.99918 2.54732 5.87768Z"
                fill="#635BFF"
            />
        </svg>
    )
}
function Gift() {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="64"
            height="64"
            fill="#000"
            version="1.1"
            viewBox="0 0 512 512"
            xmlSpace="preserve">
            <g>
                <path
                    fill="#FFEE43"
                    d="M480 252c-4.422 0-8 3.582-8 8H40c0-4.418-3.578-8-8-8s-8 3.582-8 8v208c0 22.055 17.945 40 40 40h384c22.055 0 40-17.945 40-40V260c0-4.418-3.578-8-8-8z"></path>
                <path
                    fill="#FFD100"
                    d="M480 164H32c-17.648 0-32 14.355-32 32v48c0 17.645 14.352 32 32 32h448c17.648 0 32-14.355 32-32v-48c0-17.645-14.352-32-32-32z"></path>
                <path fill="#FF4F19" d="M224 156H288V508H224z"></path>
                <path fill="#E7001E" d="M224 407.313L288 471.313 288 448.688 224 384.688z"></path>
                <path fill="#E7001E" d="M224 367.313L288 431.313 288 408.688 224 344.688z"></path>
                <path fill="#E7001E" d="M288 231.313L288 208.688 235.313 156 224 156 224 167.313z"></path>
                <path fill="#E7001E" d="M224 327.313L288 391.313 288 368.688 224 304.688z"></path>
                <path fill="#E7001E" d="M224 424.688L224 447.313 284.688 508 288 508 288 488.688z"></path>
                <path fill="#E7001E" d="M224 287.313L288 351.313 288 328.688 224 264.688z"></path>
                <path fill="#E7001E" d="M224 207.313L288 271.313 288 248.688 224 184.688z"></path>
                <path fill="#E7001E" d="M267.313 508L224 464.688 224 487.313 244.688 508z"></path>
                <path fill="#E7001E" d="M224 247.313L288 311.313 288 288.688 224 224.688z"></path>
                <g>
                    <path
                        fill="#E7001E"
                        d="M424 291.969c-31.586 0-50.203-32.934-68.203-64.785-13.333-23.584-27.024-47.742-46.855-58.039 37.64-14.671 85.006-34.957 99.145-49.094 26.516-26.512 26.516-69.652 0-96.164-26.516-26.516-69.656-26.516-96.172 0C295.42 40.378 270.544 102.129 256 140.862c-14.544-38.732-39.42-100.484-55.914-116.975-26.516-26.516-69.656-26.516-96.172 0-26.516 26.512-26.516 69.652 0 96.164 14.139 14.137 61.504 34.423 99.145 49.094-19.832 10.297-33.522 34.455-46.855 58.039-18 31.852-36.617 64.785-68.203 64.785-6.625 0-12 5.371-12 12s5.375 12 12 12c45.586 0 68.703-40.895 89.094-76.977 14.836-26.238 28.844-51.023 46.906-51.023V188h64v-.031c18.063 0 32.07 24.785 46.906 51.023 20.391 36.082 43.508 76.977 89.094 76.977 6.625 0 12-5.371 12-12s-5.376-12-12.001-12zM328.883 40.855c8.578-8.578 19.844-12.863 31.117-12.863 11.266 0 22.539 4.289 31.117 12.863 17.156 17.156 17.156 45.07 0 62.227-12.219 12.219-69.773 36.078-115.156 52.926 16.844-45.383 40.703-102.934 52.922-115.153zm-208 0c8.578-8.578 19.844-12.863 31.117-12.863 11.266 0 22.539 4.289 31.117 12.863 12.219 12.219 36.078 69.77 52.922 115.152-45.383-16.848-102.938-40.707-115.156-52.926-17.156-17.155-17.156-45.069 0-62.226z"></path>
                </g>
            </g>
        </svg>
    )
}
function Visa() {
    return (
        <svg width="50" height="32" viewBox="0 0 50 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="50" height="32" fill="white" />
            <path fillRule="evenodd" clipRule="evenodd" d="M0 0H50V6.85714H0V0Z" fill="#0061B2" />
            <path fillRule="evenodd" clipRule="evenodd" d="M0 25.1406H50V31.9978H0V25.1406Z" fill="#FDB827" />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.31506 9.75781L8.31563 9.76139H6.21875V10.1348V10.136C7.85524 10.1907 8.34384 11.0001 8.52469 11.9423L9.48956 21.9999H12.6646L19.4482 9.75781H16.2731L12.1961 17.1158L11.4901 9.75781H8.31506Z"
                fill="#0061B2"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M21.1387 9.75H24.2203L20.441 22.004H17.3594L21.1387 9.75Z"
                fill="#0061B2"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M38.1082 9.99219L32.1484 22.002H35.4918L36.3615 20.1683H40.2425L40.3756 22.002H43.4572L42.5852 9.99219C41.0929 9.99219 39.6005 9.99219 38.1082 9.99219ZM39.7275 13.0741L40.0963 18.1518H37.3184L39.7275 13.0741Z"
                fill="#0061B2"
            />
            <path
                d="M24.9742 12.8713C25.4366 10.9282 26.9545 9.60156 29.5384 9.60156C31.9588 9.60156 33.7538 11.1906 33.7538 11.1906L31.9223 13.2075C31.9223 13.2075 30.5684 12.1074 29.771 12.1074C28.7166 12.1074 28.2404 12.5121 28.143 13.1769C27.9487 14.5025 32.4424 15.3139 32.3002 18.4024C32.2052 20.465 30.1744 22.4667 27.8523 22.4667C24.3702 22.4667 22.6484 20.9999 22.6484 20.9999L24.2183 18.6469C24.2183 18.6469 25.7919 19.9718 27.2708 19.8998C28.2686 19.8512 29.0376 19.4492 28.9861 18.7386C28.8691 17.1277 24.1372 16.3883 24.9742 12.8713Z"
                fill="#0061B2"
            />
            <path
                d="M43.8208 9.99219C43.7132 9.99219 43.6079 10.0212 43.5054 10.0793C43.403 10.1373 43.3233 10.2208 43.2659 10.3289C43.2084 10.437 43.1797 10.5495 43.1797 10.6668C43.1797 10.7829 43.2081 10.8945 43.2647 11.0017C43.3213 11.1089 43.4004 11.1921 43.502 11.2513C43.6036 11.3106 43.7098 11.3402 43.8208 11.3402C43.9317 11.3402 44.038 11.3106 44.1396 11.2513C44.2412 11.1921 44.32 11.1089 44.3763 11.0017C44.4326 10.8945 44.4608 10.7829 44.4608 10.6668C44.4608 10.5495 44.4323 10.437 44.3752 10.3289C44.318 10.2208 44.2381 10.1373 44.1356 10.0793C44.0332 10.0212 43.9283 9.99219 43.8208 9.99219ZM43.8208 10.1042C43.9102 10.1042 43.9973 10.1282 44.0828 10.1767C44.1682 10.2252 44.2348 10.2947 44.2827 10.3849C44.3305 10.4751 44.3545 10.5692 44.3545 10.6668C44.3545 10.7639 44.3314 10.8567 44.2844 10.9457C44.2374 11.0347 44.1711 11.1042 44.0862 11.1539C44.0013 11.2036 43.9131 11.2282 43.8208 11.2282C43.7285 11.2282 43.6397 11.2036 43.5548 11.1539C43.4699 11.1042 43.4039 11.0347 43.3566 10.9457C43.3094 10.8567 43.286 10.7639 43.286 10.6668C43.286 10.5692 43.3097 10.4751 43.3578 10.3849C43.4059 10.2947 43.4725 10.2252 43.5577 10.1767C43.6429 10.1282 43.7308 10.1042 43.8208 10.1042Z"
                fill="#0061B2"
            />
            <path
                d="M43.5625 10.2969V11.0914H43.6745V10.7538H43.7402C43.7795 10.7538 43.8092 10.7629 43.8297 10.7812C43.8592 10.806 43.8985 10.8681 43.9468 10.9666L44.008 11.0914H44.1442L44.0599 10.9359C44.0196 10.8622 43.9856 10.8098 43.9578 10.7785C43.943 10.7622 43.9234 10.7482 43.8989 10.7365C43.9586 10.7313 44.0065 10.7068 44.0426 10.6638C44.0787 10.6207 44.0969 10.5704 44.0969 10.5123C44.0969 10.4712 44.0856 10.4328 44.064 10.3963C44.0424 10.3597 44.0138 10.3336 43.9774 10.3189C43.941 10.3042 43.8821 10.2969 43.8008 10.2969H43.5625ZM43.6745 10.4049H43.8014C43.856 10.4049 43.8937 10.4092 43.9139 10.4183C43.9341 10.4274 43.9499 10.4417 43.9613 10.4603C43.9726 10.4789 43.978 10.4995 43.978 10.523C43.978 10.5595 43.9664 10.5889 43.9428 10.6111C43.9192 10.6332 43.8749 10.6444 43.8101 10.6444H43.6745V10.4049Z"
                fill="#0061B2"
            />
            <path
                d="M48.4692 29.7109C48.3617 29.7109 48.2563 29.74 48.1538 29.798C48.0514 29.8561 47.9717 29.9396 47.9143 30.0476C47.8568 30.1557 47.8281 30.2683 47.8281 30.3856C47.8281 30.5017 47.8565 30.6133 47.9131 30.7204C47.9697 30.8276 48.0488 30.9108 48.1504 30.9701C48.252 31.0293 48.3583 31.059 48.4692 31.059C48.5802 31.059 48.6865 31.0293 48.7881 30.9701C48.8897 30.9108 48.9684 30.8276 49.0247 30.7204C49.0811 30.6133 49.1092 30.5017 49.1092 30.3856C49.1092 30.2683 49.0808 30.1557 49.0236 30.0476C48.9664 29.9396 48.8865 29.8561 48.784 29.798C48.6816 29.74 48.5768 29.7109 48.4692 29.7109ZM48.4692 29.823C48.5587 29.823 48.6457 29.8469 48.7312 29.8954C48.8167 29.9439 48.8833 30.0135 48.9311 30.1037C48.9789 30.1939 49.0029 30.2879 49.0029 30.3856C49.0029 30.4826 48.9798 30.5754 48.9328 30.6644C48.8858 30.7534 48.8195 30.823 48.7346 30.8727C48.6497 30.9224 48.5615 30.9469 48.4692 30.9469C48.377 30.9469 48.2882 30.9224 48.2033 30.8727C48.1183 30.823 48.0523 30.7534 48.0051 30.6644C47.9578 30.5754 47.9344 30.4826 47.9344 30.3856C47.9344 30.2879 47.9581 30.1939 48.0062 30.1037C48.0543 30.0135 48.1209 29.9439 48.2061 29.8954C48.2913 29.8469 48.3792 29.823 48.4692 29.823Z"
                fill="#0061B2"
            />
            <path
                d="M48.2109 30.0156V30.8101H48.3229V30.4726H48.3887C48.4279 30.4726 48.4576 30.4817 48.4781 30.4999C48.5077 30.5247 48.5469 30.5869 48.5953 30.6854L48.6564 30.8101H48.7926L48.7084 30.6547C48.668 30.581 48.6341 30.5286 48.6062 30.4973C48.5914 30.4809 48.5718 30.467 48.5474 30.4552C48.6071 30.45 48.6549 30.4256 48.691 30.3825C48.7272 30.3395 48.7453 30.2891 48.7453 30.2311C48.7453 30.19 48.734 30.1515 48.7124 30.115C48.6908 30.0785 48.6622 30.0523 48.6258 30.0376C48.5895 30.023 48.5306 30.0156 48.4493 30.0156H48.2109ZM48.3229 30.1237H48.4498C48.5044 30.1237 48.5422 30.1279 48.5624 30.137C48.5826 30.1462 48.5983 30.1605 48.6097 30.1791C48.6211 30.1976 48.6264 30.2183 48.6264 30.2418C48.6264 30.2783 48.6148 30.3076 48.5912 30.3298C48.5676 30.352 48.5233 30.3632 48.4585 30.3632H48.3229V30.1237Z"
                fill="#0061B2"
            />
        </svg>
    )
}
function Mastercard() {
    return (
        <svg width="50" height="32" viewBox="0 0 50 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M50 0H0.00808668C-0.00338484 2.71762 0.0107102 1.21672 0.0107102 2.71762V29.2824C0.0107102 30.7833 0.0260389 29.2824 0 32H50V29.2824V2.71762V0Z"
                fill="black"
            />
            <path
                d="M14.753 28.6457V26.9574C14.753 26.3099 14.3665 25.888 13.7319 25.888C13.3987 25.888 13.0421 26.0008 12.7935 26.3636C12.6002 26.0538 12.3269 25.888 11.9103 25.888C11.5957 25.8741 11.2953 26.0221 11.1105 26.282V25.9423H10.5859V28.6437H11.1105V27.098C11.1105 26.6224 11.3864 26.3948 11.773 26.3948C12.1596 26.3948 12.3802 26.6482 12.3802 27.098V28.6457H12.9048V27.098C12.9048 26.6224 13.1807 26.3948 13.5673 26.3948C13.9539 26.3948 14.1745 26.6482 14.1745 27.098V28.6457H14.753ZM23.3659 25.9722H22.3994V25.157H21.8762V25.9722H21.3243V26.4478H21.8762V27.717C21.8762 28.3359 22.0968 28.7021 22.7593 28.7021C23.0103 28.6968 23.2563 28.6291 23.4758 28.5051L23.3099 28.0295C23.161 28.1244 22.9882 28.1732 22.8127 28.1701C22.5367 28.1701 22.3987 28.0009 22.3987 27.7197V26.4261H23.3652V25.9756L23.3659 25.9722ZM28.2795 25.888C27.9888 25.8814 27.7162 26.0311 27.5616 26.282V25.9423H27.0371V28.6437H27.5616V27.1265C27.5616 26.6761 27.7823 26.3948 28.1415 26.3948C28.2627 26.3998 28.383 26.4188 28.5001 26.4512L28.6661 25.9444C28.5395 25.9119 28.4099 25.893 28.2795 25.888ZM20.8537 26.1693C20.5778 25.9722 20.1912 25.888 19.7773 25.888C19.1148 25.888 18.7009 26.1978 18.7009 26.7325C18.7009 27.1829 19.0048 27.4357 19.584 27.5206L19.86 27.5485C20.1639 27.6048 20.3572 27.7176 20.3572 27.8583C20.3572 28.0553 20.1366 28.198 19.722 28.198C19.4054 28.2046 19.0954 28.1059 18.8388 27.9167L18.5629 28.3386C18.9495 28.6199 19.4187 28.6783 19.696 28.6783C20.4692 28.6783 20.8831 28.3128 20.8831 27.806C20.8831 27.3304 20.5498 27.1028 19.9719 27.0178L19.696 26.99C19.4474 26.9621 19.2294 26.8772 19.2294 26.7087C19.2294 26.5117 19.45 26.369 19.754 26.369C20.0872 26.369 20.4165 26.5096 20.5825 26.5939L20.8537 26.1693ZM28.8594 27.295C28.8594 28.1103 29.3839 28.7021 30.2397 28.7021C30.6263 28.7021 30.9023 28.6178 31.1782 28.3923L30.9023 27.9704C30.7069 28.1317 30.4638 28.221 30.2124 28.2238C29.7458 28.2238 29.3839 27.8583 29.3839 27.3236C29.3839 26.7889 29.7412 26.4261 30.2124 26.4261C30.4638 26.4289 30.7069 26.5182 30.9023 26.6795L31.1782 26.2576C30.9023 26.0327 30.6263 25.9478 30.2397 25.9478C29.4112 25.8914 28.8594 26.4825 28.8594 27.2984V27.295ZM25.1601 25.888C24.387 25.888 23.8624 26.4505 23.8624 27.295C23.8624 28.1395 24.4143 28.7021 25.2148 28.7021C25.6042 28.7105 25.9845 28.5813 26.2912 28.3366L26.0153 27.9425C25.7935 28.1177 25.5226 28.2162 25.2421 28.2238C24.8835 28.2238 24.497 27.9989 24.4423 27.5206H26.4025V27.2957C26.4025 26.4512 25.9053 25.8887 25.1601 25.8887V25.888ZM25.1328 26.3948C25.5194 26.3948 25.7953 26.6482 25.8227 27.0702H24.3876C24.4703 26.6761 24.7209 26.3948 25.1328 26.3948ZM17.983 27.295V25.9444H17.4585V26.2841C17.2652 26.0307 16.9919 25.89 16.6026 25.89C15.8575 25.89 15.3049 26.4811 15.3049 27.2971C15.3049 28.113 15.8568 28.7041 16.6026 28.7041C16.9892 28.7041 17.2652 28.5635 17.4585 28.3101V28.6498H17.983V27.295ZM15.8575 27.295C15.8575 26.7882 16.1614 26.3948 16.686 26.3948C17.1832 26.3948 17.4858 26.7889 17.4858 27.295C17.4858 27.8297 17.1525 28.1953 16.686 28.1953C16.1614 28.2231 15.8575 27.8012 15.8575 27.295ZM36.2584 25.888C35.9678 25.8814 35.6951 26.0311 35.5406 26.282V25.9423H35.016V28.6437H35.5399V27.1265C35.5399 26.6761 35.7605 26.3948 36.1198 26.3948C36.241 26.3998 36.3613 26.4188 36.4784 26.4512L36.6444 25.9444C36.5178 25.9119 36.3882 25.893 36.2578 25.888H36.2584ZM34.2155 27.295V25.9444H33.691V26.2841C33.4977 26.0307 33.2244 25.89 32.8352 25.89C32.09 25.89 31.5375 26.4811 31.5375 27.2971C31.5375 28.113 32.0893 28.7041 32.8352 28.7041C33.2218 28.7041 33.4977 28.5635 33.691 28.3101V28.6498H34.2155V27.295ZM32.09 27.295C32.09 26.7882 32.3939 26.3948 32.9185 26.3948C33.4157 26.3948 33.7183 26.7889 33.7183 27.295C33.7183 27.8297 33.3851 28.1953 32.9185 28.1953C32.3939 28.2231 32.09 27.8012 32.09 27.295ZM39.5437 27.295V24.875H39.0192V26.282C38.8259 26.0286 38.5526 25.888 38.1634 25.888C37.4182 25.888 36.8656 26.4791 36.8656 27.295C36.8656 28.111 37.4175 28.7021 38.1634 28.7021C38.5499 28.7021 38.8259 28.5615 39.0192 28.308V28.6477H39.5437V27.295ZM37.4182 27.295C37.4182 26.7882 37.7221 26.3948 38.2467 26.3948C38.7439 26.3948 39.0465 26.7889 39.0465 27.295C39.0465 27.8297 38.7132 28.1953 38.2467 28.1953C37.7215 28.2238 37.4175 27.8019 37.4175 27.295H37.4182Z"
                fill="white"
            />
            <path d="M29.8293 5.45312H20.25V21.3797H29.8293V5.45312Z" fill="#FF5F00" />
            <path
                d="M21.2125 13.4192C21.2131 10.3151 22.6062 7.3818 24.9943 5.45584C20.92 2.19053 15.0679 2.66517 11.5514 6.54616C8.03494 10.4271 8.03494 16.4112 11.5514 20.2922C15.0679 24.1731 20.92 24.6478 24.9943 21.3825C22.6062 19.4565 21.2131 16.5232 21.2125 13.4192Z"
                fill="#EB001B"
            />
            <path
                d="M41.0867 13.4162C41.0894 17.292 38.9202 20.8289 35.5009 22.5241C32.0816 24.2192 28.0127 23.7747 25.0234 21.3795C27.4122 19.4541 28.8054 16.5204 28.8054 13.4162C28.8054 10.312 27.4122 7.37839 25.0234 5.45291C28.0127 3.05771 32.0816 2.61323 35.5009 4.30837C38.9202 6.0035 41.0894 9.54044 41.0867 13.4162Z"
                fill="#F79E1B"
            />
        </svg>
    )
}
function Union() {
    return (
        <svg width="50" height="32" viewBox="0 0 50 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M50 0H0V32H50V0Z" fill="white" />
            <path
                d="M13.8782 4.46875H23.0058C24.2795 4.46875 25.0755 5.51873 24.7769 6.80283L20.5248 25.2015C20.2264 26.4856 18.946 27.5356 17.6724 27.5356H8.54482C7.2711 27.5356 6.47514 26.4856 6.77367 25.2015L11.0257 6.80283C11.3242 5.51208 12.5978 4.46875 13.8715 4.46875H13.8782Z"
                fill="#D10429"
            />
            <path
                d="M22.202 4.46875H32.7033C33.9768 4.46875 33.3998 5.51873 33.1013 6.80283L28.8491 25.2015C28.5506 26.4856 28.6434 27.5356 27.3698 27.5356H16.8691C15.5889 27.5356 14.7995 26.4856 15.1046 25.2015L19.3568 6.80283C19.6552 5.51208 20.9288 4.46875 22.2092 4.46875H22.2025H22.202Z"
                fill="#022E64"
            />
            <path
                d="M32.3317 4.46875H41.4593C42.733 4.46875 43.529 5.51873 43.2305 6.80283L38.9784 25.2015C38.6799 26.4856 37.3996 27.5356 36.1259 27.5356H26.9984C25.718 27.5356 24.9286 26.4856 25.2271 25.2015L29.4793 6.80283C29.7778 5.51208 31.0514 4.46875 32.325 4.46875H32.3317Z"
                fill="#076F74"
            />
            <path
                d="M30.8137 21.8169H31.6761L31.9214 20.9809H31.0657L30.8137 21.8169ZM31.5036 19.5161L31.205 20.506C31.205 20.506 31.5301 20.3388 31.7091 20.2852C31.8883 20.2451 32.1536 20.2116 32.1536 20.2116L32.3592 19.5228H31.497L31.5036 19.5161ZM31.9347 18.0983L31.6495 19.048C31.6495 19.048 31.9679 18.9008 32.147 18.8541C32.3261 18.8073 32.5914 18.7939 32.5914 18.7939L32.797 18.105H31.9414L31.9347 18.0983ZM33.8386 18.0983L32.7241 21.8101H33.0226L32.7905 22.5793H32.4919L32.419 22.8133H31.3575L31.4305 22.5793H29.2812L29.4935 21.8704H29.7125L30.8403 18.0984L31.0658 17.3359H32.147L32.0342 17.7171C32.0342 17.7171 32.3194 17.5098 32.5981 17.4362C32.8702 17.3627 34.4357 17.3359 34.4357 17.3359L34.2034 18.0917H33.832L33.8386 18.0983Z"
                fill="#FEFEFE"
            />
            <path
                d="M34.2655 17.336H35.4197L35.433 17.7707C35.4265 17.8442 35.4861 17.8777 35.6254 17.8777H35.8575L35.6453 18.5934H35.0216C34.4844 18.6335 34.2787 18.3995 34.292 18.1386L34.272 17.3427L34.2655 17.336ZM34.4076 20.7408H33.3065L33.4921 20.1055H34.7525L34.9315 19.5236H33.6911L33.9034 18.8079H37.3529L37.1406 19.5236H35.9797L35.8007 20.1055H36.9615L36.7691 20.7408H35.5153L35.2898 21.0083H35.8006L35.9266 21.8109C35.9399 21.8911 35.9399 21.9446 35.9664 21.9781C35.9929 22.0049 36.1456 22.0182 36.2317 22.0182H36.3843L36.1456 22.8008H35.7541C35.6945 22.8008 35.6016 22.7941 35.4755 22.7941C35.3561 22.7807 35.2765 22.7138 35.1969 22.6737C35.1239 22.6403 35.0179 22.5533 34.9913 22.3995L34.8719 21.5969L34.3014 22.3861C34.1222 22.6335 33.877 22.8275 33.4589 22.8275H32.6629L32.8752 22.1319H33.1804C33.2667 22.1319 33.3463 22.0986 33.4061 22.065C33.4657 22.0383 33.5188 22.0115 33.5718 21.9247L34.4076 20.7408ZM22.3627 19H25.2749L25.0625 19.7023H23.9016L23.7226 20.2974H24.9166L24.6977 21.0198H23.5103L23.2184 21.9895C23.1853 22.0965 23.5037 22.1099 23.6165 22.1099L24.2134 22.0296L23.9746 22.8322H22.6346C22.5286 22.8322 22.449 22.8188 22.3295 22.7921C22.2166 22.7653 22.1636 22.7118 22.1172 22.6383C22.0707 22.558 21.9911 22.4978 22.0442 22.3239L22.4289 21.0399H21.7656L21.9845 20.3042H22.6479L22.827 19.709H22.1637L22.376 19.0067L22.3627 19ZM24.3747 17.7306H25.5687L25.3499 18.4595H23.7179L23.5388 18.6134C23.4592 18.687 23.4393 18.6602 23.3398 18.7137C23.247 18.7604 23.0546 18.8541 22.8025 18.8541H22.2784L22.4907 18.1519H22.6499C22.7826 18.1519 22.8756 18.1384 22.9219 18.1117C22.975 18.0783 23.0347 18.0047 23.0943 17.8843L23.3928 17.3359H24.5804L24.3747 17.7372V17.7306ZM26.6251 18.9344C26.6251 18.9344 26.9501 18.6335 27.5074 18.5399C27.6335 18.5132 28.4295 18.5264 28.4295 18.5264L28.549 18.1251H26.8706L26.6251 18.9411V18.9344ZM28.2039 19.242H26.5388L26.4394 19.5831H27.8855C28.058 19.5631 28.0912 19.5898 28.1045 19.5764L28.2105 19.242H28.2039ZM26.0415 17.3427H27.0563L26.9105 17.8577C26.9105 17.8577 27.2289 17.5969 27.4543 17.5032C27.68 17.4229 28.184 17.3427 28.184 17.3427L29.8291 17.336L29.2654 19.222C29.1725 19.543 29.0597 19.7504 28.9933 19.8507C28.9337 19.9443 28.8606 20.0312 28.7147 20.1115C28.5755 20.1851 28.4493 20.2319 28.33 20.2386C28.2238 20.2452 28.0514 20.252 27.8258 20.252H26.2404L25.7959 21.7434C25.7563 21.8905 25.7363 21.964 25.7629 22.0041C25.7827 22.0376 25.8424 22.0777 25.9154 22.0777L26.6118 22.0108L26.3731 22.8268H25.5903C25.3383 22.8268 25.1591 22.8201 25.0331 22.8133C24.9137 22.8 24.7875 22.8133 24.7014 22.7465C24.6284 22.6796 24.5158 22.5927 24.5223 22.5057C24.529 22.4254 24.5621 22.2916 24.6152 22.1044L26.0415 17.3427Z"
                fill="#FEFEFE"
            />
            <path
                d="M29.0074 20.3434L28.9145 20.7981C28.8748 20.9385 28.8416 21.0456 28.7354 21.1392C28.6226 21.2328 28.4967 21.3331 28.1916 21.3331L27.6277 21.3599L27.621 21.8682C27.6144 22.0085 27.6542 21.9952 27.6741 22.0219C27.7006 22.0487 27.7204 22.0554 27.747 22.0688L27.9262 22.0553L28.4635 22.0286L28.2379 22.7777H27.621C27.1898 22.7777 26.8648 22.7642 26.7653 22.684C26.6591 22.6171 26.6458 22.5368 26.6458 22.3897L26.6856 20.3967H27.6741L27.6608 20.8046H27.8996C27.9792 20.8046 28.039 20.798 28.0721 20.7779C28.1052 20.7578 28.1252 20.7244 28.1385 20.6776L28.2379 20.3566H29.0141L29.0074 20.3434ZM14.9452 10.3594C14.9121 10.5199 14.2753 13.4693 14.2753 13.4693C14.1359 14.0645 14.0364 14.4926 13.7047 14.7667C13.5124 14.9272 13.2868 15.0008 13.0281 15.0008C12.6102 15.0008 12.3714 14.7935 12.3316 14.3989L12.3249 14.2652C12.3249 14.2652 12.451 13.4693 12.451 13.4626C12.451 13.4626 13.1144 10.774 13.2338 10.4195C13.2404 10.3995 13.2404 10.3861 13.2404 10.3794C11.9468 10.3928 11.7147 10.3794 11.7014 10.3594C11.6947 10.3861 11.6616 10.5533 11.6616 10.5533L10.9849 13.5831L10.9252 13.8371L10.8125 14.6799C10.8125 14.9273 10.8589 15.1346 10.9585 15.3018C11.2702 15.8503 12.1525 15.9305 12.65 15.9305C13.2935 15.9305 13.8971 15.79 14.3017 15.5426C15.0115 15.1212 15.1972 14.4592 15.3565 13.8773L15.4361 13.5763C15.4361 13.5763 16.1194 10.7875 16.2388 10.4263C16.2454 10.4062 16.2454 10.3928 16.2521 10.3862C15.3101 10.3928 15.0381 10.3862 14.9452 10.3661V10.3594ZM18.7291 15.9032C18.2713 15.8965 18.1055 15.8965 17.5682 15.9233L17.5483 15.8832C17.5947 15.6758 17.6478 15.4752 17.6876 15.2679L17.7539 14.987C17.8534 14.5523 17.9463 14.0373 17.9596 13.8835C17.9728 13.7898 17.9993 13.5558 17.734 13.5558C17.6212 13.5558 17.5085 13.6092 17.3891 13.6627C17.3227 13.8968 17.1967 14.5523 17.1303 14.8465C16.9976 15.4752 16.991 15.5488 16.9313 15.8565L16.8915 15.8965C16.4206 15.8899 16.2547 15.8899 15.7108 15.9166L15.6842 15.8698C15.7771 15.4952 15.8633 15.1207 15.9496 14.7462C16.1751 13.7363 16.2349 13.3484 16.2945 12.8334L16.341 12.8067C16.8717 12.7331 16.9977 12.713 17.5748 12.5994L17.6212 12.6529L17.535 12.9739C17.6345 12.9137 17.7274 12.8535 17.8269 12.8067C18.0988 12.6729 18.3974 12.6328 18.5632 12.6328C18.8153 12.6328 19.0939 12.7064 19.2067 13.0007C19.3128 13.2615 19.2464 13.5825 19.1005 14.2179L19.0276 14.5389C18.8816 15.2478 18.8551 15.3749 18.7754 15.8564L18.7224 15.8965L18.7291 15.9032ZM20.5918 15.905C20.3132 15.905 20.1341 15.8983 19.9617 15.905C19.7892 15.905 19.6233 15.9184 19.3646 15.9251L19.3513 15.905L19.3381 15.8782C19.411 15.6107 19.4442 15.5171 19.484 15.4234C19.5172 15.3298 19.5503 15.2362 19.6167 14.962C19.6963 14.6075 19.7494 14.3601 19.7825 14.1393C19.8223 13.932 19.8422 13.7515 19.8688 13.5441L19.8887 13.5308L19.9085 13.5107C20.1872 13.4706 20.3597 13.4439 20.5388 13.4171C20.7179 13.3904 20.9036 13.3569 21.1888 13.3034L21.2021 13.3302L21.2087 13.3569L21.0496 14.019C20.9965 14.2398 20.9435 14.4604 20.8971 14.6812C20.7974 15.1493 20.7511 15.3232 20.7312 15.4503C20.7047 15.5706 20.6979 15.6309 20.6581 15.8716L20.6316 15.8917L20.6051 15.9118L20.5918 15.905ZM23.5367 14.2618C23.5169 14.3822 23.4107 14.8303 23.2714 15.0176C23.1719 15.158 23.0591 15.2449 22.9264 15.2449C22.8867 15.2449 22.661 15.2449 22.6545 14.9038C22.6545 14.7367 22.6876 14.5627 22.7274 14.3755C22.8469 13.8337 22.9928 13.3857 23.3576 13.3857C23.6429 13.3857 23.6628 13.7201 23.5367 14.2618ZM24.7374 14.3153C24.8966 13.6064 24.7706 13.272 24.6181 13.0713C24.3792 12.7637 23.9547 12.6634 23.5169 12.6634C23.2515 12.6634 22.628 12.6901 22.1371 13.1449C21.7856 13.4726 21.6197 13.9207 21.5269 14.3488C21.4272 14.7834 21.3146 15.566 22.031 15.8602C22.2498 15.9539 22.5683 15.9806 22.7739 15.9806C23.298 15.9806 23.8353 15.8335 24.2399 15.4055C24.5517 15.0577 24.691 14.536 24.744 14.3153H24.7374ZM35.9188 15.9844C35.3616 15.9777 35.2024 15.9777 34.6916 16.0045L34.6584 15.9644C34.7978 15.436 34.937 14.9009 35.063 14.3659C35.2222 13.6704 35.262 13.3761 35.3152 12.9681L35.3549 12.9347C35.9055 12.8545 36.0581 12.8344 36.6352 12.7274L36.6485 12.7742C36.5423 13.2156 36.4429 13.6504 36.3367 14.0851C36.1245 15.0014 36.0514 15.4695 35.9719 15.9511L35.9188 15.9912V15.9844Z"
                fill="#FEFEFE"
            />
            <path
                d="M35.1164 14.3513C35.0898 14.465 34.9837 14.9198 34.8444 15.107C34.7516 15.2408 34.526 15.3278 34.4 15.3278C34.3601 15.3278 34.1413 15.3278 34.128 14.9934C34.128 14.8261 34.1611 14.6522 34.2009 14.465C34.3203 13.9367 34.4664 13.4885 34.8312 13.4885C35.1164 13.4885 35.2425 13.8162 35.1164 14.358V14.3513ZM36.211 14.4048C36.3701 13.6959 35.7201 14.3447 35.6205 14.1105C35.4613 13.7427 35.5608 13.007 34.9241 12.7596C34.6785 12.6592 34.1015 12.7863 33.6106 13.2411C33.2657 13.5621 33.0932 14.0102 33.0003 14.4382C32.9007 14.8663 32.788 15.6555 33.4978 15.9297C33.7234 16.03 33.929 16.0567 34.1346 16.0433C34.8511 16.0032 35.395 14.913 35.7998 14.485C36.1113 14.144 36.1644 14.6121 36.211 14.4048ZM27.8815 15.9032C27.4237 15.8965 27.2645 15.8965 26.7273 15.9233L26.7073 15.8832C26.7538 15.6758 26.8069 15.4752 26.8532 15.2679L26.9129 14.987C27.0125 14.5523 27.1119 14.0373 27.1185 13.8835C27.1317 13.7898 27.1583 13.5558 26.8996 13.5558C26.7868 13.5558 26.6675 13.6092 26.5547 13.6627C26.495 13.8968 26.3623 14.5523 26.2959 14.8465C26.17 15.4752 26.1567 15.5488 26.0969 15.8565L26.0571 15.8965C25.5862 15.8899 25.4203 15.8899 24.8764 15.9166L24.8499 15.8698C24.9428 15.4952 25.029 15.1207 25.1153 14.7462C25.3408 13.7363 25.3939 13.3484 25.4601 12.8334L25.4999 12.8067C26.0307 12.7331 26.1634 12.713 26.7337 12.5994L26.7803 12.6529L26.7007 12.9739C26.7936 12.9137 26.893 12.8535 26.9859 12.8067C27.258 12.6729 27.5564 12.6328 27.7222 12.6328C27.9744 12.6328 28.2463 12.7064 28.3656 13.0007C28.4717 13.2615 28.3989 13.5825 28.2529 14.2179L28.1799 14.5389C28.0274 15.2478 28.0075 15.3749 27.9277 15.8564L27.8748 15.8965L27.8815 15.9032ZM31.8559 10.366L31.4711 10.3728C30.476 10.3861 30.078 10.3794 29.9189 10.3594C29.9056 10.433 29.8791 10.56 29.8791 10.56C29.8791 10.56 29.5209 12.2186 29.5209 12.2253C29.5209 12.2253 28.6717 15.7566 28.6319 15.9237C29.501 15.9104 29.8525 15.9104 30.0051 15.9304C30.0384 15.7632 30.2373 14.7801 30.2439 14.7801C30.2439 14.7801 30.4164 14.0578 30.4231 14.0311C30.4231 14.0311 30.476 13.9575 30.5291 13.924H30.6087C31.3584 13.924 32.2008 13.924 32.8642 13.4359C33.3153 13.1015 33.6205 12.5998 33.7598 11.9979C33.7929 11.8508 33.8194 11.6769 33.8194 11.4963C33.8194 11.2622 33.7731 11.0349 33.6404 10.8543C33.302 10.3794 32.6319 10.3728 31.8559 10.366ZM32.3534 12.0983C32.2738 12.4661 32.035 12.7804 31.7299 12.9276C31.4777 13.0546 31.1726 13.068 30.8542 13.068H30.6485L30.6618 12.9877C30.6618 12.9877 31.04 11.3291 31.04 11.3358L31.0532 11.2488L31.0598 11.182L31.2125 11.1954C31.2125 11.1954 31.9952 11.2622 32.0085 11.2622C32.3135 11.3826 32.4463 11.6903 32.3534 12.0983ZM40.5079 12.6529L40.4614 12.5994C39.8976 12.713 39.7915 12.7331 39.2741 12.8067L39.2342 12.8468C39.2342 12.8535 39.2276 12.8602 39.2276 12.8736V12.8669C38.8428 13.7697 38.8495 13.5758 38.5377 14.2848C38.5377 14.2513 38.5377 14.2312 38.531 14.1978L38.4514 12.6595L38.405 12.606C37.8079 12.7198 37.7947 12.7398 37.2507 12.8134L37.2109 12.8535C37.2042 12.8736 37.2042 12.8936 37.2042 12.9137L37.2109 12.9204C37.2773 13.2748 37.264 13.1946 37.3303 13.7497C37.3635 14.0239 37.4034 14.2981 37.4364 14.5656C37.4894 15.0204 37.5228 15.2411 37.589 15.93C37.2175 16.5452 37.1312 16.7793 36.773 17.321L36.793 17.3745C37.3303 17.3545 37.4496 17.3545 37.8476 17.3545L37.9341 17.2542C38.2324 16.6055 40.5144 12.6596 40.5144 12.6596L40.5079 12.6529ZM21.1028 13.0959C21.4079 12.8819 21.4476 12.5876 21.189 12.4338C20.9303 12.28 20.4726 12.3268 20.1674 12.5408C19.8623 12.7482 19.8291 13.0424 20.0878 13.2029C20.3399 13.3501 20.7976 13.3099 21.1028 13.0959Z"
                fill="#FEFEFE"
            />
            <path
                d="M37.8375 17.3425L37.3929 18.1117C37.2537 18.3725 36.9883 18.5732 36.5771 18.5732L35.8672 18.5598L36.0729 17.8643H36.2121C36.2851 17.8643 36.3382 17.8576 36.378 17.8375C36.4178 17.8241 36.4378 17.7974 36.4709 17.7572L36.7362 17.3359H37.844L37.8375 17.3425Z"
                fill="#FEFEFE"
            />
        </svg>
    )
}
function Amex() {
    return (
        <svg width="50" height="32" viewBox="0 0 50 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M0 0H50C50 2.78383 50 1.2465 50 2.78383V29.2175C50 30.7535 50 29.2175 50 32H0.0141956C0.00228692 29.2177 0 30.7535 0 29.2177V2.78383C0 1.2465 0.00981236 2.78383 0 0Z"
                fill="#26A6D1"
            />
            <path
                d="M8.06255 11.1328L3.125 20.8619H9.03594L9.76873 19.3107H11.4437L12.1765 20.8619H18.6828V19.6779L19.2625 20.8619H22.6281L23.2078 19.6529V20.8619H36.7392L38.3845 19.3509L39.9251 20.8619L46.8751 20.8744L41.9219 16.0245L46.8751 11.1328H40.0329L38.4313 12.6158L36.9391 11.1328H22.2189L20.9548 13.644L19.6611 11.1328H13.7625V12.2765L13.1064 11.1328H8.06255ZM9.20629 12.5144H12.0876L15.3626 19.1117V12.5144H18.519L21.0486 17.2446L23.3799 12.5144H26.5205V19.4956H24.6095L24.5939 14.0251L21.8079 19.4956H20.0985L17.2969 14.0251V19.4956H13.3656L12.6203 17.9305H8.5938L7.85005 19.4942H5.74373L9.20629 12.5144ZM28.2688 12.5144H36.0391L38.4157 14.8001L40.8689 12.5144H43.2454L39.6345 16.0231L43.2454 19.4915H40.7611L38.3845 17.1791L35.9188 19.4915H28.2688V12.5144ZM10.6079 13.6955L9.28135 16.4836H11.933L10.6079 13.6955ZM30.1876 13.9598V15.2342H34.4267V16.6547H30.1876V18.046H34.9423L37.1516 15.9967L35.0361 13.9586H30.1876V13.9598Z"
                fill="white"
            />
        </svg>
    )
}

function Country() {
    return (
        <svg width="17" height="24" viewBox="0 0 17 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M8.5 0C3.813 0 0 3.813 0 8.5C0 10.043 0.692 11.7025 0.721 11.7725C0.9445 12.303 1.3855 13.127 1.7035 13.61L7.5315 22.4405C7.77 22.8025 8.123 23.01 8.5 23.01C8.877 23.01 9.23 22.8025 9.4685 22.441L15.297 13.61C15.6155 13.127 16.056 12.303 16.2795 11.7725C16.3085 11.703 17 10.0435 17 8.5C17 3.813 13.187 0 8.5 0ZM15.3575 11.3845C15.158 11.86 14.7475 12.6265 14.462 13.0595L8.6335 21.8905C8.5185 22.065 8.482 22.065 8.367 21.8905L2.5385 13.0595C2.253 12.6265 1.8425 11.8595 1.643 11.384C1.6345 11.3635 1 9.836 1 8.5C1 4.3645 4.3645 1 8.5 1C12.6355 1 16 4.3645 16 8.5C16 9.838 15.364 11.3695 15.3575 11.3845Z"
                fill="black"
            />
            <path
                d="M8.5 4.0005C6.0185 4.0005 4 6.0195 4 8.5005C4 10.9815 6.0185 13.0005 8.5 13.0005C10.9815 13.0005 13 10.9815 13 8.5005C13 6.0195 10.9815 4.0005 8.5 4.0005ZM8.5 12.0005C6.5705 12.0005 5 10.4305 5 8.5005C5 6.5705 6.5705 5.0005 8.5 5.0005C10.4295 5.0005 12 6.5705 12 8.5005C12 10.4305 10.4295 12.0005 8.5 12.0005Z"
                fill="black"
            />
        </svg>
    )
}

function PayPal() {
    return (
        <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
            width="50.000000pt" height="50.000000pt" viewBox="0 0 50.000000 50.000000"
            preserveAspectRatio="xMidYMid meet">

            <g transform="translate(0.000000,50.000000) scale(0.100000,-0.100000)"
                fill="#000000" stroke="none">
                <path d="M151 434 c-133 -67 -158 -229 -52 -335 106 -106 268 -81 335 52 32
66 33 118 2 184 -27 59 -46 77 -106 104 -62 28 -115 26 -179 -5z m154 5 c55
-15 120 -82 135 -138 22 -83 -5 -164 -72 -214 -151 -114 -357 18 -317 205 18
83 105 156 189 158 14 0 43 -5 65 -11z"/>
                <path d="M145 278 c-9 -62 -18 -119 -21 -125 -2 -9 8 -13 30 -13 32 0 34 2 40
44 7 43 7 44 52 49 78 8 125 78 90 132 -15 23 -23 25 -95 25 l-78 0 -18 -112z"/>
                <path d="M346 273 c-22 -36 -53 -53 -97 -53 -33 0 -38 -7 -41 -70 -3 -38 0
-45 17 -45 14 0 21 9 26 33 8 42 16 50 53 54 37 4 69 45 64 84 l-3 29 -19 -32z"/>
            </g>
        </svg>
    )
}

export { Bitcoin,Atlos, Card, Sol, Shib, Bch, Bnb, Btc, Eth, Usdt, Country, Coinbase, Stripe, Visa, Mastercard, Union, Amex, Gift, PayPal }
