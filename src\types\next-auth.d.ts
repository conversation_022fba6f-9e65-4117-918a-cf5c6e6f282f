import NextAuth from "next-auth/next";

declare module "next-auth" {
    interface Session {
        user: {
            isDiscordUser: boolean;
            provider: any;
            access_token: string;
            refresh_token: string;
            expiration: number,
            id: number;
            email: string;
            status: string;
            bio: string | null;
            image: string | null;
            first_name: string | null;
            last_name: string | null;
            display_name: string | null;
            country: string | null;
            stripeCustomerID: string | null;
            referred: number | null;
            role: string;
            is_affiliate: boolean | null;
            referral_code: string | null;
            address: string | null;
            town: string | null;
            region: string | null;
            postal_code: string | null;
            phone: string | null;
            discord: string | null;
            telegram: string | null;
            otp: string | null;
            otpTime: string | null;
            metamask: string | null;
            points: number | null;
            discord_id: string | null;
            exp: number | null;
            reset_password_token: string | null;
            reset_password_expire: string | null;
            created_at: Date;
            updated_at: Date;
        }
    }
}
