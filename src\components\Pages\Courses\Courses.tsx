import { CourseModel } from '@/types/CourseModel'
import CourseCard from '../Home/CourseCard'

interface CoursesProps {
    courses: CourseModel[]
}

const Courses = ({ courses }: CoursesProps) => {
    return (
        <section className="container mx-auto" id="courses">
            <div className="space-y-8 pb-16 pt-11 max-md:space-y-3 max-md:border-b max-md:border-gray-700 max-md:pb-14 max-md:pt-9">
                <h3 className="text-headline font-medium max-md:text-callout">Top courses in Blockchain</h3>

                <div className="grid grid-cols-3 gap-9 max-lg:grid-cols-2 max-md:grid-cols-1 max-md:gap-[1.125rem]">
                    {courses.length > 0 ? (
                        courses.map((course: any) => <CourseCard key={course.id} course={course} />)
                    ) : (
                        <p>No courses available</p>
                    )}
                </div>
            </div>
        </section>
    )
}

export default Courses
