import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'
import Courses from '@/components/Pages/Courses/Courses'

export const metadata = {
    title: 'Private Courses',
    description:
        "Crypto University is the world's #1 Cryptocurrency education platform. Get access to our customized courses to help you succeed in crypto.",
    keywords: ['Courses', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const GetPrivateCourses = async () => {
    try {
        // const response = await fetchInstance('/course/all/private', {
        //     next: { revalidate: 10 },
        // })
        const response = await fetchInstance('/course/all/public', {
            next: { revalidate: 10 },
        })
        return response.courses
    } catch (error) {
        console.error('Error Calls:', error)
        return error
    }
}

const PrivateCoursesPage = async () => {
    const courses = await GetPrivateCourses()
    return (
        <section className="flex w-full flex-col text-black">
            <Courses courses={courses} />
            <CTA />
        </section>
    )
}

export default PrivateCoursesPage
