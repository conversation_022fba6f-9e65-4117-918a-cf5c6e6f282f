'use client'
import useAuth from "@/hooks/useAuth"
import { isValidEmail } from "@/lib/isValidEmail"
import { ErrorMessage, Field, Form, Formik } from "@/lib/formik"
import { useSearchParams } from 'next/navigation'

interface RegisterFormValues {
    display_name: string;
    fullname: string;
    email: string;
    password: string;
    confirmPassword: string;
}
const initialValues: RegisterFormValues = {
    display_name: "",
    fullname: "",
    email: "",
    password: "",
    confirmPassword: "",
};
const validate = (values: RegisterFormValues) => {
    const errors: Partial<RegisterFormValues> = {};

    if (!values.display_name) {
        errors.display_name = "Display name is required";
    }
    if (!values.fullname) {
        errors.fullname = "Full Name name is required";
    }

    if (!values.email) {
        errors.email = "Email is required";
    } else if (isValidEmail(values.email)) {
        errors.email = "Invalid email address";
    }

    if (!values.password) {
        errors.password = "Password is required";
    } else if (values.password.length < 8) {
        errors.password = "Password must be at least 8 characters";
    }

    if (!values.confirmPassword) {
        errors.confirmPassword = "Confirm password is required";
    } else if (values.password !== values.confirmPassword) {
        errors.confirmPassword = "Passwords must match";
    }

    return errors;
};

const RegisterForm = () => {
    const { register, error, isLoading } = useAuth()
    const searchParams = useSearchParams()

    const source: any = searchParams.get('source')
    const handleSubmit = async (values: RegisterFormValues) => {
        const { display_name,fullname, email, password } = values;
        const user = { display_name,fullname, email, password }
        await register(user,source)
    }

    return (
        <Formik initialValues={initialValues} onSubmit={handleSubmit} validate={validate}>
            {({ isSubmitting }) => (
                <Form className="w-[700px] bg-white shadow-lg px-16 py-20 border rounded flex gap-6 flex-col">
                    <h1 className="text-h3 font-semibold">Register</h1>

                    <div>
                        <Field
                            className="shadow placeholder:text-gray-600 appearance-none border rounded w-full py-2 px-3 text-gray-950 leading-tight focus:outline-none focus:shadow-outline"
                            type="text"
                            placeholder='e.g JohnDoe'
                            autoComplete="username"
                            name="display_name"
                            id="display_name"
                        />
                        <ErrorMessage name="display_name" component="div" className="text-red" />
                    </div>
                    <div>
                        <Field
                            className="shadow placeholder:text-gray-600 appearance-none border rounded w-full py-2 px-3 text-gray-950 leading-tight focus:outline-none focus:shadow-outline"
                            type="text"
                            placeholder='e.g JohnDoe'
                            autoComplete="fullname"
                            name="fullname"
                            id="fullname"
                        />
                        <ErrorMessage name="fullname" component="div" className="text-red" />
                    </div>

                    <div>
                        <Field
                            className="shadow placeholder:text-gray-600 appearance-none border rounded w-full py-2 px-3 text-gray-950 leading-tight focus:outline-none focus:shadow-outline"
                            type="email"
                            name="email"
                            autoComplete="email"
                            id="email"
                            placeholder="<EMAIL>"
                        />
                        <ErrorMessage name="email" component="div" className="text-red" />
                        {error && <div className="text-red">{error}</div>}
                    </div>

                    <div>
                        <Field
                            className="shadow placeholder:text-gray-600 appearance-none border rounded w-full py-2 px-3 text-gray-950 leading-tight focus:outline-none focus:shadow-outline"
                            type="password"
                            name="password"
                            autoComplete="new-password"
                            placeholder="your password"
                            id="password"
                        />
                        <ErrorMessage name="password" component="div" className="text-red" />
                    </div>

                    <div>
                        <Field
                            className="shadow placeholder:text-gray-600 appearance-none border rounded w-full py-2 px-3 text-gray-950 leading-tight focus:outline-none focus:shadow-outline"
                            type="password"
                            autoComplete="confirm-password"
                            name="confirmPassword"
                            placeholder="confirm password"
                            id="confirmPassword"
                        />
                        <ErrorMessage name="confirmPassword" component="div" className="text-red" />
                    </div>

                    <div className="flex items-center justify-between">
                        <button
                            className="bg-blue/90 hover:bg-blue text-white font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            type="submit"
                            disabled={isLoading || isSubmitting}
                        >
                            {isLoading || isSubmitting ? "Loading..." : "Register"}
                        </button>
                    </div>
                </Form>
            )}
        </Formik>
    )
}

export default RegisterForm