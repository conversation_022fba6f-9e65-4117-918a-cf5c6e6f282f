import { Faq, FaqCategories } from "@/types/FaqModel"
import { createClient, groq } from "next-sanity"

export async function getFAQs(): Promise<Faq> {
    const client = createClient({
        projectId: process.env.SANITY_PROJECT_ID as string,
        dataset: 'production',
        apiVersion: '2023-06-06',
        useCdn: true,
    });

    return client.fetch(
        groq`*[_type == "faq"]{
            _id,
            _createdAt,
            categories[] {
                title,
                questions[] {
                    title,
                    answer,
                }
            }
        }`
    )
}

export async function getFAQCategories(): Promise<FaqCategories[]> {
    const client = createClient({
        projectId: process.env.SANITY_PROJECT_ID as string,
        dataset: 'production',
        apiVersion: '2023-06-06',
        useCdn: true,
    });

    return client.fetch(
        groq`*[_type == "faq"]{
            categories[] {
                title,
            }
        }`
    )
}

