'use client'
import { useState, useEffect } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import useCart from '@/hooks/useCart'
import { CourseModel } from '@/types/CourseModel'

interface Props {
    product: CourseModel
    setTotalPrice: React.Dispatch<React.SetStateAction<number>>
    setSelectedItems: React.Dispatch<React.SetStateAction<number[]>>
    selectedItems: number[]
}

const CartCard = ({ product, setTotalPrice, setSelectedItems, selectedItems }: Props) => {
    const cart = useCart()
    const [isMounted, setIsMounted] = useState(false)
    useEffect(() => {
        setIsMounted(true)
    }, [])
    if (!isMounted) return null

    const OnRemoveToCart = (e: any) => {
        e.preventDefault()
        setSelectedItems(prev => prev?.filter(item => item !== product.id))
        cart.removeproduct(product.id)
        if (selectedItems.includes(product.id)) {
            setTotalPrice(prev => prev - product.priceWithoutTax)
        }
    }
    return (
        <div className="flex w-[700px] cursor-pointer select-none items-center justify-between max-xl:w-full max-xl:max-w-[700px]">
            <div className="flex items-center gap-5">
                <ImageShortcut
                    src={product.image}
                    alt={product.name}
                    width={128}
                    height={128}
                    className="h-32 w-32 rounded-xl object-contain max-md:h-16 max-md:w-16"
                />
                <div className="flex flex-col gap-3">
                    <h1 className="text-sub2 capitalize max-md:text-cap2">{product.name}</h1>
                    <p className="text-sub1 font-semibold max-md:text-cap1">${product.priceWithoutTax}</p>
                </div>
            </div>
            <button
                onClick={OnRemoveToCart}
                className="flex min-h-[56px] min-w-[56px] items-center justify-center rounded-full border border-gray-300 hover:bg-gray-300 max-md:min-h-[30px] max-md:min-w-[30px]">
                <svg
                    className="max-md:h-[14px] max-md:w-[14px]"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    fill="none"
                    viewBox="0 0 24 24">
                    <path
                        stroke="#081228"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="1.5"
                        d="M19.324 9.469s-.543 6.735-.858 9.572c-.15 1.355-.987 2.149-2.358 2.174-2.61.047-5.221.05-7.83-.005-1.318-.027-2.141-.831-2.288-2.162-.317-2.862-.857-9.58-.857-9.58M20.708 6.24H3.75M17.439 6.24a1.648 1.648 0 01-1.615-1.324L15.58 3.7a1.28 1.28 0 00-1.237-.949H10.11a1.28 1.28 0 00-1.237.949L8.63 4.916A1.648 1.648 0 017.016 6.24"></path>
                </svg>
            </button>
        </div>
    )
}

export default CartCard
