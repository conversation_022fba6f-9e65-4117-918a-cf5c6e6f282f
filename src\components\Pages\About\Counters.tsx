import Counter from '@/components/Ui/Counter'
import { Profile, Video, Heart } from '@public/home'

export const Counters = () => {
    return (
        <div className="flex grid-cols-2 flex-wrap gap-8 justify-between max-md:grid max-md:gap-y-[1.125rem]">
            <Counter icon={<Profile strok='#FCC229' />} number={30000} unit={'Members'} className="max-md:w-full" />
            <Counter
                icon={<Video strok='#FCC229' />}
                number={10000}
                unit={'Hours Of Content'}
                className="max-md:w-full max-md:border-none"
            />
            <Counter icon={<Heart strok='#FCC229' />} number={20} unit={'Partners'} className="max-md:w-full border-none" />
        </div>
    )
}
