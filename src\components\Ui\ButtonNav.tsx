import Link from 'next/link'
import { FC, ReactNode } from 'react'

interface Props {
    children: ReactNode
    href: string
}

const Button: FC<Props> = ({ children, href }) => {
    const className =
        'py-[9.5px] w-full flex items-center justify-center rounded-full border-[0.5px] border-blue text-blue font-sans select-none px-6 font-semibold text-sub3 max-md:font-medium fit-content transition-[background-color_color] duration-150 bg-white gap-2'

    return (
        <Link aria-label="Button" href={href} className={className}>
            {children}
        </Link>
    )
}

export default Button
