"use client";
import Script from "next/script";

interface SmartlookProps {
  smartLookTrackingCode: string;
  smartLookRegion: string;
}

const SmartlookComponent = ({ smartLookTrackingCode, smartLookRegion }: SmartlookProps) => {
  return (
    <>
      <Script id="smartlook" strategy="afterInteractive" type="text/javascript">
        {`
          window.smartlook||(function(d) {
            var o=smartlook=function(){ o.api.push(arguments)},h=d.getElementsByTagName('head')[0];
            var c=d.createElement('script');o.api=new Array();c.async=true;c.type='text/javascript';
            c.charset='utf-8';c.src='https://web-sdk.smartlook.com/recorder.js';h.appendChild(c);
          })(document);
          smartlook('init', '${smartLookTrackingCode}', { region: '${smartLookRegion}' });
        `}
      </Script>
    </>
  );
};

export default SmartlookComponent;
