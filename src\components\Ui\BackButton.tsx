import { cn } from "@/lib/cn";

interface BackButtonProps {
    children: React.ReactNode;
    onClick?: () => void;
    className?: string;
}

const BackButton = ({ onClick, children, className }: BackButtonProps) => {
    return (
        <button type="button" className={cn("flex font-manrope text-[#2A2E31] font-medium text-sub3 justify-start items-center gap-1", className)}onClick={onClick}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="24" height="24" rx="12" fill="#EEEEEE" />
                <path d="M14.0403 15.5717L9.95972 12L14.0403 8.42822" stroke="#292929" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            </svg> {children}
        </button>
    )
}

export default BackButton