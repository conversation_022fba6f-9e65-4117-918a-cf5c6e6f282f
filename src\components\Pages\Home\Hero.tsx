import Link from 'next/link'
import { cn } from '@/lib/cn'
import But<PERSON> from '@/components/Ui/Button'
import HeroImage from 'public/home/<USER>'
import numberWithCommas from '@/lib/formatNumber'
import ImageShortcut from '@/components/Ui/Image'
import { Counters } from '@/components/Ui/Counters'

interface props {
    number: number
    unit: string
    icon: JSX.Element
    className?: string
}

const Counter = ({ number, unit, icon, className }: props) => {
    return (
        <div className={cn('flex items-center gap-4 border-r border-gray-200 max-md:gap-2', className)}>
            {icon}
            <div className="flex flex-col pr-9 max-md:pr-0">
                <div className="text-b1 font-medium max-md:text-sub1">{numberWithCommas(number)}+</div>
                <p className="w-full text-sub2 max-md:text-cap2">{unit}</p>
            </div>
        </div>
    )
}

const Hero = () => {
    return (
        <section
            id="hero"
            className="relative w-full overflow-hidden border-b border-gray-700 pb-[4.75rem] text-black max-md:border-none">
            <div className="container relative mx-auto pt-20">
                <ImageShortcut
                    src={HeroImage}
                    alt="Hero"
                    width={704}
                    height={704}
                    priority
                    className="absolute right-0 top-0 h-auto max-xl:w-[329px] max-sm:left-44"
                />

                <div className="flex flex-col gap-[3.125rem] max-md:gap-[5.625rem]">
                    {/* Content */}
                    <div className="flex flex-col items-start gap-12 max-md:gap-16">
                        <div className="flex flex-col gap-6">
                            <h1 className="z-10 max-w-[760px] text-h1 font-medium max-md:max-w-[236px] max-md:text-b1">
                                Discover the biggest <span className="font-semibold text-blue">Crypto</span>{' '}
                                opportunities today
                            </h1>
                            <p className="max-w-[770px] text-callout max-md:font-sans max-md:text-cap1">
                                Crypto University is the{' '}
                                <span className="font-semibold">
                                    world&apos;s #1 Cryptocurrency education platform.
                                </span>
                                <br className="md:hidden" />
                                <br className="md:hidden" /> Our team of experts offers customized courses designed to
                                guide you on becoming the next Crypto experts and millionaire
                            </p>
                        </div>
                        <Link
                            aria-label="Start Now"
                            href="/register"
                            className="w-full max-w-[274px] rounded-full text-sub2 max-md:max-w-[238px] max-md:text-sub3">
                            <Button variant="primary" rounded>
                                Start Now &gt;
                            </Button>
                        </Link>
                    </div>

                    {/* Counters */}
                    <Counters />
                </div>
            </div>
        </section>
    )
}

export default Hero
