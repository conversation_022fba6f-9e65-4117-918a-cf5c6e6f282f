import { FieldAttributes, FormikProps } from "@/lib/formik";
import Select, { GroupBase, StylesConfig } from "react-select";

type OptionType = {
  value: string;
  label: string | React.JSX.Element;
};
interface CustomSelectProps extends FieldAttributes<any> {
  label?: string;
  options: OptionType[];
  formik: FormikProps<any>;
  style: StylesConfig<OptionType, false, GroupBase<OptionType>>;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  label,
  options,
  formik,
  style,
  isSearchable,
  defaultValue,
  setIsFocus,
  ...props
}) => {
  const handleChange = (selectedOption: OptionType | null) => {
    formik.setFieldValue(
      props.name,
      selectedOption ? selectedOption.value : null
    );
  };

  const handleBlur = () => {
    formik.setFieldTouched(props.name, true);
    setIsFocus(false);
  };

  return (
    <Select
      {...props}
      onChange={handleChange}
      onBlur={handleBlur}
      options={options}
      instanceId={props.name}
      value={options.find((option) => option.value === props.value)}
      theme={(theme) => ({ ...theme, borderRadius: 0 })}
      placeholder={label}
      isSearchable={isSearchable}
      styles={{
        ...style,
      }}
      openMenuOnFocus
      onFocus={() => {
        setIsFocus(true);
        if (props.name === "country_code" || props.name === "phone_number") {
          formik.setFieldTouched("country_code", false);
          formik.setFieldTouched("phone_number", false);
        } else {
          formik.setFieldTouched(props.name, false);
        }
      }}
    />
  );
};

export default CustomSelect;
