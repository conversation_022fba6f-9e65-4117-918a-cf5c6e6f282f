'use client'
import 'react-responsive-modal/styles.css'
import { CourseModel } from '@/types/CourseModel'
import DisclosureLesson from '@/components/Ui/DisclosureLesson'

interface Props {
    course: CourseModel
    TopicsCount: number
}

const Lessons = ({ course, TopicsCount }: Props) => {
    return (
        <div id={'syllabus'} className="flex flex-col gap-8 max-md:gap-6 ">
            <div className="flex items-center justify-between max-md:flex-col max-md:items-start max-md:gap-3 max-md:px-4">
                <h3 className="text-b3 font-medium max-md:text-sub1">Lesson in the class</h3>
                <p className="pr-4 font-sans text-sub2 max-md:text-cap1">
                    {course.course_lessons.length} sections • {TopicsCount} Lectures
                </p>
            </div>
            <div className="w-full">
                <DisclosureLesson isPurchasing={false} course={course} />
            </div>
        </div>
    )
}

export default Lessons
