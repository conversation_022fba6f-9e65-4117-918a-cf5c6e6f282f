'use client'
import { useState } from 'react'
import fetchInstance from '@/lib/fetch'
import Button from '@/components/Ui/Button'

type Props = {
    product: any
    setCheckoutCoupon: (x: string) => void
    disabled: boolean
    user: number | null
}

const SaveIcon = () => (
    <svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M11.531 18.5C11.4067 18.4998 11.2877 18.4504 11.1998 18.3625L1.39991 8.56273C1.31034 8.48279 1.25635 8.37021 1.25007 8.25031L1 0.987689C0.999791 0.857736 1.04897 0.732599 1.13749 0.637591C1.23103 0.546772 1.35722 0.497385 1.48759 0.500107L8.7503 0.750176C8.87063 0.751432 8.9853 0.801027 9.06901 0.887661L18.863 10.6997C19.0457 10.8828 19.0457 11.1794 18.863 11.3622L11.8756 18.3624C11.7848 18.4534 11.6605 18.5032 11.5318 18.4998L11.531 18.5ZM2.1935 8.02469L11.531 17.3996L17.8686 11.0311L8.53103 1.69367L1.95618 1.45616L2.1935 8.02469Z"
            fill="black"
        />
        <path
            d="M6.11224 4.5688C6.39893 4.55478 6.67871 4.65941 6.88587 4.858C7.09304 5.05679 7.20918 5.33197 7.20709 5.61886C7.205 5.90597 7.08467 6.17948 6.87478 6.37512C6.68037 6.57267 6.41439 6.68316 6.1371 6.68127C5.8504 6.69529 5.57063 6.59066 5.36363 6.39207C5.15646 6.19328 5.04031 5.91809 5.04241 5.63121C5.0445 5.3441 5.16462 5.07059 5.37472 4.87516C5.57122 4.68096 5.83594 4.5711 6.11218 4.5688M6.11218 3.6313C5.49611 3.63068 4.91497 3.91674 4.53959 4.40492C4.16417 4.89334 4.03715 5.52847 4.19618 6.12359C4.35501 6.71871 4.78172 7.2061 5.35049 7.44234C5.91948 7.6786 6.56593 7.63675 7.0995 7.32913C7.63333 7.02152 7.99351 6.4833 8.07445 5.87264C8.15523 5.26183 7.94743 4.64844 7.51216 4.21259C7.14135 3.84031 6.63766 3.63107 6.11217 3.63123L6.11218 3.6313Z"
            fill="black"
        />
        <path
            d="M6.11224 4.5688C6.39893 4.55478 6.67871 4.65941 6.88587 4.85799C7.09304 5.05679 7.20918 5.33197 7.20709 5.61886C7.205 5.90597 7.08467 6.17948 6.87478 6.37512C6.68037 6.57267 6.4144 6.68316 6.1371 6.68127C5.8504 6.69529 5.57063 6.59066 5.36363 6.39207C5.15646 6.19328 5.04031 5.91809 5.04241 5.63121C5.0445 5.3441 5.16461 5.07059 5.37472 4.87516C5.57122 4.68096 5.83594 4.5711 6.11218 4.5688M11.531 18.5C11.4067 18.4997 11.2877 18.4504 11.1998 18.3625L1.39991 8.56273C1.31034 8.48279 1.25635 8.37021 1.25007 8.25031L1 0.987689C0.999791 0.857736 1.04897 0.732599 1.13749 0.637591C1.23103 0.546772 1.35722 0.497385 1.48759 0.500107L8.7503 0.750176C8.87063 0.751432 8.9853 0.801027 9.06901 0.887661L18.863 10.6997C19.0457 10.8828 19.0457 11.1794 18.863 11.3622L11.8756 18.3624C11.7848 18.4534 11.6605 18.5032 11.5318 18.4998L11.531 18.5ZM2.1935 8.02469L11.531 17.3996L17.8686 11.0311L8.53103 1.69367L1.95618 1.45616L2.1935 8.02469ZM6.11218 3.6313C5.49611 3.63068 4.91497 3.91674 4.53959 4.40492C4.16417 4.89334 4.03715 5.52847 4.19618 6.12359C4.35501 6.71871 4.78172 7.2061 5.35049 7.44234C5.91948 7.6786 6.56593 7.63675 7.0995 7.32913C7.63333 7.02152 7.99351 6.4833 8.07445 5.87264C8.15523 5.26183 7.94743 4.64844 7.51216 4.21259C7.14135 3.84031 6.63766 3.63107 6.11218 3.63123L6.11218 3.6313Z"
            stroke="black"
            strokeWidth="0.2"
            strokeLinecap="round"
        />
    </svg>
)
const DeleteIcon = () => (
    <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.98887 18.5C13.9629 18.5 18 14.4851 18 9.51113C18 4.53715 13.9629 0.5 8.98887 0.5C4.01488 0.5 0 4.53715 0 9.51113C0 14.4851 4.01488 18.5 8.98887 18.5ZM8.98887 10.8943L12.3345 14.2399L13.7397 12.857L10.394 9.5114L13.7397 6.16577L12.3345 4.76061L8.98887 8.10625L5.64323 4.76061L4.26034 6.16577L7.60597 9.5114L4.26034 12.857L5.64323 14.2399L8.98887 10.8943Z"
            fill="#AAAAAA"
        />
    </svg>
)

const CouponForm = ({ product, setCheckoutCoupon, user, disabled }: Props) => {
    const [coupon, setCoupon] = useState('')
    const [amount, setAmount] = useState(null)
    const [discount, setDiscount] = useState(null)
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [formValue, setFormValue] = useState({
        title: '',
    })

    //alert(product.type);

    const handleCouponSubmit = async (e: any) => {
        e.preventDefault()
        setError(null)
        setIsLoading(true)
        if (formValue.title === '') {
            setIsLoading(false)
            setError('Please enter a coupon code')
            return
        }
        let data: any
        if (product.type === 'course') {
          data = {
            coupon_code: formValue.title,
            course_id: product.id,
            user_id: user,
          }
        } else if (product.type === 'mentorship') {
          data = {
            coupon_code: formValue.title,
            mentorship_id: product.id,
            user_id: user,
          }
        } else if (product.type === 'indicator') {
            data = {
              coupon_code: formValue.title,
              indicator_id: product.id,
              user_id: user,
            }
          } else if (product.type === 'bundle') {
          data = {
            coupon_code: formValue.title,
            bundle_id: product.id,
            user_id: user,
          }
        } else if (product.type === 'bootcamp') {
          data = {
            coupon_code: formValue.title,
            bootcamp_id: product.id,
            user_id: user,
          }
        } else {
          setIsLoading(false)
          setError('Something went wrong')
          return
        }
        // let data: any
        // if (product.type === 'course')
        //     data = {
        //         coupon_code: formValue.title,
        //         course_id: product.id,
        //         user_id: user,
        //     }
        // else if (product.type === 'mentorship')
        //     data = {
        //         coupon_code: formValue.title,
        //         mentorship_id: product.id,
        //         user_id: user,
        //     }
        // else if (product.type === 'bundle')
        //     data = {
        //         coupon_code: formValue.title,
        //         bundle_id: product.id,
        //         user_id: user,
        //     }
        // else if (product.type === 'bootcamp')
        //     data = {
        //         coupon_code: formValue.title,
        //         bootcamp_id: product.id,
        //         user_id: user,
        //     }
        // else {
        //     setIsLoading(false)
        //     setError('Something went wrong')
        //     return
        // }

        const response = await fetchInstance('/coupon/coupon-validity-on-buy', {
            method: 'POST',
            body: JSON.stringify(data),
        })
        setIsLoading(false)

        if (response.success === false) {
            setError(response.message)
            return
        }

        setFormValue({ title: '' })
        setCheckoutCoupon(formValue.title)
        setCoupon(() => formValue.title)
        setAmount(() => response.coupon.amount)
        setDiscount(() => response.coupon.discount)
    }

    const handleRemoveCoupon = async () => {
        setCoupon('')
        setCheckoutCoupon('')
        setError(null)
        setAmount(null)
        setDiscount(null)
    }

    return (
        <>
            <form onSubmit={handleCouponSubmit} className="flex h-fit flex-col gap-1">
                <div className="flex items-center gap-3">
                    <input
                        type="text"
                        placeholder="Discount Code"
                        value={formValue.title}
                        onChange={e => setFormValue({ ...formValue, title: e.target.value })}
                        className="w-full rounded-full border border-gray-600 px-6 py-4 text-black outline-none placeholder:text-gray-900 disabled:bg-gray-400 disabled:text-gray-900"
                        disabled={coupon !== '' || disabled}
                    />
                    <div className="">
                        <Button
                            type="submit"
                            rounded
                            variant="secondary"
                            disabled={isLoading || coupon !== '' || disabled}>
                            Apply
                        </Button>
                    </div>
                </div>
                {error && (
                    <p className="text-sm rounded-lg bg-red/20 p-2 font-manrope text-cap1 font-medium text-red">
                        {error}
                    </p>
                )}
            </form>

            <div className="flex select-none">
                {coupon !== '' && (
                    <div className="flex gap-[0.625rem] rounded-lg bg-gray-300 px-[0.875rem] py-2 text-sub3 font-medium">
                        <SaveIcon />
                        <p className="uppercase">{coupon}</p>
                        <div onClick={handleRemoveCoupon} className="cursor-pointer">
                            <DeleteIcon />
                        </div>
                    </div>
                )}
            </div>

            <div className="flex items-center justify-between text-sub1">
                <p>Subtotal</p>
                <p className="font-medium">${product.finalPrice - product.tax}</p>
            </div>

            <div className="flex items-center justify-between text-sub1">
                <p>Tax</p>
                <p className="font-medium">${product.tax}</p>
            </div>

            <div className="flex items-center justify-between text-sub1">
                <p>
                    Discount{' '}
                    <span className="text-sub3 uppercase text-gray-700">{coupon !== '' && '(' + coupon + ')'}</span>
                </p>
                {amount !== null ? (
                    <p className="font-medium">-${amount}</p>
                ) : discount !== null ? (
                    <p className="font-medium">-{discount}%</p>
                ) : (
                    <p className="font-medium">$0</p>
                )}
            </div>

            <div className="flex items-center justify-between text-b2 font-semibold max-md:text-sub1">
                <p>Total</p>
                {amount !== null ? (
                    <p>${product.finalPrice - amount}</p>
                ) : discount !== null ? (
                    <p>${product.finalPrice - (product.finalPrice * discount) / 100}</p>
                ) : (
                    <p>${product.finalPrice}</p>
                )}
            </div>
        </>
    )
}

export default CouponForm
