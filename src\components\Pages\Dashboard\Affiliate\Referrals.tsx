'use client'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import TransactionModel from '@/types/TransactionModel'
import OrderModel from '../Settings/OrderModel'


interface Model {
    reference: string
    description: string
    date: string
    commission: number
    status: string,
    image: string,
}

interface Props {
    referral: Model
}

const Referrals = ({ referral }: Props) => {
    const [openModel, setOpenModel] = useState(false)
    return (
        <>
            {/* <div className={cn('absolute inset-0 top-0 z-50 flex justify-center', openModel ? '' : 'hidden')}>
                <OrderModel tax={tax} setOpenModel={setOpenModel} openModel={openModel} product={referral} />
            </div> */}
            <div className="grid grid-cols-12 place-content-center place-self-center border-b border-gray-400 pb-4 text-cap1 font-medium capitalize max-md:hidden">
                <div className="col-span-3 flex items-center gap-2">
                    <ImageShortcut
                        src={
                            referral.image
                        }
                        alt="product"
                        width={40}
                        height={40}
                        className="object-cover"
                    />
                    <p>
                        {referral.reference}
                    </p>
                </div>
                <p className="col-span-3 flex items-center">{referral.description}</p>
                <p className="col-span-2 flex items-center">{referral.date}</p>
                <p
                    className={cn(
                        'col-span-2 flex items-center text-cap1',
                        referral.status == 'pending'
                            ? 'text-yellow'
                            : referral.status == 'completed'
                            ? 'text-green-dark'
                            : 'text-red'
                    )}>
                    {referral.status}
                </p>
                <p className="col-span-2 flex items-center ">${referral.commission}</p>
            </div>
            <div className="flex w-full flex-col gap-4 rounded-md p-4 shadow-md md:hidden">
                <div className="flex items-center gap-3">
                    <ImageShortcut
                        src={
                            referral.image
                        }
                        alt="product"
                        width={62}
                        height={62}
                        className="object-cover"
                    />
                    <div className="flex flex-col">
                        <p className="text-cap1 font-medium capitalize">
                            {referral.reference}
                        </p>
                        <p className="text-cap2 text-gray-700">Description: {referral.description}</p>
                    </div>
                </div>
                <div className="border-b border-gray-300" />
                <div className="flex flex-col gap-2">
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Date</p>
                        <p className="font-medium">{referral.date}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Commission</p>
                        <p className="font-medium">{referral.commission}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Status</p>
                        <p
                            className={cn(
                                'col-span-2 flex items-center text-cap2',
                                referral.status == 'pending'
                                    ? 'text-yellow'
                                    : referral.status == 'completed'
                                    ? 'text-green-dark'
                                    : 'text-red'
                            )}>
                            {referral.status}
                        </p>
                    </div>
                </div>
               
            </div>
        </>
    )
}

export default Referrals
