import { Country } from "@/types/CountryModel";

async function getCountryInfo(): Promise<Country[]> {
    // const countries = await fetch('https://restcountries.com/v3.1/all',
    const countries = await fetch('https://restcountries.com/v3.1/all?fields=name,flags,idd',
        {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
            next: { revalidate: false },
        }
    );

    const countriesData: Country[] = await countries.json();

    const countryInfo: Country[] = [];

    for (const country of countriesData) {
        const { name, flags, idd } = country;
        countryInfo.push({
            name: { common: name.common },
            flags: { svg: flags.svg },
            idd: idd,
        });
    }
    return countryInfo;
}

export default getCountryInfo;