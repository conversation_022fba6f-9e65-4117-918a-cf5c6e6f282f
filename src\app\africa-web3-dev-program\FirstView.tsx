import React from 'react'

import devprogramvideo from '../../../public/icons/devprogramvideo.svg'
import yellowarrow from '../../../public/icons/yellowarrow.svg'
import watchvideotext from '../../../public/icons/watchvideotext.svg'
import Image from 'next/image'
import enrollnowbutton from '../../../public/icons/enrollnowbutton.svg'
import Link from 'next/link'
import olu from '../../../public/icons/olu.svg'
import profiledev from '../../../public/icons/profiledev.svg'


const FirstView = () => {
  return (
    <div>
       <div className="w-full bg-[#262216] p-5">
              <div className=" mx-auto grid max-w-[400px] py-16 sm:max-w-[700px] md:max-w-[1400px]">
                  <div>
                      <h2 className="w-[343px] text-[30px] font-[600] text-white md:w-[776px] md:text-[50px]">
                          The Africa Web3 Dev Program
                      </h2>
                      <div className="justify-between md:flex">
                          <h3 className="text-[26px] font-[500] leading-[72px] text-green md:text-[48px]">
                              $1.3 Million{' '}
                              <span className="text-[28px] font-[500] leading-[42px] text-white ">
                                  in salaries paid
                              </span>
                          </h3>
                          <section className="flex gap-2 px-2  justify-end items-center">
                              <Image
                                  src={yellowarrow}
                                  alt="devProgram"
                                  className="h-[72px] w-[45px] md:h-[150px] md:w-[70px] pt-5 md:pt-7"
                                  width={45}
                                  height={72.11}
                              />
                              <Image
                                  src={watchvideotext}
                                  alt="devProgram"
                                  width={110}
                                  height={33}
                                  className="h-[33px] w-[110px] md:h-[62px] md:w-[206px]"
                              />
                          </section>
                      </div>
                  </div>
                  {/* web */}
                  <section className="hidden justify-between  md:flex">
                      <div className="relative">
                          {/* <section className="my-4">
                              <Image src={profiledev} alt="devProgram" width={452} height={110} />
                          </section> */}

                          <section className=" relative my-4 hidden w-[452px] rounded-[8px] border border-[#FCC229] p-3 md:block  ">
                              <Image
                                  src={olu}
                                  alt="devProgram"
                                  width={142}
                                  height={142}
                                  className="absolute top-[-3px] ml-[365px]"
                              />

                              <h3 className="text-[24px] font-[600] text-[#FCC229]">Lead Dev</h3>
                              <h3 className="text-[32px] font-[600] text-white">Olu Akinwande </h3>
                              <h3 className="text-[18px] font-[400] text-[#D0D0DD]">Founder Of Montech Studios INC</h3>
                          </section>
                          <section className="my-6 ">
                              <h3 className="text-[24px] font-[600] text-[#FCC229]">Mission</h3>
                              <h3 className="text-[32px] font-[600] text-white">Converting web2 devs into web3 devs</h3>
                          </section>
                          <Link href="https://cdn.forms-content.sg-form.com/b7c37a74-13e3-11ee-81a4-d673f9b62626" target='_blank' className="my-3 mt-8">
                              <Image src={enrollnowbutton} alt="devProgram" width={385} height={68} />
                          </Link>
                      </div>
                      <div className="pt-3">
                          <Image
                              src={devprogramvideo}
                              alt="devProgram"
                              width={348}
                              height={208}
                              className="h-[208px] w-[348px] md:h-[361px] md:w-[641px]"
                          />
                      </div>
                  </section>
                  {/* mobile */}
                  <section className="justify-center md:hidden">
                      <div className="">
                          <Image
                              src={devprogramvideo}
                              alt="devProgram"
                              width={348}
                              height={208}
                              className="mx-auto h-[208px] w-[348px] justify-center md:h-[361px] md:w-[641px]"
                          />
                      </div>
                      <section className="relative mx-auto my-4 w-[271px] justify-center rounded-[8px] border border-[#FCC229] p-3  md:block md:w-[452px]">
                          <Image
                              src={olu}
                              alt="devProgram"
                              width={100}
                              height={100}
                              className="absolute left-[-50px] top-[-3px] h-[100px] w-[100px]" // Adjusted the positioning
                          />
                          <div className="pl-6">
                              <h3 className="text-[13px] font-[600] text-[#FCC229] md:text-[24px]">Lead Dev</h3>
                              <h3 className="text-[17px] font-[600] text-white md:text-[32px]">Olu Akinwande</h3>
                              <h3 className="text-[11px] font-[400] text-[#D0D0DD] md:text-[18px]">
                                  Founder Of Montech Studios INC
                              </h3>
                          </div>
                      </section>

                      <section className="my-6 justify-center">
                          <h3 className="text-[14px] font-[600] text-[#FCC229] md:text-[24px]">Mission</h3>
                          <h3 className="text-[18px] font-[600] text-white md:text-[32px]">
                              Converting web2 devs into web3 devs
                          </h3>
                      </section>
                      <Link href="/dev-program" className="my-3 mt-8 justify-center">
                          <Image src={enrollnowbutton} alt="devProgram" width={385} height={68} />
                      </Link>
                  </section>
              </div>
          </div>
    </div>
  )
}

export default FirstView
