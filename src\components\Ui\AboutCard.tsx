import { cn } from '@/lib/cn'
import { ReactNode } from 'react'

interface Props {
    children: ReactNode
    className?: string
}

const AboutCard = ({ children, className }: Props) => {
    return (
        <div
            className={cn(
                'flex max-h-[309px] min-h-[309px] min-w-[309px] max-w-[309px] items-center justify-center rounded-lg border border-[#e1e1e1e1] bg-white shadow-[5px_5px_0px_0px_rgba(0,0,0,0.05)] max-sm:max-h-[156px] max-sm:min-h-[156px] max-sm:min-w-[156px] max-sm:max-w-[156px]',
                className
            )}>
            {children}
        </div>
    )
}

export default AboutCard
