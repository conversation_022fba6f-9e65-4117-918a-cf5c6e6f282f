import Link from 'next/link'
import { cn } from '@/lib/cn'
import ImageShortcut from '@/components/Ui/Image'
import { CustomSectionModel } from '@/types/CustomPageModel'
import MarkdownComponent from '@/components/Ui/MarkdownComponent'

const Section = ({ section }: { section: Partial<CustomSectionModel> }) => {
    return (
        <section className={cn('py-20', section.dark ? 'bg-[#101219] text-white' : 'bg-white text-black')}>
            <div
                className={cn(
                    'container mx-auto flex flex-wrap items-center gap-12 max-md:flex-col-reverse',
                    section.order! % 2 === 0 ? 'flex-row-reverse' : 'flex-row'
                )}>
                {section.image && (
                    <div className="flex-1">
                        <ImageShortcut
                            src={section.image}
                            width={496}
                            height={496}
                            alt="Section image"
                            className="rounded-xl md:h-full"
                        />
                    </div>
                )}

                <div
                    className={cn('flex-[2] space-y-7', !section.image && 'flex flex-col items-center justify-center')}>
                    <div className="flex max-w-[612px] flex-col justify-between gap-6">
                        <h3
                            className={cn(
                                'max-w-[608px] text-headline font-semibold',
                                !section.image && 'text-center'
                            )}>
                            {section.title}
                        </h3>
                        <p className={cn('text-callout font-medium text-blue', !section.image && 'text-center')}>
                            {section.description}
                        </p>
                        {section.body && (
                            <div
                                className={cn(
                                    'prose-invert prose-h3:text-b3 max-md:prose-h3:text-sub1',
                                    !section.image && 'text-center'
                                )}>
                                <MarkdownComponent text={section.body} />
                            </div>
                        )}
                    </div>
                    {section.ctaTitle && (
                        <div className={!section.image ? 'flex items-center justify-center' : ''}>
                            <Link href={section.ctaLink!} target="_blank">
                                <button className="select-none rounded-md bg-blue px-12 py-3 text-sub2 font-semibold text-white shadow-[0px_12px_30px_-6px_#0000008a] transition-colors duration-300 hover:bg-blue-primary">
                                    {section?.ctaTitle}
                                </button>
                            </Link>
                        </div>
                    )}
                </div>
            </div>
        </section>
    )
}

export default Section
