import Image from 'next/image'

const TESTIMONIALS = [
  {
    photo: '/testimonials/user1.jpg',
    name: '<PERSON>',
    role: 'Crypto Trader',
    quote: 'Paid off debts in 8 months thanks to CU courses. The Discord community is always there to help!',
    rating: 4.5,
  },
  {
    photo: '/testimonials/user2.jpg',
    name: '<PERSON>',
    role: 'Beginner Investor',
    quote: 'I never thought I could understand blockchain, but now I trade confidently. 5 stars!',
    rating: 5,
  },
  {
    photo: '/testimonials/user3.jpg',
    name: '<PERSON><PERSON>',
    role: 'Entrepreneur',
    quote: 'The mentorship and tools are world-class. My portfolio has grown 3x in a year.',
    rating: 5,
  },
]

function TestimonialCard({ testimonial }: { testimonial: typeof TESTIMONIALS[0] }) {
  return (
    <div className="bg-white rounded-xl shadow-md border border-gray-200 p-6 flex flex-col items-center max-w-xs">
      <Image src={testimonial.photo} alt={testimonial.name} width={64} height={64} className="w-16 h-16 rounded-full mb-4" />
      <h4 className="text-b1 font-semibold text-black">{testimonial.name}</h4>
      <span className="text-sub2 text-gray-600 mb-2">{testimonial.role}</span>
      <p className="text-b3 text-gray-800 text-center mb-4">“{testimonial.quote}” </p>
      <span className="text-yellow font-bold">{testimonial.rating}★</span>
    </div>
  )
}

export default function EnhancedTestimonials() {
  return (
    <section className="bg-gray-50 py-16">
      <h2 className="text-h2 font-bold text-center text-black mb-8">What Our Students Say</h2>
      <div className="flex flex-wrap justify-center gap-8">
        {TESTIMONIALS.map((t, i) => (
          <TestimonialCard key={i} testimonial={t} />
        ))}
      </div>
      <div className="flex justify-center items-center mt-8">
        <Image src="/trustpilot.svg" alt="Trustpilot" width={32} height={32} />
        <span className="ml-2 text-h3 font-bold text-green-dark">4.5★</span>
        <span className="ml-2 text-b3 text-gray-700">85% 5-star reviews</span>
      </div>
    </section>
  )
}