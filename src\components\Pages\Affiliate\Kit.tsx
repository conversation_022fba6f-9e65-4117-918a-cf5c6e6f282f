import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import NewsLetter from '@public/newsletter/hero.png'
import Link from 'next/link'
const Kit = () => {
    return (
        <section id='kit' className=" bg-kit-desktop w-full pb-16 pt-14 text-white max-md:pb-11 max-md:pt-[0px]">
            <div className="container mx-auto">
                <div className="relative flex flex-row-reverse items-center justify-between max-md:flex-col max-md:gap-3">
                    <ImageShortcut
                        src={NewsLetter}
                        height={340}
                        alt="newsletter"
                        className="absolute inset-0 -top-16 overflow-hidden max-md:static"
                    />
                    <div className="flex max-w-[500px] flex-1 flex-col gap-12 max-md:gap-6">
                        <div className="flex flex-col gap-2 max-md:items-center max-md:gap-3">
                            <h1 className="text-headline font-medium max-md:text-b3">Press kit</h1>
                            <p className="text-sub3 max-md:text-center max-md:text-cap2">
                                Download the full Crypto University media kit and get: banners, logos, promotion
                                materials and much more.
                            </p>
                        </div>
                        <div className=" w-full max-w-[261px] max-md:max-w-full">
                            <Link
                                href="https://drive.google.com/drive/folders/1F8XGDxpAc51elUGkf1ZH-xnIR7VbCENp?usp=sharing"
                                target="_blank">
                                <Button className="!bg-green-dark" rounded variant="primary">
                                    Download {'>'}
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Kit
