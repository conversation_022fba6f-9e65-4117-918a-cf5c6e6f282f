import { Country } from '@/types/CountryModel'
import Side from '@/components/Pages/Help/Side'
import Right from '@/components/Pages/Help/Right'
import ReachUsHidden from '@/components/Ui/ReachUsHidden'

export const metadata = {
    title: 'Help',
    description:
        "We're looking forward to your contact. Fill out your name, email, and inquiry below and a member of our team will get back with you ASAP.",
    keywords: ['Help', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

async function getCountryInfo(): Promise<Country[]> {
    const countries = await fetch('https://restcountries.com/v3.1/all', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        next: { revalidate: false },
    })

    const countriesData: Country[] = await countries.json()

    const countryInfo: Country[] = []

    for (const country of countriesData) {
        const { name, flags, idd } = country
        countryInfo.push({
            name: { common: name.common },
            flags: { svg: flags.svg },
            idd: idd,
        })
    }
    return countryInfo
}

const HelpPage = async () => {
    const countries: Country[] = await getCountryInfo()

    return (
        <section className="relative flex w-full items-center overflow-hidden max-md:flex-col max-md:items-start">
            <Side />

            <div className="h-fit grow max-md:min-w-full">
                <Right countries={countries} />
            </div>

            <ReachUsHidden />
        </section>
    )
}

export default HelpPage
