export interface BlogPost {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    author_id: number;
    image: string;
    categories: {
        post_id: number;
        category_id: number;
        category: {
            name: string;
            slug: string;
        };
    }[];
    tags: string[];
    author: {
        name: string;
        slug: string;
    };
    status: string;
    published_date: string;
    created_at: string;
    updated_at: string;
}