'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import useAuth from '@/hooks/useAuth'
import Label from '@/components/Ui/Label'
import { Fragment, useState } from 'react'
import Button from '@/components/Ui/Button'
import { Yup, useFormik } from '@/lib/formik'
import { Close } from '../../../../public/home'
import ImageShortcut from '@/components/Ui/Image'
import { Dialog, Transition } from '@/lib/headlessui'
import { Eye, EyeOff } from '../../../../public/global'
import successReset from 'public/forgot/successReset.png'
import ButtonSpinner from '@/components/Ui/buttonSpinner'

interface SearchParams {
    email: string
    token: string
}
const initialValues = {
    newPassword: '',
    confirmPassword: '',
}

const validationSchema = Yup.object({
    newPassword: Yup.string()
        .required('New Password is required')
        .min(8, 'New Password must be at least 8 characters')
        .matches(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/,
            'Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character'
        ),
    confirmPassword: Yup.string()
        .required('Confirm Password is required')
        .oneOf([Yup.ref('newPassword'), ''], 'Passwords must match'),
})

const ResetForm = ({ email, token }: SearchParams) => {
    const { isLoading, resetPassword } = useAuth()

    const [error, setError] = useState(false)
    const [isOpen, setIsOpen] = useState(false)
    const [errorMessage, setErrorMessage] = useState('')
    const [showPassword, setShowPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)

    const onSubmit = async (values: { newPassword: string; confirmPassword: string }) => {
        try {
            setError(false)
            setErrorMessage('')
            const res = await resetPassword({ email, password: values.newPassword, token })
            if (res?.data?.success) {
                setIsOpen(true)
            }
        } catch (error: any) {
            setError(true)
            setErrorMessage(error?.response?.data?.message)
            setIsOpen(true)
            formik.setErrors({ newPassword: error?.response?.data?.message })
        } finally {
            formik.resetForm()
        }
    }

    const formik = useFormik({
        initialValues,
        validationSchema,
        onSubmit,
    })

    return (
        <>
            <form onSubmit={formik.handleSubmit} className="flex flex-col space-y-6 max-xl:items-center">
                <div className="flex w-full flex-col gap-3">
                    <Label required uppercase={false}>
                        Password
                    </Label>
                    <div className="flex flex-col gap-2 max-md:w-full">
                        <div className="relative flex select-none items-center">
                            <input
                                type={showPassword ? 'text' : 'password'}
                                id="newPassword"
                                name="newPassword"
                                placeholder="new password"
                                className={cn(
                                    'w-full rounded-full border border-gray-700 px-4 py-[17.5px] focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue max-md:py-3',
                                    formik.errors.newPassword && 'border-red focus:ring-red'
                                )}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                value={formik.values.newPassword}
                            />
                            <div
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-4 z-10 cursor-pointer">
                                {showPassword ? <EyeOff /> : <Eye />}
                            </div>
                        </div>
                        {formik.touched && formik.errors.newPassword ? (
                            <div className="bg rounded-lg bg-red/20 p-3 text-red">
                                <p className="select-none text-cap1">{formik.errors.newPassword}</p>
                            </div>
                        ) : (
                            <div className="rounded-lg bg-transparent px-3">
                                <p className="select-none bg-transparent text-cap1 text-gray-900">
                                    Minimum 8 characters, mix of letters, numbers, and symbols
                                </p>
                            </div>
                        )}
                    </div>
                </div>

                <div className="flex w-full flex-col gap-3">
                    <Label required uppercase={false}>
                        Confirm Password
                    </Label>
                    <div className="relative flex items-center gap-2 max-md:w-full">
                        <input
                            type={showConfirmPassword ? 'text' : 'password'}
                            id="confirmPassword"
                            placeholder="confirm new password"
                            name="confirmPassword"
                            className={cn(
                                'w-full rounded-full border border-gray-700 px-4 py-[17.5px] placeholder:text-gray-700 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue max-md:py-3',
                                formik.errors.confirmPassword && 'border-red focus:ring-red'
                            )}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            value={formik.values.confirmPassword}
                        />
                        <div
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-4 z-10 cursor-pointer ">
                            {showConfirmPassword ? <EyeOff /> : <Eye />}
                        </div>
                    </div>
                    {formik.touched && formik.errors.confirmPassword ? (
                        <div className="bg rounded-lg bg-red/20 p-3 text-red">
                            <p className="select-none text-cap1">{formik.errors.confirmPassword}</p>
                        </div>
                    ) : (
                        <div className="rounded-lg bg-transparent px-3">
                            <p className="select-none bg-transparent text-cap1 text-gray-900">
                                Both Password must match
                            </p>
                        </div>
                    )}
                </div>

                <div className="max-xs:w-full max-xs:max-w-full max-xl:flex max-xl:w-[360px] max-xl:justify-center max-md:max-w-[310px] max-sm:px-4">
                    <Button
                        type="submit"
                        variant="primary"
                        rounded
                        disabled={
                            isLoading ||
                            !formik.isValid ||
                            formik.values.confirmPassword == '' ||
                            formik.values.newPassword == ''
                        }>
                        {isLoading && <ButtonSpinner />}Reset Password
                    </Button>
                </div>
            </form>
            <Transition show={isOpen || false} as={Fragment}>
                <Dialog
                    as="div"
                    onClose={() => setIsOpen(false)}
                    className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                    <div className="fixed inset-0 flex items-center justify-center p-4 max-md:block max-md:p-0">
                        <Transition.Child
                            as={Fragment}
                            enter="transition duration-100 ease-out"
                            enterFrom="transform scale-95 opacity-0"
                            enterTo="transform scale-100 opacity-100"
                            leave="transition duration-75 ease-out"
                            leaveFrom="transform scale-100 opacity-100"
                            leaveTo="transform scale-95 opacity-0">
                            <Dialog.Panel className="flex h-full items-center justify-center max-md:block">
                                <div className="relative flex flex-col gap-4 rounded-[1.5rem] bg-white p-10 pt-[3.25rem] font-sans max-md:h-full max-md:justify-center max-md:gap-0 max-md:rounded-none max-md:px-8 max-md:py-12">
                                    <button
                                        type="button"
                                        onClick={() => setIsOpen(false)}
                                        className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                        <Close />
                                    </button>

                                    {error ? (
                                        <svg
                                            width="82"
                                            height="82"
                                            viewBox="0 0 82 82"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="maxmd:h-[188px] self-center max-md:w-[241]">
                                            <path
                                                d="M41 82C63.6437 82 82 63.6437 82 41C82 18.3563 63.6437 0 41 0C18.3563 0 0 18.3563 0 41C0 63.6437 18.3563 82 41 82Z"
                                                fill="#ED1C26"
                                            />
                                            <path
                                                d="M64.01 63.9998C61.82 66.1998 58.26 66.1998 56.05 63.9998L41 48.9498L25.94 63.9998C23.75 66.1998 20.19 66.1998 18 63.9998C15.8 61.8098 15.8 58.2498 18 56.0598L33.05 40.9998L18 25.9398C15.8 23.7398 15.8 20.1798 18 17.9798C19.1 16.8898 20.54 16.3398 21.98 16.3398C23.42 16.3398 24.86 16.8898 25.95 17.9798L41 33.0498L56.06 17.9898C58.26 15.7998 61.82 15.7998 64.02 17.9898C65.11 19.0798 65.66 20.5198 65.66 21.9698C65.66 23.4098 65.11 24.8498 64.02 25.9498L48.95 40.9998L64.01 56.0598C66.2 58.2498 66.2 61.8098 64.01 63.9998Z"
                                                fill="white"
                                                stroke="#ED1C26"
                                                strokeWidth="3"
                                                strokeMiterlimit="10"
                                            />
                                        </svg>
                                    ) : (
                                        <ImageShortcut
                                            className="maxmd:h-[188px] self-center max-md:w-[241]"
                                            width={226}
                                            height={171}
                                            src={successReset}
                                            alt="Password resetted successfully"
                                        />
                                    )}

                                    {!error ? (
                                        <>
                                            <div className="flex max-w-[334px] flex-col items-center gap-4 self-center max-md:mt-6 max-md:max-w-full max-md:gap-1">
                                                <Dialog.Title className="text-center font-manrope text-headline font-medium max-md:text-b3">
                                                    Password Changed Successfully
                                                </Dialog.Title>
                                                <Dialog.Description className="text-center text-sub2 text-gray-900 max-md:max-w-[228px] max-md:text-black">
                                                    Your password has been successfully updated. Keep it safe!
                                                </Dialog.Description>
                                            </div>
                                            <div className="flex w-[373px] flex-col gap-5 max-md:mt-14 max-md:w-full">
                                                <Link aria-label="Go to login" href="/" className="w-full rounded-full">
                                                    <Button variant="primary" rounded>
                                                        Go To Login
                                                    </Button>
                                                </Link>
                                            </div>
                                        </>
                                    ) : (
                                        <div className="flex max-w-[334px] flex-col items-center gap-4 self-center max-md:mt-6 max-md:max-w-full max-md:gap-1">
                                            <Dialog.Title className="text-center font-manrope text-headline font-medium max-md:text-b3">
                                                Something went wrong
                                            </Dialog.Title>
                                            <Dialog.Description className="text-center text-sub2 text-gray-900 max-md:max-w-[228px] max-md:text-black">
                                                {errorMessage}
                                            </Dialog.Description>
                                        </div>
                                    )}
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
        </>
    )
}

export default ResetForm
