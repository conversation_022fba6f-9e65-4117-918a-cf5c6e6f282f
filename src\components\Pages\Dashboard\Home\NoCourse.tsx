import Link from 'next/link'
import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'

const NoCourse = () => {
    return (
        <div className="mb-14 flex w-full flex-col items-center justify-center gap-11 text-center">
            <ImageShortcut src={'/dashboard/empty.png'} width={385} height={315} priority alt="Empty Course Image" />
            <div className="flex max-w-[405px] flex-col items-center gap-4">
                <h1 className="text-headline font-medium">Start learning today!</h1>
                <p className="px-4 text-sub2 text-gray-700">
                    You don&apos;t have any courses yet, but we can help you find the perfect one.
                </p>
                <div>
                    <Link href="/courses">
                        <Button variant="primary" rounded>
                            Explore Courses
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default NoCourse
