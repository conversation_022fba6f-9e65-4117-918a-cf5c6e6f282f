import Link from 'next/link'
import Image from 'next/image'

const stats = [
  { icon: '/icons/users.svg', value: '30,000+', label: 'Students' },
  { icon: '/icons/clock.svg', value: '10,000+', label: 'Hours of Content' },
  { icon: '/icons/star.svg', value: '4.5★', label: 'Trustpilot' },
]

function Stat({ icon, value, label }: { icon: string; value: string; label: string }) {
  return (
    <div className="flex items-center gap-2">
      <Image src={icon} alt={label} width={24} height={24} className="inline" />
      <div>
        <div className="text-b1 font-bold text-black">{value}</div>
        <div className="text-sub2 text-gray-700">{label}</div>
      </div>
    </div>
  )
}

export default function EnhancedHero() {
  return (
    <section className="relative w-full overflow-hidden border-b border-gray-700 pb-20 text-black bg-gradient-to-b from-black via-[#081228] to-white">
      <div className="container mx-auto pt-24 flex flex-col md:flex-row items-center gap-12">
        <div className="flex-1 flex flex-col gap-8">
          <h1 className="text-title font-bold text-yellow drop-shadow-lg">
            Join 30,000+ Learners Succeeding in Crypto
          </h1>
          <p className="text-b1 text-white/90 max-w-xl">
            Master trading, investing, and blockchain with 10,000+ hours of expert content. Trusted worldwide.
            <span className="inline-flex items-center gap-2 ml-2">
              <Image src="/trustpilot.svg" alt="Trustpilot" width={20} height={20} />
              4.5★
            </span>
          </p>
          <div className="flex gap-4">
            <Link href="/register" className="w-fit">
              <button className="bg-yellow text-black rounded-full px-8 py-4 text-b1 font-bold shadow-lg hover:bg-yellow-400 transition">
                Start Now &rarr;
              </button>
            </Link>
            <Link href="/courses" className="w-fit">
              <button className="border-2 border-yellow text-yellow rounded-full px-8 py-4 text-b1 font-bold hover:bg-yellow hover:text-black transition">
                Browse Courses
              </button>
            </Link>
          </div>
          <div className="flex gap-8 mt-6 flex-wrap">
            {stats.map((stat) => (
              <Stat key={stat.label} {...stat} />
            ))}
          </div>
        </div>
        <div className="flex-1 flex justify-center">
          <Image
            src="/home/<USER>"
            alt="Crypto University Hero"
            width={480}
            height={480}
            className="rounded-xl shadow-xl"
            priority
          />
        </div>
      </div>
    </section>
  )
}