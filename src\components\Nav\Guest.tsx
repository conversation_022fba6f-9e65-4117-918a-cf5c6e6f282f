import { useState } from "react"
import Button from "../Ui/Button"
import ProfileDropdown from "./ProfileDropdown"
import { GetCurrentUserClient } from "@/lib/session"
import Link from "next/link"
import LoginModal from '@/components/Nav/LoginModal'

const Guest = () => {
    const user = GetCurrentUserClient();
    const [openLogin, setOpenLogin] = useState(false);
    const handleOpen = () => { }

    if (user) {
        if (user.provider !== "discord") {
            return (
                <ProfileDropdown user={user} />
            )
        }
    }

    return (
        <div className="flex items-center gap-4">
            <LoginModal isOpen={openLogin} handleOpen={handleOpen} setIsOpen={setOpenLogin} />
            <Link aria-label="Register link" href="/register" className="min-w-fit">
                <Button nav variant="primary" rounded>Get Started</Button>
            </Link>
        </div>
    )
}

export default Guest