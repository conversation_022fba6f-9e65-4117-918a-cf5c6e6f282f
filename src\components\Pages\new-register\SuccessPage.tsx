import Image from 'next/image';
import React from 'react';
import success from "@public/icons/astronaut-image.png"
import discordIcon from "@public/icons/discordicon.png"
import { MdOutlineKeyboardArrowRight } from 'react-icons/md';
import Link from 'next/link';

const SuccessPage = () => {
    return (
        <div className="mx-auto w-full max-w-md sm:rounded-[21.6px] bg-white p4 sm:p-8 pb-20 text-center sm:shadow-md">
            <Image
                src={success}
                alt="Success"
                className="mx-auto mb-6"
                width={200}
                height={151}
                onError={e => console.error('Image failed to load', e)} // Add error handling
            />{' '}
            <h1 className="mb-2 text-[22px] font-bold text-[#081228]">Your registration has been successful.</h1>
            <p className="mb-6 text-[14px] text-[#555555]">
                Your account has been created. Let’s explore and start learning!
            </p>
            <Link className="mb-4 flex w-full items-center justify-center rounded-full bg-[#5865F2] py-2 text-[15.36px] font-[600] text-white transition duration-300 hover:bg-[#4a5ac7]" href={'https://discord.gg/M9cwwCP49c'}>
                <Image
                    src={discordIcon}
                    alt="discordIcon"
                    className="mr-2" // Adjust class to align icon and text
                    width={20}
                    height={21}
                    onError={e => console.error('Image failed to load', e)}
                />
                Join Our Discord Server <MdOutlineKeyboardArrowRight className="h-5 w-5 font-[600]" />
            </Link>
            <div className="relative my-6">
                <hr className="border-[#fff]" />
                <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform bg-white px-[0.5px] text-[14px] font-[500] text-[#C1C1C1]">
                    OR
                </span>
            </div>
            <Link className="w-full rounded-full border border-[#5865F2] py-2 px-4 text-[15.36px] font-[600] text-[#5865F2] transition duration-300 hover:bg-[#f0f0f0]" href={'/'}>
                Continue to Home
            </Link>
        </div>
    )
};

export default SuccessPage;