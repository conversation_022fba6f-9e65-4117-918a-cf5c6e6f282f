'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useEffect, useRef, useState } from 'react'
import usePayment from '@/hooks/usePayment'
import { Yup, useFormik } from '@/lib/formik'
import { Country } from '@/types/CountryModel'
import useAffiliate from '@/hooks/useAffiliate'
import ImageShortcut from '@/components/Ui/Image'
import { capitalizeWords } from '@/lib/capitalize'
import { notFound, usePathname } from 'next/navigation'
import Illustration from 'public/checkout/illustration.png'
import ContainerForm from '@/components/Pages/Checkout/ContainerForm'
import { SusbcriptionPaymentMethod } from '@/components/Pages/Checkout/SusbcriptionPaymentMethod'
import { CheckoutButton } from '@/components/Pages/Checkout/CheckoutButton'
import { ContactInformation } from '@/components/Pages/Checkout/ContactInformation'
import { TextBox } from './TextBox'
import { ProfileBlack } from '@public/home'
import {
    PayPal<PERSON>Provider,
    PayPalButtons,
    FUNDING
} from '@paypal/react-paypal-js'
import { fetchNextOrderId } from '@/lib/fetchNextOrderId'

type Props = {
    course?: any
    bundle?: any
    mentorship?: any
    indicator?: any
    bootcamp?: any
    subscription?: any
    user: any
    countries: any
    disabled: boolean
}

export const SubscriptionCheckoutForm = ({ course, bundle, mentorship, indicator, bootcamp, subscription, user, countries, disabled }: Props) => {
    const [isAtlosAvailable, setIsAtlosAvailable] = useState(false);
    const [nextOrderId, setNextOrderId] = useState<string | null>(null);

    useEffect(() => {
        const checkAtlosAvailability = setInterval(() => {
            if (window.atlos) {
                setIsAtlosAvailable(true);
                clearInterval(checkAtlosAvailability);
            }
        }, 100);
        return () => clearInterval(checkAtlosAvailability);
    }, []);

    useEffect(() => {
        const getNextOrderId = async () => {
            const orderId = await fetchNextOrderId();
            setNextOrderId(orderId);
        };
        getNextOrderId();
    }, []);


    const store = useAffiliate()
    let referral: any = store?.referral
    let endDate: Date = store?.endDate

    const usernameRef = useRef<HTMLInputElement>(null)

    const pathname = usePathname()
    const { stripeSubscriptionPay, atlosPayCancelled, atlosPayCompleted, atlosPaySuccess, createPayPalOrder, onApprove, createPayPalSubscription, onApproveSubscription, atlosPay } = usePayment()
    // const currentPath = process.env.NEXT_PUBLIC_BASE_URL + pathname
    const currentPath = process.env.NEXT_PUBLIC_BASE_URL + '/discord-subscription/discord'

    let product: any
    if (course) product = { ...course, type: 'course' }
    else if (bundle) product = { ...bundle, type: 'bundle' }
    else if (mentorship) product = { ...mentorship, type: 'mentorship' }
    else if (indicator) product = { ...indicator, type: 'indicator' }
    else if (bootcamp) product = { ...bootcamp, type: 'bootcamp' }
    else if (subscription) product = { ...subscription, type: 'subscription' }
    else notFound()

    const [activeTabIndex, setActiveTabIndex] = useState('stripe')
    const [coupon, setCoupon] = useState('')

    const codes = countries.map((country: Country) => ({
        value: country.idd.root + country.idd.suffixes,
        label: (
            <span className="flex gap-1">
                <ImageShortcut src={country.flags.svg} height={32} alt={country.name.common} width={32} />
                {country.idd.suffixes?.length == 1 ? country.idd.root + country.idd.suffixes : country.idd.root}
            </span>
        ),
    }))

    // Prepare data for PayPal order creation
    const preparePayPalOrderData = () => {
        const firstName = formik.values.fullname.split(' ')[0];
        const lastName = formik.values.fullname.split(' ')[1] || '';
        const displayName = firstName;

        const values = {
            ...formik.values,
            first_name: firstName,
            last_name: lastName,
            display_name: displayName,
        };

        const orderData: any = {
            values,
            referral: referral === '' ? null : referral,
            endDate,
            product: product ? product.id : null,
            username: formik.values.username !== '' ? formik.values.username : null,
            productType: product ? product.type : null,
            path: currentPath,
            coupon: coupon === '' ? null : coupon,
            order_id: nextOrderId,
        };
        return orderData;
    };

    // Prepare data for PayPal order creation
    const preparePayPalSubscriptionData = (actions: { subscription: { create: (arg0: { plan_id: string }) => any } }) => {
        const firstName = formik.values.fullname.split(' ')[0];
        const lastName = formik.values.fullname.split(' ')[1] || '';
        const displayName = firstName;

        const values = {
            ...formik.values,
            first_name: firstName,
            last_name: lastName,
            display_name: displayName,
            order_id: nextOrderId,
        };

        const subscriptionData: any = {
            values,
            referral: referral === '' ? null : referral,
            endDate,
            product: product ? product.id : null,
            username: formik.values.username !== '' ? formik.values.username : null,
            productType: product ? product.type : null,
            plan_id: product.paypal_subscription_plan_id,
            actions,
            path: currentPath,
            coupon: coupon === '' ? null : coupon,
        };
        return subscriptionData;
    };

    const handlePayPalOrder = async (): Promise<string> => {
        const errors = await formik.validateForm();
        formik.setTouched({
            fullname: true,
            email: true,
            phoneSuffix: true,
            phonePreffix: true,
            country: true,
        });
        formik.setErrors(errors);

        if (Object.keys(errors).length === 0) {
            return createPayPalOrder(preparePayPalOrderData());
        } else {
            return "";
        }
    };

    const handlePayPalSubscription = async (data: any, actions: { subscription: { create: (arg0: { plan_id: string }) => any } }) => {
        const errors = await formik.validateForm();
        formik.setTouched({
            fullname: true,
            email: true,
            phoneSuffix: true,
            phonePreffix: true,
            country: true,
        });
        formik.setErrors(errors);

        if (Object.keys(errors).length === 0) {

            return await createPayPalSubscription(preparePayPalSubscriptionData(actions));
            // return actions.subscription.create({
            //     'plan_id': 'P-1NE02602UB435174PMZCQ44I'
            // });
        } else {
            return "";
        }
    };

    const formik = useFormik({
        initialValues: {
            fullname: user?.first_name ? user?.first_name + ' ' + user?.last_name : '',
            email: user?.email ?? '',
            phoneSuffix: user?.phone ?? '',
            phonePreffix: user?.country_code ?? codes[34].value ?? '',
            country: user?.country ?? '',
            username: '',
        },
        validationSchema: Yup.object({
            email: Yup.string().email('Invalid email address').required('Work email is required'),
            fullname: Yup.string().required('Full name is required'),
            phoneSuffix: Yup.string().required('Phone number is required'),
            phonePreffix: Yup.string(),
            country: Yup.string().required('Country is required'),
        }),

        onSubmit: async () => {
            if (activeTabIndex == 'stripe') {
                const values = {
                    ...formik.values,
                    first_name: formik.values.fullname.split(' ')[0],
                    last_name:
                        formik.values.fullname.split(' ')[1] !== undefined ? formik.values.fullname.split(' ')[1] : '',
                    display_name: formik.values.fullname.split(' ')[0],
                }
                await stripeSubscriptionPay({
                    values,
                    referral: referral === '' ? null : referral,
                    endDate,
                    product: product.id,
                    username: formik.values.username !== '' ? formik.values.username : null,
                    productType: product.type,
                    path: currentPath,
                    coupon: coupon === '' ? null : coupon,
                    order_id: nextOrderId,
                })
            } else if (activeTabIndex === 'coinbase') {
                if (isAtlosAvailable) {
                    inititateAtlosSubscription();
            
                    const currentDate = new Date().toISOString().split('T')[0]; // Generates date in 'YYYY-MM-DD' format
            
                    window.atlos.Pay({
                        merchantId: process.env.NEXT_PUBLIC_ATLOS_MERCHANT_ID as string,
                        orderId: nextOrderId,
                        orderCurrency: 'USD',
                        userEmail: formik.values.email,
                        userName: formik.values.fullname,
                        subscription: [
                            {
                                amount: product.price,
                                unit: product.subscription_interval === 'month' 
                                    ? window.atlos.RECURRENCE_MONTH 
                                    : window.atlos.RECURRENCE_YEAR,
                                interval: product.interval_count,
                                startInterval: 1,
                                startDate: currentDate, 
                                paymentCount: 100,
                            },
                        ],
                        onSuccess: handleAtlosSuccess,
                        onCanceled: handleAtlosCanceled,
                        onCompleted: handleAtlosCompleted,
                        theme: 'light',
                    });
                } else {
                    console.log('Atlos is not loaded yet');
                }
            } else {
                console.error('No payment method selected');
            }
            
        },
        initialErrors: {
            fullname: '',
            email: '',
            phoneSuffix: '',
            phonePreffix: '',
            country: '',
        },
    })

    const handleAtlosSuccess = async () => {
        const values = {
            ...formik.values,
            first_name: formik.values.fullname.split(' ')[0],
            last_name:
                formik.values.fullname.split(' ')[1] !== undefined ? formik.values.fullname.split(' ')[1] : '',
            display_name: formik.values.fullname.split(' ')[0],
        }
        await atlosPaySuccess({
            values,
            referral: referral === '' ? null : referral,
            endDate,
            product: product.id,
            username: formik.values.username !== '' ? formik.values.username : null,
            productType: product.type,
            path: currentPath,
            coupon: coupon === '' ? null : coupon,
            order_id: nextOrderId,
        })
    };

    const handleAtlosCompleted = async () => {
        const values = {
            ...formik.values,
            first_name: formik.values.fullname.split(' ')[0],
            last_name:
                formik.values.fullname.split(' ')[1] !== undefined ? formik.values.fullname.split(' ')[1] : '',
            display_name: formik.values.fullname.split(' ')[0],
        }
        await atlosPayCompleted({
            values,
            referral: referral === '' ? null : referral,
            endDate,
            product: product.id,
            username: formik.values.username !== '' ? formik.values.username : null,
            productType: product.type,
            path: currentPath,
            coupon: coupon === '' ? null : coupon,
            order_id: nextOrderId,
        })
    };

    const handleAtlosCanceled = async () => {
        const values = {
            ...formik.values,
            first_name: formik.values.fullname.split(' ')[0],
            last_name:
                formik.values.fullname.split(' ')[1] !== undefined ? formik.values.fullname.split(' ')[1] : '',
            display_name: formik.values.fullname.split(' ')[0],
        }
        await atlosPayCancelled({
            values,
            referral: referral === '' ? null : referral,
            endDate,
            product: product.id,
            username: formik.values.username !== '' ? formik.values.username : null,
            productType: product.type,
            path: currentPath,
            coupon: coupon === '' ? null : coupon,
            order_id: nextOrderId,
        })
    };

    const inititateAtlosSubscription = async () => {
        const values = {
            ...formik.values,
            first_name: formik.values.fullname.split(' ')[0],
            last_name:
                formik.values.fullname.split(' ')[1] !== undefined ? formik.values.fullname.split(' ')[1] : '',
            display_name: formik.values.fullname.split(' ')[0],
        }
        atlosPay({
            values,
            referral: referral === '' ? null : referral,
            endDate,
            product: product.id,
            username: formik.values.username !== '' ? formik.values.username : null,
            productType: product.type,
            path: currentPath,
            coupon: coupon === '' ? null : coupon,
            order_id: nextOrderId,
        })
    };

    return (
        <div className="flex flex-grow flex-wrap gap-16 pt-[1.875rem] font-sans text-black max-lg:flex-col-reverse max-lg:pt-0">
            {/* Checkout details */}
            <div className="flex flex-1 flex-col gap-16">
                <div className="flex flex-col">
                    <div className="flex items-start justify-between max-lg:hidden">
                        <h1 className="pt-11 font-manrope text-headline">Checkout</h1>
                        <ImageShortcut src={Illustration} width={177} height={177} alt={product.name} />
                    </div>

                    <div className="flex flex-col gap-8">
                        {indicator && (
                            <div className="flex flex-col gap-4">
                                <h4 className="font-manrope text-b3 font-semibold">Indicator</h4>
                                <TextBox
                                    required={true}
                                    formik={formik}
                                    Icon={<ProfileBlack />}
                                    innerRef={usernameRef}
                                    ValidationSchemaKeys={'username'}
                                    id="username"
                                    disabled={disabled ? true : false}
                                    label="Your Tradingview Username"
                                    value={formik.values.username}
                                    type="text"
                                    placeholder="e.g JohnDoe"
                                    onChange={formik.handleChange}
                                />
                            </div>
                        )}
                        <div className="space-y-4">
                            <h4 className="font-manrope text-b3 font-semibold">Contact Information</h4>
                            <ContactInformation formik={formik} countries={countries} user={user} />
                        </div>
                    </div>
                </div>
                <div className="flex flex-col gap-4">
                    <h4 className="font-manrope text-b3 font-semibold">Payment Method</h4>
                    <SusbcriptionPaymentMethod setActiveTabIndex={setActiveTabIndex} activeTabIndex={activeTabIndex} />
                </div>
                <div className="hidden flex-col gap-3 max-lg:flex">
                    {activeTabIndex == 'paypal' ? (
                        <>
                            {product.type === 'subscription' ? (

                                <PayPalScriptProvider
                                    options={{
                                        clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID as string,
                                        components: "buttons",
                                        intent: "subscription",
                                        vault: true,
                                    }}
                                >
                                    <PayPalButtons
                                        createSubscription={handlePayPalSubscription}
                                        style={{

                                            color: 'gold',
                                            shape: 'rect',
                                            label: "subscribe",
                                            height: 50,
                                        }}
                                        fundingSource={FUNDING.PAYPAL}
                                        onApprove={onApproveSubscription}
                                    />ß
                                </PayPalScriptProvider>


                            ) : (
                                <PayPalScriptProvider
                                    options={{
                                        clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID as string,
                                        currency: 'USD',
                                        intent: 'capture'
                                    }}
                                >
                                    <PayPalButtons
                                        style={{
                                            color: 'gold',
                                            shape: 'rect',
                                            label: 'pay',
                                            height: 50,
                                        }}
                                        fundingSource={FUNDING.PAYPAL}
                                        createOrder={handlePayPalOrder}
                                        onApprove={onApprove}
                                    />
                                </PayPalScriptProvider>
                            )}
                        </>


                    ) : (<CheckoutButton formik={formik} disabled={disabled} />)}
                    <p className="px-3 text-sub3 max-md:text-center">
                        Our{' '}
                        <Link
                            aria-label="Terms of Use"
                            href={'/terms-of-service'}
                            className="text-blue transition-[color] duration-150 hover:text-blue/70">
                            Terms of Use
                        </Link>{' '}
                        apply to your purchase. Click &apos;Buy&apos; to agree.
                    </p>
                </div>
            </div>

            {/* Checkout card */}
            <div className="flex min-w-[300px] flex-1 justify-end max-lg:w-full max-lg:justify-start">
                <div className="flex h-fit max-w-[531px] flex-col gap-8 border  border-gray-700 px-6 py-9 pb-4 max-lg:w-full max-lg:max-w-full max-lg:border-0 max-lg:px-0 max-lg:py-0">
                    <h3 className="font-manrope text-b3 font-medium">Summary</h3>
                    <div className="flex flex-col gap-6">
                        <div className={cn('flex justify-between', product.sale === 0 ? 'items-center' : 'items-end')}>
                            <div className="flex items-center gap-3">
                                <ImageShortcut
                                    src={product.image}
                                    width={80}
                                    height={80}
                                    className="rounded-lg"
                                    alt={product.name}
                                />
                                <h6 className="max-w-[260px] text-sub3 font-medium max-lg:max-w-full">
                                    {product.type === 'indicator'
                                        ? capitalizeWords(product.title)
                                        : capitalizeWords(product.name)}
                                </h6>
                            </div>
                            <div className={cn('text-sub2 font-semibold', product.sale === 0 ? 'pb-0' : 'pb-2')}>
                                {product.sale === 0 ? (
                                    <p>${product.priceWithoutTax}</p>
                                ) : (
                                    <div className="flex flex-col gap-[2px]">
                                        <p>${product.finalPrice - product.tax}</p>
                                        <p className="font-normal text-gray-700 line-through">
                                            ${product.priceWithoutTax}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                        <ContainerForm
                            setCheckoutCoupon={(x: string) => setCoupon(x)}
                            product={product}
                            user={user ? user.id : null}
                            disabled={disabled}
                        />
                    </div>
                    <div className="flex flex-col gap-3 max-lg:hidden">
                        {activeTabIndex == 'paypal' ? (
                            <>
                                {product.type === 'subscription' ? (
                                    <PayPalScriptProvider
                                        options={{
                                            clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID as string,
                                            components: "buttons",
                                            intent: "subscription",
                                            vault: true,
                                        }}
                                    >
                                        <PayPalButtons
                                            createSubscription={handlePayPalSubscription}
                                            style={{

                                                color: 'gold',
                                                shape: 'rect',
                                                label: "subscribe",
                                                height: 50,
                                            }}
                                            fundingSource={FUNDING.PAYPAL}
                                            onApprove={onApproveSubscription}
                                        />
                               
                                    </PayPalScriptProvider>
                                ) : (
                                    <PayPalScriptProvider
                                        options={{
                                            clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID as string,
                                            currency: 'USD',
                                            intent: 'capture'
                                        }}
                                    >
                                        <PayPalButtons
                                            style={{
                                                color: 'gold',
                                                shape: 'rect',
                                                label: 'pay',
                                                height: 50,
                                            }}
                                            fundingSource={FUNDING.PAYPAL}
                                            createOrder={handlePayPalOrder}
                                            onApprove={onApprove}
                                        />
                                    </PayPalScriptProvider>
                                )}

                            </>

                        ) : (<CheckoutButton formik={formik} disabled={disabled} />)}
                        <div className="space-y-3">
                            <p className="px-3 text-cap2 text-gray-900">
                                Our{' '}
                                <Link
                                    aria-label="Terms of Use"
                                    href={'/terms-of-service'}
                                    className="text-blue transition-[color] duration-150 hover:text-blue/70">
                                    Terms of Use
                                </Link>{' '}
                                apply to your purchase. Click &apos;Checkout&apos; to agree.
                            </p>
                            <p className="px-3 text-cap2 font-semibold text-gray-900">No refunds. No Returns</p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="hidden items-center justify-between max-lg:flex">
                <h1 className="pt-11 font-manrope text-b1 font-medium">Checkout</h1>
                <ImageShortcut
                    src={Illustration}
                    width={177}
                    height={177}
                    alt={product.name}
                    className="h-auto max-sm:w-36"
                />
            </div>
        </div>
    )
}

