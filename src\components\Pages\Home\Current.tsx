'use client'

import Link from 'next/link'
import Button from '@/components/Ui/Button'
import { usePathname } from 'next/navigation'

const Current = ({ user, slug, isEnrolled }: { user?: any; slug: string; isEnrolled: boolean }) => {
    const router = usePathname()

    if (!user || (user && !isEnrolled))
        return (
            <Link
                aria-label="Start Now"
                href={'/checkout/course/' + slug + '?previous=' + router}
                className="w-[200px] max-md:w-[188px]">
                <Button variant="primary" rounded>
                    Start Now &gt;
                </Button>
            </Link>
        )
    else if (user && isEnrolled) {
        return (
            <Link aria-label="Start Now" href={'/dashboard/' + slug} className="w-[200px] max-md:w-[188px]">
                <Button variant="primary" rounded>
                    Start Now &gt;
                </Button>
            </Link>
        )
    } else
        return (
            <Link
                aria-label="Start Now"
                href={'/checkout/course/' + slug + '?previous=' + router}
                className="w-[200px] max-md:w-[188px]">
                <Button variant="primary" rounded>
                    Start Now &gt;
                </Button>
            </Link>
        )
}

export default Current
