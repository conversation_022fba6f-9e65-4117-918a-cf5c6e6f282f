import Link from 'next/link'
import Button from './Button'
import { categories } from '@/content/faqs/faqs'
import DisclosurePage from '../Pages/Faqs/Disclosure'

const FaqsStatic = () => {
    return (
        <div className="container mx-auto w-full">
            <div className="flex justify-between py-11 max-lg:flex-col max-lg:justify-start max-lg:gap-5">
                <div className="flex max-w-[320px] flex-col items-start gap-6">
                    <h1 className="text-h3 font-medium max-lg:text-callout">FAQ&apos;S</h1>
                    <p className="text-sub1 max-lg:hidden">Have more questions? Feel free to get in touch with us!</p>
                    <div className="w-[185px] max-lg:hidden">
                        <Link href="/get-in-touch">
                            <Button rounded variant="primary">
                                Contact Us
                            </Button>
                        </Link>
                    </div>
                </div>
                <div className="max-w-[829px] flex-col gap-[70px] max-lg:flex">
                    <DisclosurePage notmain category={categories} />
                </div>
            </div>
        </div>
    )
}

export default FaqsStatic
