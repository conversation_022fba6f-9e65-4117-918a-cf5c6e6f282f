import ImageShortcut from "@/components/Ui/Image"
import Image from "next/image"
import Link from "next/link"
import { BTC, ETH, USDT, SHIBA, OTHER, OTHERCOIN, LuxuryAccomodation, DepthTraining, ExcitingActivities, DeliciousMeals, TexasBackground } from "@public/bootcamps/usa-grey-jabesi"
const USABootCamp = () => {
    const includes = [
        {
            icon: DepthTraining,
            title: "In-depth Training ",
            description: "Learn directly from <PERSON> Jabesi as he shares his expertise and insights into crypto trading. From technical analysis to risk management, you will gain the knowledge you need to thrive in the crypto market. "
        },
        {
            icon: LuxuryAccomodation,
            title: "Luxury Accommodation",
            description: "Relax and recharge in our premium accommodations located in the heart of Austin. Enjoy comfortable rooms and top-notch amenities for a truly unforgettable experience."
        },
        {
            icon: DeliciousMeals,
            title: "Delicious Meals",
            description: "Fuel your body and mind with gourmet meals prepared by our talented chefs. From breakfast to dinner, we will keep you energized throughout the day."
        },
        {
            icon: ExcitingActivities,
            title: "Exciting Activities",
            description: "Explore everything Austin has to offer with exclusive activities planned throughout the week. From networking events to sightseeing tours, you will have plenty of opportunities to connect with fellow traders and enjoy the city\'s vibrant culture."
        }

    ]

    return (
        <>
            <section style={{
                backgroundImage: `url('/bootcamps/usa-grey-jabesi/cement-background.jpg')`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
            }}>
                <div className="flex flex-col justify-between">
                    <section>
                        <div className="relative p-12"
                            style={{
                                height: '750px',
                            }}
                        >
                            <div className="absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden" style={{
                                backgroundImage: `url('/bootcamps/usa-grey-jabesi/hero-banner-desktop.jpg')`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center'
                            }}>
                                <div className="my-2 p-6 sm:p-12">
                                    <div>
                                        <h1 className="text-center text-b2 sm:text-h2 font-bold mt-2 text-white">US Crypto Retreats
                                        </h1>
                                        <div className="flex items-center justify-center">
                                            <svg className="mt-8 mx-2 mb-4" fill="#FCC229" height="30px" width="30px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 511.953 511.953" stroke="#FCC229">
                                                <svg className="mt-8 mx-2 mb-4" fill="#FCC229" height="30px" width="30px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 511.953 511.953" stroke="#FCC229">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />
                                                    <g id="SVGRepo_iconCarrier">
                                                        <g transform="translate(-1)">
                                                            <g>
                                                                <g>
                                                                    <path d="M256.995,149.287c-11.776,0-21.333,9.579-21.333,21.333c0,11.755,9.557,21.333,21.333,21.333s21.333-9.579,21.333-21.333 C278.328,158.865,268.771,149.287,256.995,149.287z" />
                                                                    <path d="M365.518,38.887C325.987,6.311,274.04-6.639,223.011,3.239C154.147,16.615,100.152,72.273,88.718,141.735 c-6.784,41.003,0.725,81.216,21.696,116.267l8.704,14.528c27.861,46.443,56.64,94.485,79.701,143.893l38.848,83.221 c3.499,7.509,11.029,12.309,19.328,12.309s15.829-4.8,19.328-12.309l34.965-74.923c23.317-49.984,52.096-98.688,79.957-145.792 l12.971-22.016c15.339-26.091,23.445-55.936,23.445-86.293C427.662,119.484,405.006,71.463,365.518,38.887z M256.995,234.62 c-35.285,0-64-28.715-64-64s28.715-64,64-64s64,28.715,64,64S292.28,234.62,256.995,234.62z" />
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </g>
                                                </svg>
                                            </svg>
                                            <h1 className="text-center text-b2 sm:text-b1 font-bold mt-6 text-[#FCC229]">Austin, Texas!</h1>
                                        </div>
                                        <div className="flex justify-center rounded mt-6 p-2">
                                            <iframe className="rounded" width="560" height="300" src="https://www.youtube.com/embed/AgEs0TH2dQ8?si=2_sun8huXVN_WhlT" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                                        </div>
                                        <h1 className="text-sub1 text-white sm:text-callout text-center font-bold my-2 sm:my-10">In-depth Training | Luxury Accommodation |  Delicious Meals | Exciting Activities</h1>
                                        <div className="flex flex-col items-center">
                                            <Link
                                                aria-label="View products"
                                                href={"/checkout/bootcamp/usa-texas-crypto-trading-bootcamp"}
                                                className="bg-[#FCC229]  hover:bg-[#FCC229]  active:bg-[#FCC229] rounded-full text-sub1 sm:text-sub2 font-semibold py-5 px-8 sm:px-12 mt-6 transition-colors duration-150"
                                            >
                                                Register Now {'>'}
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="block sm:hidden absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden" style={{
                                backgroundImage: `url('/bootcamps/usa-grey-jabesi/hero-banner-mobile.jpg')`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center'
                            }}>
                                <div className="my-2 p-6 sm:p-12">
                                    <div>
                                        <h1 className="text-center text-b2 sm:text-h2 font-bold mt-2 text-white">Crypto Trading Bootcamp
                                        </h1>
                                        <div className="flex items-center justify-center">
                                            <svg className="mt-8 mx-2 mb-4" fill="#FCC229" height="30px" width="30px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 511.953 511.953" stroke="#FCC229">
                                                <svg className="mt-8 mx-2 mb-4" fill="#FCC229" height="30px" width="30px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 511.953 511.953" stroke="#FCC229">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />
                                                    <g id="SVGRepo_iconCarrier">
                                                        <g transform="translate(-1)">
                                                            <g>
                                                                <g>
                                                                    <path d="M256.995,149.287c-11.776,0-21.333,9.579-21.333,21.333c0,11.755,9.557,21.333,21.333,21.333s21.333-9.579,21.333-21.333 C278.328,158.865,268.771,149.287,256.995,149.287z" />
                                                                    <path d="M365.518,38.887C325.987,6.311,274.04-6.639,223.011,3.239C154.147,16.615,100.152,72.273,88.718,141.735 c-6.784,41.003,0.725,81.216,21.696,116.267l8.704,14.528c27.861,46.443,56.64,94.485,79.701,143.893l38.848,83.221 c3.499,7.509,11.029,12.309,19.328,12.309s15.829-4.8,19.328-12.309l34.965-74.923c23.317-49.984,52.096-98.688,79.957-145.792 l12.971-22.016c15.339-26.091,23.445-55.936,23.445-86.293C427.662,119.484,405.006,71.463,365.518,38.887z M256.995,234.62 c-35.285,0-64-28.715-64-64s28.715-64,64-64s64,28.715,64,64S292.28,234.62,256.995,234.62z" />
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </g>
                                                </svg>
                                            </svg>
                                            <h1 className="text-center text-b2 sm:text-b1 font-bold mt-6 text-[#FCC229]">Austin, Texas!</h1>
                                        </div>
                                        <div className="flex justify-center rounded mt-6 p-2">
                                            <iframe className="rounded" width="560" height="300" src="https://www.youtube.com/embed/h3uJRK6rpi0?si=JZZwzhZ023yQC1MJ" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                                        </div>
                                        <h1 className="text-sub1 text-white sm:text-callout text-center font-bold my-2 sm:my-10">In-depth Training | Luxury Accommodation |  Delicious Meals | Exciting Activities</h1>
                                        <div className="flex flex-col items-center">
                                            <Link
                                                aria-label="View products"
                                                href={"/checkout/bootcamp/usa-texas-crypto-trading-bootcamp"}
                                                className="bg-[#FCC229]  hover:bg-[#FCC229]  active:bg-[#FCC229] rounded-full text-sub1 sm:text-sub2 font-semibold py-5 px-8 sm:px-12 mt-6 transition-colors duration-150"
                                            >
                                                Register Now {'>'}
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section className="relative" style={{
                        backgroundImage: `url('/bootcamps/usa-grey-jabesi/austin-texas.jpg')`,
                        height: '700px',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat'
                    }}>
                        {/* Info */}
                        <div className="absolute bottom-24 sm:bottom-12 left-6 bg-gray-400 w-auto sm:w-2/4 h-80 sm:h-56 p-10 mt-11 mx-10 rounded-lg">
                            <h1 className="text-b3 sm:text-headline font-bold">Austin, Texas!</h1>
                            <p className="my-10 text-md">Are you ready to dive into the world of crypto trading and unlock the secrets of successful trading strategies? Join us for a week-long intensive bootcamp led by the renowned crypto expert!</p>
                        </div>
                    </section>
                </div>

                {/* <section>
                    <div className="relative p-12"
                     style={{
                        height: '750px',
                        backgroundImage: `url('/bootcamps/usa-grey-jabesi/hero-banner-desktop.jpg')`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                    }}
                    >
                        <div className="absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden bg-fixed">
                            <div className="my-2 p-6 sm:p-12">
                                <div>
                                    <h1 className="text-center text-b2 sm:text-h2 font-bold mt-2 text-white">Crypto Trading Bootcamp
                                    </h1>
                                    <div className="flex items-center justify-center">
                                        <svg className="mt-8 mx-2 mb-4" fill="#FCC229" height="30px" width="30px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 511.953 511.953" stroke="#FCC229">
                                            <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />
                                            <g id="SVGRepo_iconCarrier">
                                                <g transform="translate(-1)">
                                                    <g>
                                                        <g>
                                                            <path d="M256.995,149.287c-11.776,0-21.333,9.579-21.333,21.333c0,11.755,9.557,21.333,21.333,21.333s21.333-9.579,21.333-21.333 C278.328,158.865,268.771,149.287,256.995,149.287z" />
                                                            <path d="M365.518,38.887C325.987,6.311,274.04-6.639,223.011,3.239C154.147,16.615,100.152,72.273,88.718,141.735 c-6.784,41.003,0.725,81.216,21.696,116.267l8.704,14.528c27.861,46.443,56.64,94.485,79.701,143.893l38.848,83.221 c3.499,7.509,11.029,12.309,19.328,12.309s15.829-4.8,19.328-12.309l34.965-74.923c23.317-49.984,52.096-98.688,79.957-145.792 l12.971-22.016c15.339-26.091,23.445-55.936,23.445-86.293C427.662,119.484,405.006,71.463,365.518,38.887z M256.995,234.62 c-35.285,0-64-28.715-64-64s28.715-64,64-64s64,28.715,64,64S292.28,234.62,256.995,234.62z" />
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <h1 className="text-center text-b2 sm:text-b1 font-bold mt-6 text-[#FCC229]">Austin, Texas!</h1>
                                    </div>
                                    <div className="flex justify-center rounded mt-6 p-2">
                                        <iframe className="rounded" width="560" height="300" src="https://www.youtube.com/embed/h3uJRK6rpi0?si=JZZwzhZ023yQC1MJ" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                                    </div>

                                    <h1 className="text-sub1 text-white sm:text-callout text-center font-bold my-2 sm:my-10">In-depth Training | Luxury Accommodation |  Delicious Meals | Exciting Activities</h1>
                                    <div className="flex flex-col items-center">
                                        <Link
                                            aria-label="View products"
                                            href={"/checkout/bootcamp/usa-texas-crypto-trading-bootcamp"}
                                            className="bg-[#FCC229]  hover:bg-[#FCC229]  active:bg-[#FCC229] rounded-full text-sub1 sm:text-sub2 font-semibold py-5 px-8 sm:px-12 mt-6 transition-colors duration-150"
                                        >
                                            Register Now {'>'}
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section className="relative" style={{
                    backgroundImage: `url('/bootcamps/usa-grey-jabesi/texas.jpg')`,
                    height: '700px',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat'
                }}>
                    <div className="absolute bottom-24 sm:bottom-12 left-6 bg-gray-400 w-auto sm:w-2/4 h-80 sm:h-56 p-10 mt-11 mx-10 rounded-lg">
                        <h1 className="text-b3 sm:text-headline font-bold">Austin, Texas!</h1>
                        <p className="my-10 text-md">Are you ready to dive into the world of crypto trading and unlock the secrets of successful trading strategies? Join us for a week-long intensive bootcamp led by the renowned crypto expert!</p>
                    </div>
                </section> */}

                <section className="px-12 py-2 sm:py-12">
                    <div>
                        <h1 className="text-b3 sm:text-headline text-center font-bold my-8 sm:my-16">Here is what you can expect</h1>
                        {/* On Desktop */}
                        <div className="hidden lg:block">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 ">
                                {includes.map(item => (
                                    <div key={item.title} className="p-4 sm:flex sm:items-center">
                                        <div className="mr-6">
                                            <ImageShortcut
                                                src={item.icon}
                                                width={200}
                                                height={200}
                                                alt="thumbnail"
                                                className="object-cover h-200 w-200"
                                                priority={true}
                                            />
                                        </div>
                                        <div>
                                            <h2 className="text-b3 my-4 font-semibold">{item.title}</h2>
                                            <p>{item.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                        {/* On Mobile */}
                        <div className="block sm:hidden">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 ">
                                {includes.map(item => (
                                    <div key={item.title} className="p-4 ">
                                        <div className="mr-6 flex flex-col items-center">
                                            <ImageShortcut
                                                src={item.icon}
                                                width={100}
                                                height={100}
                                                alt="thumbnail"
                                                className="object-cover  h-100 w-100"
                                                priority={true}
                                            />
                                        </div>
                                        <div>
                                            <h2 className="text-b3 my-4 text-center font-semibold">{item.title}</h2>
                                            <p>{item.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                <section className="px-12 py-2 sm:py-6">
                    <h1 className="text-b3 sm:text-headline text-center font-bold my-2 sm:my-16">Limited spots available, so reserve your place now!</h1>
                    <div className="flex flex-col items-center">
                        <Link
                            aria-label="View products"
                            href={"/checkout/bootcamp/usa-texas-crypto-trading-bootcamp"}
                            className="bg-blue mb-4  hover:bg-[#5462dd]  text-white active:bg-blue rounded-full text-sub1 sm:text-sub2 font-semibold py-5 px-8 sm:px-12 mt-6 transition-colors duration-150"
                        >
                            Register Now {'>'}
                        </Link>
                    </div>
                </section>
            </section>

        </>
    )
}

export default USABootCamp
