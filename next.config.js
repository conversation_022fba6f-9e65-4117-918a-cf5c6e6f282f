/** @type {import('next').NextConfig} */
const nextConfig = {
    env: {
        API_URL: process.env.API_URL,
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
        STRIPE_PUBLIC_KEY: process.env.STRIPE_PUBLIC_KEY,
        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
        SANITY_PROJECT_ID: process.env.SANITY_PROJECT_ID,
        BLOG_API: process.env.BLOG_API,
        WHOP_PRODUCT_API: process.env.WHOP_PRODUCT_API,
        WHOP_BEARER: process.env.WHOP_BEARER,
    },
    images: {
        domains: [
            'localhost',
            'res.cloudinary.com',
            'i.ibb.co',
            'i0.wp.com',
            'lh3.googleusercontent.com',
            'cryptouniversity.network',
            'res.cloudinary.com',
            'cryptoublog.greyjabesi.com',
            'cdn.pixabay.com'
        ],
    },
    async redirects() {
        return [
          {
            source: '/exclusive-crypto-discord-membership',
            destination: '/memberships',
            permanent: true,
          }
          ,
          {
            source: '/alpha-group',
            destination: '/memberships',
            permanent: true,
          }
        ]
      },
}

module.exports = nextConfig
