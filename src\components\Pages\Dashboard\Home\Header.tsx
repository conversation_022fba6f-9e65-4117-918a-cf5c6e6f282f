import { cn } from '@/lib/cn'
import Link from 'next/link'
import { user } from '@/types/UserModel'
interface props {
    user: Partial<user>
    filter: string
}

const Header = ({ user, filter }: props) => {
    const filters = [
        {
            name: 'All Course',
            filter: 'all',
            pageNumbers: 1,
        },
        {
            name: 'In Progress',
            filter: 'inProgress',
            pageNumbers: 1,
        },
        {
            name: 'Finished',
            filter: 'finished',
            pageNumbers: 1,
        },
    ]
    return (
        <div className="flex w-full items-center justify-between border-b border-gray-700 px-8 py-[1.6875rem] max-md:flex-col max-md:gap-4 max-md:px-5">
            <p className="flex flex-col flex-wrap gap-4 font-manrope max-md:flex-row max-md:justify-between">
                <span className="text-sub2 font-normal capitalize text-gray-900">Hi, {user.display_name} 👋</span>
                <span className="text-sub2 font-semibold">Learn. Trade. Invest. Play</span>
            </p>
            <div className="hide-scroll-bar flex gap-4 overflow-x-scroll max-md:max-w-full">
                {filters.map((item, index) => (
                    <Link
                        aria-label={item.name}
                        key={index}
                        href={'/dashboard?filter=' + item.filter + '&pageNumber=' + item.pageNumbers}
                        className={cn(
                            'w-max whitespace-nowrap rounded-lg px-6 py-[0.844rem]',
                            item.filter == filter ? 'bg-blue text-white' : 'bg-gray-300 text-black'
                        )}>
                        {item.name}
                    </Link>
                ))}
            </div>
        </div>
    )
}

export default Header
