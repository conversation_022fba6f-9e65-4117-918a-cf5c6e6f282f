"use client";
import { useState } from "react";
import useAuth from "@/hooks/useAuth";
import Label from "@/components/Ui/Label";

export const VerifyEmail = ({ user }: { user: any }) => {
  const { verifyEmail } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    await verifyEmail(user.email);
    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Label required uppercase={false}>
        Verify Email
      </Label>
      <button
        className="border border-violet-800"
        type="submit"
        disabled={isLoading}
      >
        {isLoading ? "Verifying" : "Verify"}
      </button>
    </form>
  );
};
