'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import Summary from './Summary'
import CartCard from './CartCard'
import useCart from '@/hooks/useCart'
import Button from '@/components/Ui/Button'
import { useState, useEffect } from 'react'
import ImageShortcut from '@/components/Ui/Image'

const Cart = () => {
    const cart = useCart()
    const [isMounted, setIsMounted] = useState(false)
    const [selectAllChecked, setSelectAllChecked] = useState(false)
    const [selectedItems, setSelectedItems] = useState<number[]>([])
    const [totalPrice, setTotalPrice] = useState(0)

    const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
        const isChecked = event.target.checked
        setSelectAllChecked(isChecked)

        if (isChecked) {
            const allIndices = cart.products.map(product => product.id)
            const calculatedTotalPrice = cart.products.reduce((total, item) => total + item.priceWithoutTax, 0)
            setTotalPrice(calculatedTotalPrice)
            setSelectedItems(allIndices)
        } else {
            setSelectedItems([])
            setTotalPrice(0)
        }
    }

    const handleCheckboxChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
        const isChecked = event.target.checked
        let updatedSelectedItems = [...selectedItems]

        if (isChecked) {
            updatedSelectedItems.push(index)
            if (cart && cart.products) {
                const item = cart.products.find(item => item.id === index)
                if (item) {
                    setTotalPrice(totalPrice + item.priceWithoutTax)
                }
            }
        } else {
            updatedSelectedItems = updatedSelectedItems.filter(item => item !== index)
            if (cart && cart.products) {
                const item = cart.products.find(item => item.id === index)
                if (item) {
                    setTotalPrice(totalPrice - item.priceWithoutTax)
                }
            }
        }
        if (updatedSelectedItems.length === cart.products.length) setSelectAllChecked(true)
        else setSelectAllChecked(false)

        setSelectedItems(updatedSelectedItems)
    }

    useEffect(() => {
        setIsMounted(true)
    }, [])
    if (!isMounted) return null

    return (
        <section id="cart" className="container mx-auto min-h-[800px] py-14 max-md:py-[40px] ">
            <div className="flex  w-full items-start justify-between gap-10 max-md:flex-col max-md:gap-14">
                <div className={cn('flex w-full flex-col gap-10', cart.products.length == 0 ? 'w-full' : '')}>
                    {cart.products.length > 0 ? (
                        <>
                            <h1 className="flex items-center gap-2 text-headline max-md:text-callout">
                                My Cart
                                <span className="borer-gray-700 flex h-12 w-12 items-center justify-center rounded-full border text-sub3 font-semibold">
                                    {cart.products.length}
                                </span>
                            </h1>
                            <div className="grid grid-cols-1 gap-8 ">
                                <div className="col-span-1 flex items-center gap-2 accent-blue ">
                                    <input
                                        id="select-all-checkbox"
                                        type="checkbox"
                                        value=""
                                        className="h-4 w-4 rounded-xl "
                                        checked={selectAllChecked}
                                        onChange={handleSelectAll}
                                    />
                                    <label
                                        htmlFor="select-all-checkbox"
                                        className="text-sub3 font-medium text-gray-900">
                                        Select All
                                    </label>
                                </div>
                                {cart.products.map((item, index) => (
                                    <div className="col-span-1 flex items-center gap-5 accent-blue " key={index}>
                                        <input
                                            id={`default-checkbox-${index}`}
                                            type="checkbox"
                                            value=""
                                            className="h-4 w-4 rounded "
                                            checked={selectedItems.includes(item.id)}
                                            onChange={event => handleCheckboxChange(item.id, event)}
                                        />
                                        <label className="w-full" htmlFor={`default-checkbox-${index}`}>
                                            <CartCard
                                                selectedItems={selectedItems}
                                                setSelectedItems={setSelectedItems}
                                                setTotalPrice={setTotalPrice}
                                                key={index}
                                                product={item}
                                            />
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </>
                    ) : (
                        <div className="flex flex-col items-center justify-center gap-5">
                            <ImageShortcut src={'/cart/empty.png'} alt="empty" width={430} height={370} />
                            <h1 className="text-headline">Oops! Your Cart is Empty</h1>
                            <div>
                                <Link aria-label="Explore Our Products" href={'/products'}>
                                    <Button className="!px-11" variant="primary" rounded>
                                        Explore Our Products
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    )}
                </div>
                {cart.products.length > 0 && <Summary length={selectedItems.length} total={totalPrice} />}
            </div>
        </section>
    )
}

export default Cart
