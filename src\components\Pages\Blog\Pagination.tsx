'use client'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import ReactPaginate from 'react-paginate'
import { usePathname, useRouter } from 'next/navigation'

const Labels = ({ label }: { label: 'next' | 'previous' }) => (
    <div className={cn('flex items-center gap-2 font-medium text-[#667085]', label == 'next' && 'flex-row-reverse')}>
        {label == 'next' ? (
            <span className="rotate-180">
                <Icon />
            </span>
        ) : (
            <Icon />
        )}
        <span className="capitalize">{label}</span>
    </div>
)
const Icon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
        <path
            d="M15.8327 10.5003H4.16602M4.16602 10.5003L9.99935 16.3337M4.16602 10.5003L9.99935 4.66699"
            stroke="#667085"
            strokeWidth="1.67"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)

const Pagination = ({ page, count }: { page: string | string[] | undefined; count: number }) => {
    const router = useRouter()
    const pathname = usePathname()
    const [pageNumber, setPageNumber] = useState(0)
    const postsPerPage = 9
    const pageCount = Math.ceil(count / postsPerPage)

    const handlePageChange = ({ selected }: { selected: number }) => {
        setPageNumber(selected)
        if (selected + 1 !== 1) router.push(pathname + '?page=' + (selected + 1))
        else router.push(pathname)
    }

    return (
        <section className="overflow-hidden border-t border-[#EAECF0] pt-5">
            <ReactPaginate
                previousLabel={<Labels label="previous" />}
                nextLabel={<Labels label="next" />}
                breakLabel={'...'}
                pageCount={pageCount}
                marginPagesDisplayed={1}
                pageRangeDisplayed={5}
                onPageChange={handlePageChange}
                forcePage={
                    page !== undefined
                        ? parseInt(page.toString()) <= 0
                            ? 0
                            : parseInt(page.toString()) - 1
                        : pageNumber
                }
                previousClassName={pageNumber === 0 ? '!cursor-not-allowed' : ''}
                nextClassName={pageNumber === pageCount - 1 ? 'disabled !cursor-not-allowed' : ''}
                containerClassName={'flex select-none justify-center flex-wrap items-center'}
                pageClassName={'rounded-lg mx-1'}
                activeClassName={'bg-gray-300'}
                activeLinkClassName="text-black"
                pageLinkClassName={
                    'font-medium text-[14px] leading-[20px] w-[40px] h-[40px] flex items-center justify-center text-[#667085] bg-transparent hover:bg-gray-300 rounded-lg'
                }
                previousLinkClassName={'bg-gray-300'}
                nextLinkClassName={'bg-gray-300'}
                breakLinkClassName={''}
            />
        </section>
    )
}

export default Pagination
