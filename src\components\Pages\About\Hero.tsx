import { Counters } from './Counters'
import HeroImage from '@public/about/hero.png'
import ImageShortcut from '@/components/Ui/Image'
import { Rating, RatingMobile, TrustPilot, TrustPilotMobile } from '@public/home'

const Hero = () => {
    return (
        <section id='hero' className="relative bg-[#03060E] text-[#F3F3F3]">
            <div className="container relative mx-auto flex justify-between overflow-hidden pb-12 pt-16 max-md:pt-[228px]">
                <div className="z-30 max-w-[655px] space-y-[54px]">
                    <div className="space-y-6">
                        <h1 className="text-h3 font-medium max-md:text-center max-md:text-headline">
                            Learn. Trade. Invest. Play
                        </h1>
                        <div className="space-y-3 text-sub3 max-md:text-sub4">
                            <p className="max-md:text-center">
                                Crypto University is the world’s #1 Cryptocurrency education platform that offers
                                various courses on Cryptocurrency and Blockchain.
                            </p>
                            <p className="max-md:text-center">
                                We have a total community of over <b className="font-semibold">50K+</b> Crypto
                                enthusiasts from around the world.
                            </p>
                        </div>
                    </div>

                    <Counters />

                    <div className="flex items-center gap-6">
                        <div className="h-[0.5px] w-[77px] bg-[#676A73] max-md:hidden" />
                        <div className="flex items-center gap-3">
                            <div className="flex flex-col items-center max-md:items-start">
                                <span className="text-cap1 max-md:text-cap4">Rated</span>
                                <p className="text-b1 font-semibold max-md:text-sub1">4.5</p>
                                <span className="text-cap1 max-md:text-cap4">Out of 5</span>
                            </div>
                            <div className="hidden flex-col justify-end gap-1 py-[2px] max-md:flex">
                                <TrustPilotMobile fill="white" />
                                <RatingMobile />
                            </div>
                            <div className="flex flex-col justify-end gap-3 py-[2px] max-md:hidden">
                                <TrustPilot fill="white" />
                                <Rating />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="absolute right-0 top-0 z-10">
                    <ImageShortcut src={HeroImage} alt="Hero Image" />
                    <div className="md:hidden bg-gradient-to-t from-[#060E1E] to-[#060E1E30]  w-full h-[375px] absolute top-0" />
                </div>
            </div>

            {/* Overlay */}
            <div className="absolute inset-0 z-20 bg-gradient-to-t from-[#060E1E] to-[#060E1E30]" />
        </section>
    )
}

export default Hero
