const Burger = ({ isOpen, handleOpen }: { isOpen: boolean, handleOpen: any }) => {
    return (
        <div id="menu-icon" className="menu-icon">
            <input className="menu-icon__cheeckbox" aria-label="menu-icon" checked={isOpen} onChange={handleOpen} type="checkbox" />
            <div>
                <span></span>
                <span></span>
            </div>
        </div>
    )
}

export default Burger