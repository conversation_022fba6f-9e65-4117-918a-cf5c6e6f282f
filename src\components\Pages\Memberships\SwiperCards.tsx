'use client'
import 'swiper/css'
import 'swiper/css/bundle'
import { Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'
import { MembershipModel } from '@/types/MembershipModel'
import Card from './Card'

const SwiperCards = ({ data }: { data: MembershipModel[] }) => {
    return (
        <div className="w-full md:hidden">
            <Swiper
                slidesPerView={1.7}
                centeredSlides={true}
                spaceBetween={20}
                grabCursor={true}
                pagination={{
                    clickable: true,
                }}
                modules={[Pagination]}
                breakpoints={{
                    320: {
                        slidesPerView: 1.2,
                        spaceBetween: 20,
                    },
                    480: {
                        slidesPerView: 1.7,
                        spaceBetween: 30,
                    },
                }}
                className="mySwiper"
                initialSlide={1}>
                {data.map((item, index) => (
                    <SwiperSlide key={index} className="py-10 max-md:py-16">
                        <Card item={item} index={index} />
                    </SwiperSlide>
                ))} 
            </Swiper>
        </div>
    )
}

export default SwiperCards
