import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import AffiliateDisclosure from '../Faqs/AffiliateDisclosure'
import { AffiliateFaq } from '@/content/faqs/faqs'

const AffiliateFaqs = () => {
    return (
        <section id='faqs' className="container mx-auto w-full">
            <div className="flex justify-between py-11 max-lg:flex-col max-lg:justify-start max-lg:gap-5">
                <div className="flex max-w-[320px] flex-col items-start gap-6">
                    <h1 className="text-h3 font-medium max-lg:text-callout">FAQ&apos;S</h1>
                    <p className="text-sub1 max-lg:hidden">Have more questions? Feel free to get in touch with us!</p>
                    <div className="w-[185px] max-lg:hidden">
                        <Link href="/get-in-touch">
                            <Button rounded variant="primary">
                                Connect us
                            </Button>
                        </Link>
                    </div>
                </div>
                <div className="max-w-[829px] flex-col gap-[70px] max-lg:flex">
                    <AffiliateDisclosure notmain category={AffiliateFaq as any} />
                </div>
            </div>
        </section>
    )
}

export default AffiliateFaqs
