'use client'
import { cn } from '@/lib/cn'
import CustomButtons from './CustomButtons'
import TestimonialCard from './TestimonialCard'
import { Swiper, SwiperSlide } from '@/lib/swiper'
import TestimonialInfo from '@/config/testimonials'

const TestemonialSwiper = () => {
    return (
        <Swiper
            slidesPerView="auto"
            spaceBetween={24}
            className={cn('mySwiper', 'cursor-grab active:cursor-grabbing')}
            breakpoints={{
                480: {
                    slidesPerView: 1,
                    spaceBetween: 30,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 40,
                },
                1024: {
                    slidesPerView: 3.5,
                    spaceBetween: 50,
                },
            }}>
            {TestimonialInfo.map((user, index) => (
                <SwiperSlide className="!h-auto" key={index}>
                    <TestimonialCard user={user} key={index} />
                </SwiperSlide>
            ))}
            <CustomButtons />
        </Swiper>
    )
}

export default TestemonialSwiper
