import Link from 'next/link'
import ImageShortcut from '@/components/Ui/Image'
import Skins from '@/components/Pages/Resources/Skins'
import { brave, unstoppable, account } from '@public/resources/extra'

export const metadata = {
    title: 'Extra Resources',
    description:
        'Extra resources to help you succeed in crypto. Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
    keywords: ['Extra Resources', 'Crypto University', 'Cryptocurrency', 'Bitcoin', 'Ethereum', 'Trading', 'Investing'],
}

const ExtraPage = () => {
    const data = [
        {
            category: 'Internet Browsing',
            items: [{ name: 'Brave', link: 'https://brave.com/?ref=fbo424', img: brave }],
        },
        {
            category: 'Crypto Domains',
            items: [
                {
                    name: 'Unstoppable domains',
                    link: 'https://unstoppabledomains.com/?ref=885a58b434d845c',
                    img: unstoppable,
                },
            ],
        },
    ]

    return (
        <section className="w-full">
            <Skins data={data} />
            <div className="container mx-auto py-[50px]">
                <div className="flex flex-col gap-[100px]">
                    <div className="flex flex-col gap-6">
                        <h2 className="text-headline font-medium">Accounting Tools</h2>
                        <div className="flex flex-wrap gap-6">
                            <div className="flex max-w-[800px] items-center gap-8 p-4 shadow-xl max-md:max-w-full max-md:flex-col max-md:items-start">
                                <ImageShortcut
                                    src={account}
                                    alt="Account"
                                    className="h-[210px] w-[300px] object-cover max-md:w-full"
                                />
                                <div className="flex flex-col items-start gap-3">
                                    <h3 className="text-headline font-medium">Coin Tracking</h3>
                                    <p className="text-sub3">
                                        CoinTracking is a good portfolio tracker as it analyzes your trades in real-time
                                        giving you overview of the value of your coins and much more.
                                    </p>
                                    <Link
                                        target="_blank"
                                        href={
                                            'https://docs.google.com/spreadsheets/d/1tvrdJLfEri5EGuPi0DugSAP3K4XD7RI8vCj0-FbzyNk/edit#gid=**********'
                                        }
                                        aria-label='Open spreadsheet'
                                        className="rounded-lg bg-[#00B24A] px-[0.938rem] py-3 text-sub3 font-semibold text-white transition-colors duration-150 hover:bg-[#00a043] active:bg-[#017933]">
                                        Open Spreadsheet
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default ExtraPage
