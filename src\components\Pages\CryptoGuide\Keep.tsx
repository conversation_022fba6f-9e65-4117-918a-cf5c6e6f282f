
'use client'
import ImageShortcut from '@/components/Ui/Image'
import { Search } from '@public/global'
import { FiMenu } from "react-icons/fi";
import LeftSide from './LeftSide'
import RightSide from './RightSide'
import { useState, useEffect } from 'react';
import { Ri<PERSON>rrowUpSLine, RiArrowDownSLine, RiCloseLine } from "react-icons/ri";
import { CryptoGuideCategoryPost, CryptoGuideResponse, CryptoGuide, CryptoGuidePost } from '@/types/CryptoGuideCategoryPostModel';
import axios from '@/lib/axios'


const Hero = () => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [openSection, setOpenSection] = useState<string | null>('Crypto 101');
    const [categories, setCategories] = useState<CryptoGuideCategoryPost[]>([]);
    const [selectedPost, setSelectedPost] = useState<CryptoGuide | null>(null);
    const [selectedPostTitles, setSelectedPostTitles] = useState<CryptoGuidePost[] | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [categoriesResponse, postsResponse] = await Promise.all([
                    axios.get('/cryptoguide/categories-with-posts'),
                    axios.get('/cryptoguide/titles')
                ]);

                const cryptoGuideResponse: CryptoGuideResponse = categoriesResponse.data;
                const cryptoGuidePosts: CryptoGuidePost[] = postsResponse.data.cryptoguidePosts;

                setCategories(cryptoGuideResponse.categories);
                setSelectedPostTitles(cryptoGuidePosts);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, []);

    const toggleSection = (title: string) => {
        setOpenSection(openSection === title ? null : title);
    };

    const handlePostClick = async (slug: string | null | undefined, e?: React.MouseEvent) => {
        if (e) e.preventDefault();
        if (!slug) return;
        try {
            const response = await axios.get(`/cryptoguide/post-by-slug?slug=${slug}`);
            setSelectedPost(response.data.cryptoguide);
        } catch (error) {
            console.error('Error loading post:', error);
        }
    };

    const content = (
        <div className="h-full overflow-y-auto bg-white md:p-5">
            <div className="flex h-full flex-col">
                {/* Mobile Header */}
                <div className="flex items-center justify-between border-b border-gray-200 bg-[#FFF9EA] p-4 md:hidden">
                    <h2 className="text-xl font-bold">Topics</h2>
                    {!isMobileMenuOpen && (
                        <button
                            onClick={() => setIsMobileMenuOpen(false)}
                            className="text-2xl text-gray-500 hover:text-gray-700"
                            aria-label="Close menu">
                            <RiCloseLine />
                        </button>
                    )}
                </div>

                {/* Navigation List */}
                <nav className="flex-grow p-4">
                    <ul className="space-y-2">
                        {categories?.map(category => (
                            <li key={category.name} className="border-b md:border-b-0 border-gray-200 py-2">
                                <button
                                    onClick={() => toggleSection(category.name)}
                                    className="text-lg text-left flex w-full items-center justify-between font-medium">
                                    {category.name}
                                    {openSection === category.name ? <RiArrowUpSLine /> : <RiArrowDownSLine />}
                                </button>
                                {openSection === category.name && (
                                    <ul className="mt-2 space-y-2 pl-4">
                                        {category.posts.map((post, index) => (
                                            <li key={index}>
                                                <a
                                                    href={'/cryptoguide?' + post.slug}
                                                    className="block py-1 text-gray-600 hover:text-blue-600"
                                                    onClick={(e) => handlePostClick(post.slug, e)}
                                                >
                                                    {post.title}
                                                </a>
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </li>
                        ))}
                    </ul>
                </nav>
            </div>
        </div>
    );

    return (
        <>
            {/* Mobile menu */}
            <div className="h-10 bg-[#fdfaf2] md:hidden">
                {isMobileMenuOpen && (
                    <div className="fixed inset-0 z-50 bg-white md:hidden">
                        {content}
                    </div>
                )}
                <div className="hidden md:block">
                    {content}
                </div>
                <button
                    className="text-2xl mr-8 flex items-center p-4 md:hidden"
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    aria-label="Toggle menu">
                    <FiMenu /> <span className="ml-2">Topics</span>
                </button>
            </div>

            <section id="hero" className="w-full border-b border-gray-700 bg-yellow-light">
                <div className="container mx-auto flex w-full items-center justify-between py-12">
                    <div className="flex items-center">
                        <ImageShortcut
                            src="/guide/hero.png"
                            alt="Crypto Guide"
                            className="object-contain"
                            width={190}
                            height={190}
                        />
                    </div>
                    <div className="flex max-w-[750px] flex-col gap-6">
                        <h1 className="text-headline font-semibold">Cryptoguide</h1>
                        <p className="text-sub3 text-gray-700">
                            Master the art of cryptocurrency with our in-depth guides and learn all the basics as well
                            as the advanced lessons of blockchain.
                        </p>
                        <div className="relative flex items-center">
                            <input
                                type="search"
                                name="search"
                                id="search_crypto_guide"
                                placeholder="Search cryptoguide"
                                className="w-[319px] rounded-full border-[1.5px] border-black bg-transparent bg-white
                                py-[13.5px] pl-14 pr-4 text-sub3 font-medium
                                leading-[20.8px] shadow-[2px_4px_30px_0px_#0000001A] outline-none placeholder:text-black max-md:w-full
                                max-sm:py-[1.125rem] max-sm:text-cap2 max-sm:placeholder:text-[#676A73]"
                            />
                            <div className="absolute left-6 max-sm:hidden">
                                <Search />
                            </div>
                            <div className="absolute left-6 sm:hidden">
                                <Search height={15} width={15} />
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col gap-6">
                        <ImageShortcut
                            src="/guide/paxfull.png"
                            alt="Crypto Guide"
                            className="object-contain"
                            width={240}
                            height={75}
                        />
                        <ImageShortcut
                            src="/guide/crypto.png"
                            alt="Crypto Guide"
                            className="object-contain"
                            width={240}
                            height={75}
                        />
                    </div>
                </div>
            </section>

            <div className="flex">
                <div className='hidden md:block flex-[20%]'>
                    <LeftSide categories={categories} />
                </div>
                <div className='grid md:flex'>
                    <div className="mx-auto max-w-7xl p-8">
                        {/* Title Section */}
                        {selectedPost && (
                            <div dangerouslySetInnerHTML={{ __html: selectedPost.content }} />
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between mt-8 mb-12">
                            {selectedPost?.previous ? (
                                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100"
                                    onClick={() => handlePostClick(selectedPost.previous)}
                                >
                                    ← Prev
                                </button>
                            ) : (
                                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100" disabled>
                                    ← Prev
                                </button>
                            )}
                            {selectedPost?.next ? (
                                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100"
                                    onClick={() => handlePostClick(selectedPost.next)}
                                >
                                    Next →
                                </button>
                            ) : (
                                <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100" disabled>
                                    Next →
                                </button>
                            )}
                        </div>

                        {/* Explore All Topics */}
                        <div className="mt-12">
                            <h2 className="text-xl font-bold mb-4">Explore All Topics</h2>
                            <p className="text-[#424242] mb-4">
                                If this is your first semester at CryptoGuide, we recommend starting at the beginning and working your way through
                                this guide.
                            </p>
                            <ul className="space-y-2 text-[#0000FF]">
                                {selectedPostTitles?.map((selectedPost, index) => (
                                    <li key={index}>
                                        <a
                                            href={'/cryptoguide?' + selectedPost.slug}
                                            className="hover:underline"
                                            onClick={(e) => handlePostClick(selectedPost.slug, e)}
                                        >
                                            {selectedPost.title}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                    <div className='flex-[20%]'>
                        <RightSide />
                    </div>
                </div>
            </div>
        </>
    )
}

export default Hero

// 'use client'
// import ImageShortcut from '@/components/Ui/Image'
// import { Search } from '@public/global'
// import { FiMenu } from "react-icons/fi";
// import LeftSide from './LeftSide'
// import RightSide from './RightSide'
// import { useState, useEffect } from 'react';
// import { RiArrowUpSLine, RiArrowDownSLine, RiCloseLine } from "react-icons/ri";
// import { CryptoGuideCategoryPost, CryptoGuideResponse, CryptoGuide, CryptoGuidePost } from '@/types/CryptoGuideCategoryPostModel';
// import axios from '@/lib/axios'


// const Hero = () => {
//     const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
//     const [openSection, setOpenSection] = useState<string | null>('Crypto 101');
//     const [categories, setCategories] = useState<CryptoGuideCategoryPost[] | null>(null);
//     const [selectedPost, setSelectedPost] = useState<CryptoGuide | null>(null);
//     const [selectedPostTitles, setSelectedPostTitles] = useState<CryptoGuidePost[] | null>(null);

//     useEffect(() => {
//         handlePostClick('how-to-register-for-crypto-university')

//         const fetchData = async () => {
//             try {
//               const [categoriesResponse, postsResponse] = await Promise.all([
//                 axios.get('/cryptoguide/categories-with-posts'),
//                 axios.get('/cryptoguide/titles'),
//               ]);
        
//               const cryptoGuideResponse: CryptoGuideResponse = categoriesResponse.data;
//               const cryptoGuidePosts: CryptoGuidePost[] = postsResponse.data.cryptoguides;
        
//               setCategories(cryptoGuideResponse.categories);
//               setSelectedPostTitles(cryptoGuidePosts);
//             } catch (error) {
//               console.error('Error fetching data:', error);
//             }
//           };
        
//           fetchData();
//     }, []);

//     const toggleSection = (title: string) => {
//         setOpenSection(openSection === title ? null : title);
//     };

//     const handlePostClick = async (slug: string | null | undefined) => {
//         try {
//             const response: any = await axios.get(`/cryptoguide/post-by-slug?${slug}`);
//             const fetchedPost: CryptoGuide = response.data.cryptoguide;
//             setSelectedPost(fetchedPost);
//         } catch (error) {
//             console.error(error);
//         }
//     };

//     const content = (
//         <div className="h-full overflow-y-auto bg-white md:p-5">
//             <div className="flex h-full flex-col">
//                 {/* Mobile Header */}
//                 <div className="flex items-center justify-between border-b border-gray-200 bg-[#FFF9EA] p-4 md:hidden">
//                     <h2 className="text-xl font-bold">Topics</h2>
//                     {!isMobileMenuOpen && (
//                         <button
//                             onClick={() => setIsMobileMenuOpen(false)}
//                             className="text-2xl text-gray-500 hover:text-gray-700"
//                             aria-label="Close menu">
//                             <RiCloseLine />
//                         </button>
//                     )}
//                 </div>

//                 {/* Navigation List */}
//                 <nav className="flex-grow p-4">
//                     <ul className="space-y-2">
//                         {categories?.map(category => (
//                             <li key={category.name} className="border-b md:border-b-0 border-gray-200 py-2">
//                                 <button
//                                     onClick={() => toggleSection(category.name)}
//                                     className="text-lg text-left flex w-full items-center justify-between font-medium">
//                                     {category.name}
//                                     {openSection === category.name ? <RiArrowUpSLine /> : <RiArrowDownSLine />}
//                                 </button>
//                                 {openSection === category.name && (
//                                     <ul className="mt-2 space-y-2 pl-4">
//                                         {category.posts.map((post, index) => (
//                                             <li key={index}>
//                                                 <a
//                                                     href={'/cryptoguide?' + post.slug}
//                                                     className={`block py-1 ${post.title === 'Crypto 101' && index === 1
//                                                         ? 'text-blue-600'
//                                                         : 'text-gray-600'
//                                                         }`}
//                                                     onClick={() => handlePostClick(post.slug)}
//                                                 >
//                                                     {post.title}
//                                                 </a>

//                                             </li>
//                                         ))}
//                                     </ul>
//                                 )}
//                             </li>
//                         ))}
//                     </ul>
//                 </nav>
//             </div>
//         </div>
//     );

//     return (
//         <>
//             {/* Mobile menu */}
//             <div className="h-10 bg-[#fdfaf2] md:hidden">

//                 {/* Mobile view */}
//                 {isMobileMenuOpen && (
//                     <div className="fixed inset-0 z-50 bg-white md:hidden">
//                         {content}
//                     </div>
//                 )}

//                 {/* Desktop view */}
//                 <div className="hidden md:block">
//                     {content}
//                 </div>
//                 <button
//                     className="text-2xl mr-8 flex items-center p-4 md:hidden"
//                     onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
//                     aria-label="Toggle menu">
//                     <FiMenu /> <span className="ml-2">Topics</span>
//                 </button>
//             </div>

//             <section id="hero" className="w-full border-b border-gray-700 bg-yellow-light">
//                 <div className="container mx-auto flex w-full items-center justify-between py-12">
//                     <div className="flex items-center">
//                         <ImageShortcut
//                             src="/guide/hero.png"
//                             alt="Crypto Guide"
//                             className="object-contain"
//                             width={190}
//                             height={190}
//                         />
//                     </div>
//                     <div className="flex max-w-[750px] flex-col gap-6">
//                         <h1 className="text-headline font-semibold">Cryptoguide</h1>
//                         <p className="text-sub3 text-gray-700">
//                             Master the art of cryptocurrency with our in-depth guides and learn all the basics as well
//                             as the advanced lessons of blockchain.
//                         </p>
//                         <div className="relative flex items-center">
//                             <input
//                                 type="search"
//                                 name="search"
//                                 id="search_crypto_guide"
//                                 placeholder="Search cryptoguide"
//                                 className="w-[319px] rounded-full border-[1.5px] border-black bg-transparent bg-white
// py-[13.5px] pl-14 pr-4 text-sub3 font-medium
// leading-[20.8px] shadow-[2px_4px_30px_0px_#0000001A] outline-none placeholder:text-black max-md:w-full
// max-sm:py-[1.125rem] max-sm:text-cap2 max-sm:placeholder:text-[#676A73]"
//                             />
//                             <div className="absolute left-6 max-sm:hidden">
//                                 <Search />
//                             </div>
//                             <div className="absolute left-6 sm:hidden">
//                                 <Search height={15} width={15} />
//                             </div>
//                         </div>

//                     </div>
//                     <div className="flex flex-col gap-6">
//                         <ImageShortcut
//                             src="/guide/paxfull.png"
//                             alt="Crypto Guide"
//                             className="object-contain"
//                             width={240}
//                             height={75}
//                         />
//                         <ImageShortcut
//                             src="/guide/crypto.png"
//                             alt="Crypto Guide"
//                             className="object-contain"
//                             width={240}
//                             height={75}
//                         />
//                     </div>
//                 </div>
//             </section>
//             <div className="flex">
//                 <div className='hidden md:block flex-[20%]'>
//                     <LeftSide />
//                 </div>
//                 <div className='grid md:flex'>
//                     <div>
//                         {' '}
//                         <div className="mx-auto max-w-7xl p-8">
//                             {/* Title Section */}

//                             {selectedPost && (
//                                 <div dangerouslySetInnerHTML={{ __html: selectedPost.content }} />
//                             )}

//                             {/* Navigation */}
//                             <div className="flex justify-between mt-8 mb-12">
//                                 {selectedPost?.previous ? (
//                                     <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100"
//                                         onClick={() => handlePostClick(selectedPost?.previous)}
//                                     >
//                                         ← Prev
//                                     </button>
//                                 ) :
//                                     (
//                                         <button className="disabled px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100">
//                                             ← Prev
//                                         </button>
//                                     )}

//                                 {selectedPost?.next ? (
//                                     <button className="px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100"
//                                         onClick={() => handlePostClick(selectedPost?.next)}
//                                     >
//                                         Next →
//                                     </button>
//                                 ) :
//                                     (
//                                         <button className="disabled px-4 py-2 border border-gray-300 rounded-md text-[#424242] hover:bg-gray-100">
//                                             Next →
//                                         </button>
//                                     )}



//                             </div>

//                             {/* Explore All Topics */}
//                             <div className="mt-12">
//                                 <h2 className="text-xl font-bold mb-4">Explore All Topics</h2>
//                                 <p className="text-[#424242] mb-4">
//                                     If this is your first semester at CryptoGuide, we recommend starting at the beginning and working your way through
//                                     this guide.
//                                 </p>


//                                 <ul className="space-y-2 text-[#0000FF]">
//                                     {selectedPostTitles?.map((selectedPost, index) => (
//                                         <li key={index}><a href={'/cryptoguide?' + selectedPost.slug} className="hover:underline">{selectedPost.title}</a></li>
//                                     ))}


//                                 </ul>
//                             </div>
//                         </div>
//                     </div>
//                     <div className='flex-[20%]'>
//                         {' '}
//                         <RightSide />
//                     </div>
//                 </div>

//             </div>
//         </>
//     )
// }

// export default Hero
