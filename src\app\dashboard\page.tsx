import fetchInstance from '@/lib/fetch'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import { CourseModelV2 } from '@/types/CourseModel'
import Notfound from '@/components/Pages/NotFound/Notfound'
import Header from '@/components/Pages/Dashboard/Home/Header'
import Courses from '@/components/Pages/Dashboard/Home/Courses'
import NoCourse from '@/components/Pages/Dashboard/Home/NoCourse'
import Pagination from '@/components/Pages/Dashboard/Home/Pagination'

interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

export const metadata = {
    title: 'My Courses',
    description: 'Courses page for your Crypto University account.',
}

const DashboardPage = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const GetCourses = async () => {
        try {
            const response = await fetchInstance(
                '/user-course/my-courses?type=' +
                    (booleanFilter ? filter : 'all') +
                    '&pageNumber=' +
                    pageNumber +
                    '&pageSize=9',
                {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                }
            )
            return response
        } catch (error) {
            console.error('Error Users: ', error)
            return error
        }
    }

    const courses: Model = await GetCourses()
    return (
        <section className="flex w-full flex-col gap-14 text-black">
            <Header filter={typeof filter == 'string' ? filter : ''} user={user} />
            {courses.meta && courses.meta.totalCount == 0 ? (
                <NoCourse />
            ) : (typeof pageNumber == 'string' &&
                  parseInt(pageNumber) >= 1 &&
                  parseInt(pageNumber) <= courses.meta.totalPages) ||
              pageNumber == undefined ? (
                <div className="flex h-full flex-col justify-between">
                    <Courses courses={courses.courses} />
                    <Pagination
                        courses={courses}
                        filter={typeof filter == 'string' ? filter : 'all'}
                        pageNumber={pageNumber ?? '1'}
                    />
                </div>
            ) : (
                <Notfound />
            )}
        </section>
    )
}

export default DashboardPage
