import { Metada<PERSON> } from 'next'
import fetchInstance from '@/lib/fetch'
import { notFound } from 'next/navigation'
import getCountryInfo from '@/lib/countries'
import { getCurrentUser } from '@/lib/session'
import { Country } from '@/types/CountryModel'
import { capitalizeWords } from '@/lib/capitalize'
import SuccessModel from '@/components/Pages/Checkout/SuccessModel'
import { SubscriptionCheckoutForm } from '@/components/Pages/Checkout/SubscriptionCheckoutForm'
import SuccessModelNewUser from '@/components/Pages/Checkout/SuccessModelNewUser'

interface MetaProps {
    params: {
        slug: any
    }
}
interface PageProps {
    slug: any
}
interface PageSlugProps {
    referral?: string
    hash?: string
    newUser?: string
}

async function getSubscriptionFromParams(params: PageProps) {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await fetchInstance(`/subscription/byslug/${slug}`, { next: { revalidate: 60 } })
            return response.subscription
        } catch (error: any) {
            console.error('Subscription Error:', error)
            return undefined
        }
}
async function checkIfUserHasSubscription(subscriptionSlug: string, user: any) {
    const res = await fetchInstance(`/user-subscription/have-subscription?slug=${subscriptionSlug}`, {
        headers: {
            Authorization: `Bearer ${user?.access_token}`,
        },
    })
    return res.status
}
async function isSuccessPayment(searchParams: PageSlugProps, user: any) {
    const { hash, newUser } = searchParams
    if (hash !== undefined)
        try {
            if (newUser === 'true' && !user) return { user_status: false }
            else {
                const check = await fetch(`${process.env.API_URL}/payment/check_hax?hash=${hash as string}`, {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                })
                const data = await check.json()
                return data
            }
        } catch (error: any) {
            console.error('Hash Error:', error)
            return error
        }
    else return { statusCode: 402 }
}
export async function generateMetadata({ params }: MetaProps): Promise<Metadata> {
    const slug = params?.slug
    if (slug !== undefined)
        try {
            const response = await getSubscriptionFromParams({ slug })
            if (response === null) return {}
            else {
                return {
                    title: 'Checkout ' + capitalizeWords(response.title),
                    description: 'Checkout page for ' + response.title,
                    openGraph: {
                        title: response.title,
                        description: response.description,
                        type: 'website',
                        url: response.slug,
                        images: [
                            {
                                url: response.image,
                                width: 1200,
                                height: 630,
                                alt: response.title,
                            },
                        ],
                    },
                    twitter: {
                        card: 'summary_large_image',
                        title: response.title,
                        description: 'Checkout page for ' + response.title,
                        images: [response.image],
                    },
                }
            }
        } catch (error: any) {
            console.error('Metadata:', error)
            return error
        }
    else return {}
}

const SubscriptionPage = async ({ params, searchParams }: { params: PageProps; searchParams: PageSlugProps }) => {
    const subscription = await getSubscriptionFromParams(params)
    const countries: Country[] = await getCountryInfo()

    if (subscription === null || subscription === undefined) notFound()

    const user = await getCurrentUser()

    let isEnrolled = false
    if (user !== null && user !== undefined) isEnrolled = await checkIfUserHasSubscription(subscription.slug, user)

    if (searchParams.hash) {
        return (

            <section className="mx-auto pb-12">
                { searchParams.newUser == 'true' ? (
                        <SuccessModelNewUser success={true} />
                    ) : (
                        <SuccessModel success={true} type='subscription' />
                    )
                    }
            </section>
        )
    }

    return (
        <section className="mx-auto pb-12">
            <SubscriptionCheckoutForm countries={countries} subscription={subscription} user={user} disabled={isEnrolled} />
        </section>
    )
}

export default SubscriptionPage
