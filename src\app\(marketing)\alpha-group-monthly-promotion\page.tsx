import AlphaGroupMonthlyPromotion from "@/components/Pages/AlphaGroupMonthlyPromotion/AlphaGroupMonthlyPromotion"
import { ComingSoon } from "@/components/Pages/Memberships/ComingSoon/ComingSoon"

export const metadata = {
    title: 'Alpha Group Monthly Subscriptions  - The Crypto University',
    description:
        'Get exclusive access to Web3, AI insights, masterclass courses and signals in our discord at unbeatable discount price',
    keywords: ['Alpha Group', 'Discount', 'Crypto University', 'Promotion', 'Crypto', 'P2P', 'Cryptocurrency'],
    openGraph: {
        images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1717966317/Alpha_Group_Webpage_Thumbnail_1200x628_1_ie2zok.jpg',
      },
}
const AlphaGroupMonthlyPromotionPage = () => {
    return (
        <section className="w-full bg-[#222121]">
              {/* <ComingSoon/> */}
            <AlphaGroupMonthlyPromotion/>
        </section>
    )
}

export default AlphaGroupMonthlyPromotionPage
