import { user } from '@/types/UserModel'
interface props {
    user: Partial<user>
}

const HeaderWithNoAction = ({ user }: props) => {
   
    return (
        <div className="flex w-full items-center justify-between border-b border-gray-700 px-8 py-[1.6875rem] max-md:flex-col max-md:gap-4 max-md:px-5">
            <p className="flex flex-col flex-wrap gap-4 font-manrope max-md:flex-row max-md:justify-between">
                <span className="text-sub2 font-normal capitalize text-gray-900">Hi, {user.display_name} 👋</span>
                <span className="text-sub2 font-semibold">Learn. Trade. Invest. Play</span>
            </p>
        </div>
    )
}

export default HeaderWithNoAction
