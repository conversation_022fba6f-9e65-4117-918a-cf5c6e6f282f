import { cn } from '@/lib/cn'
import { FC, ReactNode } from 'react'

interface Props {
    variant?: 'default' | 'primary' | 'secondary' | 'dashed'
    rounded?: boolean
    nav?: boolean
    children: ReactNode
    onClick?: ((e: any) => Promise<void>) | (() => void) | ((e:any) => void)
    type?: 'button' | 'submit' | 'reset'
    className?: string
    disabled?: boolean
    gradient?: boolean
}

const Button: FC<Props> = ({
    variant = 'default',
    rounded = false,
    nav = false,
    children,
    onClick,
    className,
    type = 'button',
    disabled = false,
    gradient = false,
}) => {
    const className1 = cn(
        'py-[17.5px] flex items-center gap-2 justify-center font-sans max-md:py-4 max-md:text-sub3 select-none w-full px-6 font-semibold text-sub2 min-w-fit transition-[background-color] duration-150 disabled:cursor-not-allowed', className,
        {
            'py-[13.5px]': nav,
            'rounded-full': rounded,
            'rounded-[8px]': !rounded,
            'gradient': gradient,
            'bg-blue hover:bg-[#224DE6] active:bg-[#1E44CC] text-white disabled:bg-[#E9EEFF] disabled:text-[#BCCAFF]': variant === 'primary',
            'bg-black hover:bg-[#071024]/90 active:bg-[#060E20]/80 text-white disabled:bg-[#E8EAED] disabled:text-[#676A73]': variant === 'secondary',
            'border-[1px] border-dashed border-black hover:bg-blue-100 text-black active:opacity-60 disabled:opacity-40': variant === 'dashed',
            'bg-white border border-gray-600 text-black hover:bg-gray-500 hover:border-gray-500 active:border-gray-600 active:bg-gray-600 disabled:opacity-30': variant === 'default',
        }
    )

    return (
        <button type={type} className={className1} onClick={onClick} disabled={disabled}>
            {children}
        </button>
    )
}

export default Button