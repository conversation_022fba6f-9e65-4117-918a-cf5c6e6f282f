import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@public/home/<USER>'

const TestimonialInfo = [
    {
        initials: '<PERSON><PERSON>',
        img: <PERSON><PERSON>,
        name: '<PERSON><PERSON>',
        description:
            'I lost my job due to the pandemic. I was devastated and in huge debt. While exploring a new path by joining in Crypto University and learned Bitcoin P2P. After 8 months, I managed to become self employed and paid all my debt…',
    },
    {
        initials: 'H<PERSON>',
        img: null,
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description:
            'As a person who loves knowledge, I took this as a new venture to learn and to get more involved in the crypto world and didn\'t expect it to be one of the best experiences ever. Guided by a well-organized professional team, I\'m grateful for every minute I spent with Crypto University. ',
    },
    {
        initials: '<PERSON><PERSON>',
        img: <PERSON><PERSON>,
        name: '<PERSON><PERSON>',
        description:
            '<PERSON>, owner of Crypto University, and I met early 2017 when he was already enthusiastic about BTC and I wasn\'t at all. He encouraged me to buy BTC, which I didn\'t at that point and deeply regret today :-) He did plant the BTC seed in my brain.',
    },
    {
        initials: '<PERSON>',
        img: null,
        name: '<PERSON><PERSON><PERSON>',
        description:
            'At crypto university I learn how to trade the Cryptocurrency markets. What is more interesting is the community, where we hold weekly Livestreams and I get to meet and learn from different traders all over the world.',
    },
    {
        initials: 'SS',
        img: null,
        name: 'Sajal Sharma',
        description:
            'The courses are good and pretty straightforward. Community support is great on Discord where i can share my trade experiences and take advise from other traders. Weekly Discord events and webinar are a plus point.',
    }
    ,
    {
        initials: 'YC',
        img: Yadith,
        name: 'Yadith Casanova',
        description: `They always support you... You feel comfortable and secure with CU`,
    }
    ,
    {
        initials: 'DA',
        img: null,
        name: 'Dreamer ae',
        description:
            'All you need to get started is consolidated in one place.',
    }
]

export default TestimonialInfo