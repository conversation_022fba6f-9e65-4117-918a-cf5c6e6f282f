import Card from './Card'
import CardFlex from './CardFlex'
import { Blogs } from '@/types/BlogModel'

const Recent = ({ blogs }: { blogs: Blogs }) => {
    return (
        <div className="space-y-8">
            <h4 className="text-b3 font-semibold">Recent Blog Posts</h4>
            <div className="grid grid-cols-2 gap-8 max-lg:grid-cols-2 max-md:grid-cols-1">
                {blogs !== null
                    ? blogs.slice(0, 1).map((blog, index) => <Card key={index} {...blog} />)
                    : 'No recent posts'}
                <div className="space-y-8 max-lg:hidden">
                    {blogs !== null
                        ? blogs.slice(1, 3).map((blog, index) => <CardFlex key={index} {...blog} />)
                        : 'No recent posts'}
                </div>
                <div className="hidden space-y-8 max-lg:block">
                    {blogs !== null
                        ? blogs.slice(1, 3).map((blog, index) => <Card key={index} {...blog} />)
                        : 'No recent posts'}
                </div>
            </div>
        </div>
    )
}

export default Recent
