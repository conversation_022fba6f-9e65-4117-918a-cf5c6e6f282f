import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import { CourseDetailsModel, CourseModelV2 } from '@/types/CourseModel'
import NPButtons from '@/components/Pages/Dashboard/Course/NPButtons'
import BreadCrumb from '@/components/Pages/Dashboard/Course/BreadCrumb'
import {
    GetCourses,
    GetFirstTopic,
    GetOneCourse,
    checkLessons,
    checkTopics,
    getCourseById,
    getCurrentLesson,
    getFirstLesson,
    getStats,
    getTopic,
} from '@/config/validationCourseDashboard'
import Video from '@/components/Pages/Dashboard/Course/Video'
import Buttons from '@/components/Pages/Dashboard/Course/Buttons'
import Overview from '@/components/Pages/Dashboard/Course/Overview'

interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}
type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
        lesson: string
        topic: string
    }
}

const TopicPage = async ({ searchParams, params }: Props) => {
    // Check if user exists
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

    // Get filters and page
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })

    if (courses.courses.length === 0 || getCourseById({ courses, params }) === null) {
        redirect('/dashboard')
    }

    const course = await GetOneCourse(user, getCourseById({ courses, params })?.slug ?? '')
    const stat: CourseDetailsModel = (await getStats(course?.course.slug ?? '')) as CourseDetailsModel
    if (
        course === undefined ||
        getFirstLesson({ course }) === undefined ||
        checkLessons({ course, params }) === false ||
        checkTopics({ course, params }) === false
    ) {
        redirect('/dashboard')
    }
    return (
        <section className="container mx-auto flex w-full flex-col  items-start gap-9 py-8">
            <div className="flex w-full flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
                <BreadCrumb />
                <div className="max-md:hidden">
                    <NPButtons course={course?.course} />
                </div>
            </div>

            <Video bannerImage={course.course.bannerImage} topic={getTopic({ course, params })} />

            <div className="flex w-full justify-center md:hidden">
                <NPButtons course={course?.course} />
            </div>
            <div className=" w-full border-b border-gray-700 md:hidden" />
            <Buttons
                course={course?.course}
                TopicSlug={getTopic({ course, params })}
                user={user}
                slug={'/dashboard/' + params.course + '/' + params.lesson + '/' + params.topic}
            />
            <Overview
                lesson={getCurrentLesson({ course, params })}
                topic={getTopic({ course, params })}
                instructor={course.instructor}
                numberOflessons={stat?.count.topics}
            />
        </section>
    )
}

export default TopicPage
