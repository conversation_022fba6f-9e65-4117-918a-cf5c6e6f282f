import { authOptions } from '@/lib/auth'
import fetchInstance from '@/lib/fetch'
import { getCurrentUser } from '@/lib/session'
import TransactionModel from '@/types/TransactionModel'
import { redirect } from 'next/navigation'
import Referrals from '@/components/Pages/Dashboard/Affiliate/Referrals'

interface Model {
    reference: string
    description: string
    date: string
    commission: number
    status: string
    image: string
}

const ReferralsPage = async () => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

    const GetReferrals = async () => {
        try {
            const response:any = await fetchInstance('/affiliate/referrals', {
                headers: {
                    Authorization: `Bearer ${user?.access_token}`,
                },
            })
            return response.referrals
        } catch (error) {
            console.error('Error Referrals: ', error)
            return error
        }
    }


    const referrals: Model[] = await GetReferrals()
    return (
        <div className="container mx-auto py-12">
            <div className="grid grid-cols-12 gap-5">
                <div className="max-md:hidden col-span-12 grid grid-cols-12 place-items-start place-content-center rounded-xl bg-gray-300 px-6 py-4 text-cap2 font-semibold text-gray-700">
                    <p className="col-span-3">REFERENCE </p>
                    <p className="col-span-3">DESCRIPTION</p>
                    <p className="col-span-2">DATE</p>
                    <p className="col-span-2">STATUS</p>
                    <p className="col-span-2">COMMISION</p>
                </div>
                {referrals.map((referral, index) => (
                    <div key={index} className="col-span-12">
                        <Referrals referral={referral} />
                    </div>
                ))}
            </div>
        </div>
    )
}

export default ReferralsPage
