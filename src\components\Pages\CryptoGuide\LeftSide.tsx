"use client";
import { Ri<PERSON>rrowUpSLine, RiArrowDownSLine, RiCloseLine } from "react-icons/ri";
import { CryptoGuideCategoryPost } from "@/types/CryptoGuideCategoryPostModel";
import Link from "next/link";
import { useState } from "react";
import { usePathname } from "next/navigation"; // Import usePathname

interface LeftSideProps {
  onClose?: () => void;
  isOpen?: boolean;
  categories: CryptoGuideCategoryPost[];
}

const LeftSide = ({ onClose, isOpen, categories }: LeftSideProps) => {
  const [openSection, setOpenSection] = useState<string | null>("Crypto 101");
  const pathname = usePathname(); // Get the current pathname

  const toggleSection = (title: string) => {
    setOpenSection(openSection === title ? null : title);
  };

  const content = (
    <div className="h-full overflow-y-auto bg-white md:p-5">
      <div className="flex h-full flex-col">
        {/* Mobile Header */}
        <div className="flex items-center justify-between border-b border-gray-200 bg-[#FFF9EA] p-4 md:hidden">
          <h2 className="text-xl font-bold">Topics</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-2xl text-gray-500 hover:text-gray-700"
              aria-label="Close menu"
            >
              <RiCloseLine />
            </button>
          )}
        </div>

        {/* Navigation List */}
        <nav className="flex-grow p-4">
          <ul className="space-y-2">
            {categories?.map((category) => (
              <li
                key={category.name}
                className="border-b md:border-b-0 border-gray-200 py-2"
              >
                <button
                  onClick={() => toggleSection(category.name)}
                  className="text-lg text-left flex w-full items-center justify-between font-medium"
                >
                  {category.name}
                  {openSection === category.name ? (
                    <RiArrowUpSLine />
                  ) : (
                    <RiArrowDownSLine />
                  )}
                </button>
                {openSection === category.name && (
                  <ul className="mt-2 space-y-2 pl-4">
                    {category.posts.map((post, index) => (
                      <li key={index}>
                        <Link
                          aria-label={post.title + " page"}
                          href={`/cryptoguide/${post.slug}`}
                          onClick={() => setOpenSection(null)} // Collapse the category on click
                          className={`block py-1 ${
                            pathname === `/cryptoguide/${post.slug}`
                              ? "text-blue-600 font-semibold" // Highlight the active link
                              : "text-gray-600 hover:text-blue-600"
                          }`}
                        >
                          {post.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );

  // Conditional Rendering for Mobile and Desktop
  return (
    <>
      {/* Mobile view */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-white md:hidden">{content}</div>
      )}

      {/* Desktop view */}
      <div className="hidden md:block">{content}</div>
    </>
  );
};

export default LeftSide;