import { useState } from 'react'
import axios, { AxiosError } from 'axios'
import { useMutation } from 'react-query'

type Props = {
    referral: string | null
    endDate: Date | null
    username: string | null
    values: any
    product: any
    productType: string
    path: string
    coupon: any
}

type SubscriptionOrderProps = {
    referral: string | null
    endDate: Date | null
    username: string | null
    values: any
    product: any
    productType: string
    path: string
    coupon: any
    order_id: string | null
}

type SubscriptionProps = {
    referral: string | null
    endDate: Date | null
    username: string | null
    values: any
    product: any
    productType: string
    path: string
    coupon: any
    actions: any
    plan_id: string
}

export type OnApproveData = {
    billingToken?: string | null;
    facilitatorAccessToken: string;
    orderID: string;
    subscriptionID?: string | null;
    payerID?: string | null;
    paymentID?: string | null;
    authCode?: string | null;
};

const usePayment = () => {
    const [error, setError] = useState('')
    const [isLoading, setIsLoading] = useState(false)

    const reset = () => {
        setError('')
        setIsLoading(false)
    }

    const stripePay = async ({ referral, endDate, values, product, username, productType, path, coupon }: Props) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                username,
                path: path as string,
                endDate,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } else if (productType === 'subscription') {
                body.subscription_id = product
            }
            
            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/payment/charge/stripe?referralCode=${referral}`
            else url = `/payment/charge/stripe`

            const stripe = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const stripeData = await stripe.json()
            if (stripeData && stripeData.statusCode == 201) window.location.href = stripeData.checkout_url
            else alert(stripeData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }

    const stripeSubscriptionPay = async ({ referral, endDate, values, product, username, productType, path, coupon }: SubscriptionOrderProps) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                username,
                path: path as string,
                endDate,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } else if (productType === 'subscription') {
                body.subscription_id = product
            }
            
            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/payment/charge/stripe?referralCode=${referral}`
            else url = `/payment/charge/stripe`

            const stripe = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const stripeData = await stripe.json()
            if (stripeData && stripeData.statusCode == 201) window.location.href = stripeData.checkout_url
            else alert(stripeData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }

    const coinbasePay = async ({ referral, endDate, values, product, productType, path, coupon }: Props) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                path: path as string,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } else if (productType === 'subscription') {
                body.subscription_id = product
            }

            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/payment/charge/coinbase?referralCode=${referral}`
            else url = `/payment/charge/coinbase`

            const coinbase = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const coinbaseData = await coinbase.json()
            if (coinbaseData && coinbaseData.statusCode == 201) window.location.href = coinbaseData.link
            else alert(coinbaseData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }

     const atlosPay = async ({ referral, endDate, values, product,order_id, productType, path, coupon }: SubscriptionOrderProps) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                path: path as string,
                order_id: order_id,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } 
            // else if (productType === 'subscription') {
            //     body.subscription_id = product
            // }

            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/atlos/payment/charge?referralCode=${referral}`
            else url = `/atlos/payment/charge`

            const atlos = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const atlosData = await atlos.json()
            if (atlosData && atlosData.statusCode == 201) { 
                // window.location.href = atlosData.checkout_url
            }
            else alert(atlosData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }

    const atlosPaySuccess = async ({ referral, endDate, values, product,order_id, productType, path, coupon }: SubscriptionOrderProps) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                path: path as string,
                order_id: order_id,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } else if (productType === 'subscription') {
                body.subscription_id = product
            }

            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/atlos/payment/success?referralCode=${referral}`
            else url = `/atlos/payment/success`

            const atlos = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const atlosData = await atlos.json()
            if (atlosData && atlosData.statusCode == 201) window.location.href = atlosData.checkout_url
            else alert(atlosData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }

    const atlosPayCompleted = async ({ referral, endDate, values, order_id, product, productType, path, coupon }: SubscriptionOrderProps) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                path: path as string,
                order_id: order_id,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } else if (productType === 'subscription') {
                body.subscription_id = product
            }

            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/atlos/payment/completed?referralCode=${referral}`
            else url = `/atlos/payment/completed`

            const atlos = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const atlosData = await atlos.json()
            if (atlosData && atlosData.statusCode == 201) window.location.href = atlosData.checkout_url
            else alert(atlosData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }

    const atlosPayCancelled = async ({ referral, endDate, order_id, values, product, productType, path, coupon }: SubscriptionOrderProps) => {
        setIsLoading(true)
        try {
            const currentDate = new Date()
            const body = {
                ...values,
                path: path as string,
                order_id: order_id,
                coupon,
            }
            if (productType === 'course') {
                body.course_id = product
            } else if (productType === 'bundle') {
                body.bundle_id = product
            } else if (productType === 'mentorship') {
                body.mentorship_id = product
            } else if (productType === 'indicator') {
                body.indicator_id = product
            } else if (productType === 'bootcamp') {
                body.bootcamp_id = product
            } else if (productType === 'subscription') {
                body.subscription_id = product
            }

            if (endDate) {
                const parsedEndDate = new Date(endDate)
                if (currentDate > parsedEndDate) referral = null
            }

            let url: string
            if (referral) url = `/atlos/payment/cancelled?referralCode=${referral}`
            else url = `/atlos/payment/cancelled`

            const atlos = await fetch(process.env.API_URL + url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            const atlosData = await atlos.json()

            console.log()

            // if (atlosData && atlosData.statusCode == 201) window.location.href = atlosData.checkout_url
            // else alert(atlosData.message)

            setIsLoading(false)
        } catch (error: any) {
            console.error(error)
            setIsLoading(false)
            setError(error)
        }
    }


    const createMutation = useMutation<{ data: any }, AxiosError, any, Response>(
        (data): any => axios.post(data.url, data),
    )
    const captureMutation = useMutation<string, AxiosError, any, Response>(
        (data): any => axios.post(data.url, data),
    )

    const createPayPalOrder = async ({ referral, endDate, values, product, productType, path, coupon }: Props): Promise<string> => {
        const backendURL = process.env.API_URL;

        let url: string
        if (referral) url = `${backendURL}/paypal/order/create?referralCode=${referral}`
        else url = `${backendURL}/paypal/order/create`

        const currentDate = new Date()
        const body = {
            ...values,
            path: path as string,
            coupon,
        }
        if (productType === 'course') {
            body.course_id = product
        } else if (productType === 'bundle') {
            body.bundle_id = product
        } else if (productType === 'mentorship') {
            body.mentorship_id = product
        } else if (productType === 'indicator') {
            body.indicator_id = product
        } else if (productType === 'bootcamp') {
            body.bootcamp_id = product
        } else if (productType === 'subscription') {
            body.subscription_id = product
        }

        body.url = url

        if (endDate) {
            const parsedEndDate = new Date(endDate)
            if (currentDate > parsedEndDate) referral = null
        }
        try {
            
            const response = await createMutation.mutateAsync(body);
            return response.data.orderID;
        } catch (error) {
            if (axios.isAxiosError(error) && error.response) {
                // Log the error response from the server
                console.error("Error Response:", error.response.data);
                alert(error.response.data.message)

            } else {
                console.error("Error creating PayPal order:", error);
                alert(error)
            }
            throw error;
        }
    };

    const createPayPalSubscription = async ({ referral, endDate, values, product, productType, path, actions, plan_id, coupon }: SubscriptionProps): Promise<any> => {

           
        const backendURL = process.env.API_URL;

        let url: string
        if (referral) url = `${backendURL}/paypal/order/create?referralCode=${referral}`
        else url = `${backendURL}/paypal/subscription/create`

        const currentDate = new Date()
        const body = {
            ...values,
            path: path as string,
            coupon,
        }
        if (productType === 'course') {
            body.course_id = product
        } else if (productType === 'bundle') {
            body.bundle_id = product
        } else if (productType === 'mentorship') {
            body.mentorship_id = product
        } else if (productType === 'indicator') {
            body.indicator_id = product
        } else if (productType === 'bootcamp') {
            body.bootcamp_id = product
        } else if (productType === 'subscription') {
            body.subscription_id = product
        }

        body.url = url

        if (endDate) {
            const parsedEndDate = new Date(endDate)
            if (currentDate > parsedEndDate) referral = null
        }
        try {
            
           const subscriptionID = await actions.subscription.create({
                'plan_id': plan_id
            });

            body.subscriptionID = subscriptionID;

            const response = await createMutation.mutateAsync(body);

            return subscriptionID;

            // return response.data.orderID;
        } catch (error) {
            if (axios.isAxiosError(error) && error.response) {
                // Log the error response from the server
                console.error("Error Response:", error.response.data);
                alert(error.response.data.message)
    
                // Optionally, handle different status codes differently
                // if (error.response.status === 400) {
                //     console.error("Bad Request Error:", error.response.data);
                // }
            } else {
                // Log general errors
                console.error("Error creating PayPal order:", error);
                alert(error)
            }
            throw error;
        }
    };

    const onApprove = async (data: OnApproveData): Promise<void> => {
        const backendURL = process.env.API_URL;
        let url = `${backendURL}/paypal/order/capture2`;

        try {
            // Using mutateAsync to wait for the mutation to complete and get the response
            const response: any = await captureMutation.mutateAsync({ orderID: data.orderID, url: url });
            const orderStatus = response.data.data.order.status;
            window.location.href = response.data.checkout_url
            if (orderStatus === "COMPLETED") {
                window.location.href = response.data.checkout_url
            }

        } catch (error) {
            // Handle any errors here
            console.error('Error in capturing PayPal order:', error);
        }
    };

    const onApproveSubscription = async (data: OnApproveData): Promise<void> => {
        const backendURL = process.env.API_URL;
        let url = `${backendURL}/paypal/subscription/webhook`;

        try {
            // Using mutateAsync to wait for the mutation to complete and get the response
            const response: any = await captureMutation.mutateAsync({ orderID: data.orderID, subscriptionID: data.subscriptionID, url: url });
            const orderStatus = response.data.data.order.status;
            window.location.href = response.data.checkout_url
            // if (orderStatus === "COMPLETED") {
            //     window.location.href = response.data.checkout_url
            // }

        } catch (error) {
            // Handle any errors here
            console.error('Error in capturing PayPal order:', error);
        }
    };

    return {
        error,
        reset,
        isLoading,
        stripePay,
        stripeSubscriptionPay,
        coinbasePay,
        createPayPalOrder,
        onApprove,
        createPayPalSubscription,
        onApproveSubscription,
        atlosPaySuccess,
        atlosPayCompleted,
        atlosPayCancelled,
        atlosPay,
    }
}

export default usePayment
