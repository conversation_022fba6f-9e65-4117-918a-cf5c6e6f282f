'use client'
import { cn } from '@/lib/cn'
import fetch from '@/lib/fetch'
import { toast } from 'react-toastify'
import { Email } from '@public/global'
import { useRef, useState } from 'react'
import Label from '@/components/Ui/Label'
import Button from '@/components/Ui/Button'
import { FormikErrors } from '@/lib/formik'
import { ProfileBlack } from '@public/home'
import ButtonSpinner from '@/components/Ui/buttonSpinner'
import { FormikProps, Yup, useFormik } from '@/lib/formik'

const Activity = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                stroke={'#081228'}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M7.244 14.781l2.993-3.89 3.415 2.682 2.929-3.78"></path>
            <circle
                cx="19.994"
                cy="4.2"
                r="1.922"
                stroke={'#081228'}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"></circle>
            <path
                stroke={'#081228'}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M14.924 3.12H7.656c-3.011 0-4.879 2.133-4.879 5.144v8.083c0 3.011 1.831 5.135 4.88 5.135h8.603c3.012 0 4.879-2.124 4.879-5.135v-7.04"></path>
        </svg>
    )
}

interface TextBoxProps {
    setEmail?: any
    placeholder: string
    error?: string | undefined | string[] | FormikErrors<any> | FormikErrors<any>[]
    label: string
    className?: string
    Icon?: React.ReactNode
    CodeSelect?: React.ReactNode
    formik: FormikProps<any>
    id: ValidationSchemaKeys
    value: any
    onChange: any
    innerRef: React.RefObject<HTMLInputElement>
    type?: string
    isSelectFocus?: boolean
}
type ValidationSchemaKeys = 'full_name' | 'phone_number' | 'product' | 'issue' | 'email'

const showError = (
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>,
    key: ValidationSchemaKeys,
    formik: FormikProps<any>
) => {
    if (ref.current) {
        ref.current.onfocus = () => {
            formik.setFieldTouched(key, false)
        }
    }
    return formik.touched[key] && (formik.errors as { [key: string]: string })[key]?.length > 0
}
const TextBox = ({ placeholder, error, label, className, Icon, id, value, innerRef, formik, type }: TextBoxProps) => {
    const [focus, setFocus] = useState(false)

    return (
        <div className={cn('flex h-fit w-full flex-col gap-3', className)}>
            <Label className="" required uppercase={false}>
                {label}
            </Label>

            <div className="flex flex-col gap-2 max-md:w-full">
                <div
                    onBlur={() => {
                        setFocus(false)
                    }}
                    onClick={() => setFocus(true)}
                    className={cn(
                        'relative flex flex-1 select-none flex-row items-center rounded-[50px] border border-gray-700 ',
                        focus && 'border-blue'
                    )}>
                    {Icon ? <div className="absolute left-4">{Icon}</div> : ''}
                    <input
                        autoComplete="new-password"
                        ref={innerRef}
                        type={id == 'email' ? 'email' : id == 'phone_number' ? 'number' : type ?? 'text'}
                        name={id}
                        id={id}
                        value={value}
                        onBlur={formik.handleBlur}
                        onChange={formik.handleChange}
                        className={cn(
                            'w-full rounded-full border border-gray-700 border-transparent px-4  py-[17.5px] pr-2 ring-0 focus:outline-none max-md:py-3.5 max-md:text-cap1',
                            Icon && 'pl-12'
                        )}
                        placeholder={placeholder}
                    />
                </div>
                {showError(innerRef, id, formik) && (
                    <div className="rounded-lg bg-red/20 p-3">
                        <p className="text-cap1 text-red">{error?.toString()}</p>
                    </div>
                )}
            </div>
        </div>
    )
}

export const InfoForm = () => {
    const [isLoading, setIsLoading] = useState(false)

    const formik = useFormik({
        initialValues: {
            full_name: '',
            email: '',
            product: '',
            issue: '',
        },
        validationSchema: Yup.object({
            email: Yup.string().email('Invalid email address').required('Email is required'),
            full_name: Yup.string().required('Full name is required'),
            product: Yup.string().required('Product is required'),
            issue: Yup.string().required('Issue is required'),
        }),
        onSubmit: async (values: any) => {
            setIsLoading(true)
            const data = await fetch('/mail/refund', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(values),
            })
            if (data.success == true) {
                setIsLoading(false)
                toast.success('Your refund request has been submitted')
                formik.setValues({
                    full_name: '',
                    product: '',
                    email: '',
                    issue: '',
                })
                formik.setTouched({
                    full_name: false,
                    product: false,
                    email: false,
                    issue: false,
                })
            } else {
                setIsLoading(false)
                toast.error('An error occurred while submitting your form')
            }
        },
        initialErrors: {
            full_name: '',
            product: '',
            email: '',
            issue: '',
        },
    })

    const nameRef = useRef<HTMLInputElement>(null)
    const emailRef = useRef<HTMLInputElement>(null)
    const productRef = useRef<HTMLInputElement>(null)
    const issueRef = useRef<HTMLInputElement>(null)

    return (
        <form
            className="grid h-fit w-full max-w-[1200px] grid-cols-2 place-content-start gap-6 font-sans max-lg:flex max-lg:flex-col"
            onSubmit={formik.handleSubmit}>
            <h1 className="col-span-2 w-full self-end text-start font-manrope text-b3 font-semibold">
                Fill this Information
            </h1>
            <TextBox
                error={formik.errors.full_name}
                innerRef={nameRef}
                formik={formik}
                value={formik.values.full_name}
                onChange={formik.handleChange}
                id="full_name"
                placeholder="e.g John Smith"
                label="Enter Full Name"
                Icon={<ProfileBlack />}
            />
            <TextBox
                error={formik.errors.email}
                innerRef={emailRef}
                formik={formik}
                value={formik.values.email}
                onChange={formik.handleChange}
                id="email"
                placeholder="e.g <EMAIL>"
                label="Email"
                Icon={<Email />}
            />
            <TextBox
                error={formik.errors.product}
                innerRef={productRef}
                formik={formik}
                value={formik.values.product}
                onChange={formik.handleChange}
                id="product"
                placeholder="e.g Web3 Masterclass Course"
                label="Product to refund"
                Icon={<Activity />}
            />
            <TextBox
                type="text"
                className="col-span-2"
                error={formik.errors.issue}
                innerRef={issueRef}
                value={formik.values.issue}
                onChange={formik.handleChange}
                formik={formik}
                id="issue"
                label="Issue"
                placeholder="For eg: Not able to access the course i paid for.. etc."
            />

            <span className="pr-[50%]">
                <Button variant="primary" rounded type="submit" disabled={formik.isSubmitting || !formik.isValid}>
                    {isLoading && <ButtonSpinner />}Submit
                </Button>
            </span>
        </form>
    )
}

export default InfoForm
