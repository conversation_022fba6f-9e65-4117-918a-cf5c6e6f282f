import React, { useEffect, useRef } from 'react'
import starRatings from '../../../../public/icons/starRatings.svg'
import Image from 'next/image'
import TestimonialInfo from '@/config/testimonials'


interface Props {
    scrollValue: number
    flag: any
}

const StudentTestimonies: React.FC<Props> = ({ scrollValue, flag }) => {
   
    const rowContainer = useRef<HTMLDivElement>(null)
    useEffect(() => {
        if (rowContainer.current) {
            rowContainer.current.scrollLeft = scrollValue
        }
    }, [scrollValue])

 
  return (
      <div
          className={`my-4 flex w-full items-center gap-6 scroll-smooth ${
              flag ? 'scrollbar-none overflow-x-scroll' : 'flex-wrap overflow-x-hidden'
          }`}
          ref={rowContainer}>
          {TestimonialInfo.map((user, index) => (
              <div key={index} className="h-[250px] w-full sm:h-full">
                  <div className="grid h-[246px] w-[383px] rounded-[8px] border-[1px] p-4 md:h-[262px] md:w-[650px] ">
                      <div className="flex justify-between">
                          <section className="flex items-center gap-3">
                              {user.img ? (
                                  <Image
                                      src={user.img}
                                      alt="video"
                                      width={38}
                                      height={38}
                                      className="h-[38px] w-[38px] rounded-full md:h-[58px] md:w-[58px]"
                                  />
                              ) : (
                                  <div className="flex h-[53px] w-[53px] items-center justify-center rounded-full bg-blue/50 font-semibold">
                                      {user.initials}
                                  </div>
                              )}
                              <div className="text-center ">
                                  <h2 className="text-[18px] font-[500]">{user.name}</h2>
                                  <h3 className="text-[14px] font-[400] text-[#5B5B5B]">April 01, 2024</h3>
                              </div>
                          </section>
                          <Image
                              src={starRatings}
                              alt="video"
                              width={132.02}
                              height={24.75}
                              className="hidden rounded-[4.0px] md:block"
                          />
                      </div>
                      <p className="mt-1 text-[12px] font-[400] md:mt-4 md:text-[14px]">{user.description}</p>
                  </div>
              </div>
          ))}
      </div>
  )
}

export default StudentTestimonies
