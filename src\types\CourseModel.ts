export interface CourseModel {
    id: number
    name: string
    slug: string
    short_description: string
    type: string
    tax: number
    description: string
    priceWithoutTax: number
    material: string
    image: string
    sale: number | null
    price: string | null
    status: string
    rewardable: boolean
    instructor_id: number
    txHash: string | null
    bannerImage: string | null
    created_at: string
    updated_at: string
    lessonsCount: number
    nbOfVideos: number
    finalPrice: number
    key_points: { title: string }[]
    course_lessons: [
        {
            description: string
            id: number
            name: string
            rank: number
            video_link: string
            quizzes: [
                {
                    id: number
                    question: string
                    choices: string[]
                    answer: string
                    lesson_id: number | null
                    course_id: number | null
                    created_at: Date
                    updated_at: Date
                    archived_course_id: number | null
                }
            ]
            course_topics: [
                {
                    id: number
                    name: string
                    rank: number
                    video_link: string
                    description: string
                    isCompleted: boolean
                }
            ]
        }
    ]
}

export interface CoursesSlugs {
    slugs: string[]
    success: boolean
    statusCode: number
}

export interface CoursesResponseModel {
    popularCourses: CourseModel[]
    success: boolean
    statusCode: number
}

export interface CourseModelV2 {
    id: number
    name: string
    slug: string
    short_description: string
    type: string
    description: string
    material: string
    image: string
    sale: number | null
    price: string | null
    status: string
    rewardable: boolean
    instructor_id: number
    txHash: string | null
    bannerImage: string | null
    created_at: string
    updated_at: string
    isEnrolled: boolean
    notStarted: boolean
    videosCount: number
    lessonsCount: number
    userWatchedVideosCount: number
}

export interface CourseDetailsModel {
    lessons: {
        id: number
        name: string
        description: string
        video_link: string
        rank: number
        slug: string
        course_topics: {
            id: number
            name: string
            slug: string
            rank: number
        }[]
    }[]
    count: {
        topics: number
        lessons: number
        detailed: {
            lessonId: number
            topicCount: number
        }[]
    }
}
