import axios from "@/lib/axios"
import { redirect } from "next/navigation"
import ImageShortcut from "@/components/Ui/Image"
import Artwork from "../../../../public/verify/artwork.png"

export const metadata = {
    title: 'Verify Email',
    description: 'Verify Email',
};

interface PageProps {
    searchParams: {
        email: string;
        token: string;
    }
}
type VerifyProps = {
    email: string;
    token: string;
}

async function RedirectParam(searchParams: PageProps['searchParams']) {
    const { email, token } = searchParams;
    if (!email || !token)
        redirect("/")
    else {
        try {
            await axios.get<VerifyProps>(`/auth/verify-email?email=${email}&token=${token}`)
        } catch (error: any) {
            redirect(`/verification-error?message=${error?.response?.data?.message}&success=${error?.response?.data?.success}`)
        }
    }
}

const VerifyEmail = async ({ searchParams }: PageProps) => {
    await RedirectParam(searchParams);

    return (
        <section className="container mx-auto">
            <div className="flex flex-col items-center relative mt-60 max-md:mt-28 gap-6">
                <ImageShortcut
                    src={Artwork}
                    priority
                    width={200}
                    height={200}
                    className=""
                    alt={"Verify Email"}
                />

                <div className="space-y-4">
                    <h1 className="text-h3 max-md:text-b2 text-center font-semibold text-gray-800">Verified Successfully</h1>
                    <p className="text-sub3 font-sans text-gray-600 text-center">Thank you for verifying your email. You can now leave the page.</p>
                </div>
            </div>
        </section>
    )
}

export default VerifyEmail