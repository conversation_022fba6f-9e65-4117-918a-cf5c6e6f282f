import Link from 'next/link'
import { Discord, Facebook, Telegram, Whatsapp, Youtube, Instagram, Twitter } from '@public/join'

const socials = [
    { name: 'discord', link: 'https://discord.com/invite/M9cwwCP49c', icon: Discord },
    {
        name: 'facebook',
        link: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=b65931f0b4&e=8f6f07dee9',
        icon: Facebook,
    },
    { name: 'telegram', link: 'https://t.me/thecryptou', icon: Telegram },
    { name: 'whatsApp', link: 'https://wa.me/+27657185156', icon: Whatsapp },
    { name: 'youTube', link: 'https://www.youtube.com/@thecryptouniversity', icon: Youtube },
    {
        name: 'instagram',
        link: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=e587f742d2&e=8f6f07dee9',
        icon: Instagram,
    },
    {
        name: 'twitter',
        link: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=cc7ec96606&e=8f6f07dee9',
        icon: Twitter,
    },
]

const Socials = () => {
    return (
        <section
            id="socials"
            className="container mx-auto flex flex-col gap-8 overflow-hidden pb-24 pt-[4.375rem] font-manrope max-md:pb-16 max-md:pt-[3.25rem]">
            {/* Headline */}
            <h1 className="text-headline font-medium max-md:text-b3">Crypto University Social Medias</h1>

            {/* Socials */}
            <div className="flex w-full select-none flex-wrap gap-5">
                {socials.map((social, index) => (
                    <Link aria-label={social.name + ' page'} target={'_blank'} key={index} href={social.link}>
                        <div className="transitin-[border] flex h-[180px] w-[170px] flex-col items-center justify-center gap-3 rounded-[6px] border border-[#e3e3e3] duration-150 hover:border-gray-700 max-md:h-[160px] max-md:w-[160px]">
                            <social.icon />
                            <p className="font-sans text-sub2 font-semibold capitalize">{social.name}</p>
                        </div>
                    </Link>
                ))}
            </div>
        </section>
    )
}

export default Socials
