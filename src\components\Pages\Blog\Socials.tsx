'use client'
import {
    FacebookIcon,
    TwitterIcon,
    LinkedinIcon,
    RedditIcon,
    TelegramIcon,
    FacebookShareButton,
    TwitterShareButton,
    LinkedinShareButton,
    RedditShareButton,
    TelegramShareButton,
    WhatsappIcon,
    WhatsappShareButton,
} from 'next-share'
import { usePathname } from 'next/navigation'

const Socials = () => {
    const url = usePathname()
    return (
        <div className="space-y-8">
            <p className="text-b3 font-semibold max-md:text-sub2">Share Posts</p>
            <div className="flex flex-wrap items-center gap-7">
                <FacebookShareButton
                    url={process.env.NEXT_PUBLIC_BASE_URL + url}
                    quote={'Check out the latest blog from Crypto Univeristy!'}
                    hashtag={'#cryptouniversity #blogpost'}>
                    <FacebookIcon size={30} round />
                </FacebookShareButton>

                <WhatsappShareButton
                    url={process.env.NEXT_PUBLIC_BASE_URL + url}
                    title={'Check out the latest blog from Crypto Univeristy!'}>
                    <WhatsappIcon size={30} round />
                </WhatsappShareButton>

                <TwitterShareButton
                    url={process.env.NEXT_PUBLIC_BASE_URL + url}
                    title={'Check out the latest blog from Crypto Univeristy!'}>
                    <TwitterIcon size={30} round />
                </TwitterShareButton>

                <LinkedinShareButton url={process.env.NEXT_PUBLIC_BASE_URL + url}>
                    <LinkedinIcon size={30} round />
                </LinkedinShareButton>

                <RedditShareButton
                    url={process.env.NEXT_PUBLIC_BASE_URL + url}
                    title={'Check out the latest blog from Crypto Univeristy!'}>
                    <RedditIcon size={30} round />
                </RedditShareButton>

                <TelegramShareButton
                    url={process.env.NEXT_PUBLIC_BASE_URL + url}
                    title={'Check out the latest blog from Crypto Univeristy!'}>
                    <TelegramIcon size={30} round />
                </TelegramShareButton>
            </div>
        </div>
    )
}

export default Socials
