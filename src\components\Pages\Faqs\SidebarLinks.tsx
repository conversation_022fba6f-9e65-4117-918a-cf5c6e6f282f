'use client'
import Link from 'next/link'
import { StringToID } from '@/lib/stringToId'

const SidebarLinks = ({ Links }: any) => {
    const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        e.preventDefault()
        const href = e.currentTarget.href
        const targetId = href.replace(/.*\#/, '')
        const elem = document.getElementById(targetId)
        window.scrollTo({
            top: elem?.getBoundingClientRect().top! + window.scrollY - 100,
            behavior: 'smooth',
        })
    }

    return (
        <>
            <p className="select-none text-sub3 text-gray-700">Quick Scroll</p>
            {Links?.map((link: any, index: any) => (
                <Link
                    aria-label={link.title}
                    key={index}
                    href={'/faqs/#' + StringToID(link.title)}
                    onClick={handleScroll}
                    className={'font-sans text-sub3 font-medium capitalize hover:text-blue'}>
                    {link.title}
                </Link>
            ))}
        </>
    )
}

export default SidebarLinks
