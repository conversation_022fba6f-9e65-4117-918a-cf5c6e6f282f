'use client'
import Link from 'next/link'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import { AlphaGroupCounter } from '@/components/Ui/AlphaGroupCounter'
import 'react-responsive-modal/styles.css'
import { alphaGroupBannner, alphaGroupBannerMobile, discount50 } from '@public/alpha-group'

const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault()
    const href = e.currentTarget.href
    const targetId = href.replace(/.*\#/, '')
    const elem = document.getElementById(targetId)
    window.scrollTo({
        top: elem?.getBoundingClientRect().top! + window.scrollY - 100,
        behavior: 'smooth',
    })
}
const Membership2 = ({ main }: { main?: boolean }) => {
    const [isOpen, setIsOpen] = useState(false)
    const openModal = () => {
        setIsOpen(true)
    }

    const closeModal = () => {
        setIsOpen(false)
    }
    const headerItems = [
        {
            title: 'Web3, AI, Content Creation Masterclass Courses'
        },
        {
            title: 'Alpha Trading & Alpha Stream'
        },
        {
            title: 'Weekly Live Session'
        },
        {
            title: 'Trading indicators'
        }
        ,
        {
            title: 'Live Support'
        }
    ]

    return (
        <section>
            <section>
                <div className="hidden sm:block relative px-4 pt-12" style={{ height: '580px' }}>
                    {/* Desktop View */}
                    <div className=" absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden"
                        style={{
                            backgroundImage: `url('/alpha-group/desktop-banner.png')`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                        }}
                    >
                        <div className="grid grid-cols-10">
                            <div className="col-span-7">
                                <div className=" p-6 sm:px-12 sm:mx-[9rem]">
                                    <h1 className="text-left text-b2 sm:text-[34px] font-bold mt-2">
                                        Become A <span className='text-[#009E36]'> Profitable Trader</span>, <br />
                                        With the World’s #1 Crypto Education
                                    </h1>

                                    <div className="flex justify-betwenn">
                                        <Link
                                            aria-label="View products"
                                            target='_blank'
                                            href={"https://whop.com/checkout/1eRo75cqmZz5Um5ovD-IPof-Gh1m-qj1W-Amwlo3bRnpTH/"}
                                            className="border-2 my-8 border-[#14151a] text-[#2655FF] hover:bg-yellow-600 active:bg-yellow-800 rounded-full text-sub2 sm:text-sub3 font-semibold py-3 px-8 transition-colors duration-150 inline-block"
                                        >
                                            12 Months Alpha Membership for $2,999
                                        </Link>
                                        <ImageShortcut
                                            src={discount50}
                                            width={40}
                                            height={40}
                                            alt="thumbnail"
                                            className="object-contain h-[4rem] w-[4rem] ml-2 mt-6"
                                            priority
                                        />
                                    </div>

                                    <h2 className="text-callout font-bold text-left">What’s included?</h2>

                                    <div className=" mt-6" >
                                        <div className="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                <path fill="#00BF77" d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                                <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                <path stroke="#FFF" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            </svg>
                                            <div className="mx-2">Web3, AI, Content Creation Masterclass Courses</div>
                                        </div>
                                    </div>
                                    <div className="flex justify-between  mb-2 mt-2 w-full">
                                        <div className="flex flex-col">
                                            {headerItems.slice(1, 3).map(item => (
                                                <div key={item.title} className="flex items-center mb-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                        <path fill="#00BF77" d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                                        <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                        <path stroke="#FFF" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                    </svg>
                                                    <div className="mx-2">{item.title}</div>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="flex flex-col">
                                            {headerItems.slice(3, 5).map(item => (
                                                <div key={item.title} className="flex items-center mb-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                        <path fill="#00BF77" d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                                        <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                        <path stroke="#FFF" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                    </svg>
                                                    <div className="mx-2">{item.title}</div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    <Link
                                        aria-label="View products"
                                        href={"#plans"}
                                        className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub3 font-semibold py-4 w-[24rem] px-12 mt-6 transition-colors duration-150 inline-block text-center bg-opacity-90"
                                    >
                                        Membership Plans {'>'}
                                    </Link>
                                </div>
                            </div>
                            <div className="col-span-3 mt-10">
                                <ImageShortcut
                                    src={alphaGroupBannner}
                                    width={400}
                                    height={700}
                                    alt="thumbnail"
                                    className="object-contain hidden h-auto w-700"
                                    priority
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="block sm:hidden relative px-12 pt-12" style={{ height: '880px' }}>
                    {/* Mobile View */}
                    <div className=" absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden">
                        <ImageShortcut
                            src={alphaGroupBannerMobile}
                            width={400}
                            height={700}
                            alt="thumbnail"
                            className="object-contain h-700 w-400"
                            priority
                        />
                        <div className="my-2 p-6 sm:p-12">

                            <h1 className="text-left text-b2 sm:text-sub2 font-bold mx-2">
                                Become A <span className='text-[#009E36]'> Profitable Trader</span>,
                                With the World’s #1 Crypto Education
                            </h1>

                            <Link
                                aria-label="View products"
                                target='_blank'
                                href={"https://whop.com/checkout/1eRo75cqmZz5Um5ovD-IPof-Gh1m-qj1W-Amwlo3bRnpTH/"}
                                className="border-2 my-8 border-[#2655FF] text-[#2655FF] hover:bg-yellow-600 active:bg-yellow-800 rounded-full text-sub4 sm:text-sub2 font-semibold py-3 px-8 transition-colors duration-150 inline-block"
                            >
                                12 Months Alpha Membership for $2,999
                            </Link>
                            <h2 className="text-callout font-bold text-left mb-2">What’s included?</h2>
                            <ImageShortcut
                                src={discount50}
                                width={40}
                                height={40}
                                alt="thumbnail"
                                className="absolute left-[76%] sm:left-[96%] mt-[50px] object-contain h-[4rem] w-[4rem] ml-2"
                                priority
                            />
                            <ul className="ml-4">
                                {headerItems.map(item => (
                                    <li key={item.title} className="flex items-center mb-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                            <path fill="#00BF77"
                                                d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                                            </path>
                                            <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            <path stroke="#FFF" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5"
                                                d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                        </svg>
                                        <div className="mx-2 text-sub4">{item.title}</div>
                                    </li>
                                ))}
                            </ul>
                            <Link
                                aria-label="View products"
                                href={"https://whop.com/crypto-university/?pass=prod_B4FkZoS6b6Q6X&plan=plan_WZ77hGw4gptpR"}
                                className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub3 font-semibold py-4 w-[24rem] px-12 mt-6 transition-colors duration-150 inline-block text-center bg-opacity-90"
                            >
                                Membership Plans {'>'}
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            <section className='mb-6 bg-[#F7F7FF]'>
                <div className="container mx-auto flex justify-center">
                    <AlphaGroupCounter />
                </div>
            </section>

        </section>
    )
}

export default Membership2
