'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import But<PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { Counters } from '@/components/Ui/Counters'
import HeroImage from '@public/memberships/hero.png'
import PopupVideo from '@/components/Ui/PopupVideo'
import 'react-responsive-modal/styles.css'

const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault()
    const href = e.currentTarget.href
    const targetId = href.replace(/.*\#/, '')
    const elem = document.getElementById(targetId)
    window.scrollTo({
        top: elem?.getBoundingClientRect().top! + window.scrollY - 100,
        behavior: 'smooth',
    })
}

const Heros = ({ main }: { main?: boolean }) => {
    const [isOpen, setIsOpen] = useState(false)
    const openModal = () => {
        setIsOpen(true)
    }

    const closeModal = () => {
        setIsOpen(false)
    }
    const data = [
        'Access CU Courses: Web3, Al, Content Creation Masterclass.',
        'Access CU Alpha group on Discord: Trading Ideas, Live Coaching, Whitelists.',
        'Access Indicators: Trading Signals and Bots. ',
    ]

    return (
        <div className="container mx-auto overflow-hidden">
            <div className="flex items-start justify-around gap-4 py-[108px] max-md:py-[45px]">
                <ImageShortcut
                    src={HeroImage}
                    alt={'Hero'}
                    priority
                    className={cn('max-md:hidden', main && 'max-h-[450px] object-contain')}
                />
                <div className="z-10 flex flex-col items-start gap-11">
                    <div className="items-start max-md:flex ">
                        <div className="flex flex-col gap-1 ">
                            <h1 className="text-h3 font-medium max-sm:text-b2">Crypto University Membership</h1>
                            <p className="text-headline text-blue text-center  max-sm:text-sub2">
                                {/* Your passport to the world of cryptocurrency. */}
                                Become A Profitable Trader,
                            </p>
                            <p className="text-headline text-blue text-center  max-sm:text-sub2">
                                With the World’s #1 Crypto Education
                            </p>
                        </div>
                        <ImageShortcut
                            src={HeroImage}
                            alt={'Hero'}
                            className="h-[275px] w-[275px] flex-1 object-contain max-sm:mt-[-20px] max-sm:h-[200px] max-sm:w-[200px] md:hidden"
                        />
                    </div>
                    <div className="flex flex-col gap-11 mx-8">
                        <p className="text-sub2 font-medium max-sm:text-cap2 text-center mb-0 px-6">
                            Become part of the world’s #1 cryptocurrency education,
                            <br></br> Where you’ll be guided by Crypto Millionaires, <br></br>
                            And get customized courses designed for you and your needs.
                        </p>
                        {/* {data.map((item, index) => (
                            <div key={index} className="flex items-center gap-5">
                                <div>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="23"
                                        height="22"
                                        fill="none"
                                        viewBox="0 0 23 22">
                                        <path
                                            fill="#00BF77"
                                            d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                        <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                        <path
                                            stroke="#fff"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="1.5"
                                            d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                    </svg>
                                </div>
                                <p className="text-sub2 font-medium max-sm:text-cap2">{item}</p>
                            </div>
                        ))} */}
                    </div>
                    <div className="flex w-full gap-8 max-md:flex-col max-md:gap-3  ">
                        <div className="w-[364px] max-md:w-full">
                            <Link
                                aria-label="Membership Plans"
                                onClick={e => handleScroll(e)}
                                href={'/alpha-group/#plans'}>
                                <Button variant="primary" rounded>
                                    Membership Plans{' > '}
                                </Button>
                            </Link>
                        </div>
                        <div className="w-[364px] max-md:w-full">
                            <>
                                <Button onClick={() => openModal()} variant="default" rounded>
                                    <span>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            fill="none"
                                            viewBox="0 0 24 24">
                                            <path
                                                fill="#03060E"
                                                d="M12 2a10 10 0 100 20 10 10 0 000-20zm-2 14.5v-9l6 4.5-6 4.5z"></path>
                                        </svg>
                                    </span>{' '}
                                    Watch Video
                                </Button>
                                <PopupVideo
                                    isOpen={isOpen}
                                    closeModal={closeModal}
                                    link="https://media.publit.io/file/Memberships/cu-membership-horizontal.html?player=none"
                                />
                            </>
                        </div>
                    </div>
                    <div className={cn('w-full', main && 'hidden')}>
                        <Counters />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Heros
