'use client'
import Link from 'next/link'
import ImageShortcut from '@/components/Ui/Image'

const Wallets = ({ data }: any) => {
    return (
        <div className="container mx-auto py-[50px]">
            <div className="flex flex-col gap-[100px]">
                {data.map((category: any, categoryIndex: number) => (
                    <div key={categoryIndex} className="flex flex-col gap-6">
                        <h2 className="text-headline font-medium">{category.category}</h2>
                        <div className="flex flex-wrap gap-6">
                            {category.items.map((item: any, itemIndex: number) => (
                                <Link aria-label={item.name + ' page'} target="_blank" href={item.link} key={itemIndex}>
                                    <ImageShortcut src={item.img} alt={item.name} className="h-[110px] w-[240px]" />
                                </Link>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default Wallets
