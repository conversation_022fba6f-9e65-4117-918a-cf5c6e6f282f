'use client'
import { cn } from '@/lib/cn'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

const Nav = () => {
    const currentPath = usePathname()
    return (
        <div className="container no-scrollbar max-md:overflow-x-scroll max-md:overflow-hidden mx-auto relative grid grid-cols-4 max-md:grid-cols-2 text-b3 transition-colors max-md:text-cap2 max-sm:gap-0">
            <label className={cn('flex border-b-4 border-[#E2E2E2] cursor-pointer items-center justify-center gap-2 pb-4 pl-4', '')}>
                <Link href={"/dashboard/settings"} className="select-none font-semibold">Account Details</Link>
            </label>
            <label className="flex border-b-4  border-[#E2E2E2] cursor-pointer items-center justify-center gap-2 pb-4 ">
                <Link href={"/dashboard/settings/order-history"} className="select-none font-semibold">Order History</Link>
                 {/* <Link href={"/dashboard/settings/memberships"} className="select-none font-semibold">Memberships</Link> */}
            </label>
            <span
                className={cn(
                    'absolute -bottom-[0px] left-4 col-span-4 max-md:col-span-2 block h-1 w-1/4 max-md:w-1/2 bg-blue transition-all duration-300',
                    currentPath == '/dashboard/settings'
                        ? 'translate-x-0'
                        // : currentPath == '/dashboard/settings/memberships'
                        // ? 'translate-x-[100%]'
                        // : ''
                        : currentPath == '/dashboard/settings/order-history'
                        ? 'translate-x-[100%]'
                        : ''
                )}
            />
        </div>
    )
}

export default Nav
