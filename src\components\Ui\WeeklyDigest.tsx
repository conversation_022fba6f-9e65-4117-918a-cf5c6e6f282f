import Link from 'next/link'
import ImageShortcut from './Image'
import Weekly from '@public/global/weekly.png'

const WeeklyDigest = () => {
    return (
        <section id='digest' className="relative overflow-hidden bg-[#0B0E11] py-7 text-white max-md:pb-8 max-md:pt-14">
            <div className="container mx-auto flex justify-between">
                <div className="z-[2] flex flex-col justify-center gap-9">
                    <div className="space-y-6">
                        <h4 className="text-headline font-semibold max-md:text-b3">Weekly Digest</h4>
                        <p className="max-w-[549px] text-sub3 font-medium leading-[28px] max-md:max-w-[300px] max-md:text-[14px]">
                            Sign up to our newsletter to receive crypto news, events, community updates & MORE.
                        </p>
                    </div>
                    <div>
                        <Link
                            aria-label="Subscribe Now"
                            href="/newsletter"
                            className="text-sub2 text-yellow underline underline-offset-2">
                            Subscribe Now
                        </Link>
                    </div>
                </div>
                <ImageShortcut
                    src={Weekly}
                    alt="Crypto University"
                    className="h-auto w-[359px] object-contain max-md:absolute max-md:-right-20 max-md:top-0 max-md:h-full max-md:w-auto max-md:opacity-20"
                />
            </div>
        </section>
    )
}

export default WeeklyDigest
