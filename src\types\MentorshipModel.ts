export interface MentorshipModel {
    id: number;
    name: string;
    slug: string;
    short_description: string;
    description: string;
    image: string;
    category: string;
    status: string;
    price: number;
    coaching: boolean;
    instructor_id: number;
    sale: number;
    tax: number;
    created_at: string;
    updated_at: string;
    transactions: Transactions[]
}

interface Transactions {
    created_at: string;
}