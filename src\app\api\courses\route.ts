import fetchInstance from '@/lib/fetch';
import { CoursesResponseModel } from '@/types/CourseModel';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
    const cacheControlHeaders = new Headers(request.headers);
    cacheControlHeaders.set('Vercel-CDN-Cache-Control', 'max-age=3600');
    cacheControlHeaders.set('CDN-Cache-Control', 'max-age=60');
    cacheControlHeaders.set('Cache-Control', 'max-age=10');

    
    try {
        const response = await fetchInstance("/website-home-page/top-courses", { next: { revalidate: 60 } }) as CoursesResponseModel;
        return NextResponse.json(response.popularCourses, { headers: cacheControlHeaders });
    } catch (error) {
        console.error("Error:", error);
        return error;
    }
}