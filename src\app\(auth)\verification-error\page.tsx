import ImageShortcut from "@/components/Ui/Image"
import { redirect } from "next/navigation";
import Artwork from "../../../../public/verify/error-artwork.png"

export const metadata = {
    title: 'Verify Email',
    description: 'Verify Email',
};

interface PageProps {
    searchParams: {
        message: string;
        success: boolean;
    }
}

async function RedirectParam(searchParams: PageProps['searchParams']) {
    const { message, success } = searchParams;
    if (!message || !success)
        redirect("/")
}

const VerificationErrorPage = async ({ searchParams }: PageProps) => {
    await RedirectParam(searchParams);
    const { message } = searchParams;

    return (
        <section className="container mx-auto">
            <div className="flex flex-col items-center relative mt-60 max-md:mt-28 gap-6">
                <ImageShortcut
                    src={Artwork}
                    width={200}
                    height={200}
                    className=""
                    priority
                    alt={"Verify Email Error"}
                />

                <div className="space-y-4">
                    <h1 className="text-h3 max-md:text-b2 text-center font-semibold text-gray-800">Verification Error</h1>
                    <p className="text-sub3 font-sans text-gray-600 text-center">{message}</p>
                </div>
            </div>
        </section>
    )
}

export default VerificationErrorPage