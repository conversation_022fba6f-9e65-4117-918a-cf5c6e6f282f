import AcademyPage from '@/app/templates/AcademyPage';
import EventPage from '@/app/templates/EventPage'
import { getPageData } from '@/lib/pageData';

interface GeneratedPageProps {
    params: { slug: string }
}

export default async function GeneratedPage({ params }: GeneratedPageProps) {
    const { slug } = params;
    const pageData = await getPageData(slug);

    if (!pageData) {
        return <div>Page not found</div>;
    }

    const { pageType, pageData: templateData } = pageData;

    if (pageType === 'event') {
        return <EventPage {...templateData} />;
    } else if (pageType === 'academy') {
        return <AcademyPage {...templateData} />;
    }

    return <div>Invalid page type</div>;
}
