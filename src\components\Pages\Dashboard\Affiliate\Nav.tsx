'use client'
import { cn } from '@/lib/cn'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

const Nav = () => {
    const currentPath = usePathname()

    return (
        <>
        <div className="text-sm font-medium text-center text-blue border-b border-blue">
          <ul className="flex flex-wrap -mb-px">
            <li className="me-2">
              <Link href="/dashboard/affiliate" className={`inline-block p-4 text-blue-primary border-b-2 border-blue rounded-t-lg ${currentPath == '/dashboard/affiliate' && 'underline'} `}>
                Dashboard
              </Link>
            </li>
            <li className="me-2">
              <Link href="/dashboard/affiliate/referrals" className={`inline-block p-4 text-blue border-b-2 border-blue rounded-t-lg ${currentPath == '/dashboard/affiliate/referrals' && 'underline'} `}>
                Referral
              </Link>
            </li>
            <li className="me-2">
              <Link href="/dashboard/affiliate/payouts" className={`inline-block p-4 text-blue-primary border-b-2 border-blue rounded-t-lg ${currentPath == '/dashboard/affiliate/payouts' && 'underline'} `}>
                Payout
              </Link>
            </li>
          </ul>
        </div>
        <div className="border-b-4 border-[#E2E2E2] -mt-1" />
      </>
        // <div className="container no-scrollbar max-md:overflow-x-scroll max-md:overflow-hidden mx-auto relative grid grid-cols-4 max-md:grid-cols-2 text-b3 transition-colors max-md:text-cap2 max-sm:gap-0">
        //     <label className={cn('flex border-b-4 border-[#E2E2E2] cursor-pointer items-center justify-center gap-2 pb-4 pl-4', '')}>
        //         <Link href={"/dashboard/affiliate"} className="select-none font-semibold">Dashboard</Link>
        //     </label>
        //     <label className="flex border-b-4  border-[#E2E2E2] cursor-pointer items-center justify-center gap-2 pb-4 ">
        //         <Link href={"/dashboard/affiliate/referrals"} className="select-none font-semibold">Referrals</Link>
        //     </label>
        //     <label className="flex border-b-4  border-[#E2E2E2] cursor-pointer items-center justify-center gap-2 pb-4 ">
        //         <Link href={"/dashboard/affiliate/payouts"} className="select-none font-semibold">Payouts</Link>
        //     </label>
        //     <span
        //             className={cn(
        //                 'absolute -bottom-1 left-0 col-span-2 block h-1 w-[40%] bg-blue transition-all duration-300',
        //                 currentPath == '/dashboard/affiliate'
        //                     ? 'translate-x-0'
        //                     : currentPath == '/dashboard/affiliate/referrals'
        //                     ? 'translate-x-[50%]'
        //                     : currentPath == '/dashboard/affiliate/payouts'
        //                     ? 'translate-x-[100%]'
        //                     : ''
        //             )}
        //         />

        // </div>
    )
}

export default Nav
