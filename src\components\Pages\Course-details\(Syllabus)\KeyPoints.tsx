interface props {
    points: { title: string }[]
}

const CheckIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20">
        <g clipPath="url(#clip0_13_135)">
            <rect width="20" height="20" fill="#E9EEFF" rx="6"></rect>
            <path
                fillRule="evenodd"
                stroke="#E9EEFF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M14.334.75H5.665C2.645.75.75 2.89.75 5.916v8.168c0 3.027 1.885 5.166 4.915 5.166h8.668c3.031 0 4.917-2.139 4.917-5.166V5.916C19.25 2.89 17.364.75 14.334.75z"
                clipRule="evenodd"></path>
            <path
                stroke="#2655FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M6.44 10l2.374 2.373 4.746-4.746"></path>
        </g>
        <defs>
            <clipPath id="clip0_13_135">
                <rect width="20" height="20" fill="#fff" rx="6"></rect>
            </clipPath>
        </defs>
    </svg>
)

const KeyPoints = ({ points }: props) => {
    if (points.length == 0) return <></>
    return (
        <div id="keypoint" className="flex flex-col gap-8 pr-11 max-md:container max-md:mx-auto">
            <h1 className="text-b3 max-md:text-sub1">Key Points</h1>
            <div className="grid grid-cols-2 gap-6 max-md:grid-cols-1 max-md:gap-4">
                {points.map((point, index) => (
                    <div key={index} className="flex items-center gap-4">
                        <CheckIcon />
                        <h3 className="max-w-[340px] font-sans text-sub2 capitalize max-md:text-cap1">{point.title}</h3>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default KeyPoints
