import Link from 'next/link'
import Image from '@/components/Ui/Image'
import { IndicatorModel } from '@/types/IndicatorModel'
import Current from './Current'

interface IndicatorCardProps {
    indicator: IndicatorModel,
    link: string  // Add link here
}

const IndicatorCard = ({ indicator,link }: IndicatorCardProps) => {
    return (
        <div className="flex max-w-[400px] flex-grow flex-col justify-between space-y-6 rounded-[0.5rem] border border-gray-700 p-4 max-md:max-w-full max-md:space-y-3 max-md:p-[0.875rem]">
            <div>
                <Image
                    src={indicator.image}
                    width={207}
                    height={240}
                    className="h-[240px] w-auto max-md:h-[154px]"
                    alt={indicator.title}
                />
            </div>

            <div className="flex flex-1 flex-col gap-4">
                {/* Content */}
                <div className="flex flex-grow flex-col justify-between gap-4 max-md:gap-2">
                    <div className="flex flex-col gap-4 max-md:gap-2">
                        <p className="text-b3 font-medium capitalize max-md:text-sub2">{indicator.title}</p>
                        <p className="text-cap text-sub3 max-md:text-cap1">{indicator.short_description}</p>
                    </div>
                </div>
                <div className="flex flex-col justify-center gap-4 max-md:gap-2">
                    <Link
                        aria-label="Learn More"
                        href={`/${link}`}
                        className="text-cap1 text-center justify-center font-semibold underline-offset-4 focus:underline active:underline">
                        Learn More &gt;
                    </Link>
                </div>

                {/* Price */}
                <div className="flex items-center justify-between">
                    <div className="flex cursor-default flex-col gap-[0.125rem] font-sans max-md:flex-row-reverse max-md:items-center max-md:gap-1">
                        {indicator.sale ? (
                            <>
                                <h5 className="flex items-center gap-1 text-callout font-medium max-md:text-sub2">
                                    ${indicator.finalPrice - indicator.tax}{' '}
                                    <span className="text-cap1 text-green-dark max-md:text-cap3">
                                        (-{indicator.sale}%)
                                    </span>
                                </h5>
                                <p className="text-sub2 text-gray-900 line-through max-md:text-sub3">
                                    ${parseInt(indicator.price || '0') - indicator.tax}
                                </p>
                            </>
                        ) : (
                            <span className="text-callout font-medium max-md:text-sub2">
                                ${indicator.finalPrice - indicator.tax}
                            </span>
                        )}
                    </div>



                    <Current {...indicator} />
                </div>
            </div>
        </div>
    )
}

export default IndicatorCard
