import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import GetInTouch1 from '@public/memberships/question1.png'
import GetInTouch2 from '@public/memberships/question2.png'

const Question = () => {
    return (
        <div className="container mx-auto overflow-hidden py-14">
            <div className="flex flex-wrap items-center justify-between max-md:flex-col max-md:justify-center">
                <ImageShortcut
                    src={GetInTouch1}
                    alt={'Call Illustration'}
                    className="max-lg:w-[200px] max-md:w-[110px]"
                />
                <div className="flex flex-col items-start gap-9 max-md:items-center max-md:gap-4">
                    <h1 className="text-h3 font-medium max-md:text-sub2">Have Questions?</h1>
                    <Link aria-label="Get in Touch" href="/get-in-touch" className="w-[274px]">
                        <Button variant="primary" rounded>
                            Get in Touch {'>'}
                        </Button>
                    </Link>
                </div>
                <ImageShortcut
                    src={GetInTouch2}
                    alt={'Trophies Illustration'}
                    className="max-lg:w-[200px] max-md:hidden"
                />
            </div>
        </div>
    )
}

export default Question
