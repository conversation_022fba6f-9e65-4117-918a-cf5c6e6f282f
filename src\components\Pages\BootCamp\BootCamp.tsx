import ImageShortcut from "@/components/Ui/Image"
import Link from "next/link"

const BootCamp = () => {
    const headerItems = [
        {
            item: "Day Trading"
        },
        {
            item: "Free Signals"
        },
        {
            item: "Free Strategies"
        }
    ]

    const includes = [
        {
            icon: "/bootcamp/Zoom-Call.png",
            title: "Interactive Zoom Sessions in 30 Days:",
            description: "Experience real-time learning and dynamic discussions with our expert instructors through live Zoom sessions. "
        },
        {
            icon: "/bootcamp/Free-Signals.png",
            title: "Free Signals:",
            description: "Gain access to exclusive trading signals generated by our team of experts."
        },
        {
            icon: "/bootcamp/Direct-Support.png",
            title: "Direct Support:",
            description: "Get your queries addressed promptly, seek guidance when needed."
        }
    ]

    const testimonials = [
        {
            image: "/bootcamp/Profile-picture.jpg",
            name: "Nelz Ngobi - Mar 24, 2023",
            description: "I\'m satisfied with the results I\'m getting from the boot camp wish I was a beneficiary I\'m happy, am learning artificial intelligence through the crypto University scholarship scheme. I\'m so happy is an opportunity and I'm taking every step to learn more thank you mister grey!"
        },
        {
            image: "/bootcamp/Profile-picture2.jpg",
            name: "Felistus S - May 20, 2023",
            description: "It\'s a splendid experience for me, the course videos are so clear and highly informative. Loving every session, it\'s only been a week so far but I am already starting to feel more confident in my crypto analysis game. Thank you very much for this wonderful opportunity."
        },
        {
            image: "/bootcamp/Profile-picture3.jpg",
            name: "Lonjezo zimba - Apr 20, 2023",
            description: "Well i just like to thank the people who organized all of this cause I\'ve had a great experience so far and I\'ve learnt a lot from crypto university they have really opened my mind and given me hope that In this journey i can really do something productive and am loving everything in it....am really looking forward to learn more with you guys..."
        }
    ];

    return (
        <>
            <section>
                <div className="relative overflow-hidden bg-cover bg-no-repeat p-12"
                    style={{
                        backgroundImage: `url('/bootcamp/Banner-updated-mobile.jpg')`,
                        height: '750px'
                    }}>
                    <div className="absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden bg-fixed"
                        style={{
                            backgroundColor: 'rgba(0, 0, 0, 0.6)'
                        }}>
                        <div className="my-12 p-12 sm:p-36">
                            <div>
                                <h1 className="text-left text-b2 sm:text-h2 font-bold mt-10 text-white">The Crypto Trading <br /> Bootcamp
                                </h1>
                                <p className="text-lg my-4 text-white font-semibold">4 Weeks Of Trading With The Pros. Become a full
                                    time trader.
                                </p>
                                <div className="grid grid-cols-1 sm:grid-cols-3 my-4 text-white text-lg font-semibold">
                                    {headerItems.map(item => (
                                        <div key={item.item} >
                                            <div className="flex">
                                                <div className="flex-none w-9 h-14">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="23" fill="none"
                                                        viewBox="0 0 23 22">
                                                        <path fill="#E9B824"
                                                            d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                                                        </path>
                                                        <path fill="#E9B824" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                        <path stroke="#2E4374" strokeLinecap="round" strokeLinejoin="round"
                                                            strokeWidth="1.5" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                    </svg>
                                                </div>
                                                <div className="flex-initial w-90">
                                                    {item.item}
                                                </div>
                                            </div>
                                        </div>
                                    ))}

                                </div>
                                <div>                 <Link
                                    aria-label="View products"
                                    href={"/checkout/bootcamp/the-crypto-trading-bootcamp"}
                                    className="bg-[#00ce63] mb-4  hover:bg-[#00a043]  text-white active:bg-[#017933] rounded-lg text-sub2 font-semibold py-3 px-12 mt-6 transition-colors duration-150"
                                >
                                    Apply Now
                                </Link>
                                </div>

                                <div> <h1 className="text-white text-b1 mt-10 font-semibold">Tuition: $5000</h1></div>
                                <div className="my-6">
                                    <ImageShortcut
                                        src={'/bootcamp/Numbers-strip.png'}
                                        width={500}
                                        height={25}
                                        alt="thumbnail"
                                        className="object-cover h-25 w-500"
                                        priority
                                    />
                                </div>
                                <p className="my-10 text-white text-md">Designed for All Skill Levels. Intensive Training with
                                    Experts. Monthly Cohort.</p>
                            </div>
                        </div>
                    </div>
                </div>

            </section>

            <section className="mb-4">
                <div>
                    <h1 className="text-center mt-10 text-headline font-bold">Introduction Video</h1>
                </div>
                <div className="flex justify-center rounded mt-10 p-6">
                    <iframe className="rounded" width="720" height="500" src="https://www.youtube.com/embed/ZlQ_LlgHYvk" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                </div>
            </section>
            <section className="px-12 py-2 sm:py-12">
                <div >
                    <h1 className="text-b3 sm:text-headline font-bold text-left my-2 sm:my-16">What’s Included?</h1>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        {includes.map(item => (
                            <div key={item.title} className="p-4">
                                <ImageShortcut
                                    src={item.icon}
                                    width={20}
                                    height={35}
                                    alt="thumbnail"
                                    className="object-cover h-35 w-20"
                                    priority
                                />
                                <h2 className="text-heading my-4 font-semibold">{item.title}</h2>
                                <p>{item.description}</p>
                            </div>
                        ))}
                    </div>
                    <h1 className="text-b3 sm:text-headline font-bold text-left my-2 sm:my-12">Bootcamp Admission Process</h1>
                    <h2 className="text-heading font-bold text-left pb-4">Step 1 – Registration</h2>
                    <div className="my-4">
                        <ul>
                            <li>Click on the <a href="/checkout/bootcamp/the-crypto-trading-bootcamp" className="text-blue">Register Now</a> button to access the application form.</li>
                        </ul>
                    </div>
                    <div className="my-6"><Link
                        aria-label="View products"
                        href={"/checkout/bootcamp/the-crypto-trading-bootcamp"}
                        className="bg-blue mb-4  hover:bg-[#5462dd]  text-white active:bg-blue rounded-lg text-cap2 sm:text-sub2 font-semibold py-3 px-6 sm:px-12 mt-6 transition-colors duration-150"
                    >
                        Register Now
                    </Link>
                    </div>
                    <h2 className="text-heading font-bold text-left pb-4 pt-4">Step 2 – Application Review and Selection</h2>
                    <ul >
                        <li>During the review process, we look for applicants who are genuinely passionate about crypto trading
                            and commit to participate in the bootcamp assignments.</li>
                    </ul>
                    <h2 className="text-heading font-bold text-left pb-4 pt-4">Step 3 – Admission and Enrollment</h2>
                    <ul>
                        <li>Upon completion of the application review process, successful candidates will be notified of their
                            admission to The Crypto Trading Bootcamp via email which contain important enrollment details.</li>
                    </ul>
                </div>
            </section>

            <section className="bg-slate-900 p-12 ">
                <h1 className="text-b3 sm:text-h2 font-bold text text-white text-center my-4">Testimonials</h1>
                <p className="text-white text-center">Hear What Our Users & Traders Have to Say</p>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-12 mt-10 mx-10">
                    {testimonials.map(testimony => (
                        <div key={testimony.name} className="bg-[#00394F] rounded p-4">
                            <div className="flex justify-center">

                                <ImageShortcut
                                    src={testimony.image}
                                    width={100}
                                    height={100}
                                    alt="thumbnail"
                                    className="rounded-full w-100 h-100"
                                    priority
                                />

                            </div>
                            <p className="text-white my-4">{testimony.description}</p>
                            <p className="text-green text-lg font-semibold my-4">{testimony.name}</p>
                        </div>
                    ))}

                </div>
                <div className="flex justify-center mt-4">

                    <ImageShortcut
                        src={'/bootcamp/dev-academy-trustpilot-white.png'}
                        width={160}
                        height={50}
                        alt="thumbnail"
                        className="object-cover h-50 w-160"
                        priority
                    />
                </div>
            </section>
        </>
    )
}

export default BootCamp
