import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import HelloZambia from '@/components/Pages/ZambiaPage/HelloZambia'
import HowTo from '@/components/Pages/ZambiaPage/HowTo'

export const metadata = {
    title: 'Join Web3 Developer Program - The Crypto University',
    description:
        'Join Web3 Developer Program to multiply your salary to embrace the future of blockchain technology',
    keywords: ['ICP Web3 Developer', 'Developer', 'Web3 Developer', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
    openGraph: {
        images: 'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1706973238/dev_program_dr5q5k.jpg',
      },
}


const ZambiaWeb3Page = () => {
    return (
        <section className="flex w-full flex-col">
            <HelloZambia />
            <HowTo/>
             <div className='border-b-2 border-[#E2E2E2] my-2 pr-4' />
            <section id='question' className="container mx-auto pb-[20px] max-md:py-9">
                <div className="flex- flex items-center gap-10 max-md:flex-col max-md:items-start max-md:gap-0">
                    <ImageShortcut
                        src={'/join/contact.png'}
                        width={280}
                        height={280}
                        className={'max-md:hidden max-md:h-auto max-md:w-[190px]'}
                        alt={'Contact Icon'}
                    />
                    <div className="flex flex-col gap-8 max-md:gap-4">
                        <div className="flex flex-col gap-2 max-md:gap-[0.375rem]">
                            <p className="text-headline font-semibold max-md:text-b3">Have Any Questions?</p>
                        </div>
                        <div className="flex flex-col gap-5">
                            <div className="w-[310px] max-md:w-[220px]">
                                <Button gradient variant="primary" rounded>
                                    <Link
                                        href={'mailto:<EMAIL>'}
                                        aria-label="Email crypto university">
                                        Help {'>'}
                                    </Link>
                                </Button>
                            </div>
                            <div className="flex flex-wrap gap-1 text-callout max-md:flex-col max-md:gap-2 max-md:text-sub3">
                                <p>OR Shoot us an email on:</p>{' '}
                                <span className="text-blue">
                                    {' '}
                                    <Link
                                        aria-label="Email crypto university"
                                        href={'mailto:<EMAIL>'}>
                                        <EMAIL>
                                    </Link>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </section>
    )
}

export default ZambiaWeb3Page
