import Link from 'next/link'
import ImageShortcut from '@/components/Ui/Image'
import DiscordBanner from '@public/memberships/discord-banner.png'

const Discord = () => {
    return (
        <div className="relative z-[1] overflow-hidden bg-black py-14 text-white max-md:py-20">
            {/* OG Image */}
            <ImageShortcut
                src={DiscordBanner}
                alt="Discord Banner"
                className="absolute inset-0 z-[1] h-full w-full object-cover object-center"
            />

            {/* Overlay */}
            <div className="banner z-[2]" />

            {/* Content */}
            <div className="container mx-auto flex flex-wrap items-center justify-center gap-[3.75rem] max-lg:flex-col-reverse max-lg:gap-6">
                <div className="z-[3] min-w-[300px] max-w-[396px] flex-1 max-lg:w-full">
                    <iframe
                        loading="lazy"
                        src="https://discord.com/widget?id=750800463393456261&amp;theme=dark"
                        width="100%"
                        height="350"
                        className="w-full"
                        sandbox="allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts"
                    />
                </div>

                <div className="z-[3] flex flex-col gap-9 max-lg:gap-0">
                    <div className="space-y-2">
                        <h2 className="text-headline font-semibold leading-[2.625rem] max-lg:max-w-[300px] max-lg:text-center max-md:text-b2">
                            Join CU Discord Community!
                        </h2>
                        <p className="text-sub3 leading-[1.8rem] max-lg:max-w-[300px] max-lg:text-center max-md:text-[14px] max-md:leading-relaxed">
                            <strong>6.5K+ members</strong> | Join weekly public events | Explore job opportunities
                        </p>
                    </div>

                    <div>
                        <Link
                            aria-label="Join CU Discord Community"
                            href={'https://discord.com/invite/M9cwwCP49c'}
                            target="_blank"
                            className="rounded-lg bg-[#00B24A] px-14 py-3 text-sub3 font-semibold shadow-lg transition-colors duration-150 hover:bg-[#00a043] active:bg-[#017933] max-lg:hidden">
                            Join Now
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Discord
