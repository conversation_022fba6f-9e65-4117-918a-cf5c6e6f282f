import { NextResponse } from 'next/server'

export async function GET() {
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.WHOP_BEARER}`,
        },
    }

    const response = await (
        await fetch((process.env.WHOP_PRODUCT_API as string) + 'prod_Sm0V45LhwLGzT?expand=plans', options)
    ).json()

    return NextResponse.json(
        {
            title: {
                text: 'The Ultimate Learning Experience.',
                color: 'black',
            },
            sub: {
                text: response.name,
                color: 'black',
            },
            price: {
                // text: response.plans[0].renewal_price,
                text: "5000",
                color: 'black',
            },
            icon: {
                src: '/memberships/card3.png',
                alt: 'Supreme Membership Icon',
            },
            cards: {
                text: [
                    'Web3, AI, Content Creation Masterclass',
                    'Weekly Live Session',
                    'Live Support',
                    'Alpha Trading',
                    'Alpha Stream',
                    'Trading indicators',
                    'Day-to-day Live Session',                    
                    'Unlimited Live Support',
                    'VIP Stream',                    
                    'VIP Trading',                    
                    'VIP Q&A',
                ],
                color: 'black',
            },
            plans: response.plans[0].direct_link,
        },
        { status: 200 }
    )
}
