import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import { CGU, CoinW, Celo, Nexo, Noones, Re<PERSON><PERSON>, <PERSON>pto, Unstoppable, Bybit, <PERSON><PERSON><PERSON>, Binance, <PERSON><PERSON> } from '@public/home/<USER>'
import Link from 'next/link'
const partners = [
    {
        url: "https://cgu.io",
        name: 'CG<PERSON>',
        logo: CGU,
        width: 114,
        height: 44,
        position: 0
    },
    {
        url: "https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US",
        name: 'Coin W',
        logo: CoinW,
        width: 159,
        height: 48,
        position: 1
    },
    {
        url: "https://celo.org/",
        name: '<PERSON><PERSON>',
        logo: <PERSON><PERSON>,
        width: 148,
        height: 52,
        position: 2
    },

    {
        url: "https://noones.com/?r=ninja265",
        name: 'Noones',
        logo: Noones,
        width: 158.87,
        height: 56,
        position: 3
    },

    {
        url: "https://www.youtube.com/watch?v=Yw-CC4Z4X6A&ab_channel=CryptoHustle",
        name: '<PERSON><PERSON><PERSON> Hust<PERSON>',
        logo: Crypto,
        width: 80,
        height: 83.55,
        position: 4
    },
    {
        url: "https://unstoppabledomains.com/r/885a58b434d845c",
        name: 'Unstoppable Domains',
        logo: Unstoppable,
        width: 192.66,
        height: 56,
        position: 5
    },
    {
        url: "https://bit.ly/3y2wOgg",
        name: 'Bybit',
        logo: Bybit,
        width: 158.87,
        height: 56,
        position: 6
    },
    {
        url: "https://3commas.io/?c=MOON2025",
        name: '3commas',
        logo: Commas,
        width: 190.64,
        height: 67.2,
        position: 7
    },
    {
        url: "https://bit.ly/3JVH7Zq",
        name: 'Binance',
        logo: Binance,
        width: 190.64,
        height: 67.2,
        position: 8
    },
    {
        url: "https://yolo.io",
        name: 'Yolo Investments',
        logo: Yolo,
        width: 131.14,
        height: 67.2,
        position: 9
    }
]

const Partner = ({ name, logo, position, width, height, url }: { name: string, logo: any, position: number, width: number, height: number, url: string }) => {
    return (
        <div className={cn(
            'flex items-center border-b justify-center border-gray-700 border-dashed',
            position == 4 || position == 9 ? "border-r-0" : "border-r",
            position > 4 && "border-b-0 max-md:border-b",
            (position + 1) % 2 == 0 && "max-md:border-r-0",
            (position == 8 || position == 9) && "max-md:border-b-0"
        )}>
            <div className='my-9 max-md:my-4'>
            <Link href={url}  target="_blank">
                <ImageShortcut
                    src={logo}
                    width={width}
                    height={height}
                    className="w-auto"
                    alt={name}
                />
                </Link>
            </div>
        </div>
    )
}

const Partners = () => {
    return (
        <section id="partners" className="border-b text-black max-md:border-none border-gray-700 w-full py-16 max-md:pt-0 max-md:pb-20">
            <div className="container mx-auto flex flex-col gap-16 max-md:gap-6">
                {/* Title */}
                <div className="space-y-3 max-md:space-y-1">
                    <h1 className='text-h3 max-md:text-callout font-medium max-md:font-semibold'>Our Partners</h1>
                    <p className="text-callout max-md:text-cap1">We collaborate with Cryptocurrency leading institutions and companies</p>
                </div>

                {/* Partner */}
                <div>
                    <div className='grid grid-flow-col grid-cols-5 max-md:grid-cols-2 max-md:grid-flow-row'>
                        {partners.slice(0, 5).map((partner, index) => (
                            <Partner key={index} width={partner.width} height={partner.height} position={partner.position} name={partner.name} logo={partner.logo} url={partner.url} />
                        ))}
                    </div>
                    <div className='grid grid-flow-col grid-cols-5 max-md:grid-cols-2 max-md:grid-flow-row'>
                        {partners.slice(5, 10).map((partner, index) => (
                            <Partner key={index} width={partner.width} height={partner.height} position={partner.position} name={partner.name} logo={partner.logo} url={partner.url} />
                        ))}
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Partners