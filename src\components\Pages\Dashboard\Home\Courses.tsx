import { CourseModelV2 } from "@/types/CourseModel";
import CardCourse from "./CardCourse";

interface props {
  courses: CourseModelV2[];
}

const Courses = ({ courses }: props) => {
  return (
    <div className="w-full flex gap-8 px-8 max-md:px-5 items-start flex-wrap">
      {courses && courses.map((item, index) => (
        <CardCourse key={index} course={item} />
      ))}
    </div>
  );
};

export default Courses;
