import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import { CourseModelV2 } from '@/types/CourseModel'
import { GetCourses, getCourseById, GetOneCourse, getFirstLesson } from '../../../config/validationCourseDashboard'

interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}
type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
    }
}

const CoursePage = async ({ searchParams, params }: Props) => {
    // Redirect if user is not logged in
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

    // Get page and filters
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })

    const courseId = getCourseById({ courses, params })
    const course = await GetOneCourse(user,courseId?.slug ?? '')
    if (courseId === null || course === undefined) {
        redirect('/dashboard')
    } else {
        const firstLesson = getFirstLesson({ course })
        if (firstLesson) {
            const lessonName = firstLesson.name.toLowerCase().replace(/\s/g, '-')
            redirect(`/dashboard/${courseId.slug}/${lessonName}`)
        } else {
            redirect('/dashboard')
        }
    }
}

export default CoursePage
