"use client";
import { FC } from "react";
import { cn } from "@/lib/cn";
import Image from "next/image";

interface ImageShortcutProps {
  src: any;
  width?: number;
  height?: number;
  alt?: string;
  className?: string;
  priority?: boolean;
  quality?: number;
  unoptimized?: boolean;
}

const ImageShortcut: FC<ImageShortcutProps> = ({
  src,
  unoptimized,
  width,
  height,
  priority = false,
  quality,
  alt,
  className,
}) => {
  return (
    <Image
      src={src}
      width={width && width}
      height={height && height}
      quality={quality}
      priority={priority}
      className={cn(
        className,
        "select-none duration-150 transition-[opacity] opacity-0"
      )}
      alt={alt ? alt : "Unspecified image"}
      draggable={false}
      onLoadingComplete={(image) => image.classList.remove("opacity-0")}
      unoptimized={unoptimized}
    />
  );
};

export default ImageShortcut;
