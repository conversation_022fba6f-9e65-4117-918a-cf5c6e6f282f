// interface PortableTextBlock {
//     type: 'paragraph' | 'link'
//     children: PortableTextSpan[]
//     href?: string
// }
import { PortableTextBlock } from "@/lib/sanity"

interface PortableTextSpan {
    type: 'span'
    text: string
}

const transformStringToPortableText = (string: string): PortableTextBlock[] => {
    const blocks: PortableTextBlock[] = []

    const lines = string.split('\n')

    lines.forEach(line => {
        // Handle links in the string
        const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g
        let match
        let lastIndex = 0

        while ((match = linkRegex.exec(line)) !== null) {
            const linkText = match[1]
            const linkUrl = match[2]

            if (match.index > lastIndex) {
                // Add regular text before the link
                const text = line.slice(lastIndex, match.index)
                blocks.push({
                    type: 'paragraph',
                    children: [
                        {
                            type: 'span',
                            text: text,
                        },
                    ],
                    _type: "",
                    _key: ""
                })
            }

            // Add the link
            blocks.push({
                type: 'link',
                href: linkUrl,
                children: [
                    {
                        type: 'span',
                        text: linkText,
                    },
                ],
                _type: "",
                _key: ""
            })

            lastIndex = match.index + match[0].length
        }

        if (lastIndex < line.length) {
            // Add any remaining regular text after the last link
            const text = line.slice(lastIndex)
            blocks.push({
                type: 'paragraph',
                children: [
                    {
                        type: 'span',
                        text: text,
                    },
                ],
                _type: "",
                _key: ""
            })
        }
    })

    return blocks
}

const categories = {
    title: 'General',
    questions: [
        {
            title: 'How do I register for courses?',
            answer: transformStringToPortableText(
                'Please read this [guide]() or watch this  [video](https://www.youtube.com/watch?v=4P2e_dqPngs&t=193s) for step by step instructions on how to get registered at Crypto University. If you require more assistance please don’t hesitate to contact our sales and customer support at [<EMAIL>](mailto:<EMAIL>)'
            ),
        },
        {
            title: 'The courses I paid for are not working. The website says “not enrolled”. How do I get access to my courses?',
            answer: transformStringToPortableText(
                'If you have any queries regarding your course purchases from our website, please email [<EMAIL>](mailto:<EMAIL>) to resolve the issue.'
            ),
        },
        {
            title: 'I cannot log into my course. I keep getting “error” or “bad request” messages on my computer. How do I get access to my course?',
            answer: transformStringToPortableText(
                'Please watch the following [video](https://www.youtube.com/watch?v=Z3ErXE7UEj4) on how you can clear your cache. Clearing cache is a quick and easy way to free up space and (hopefully) fix a misbehaving app.'
            ),
        },
        {
            title: 'How long do I have access to my courses?',
            answer: transformStringToPortableText('If you bought any of our products, you have lifetime access to it.'),
        },
    ],
}

const AffiliateFaq = {
    title: 'General',
    questions: [
        {
            title: 'How much commission will I receive?',
            answer: 'Affiliates receive 30% of the total sale. Partners receive 35% of the net sale revenue. NOTE: The affiliate will be sent the exact amount of commission earned. Network transaction fees are deducted from this amount. Please consider this when choosing a payment method. If you choose to be paid in cryptocurrency, it is advised to choose one with low transaction fees. You can always convert to a more desired cryptocurrency once you receive the funds to avoid high transaction fees.',
        },
        {
            title: 'How do I become a partner?',
            answer: `
        To become a partner, if you have any questions <NAME_EMAIL>.\n

        1. Register a user account\n
        2. Important: Verify your email address\n
        3. Login your user account\n
        4. Go to Setting\n
        5. Menu - Affiliate - Register as an affiliate\n
        6. Important: add your USDT (TRC20) Wallet Address. - submit\n
        7. If you don’t have a wallet yet:\n
            * Bybit: https://bit.ly/3y2wOgg\n
            * OKX: https://bit.ly/3zKDYcO\n
            * CoinW CU (No KYC): http://bit.ly/3uifl7k\n
            * TooBit (No KYC): https://www.toobit.com/en-US/copytrad\n
            * BingX (No KYC): https://bingx.com/int/4Uplwy\n

        8. Share your affiliate links and start to earn.
      `,
        },
        {
            title: 'When and how do I get paid?',
            answer: 'Affiliates get paid in cryptocurrency every Monday. NOTE: The affiliate will be sent the exact amount of commission earned. Network transaction fees are deducted from this amount. Please consider this when choosing a payment method. If you choose to be paid in cryptocurrency, it is advised to choose one with low transaction fees. You can always convert to a more desired cryptocurrency once you receive the funds to avoid high transaction fees.',
        },
        {
            title: 'Can I use Crypto University platforms to promote my affiliate links and coupon codes?',
            answer: 'No, Affiliates/ Partners cannot use any Crypto University communities to promote their referral links/ discount codes. This includes our Whatsapp groups, Discord server or Facebook/Instagram/Twitter pages etc. Affiliates/ Partners cannot make use of Crypto University communities to find potential customers. Affiliates are expected to make use of their own platforms to promote and sell Crypto University products.',
        },
        {
            title: 'Is there a support group for Affiliates/ Partners?',
            answer: 'Yes, If you have any questions <NAME_EMAIL> <EMAIL>',
        },
        {
            title: 'How much discount does the customer get with my coupon code?',
            answer: 'The customer will receive 10% off by using the Affiliate/ Partner discount code. This code is automatically generated and can be viewed on our website in the affiliate dashboard. If you wish to have a bigger discount, you can request this. Please note that your commission will be adjusted proportionally. Example: If you choose to have a 15% discount, you will receive 15% commission.',
        },
    ],
}

const CoinWCompetitionFAQ = {
    title: 'General',
    questions: [
        {
            title: 'What level of trading experience is required for participation?',
            answer: 'No prior trading experience is necessary. Anyone with a registered CU account  is eligible to take part in our CoinW Trading Competition.',
        },
        {
            title: 'Am I allowed to utilize multiple accounts for trading?',
            answer: 'There are no restrictions on the number of CoinW trading accounts you can have. However, each participant must select one trading account for prize redemption. Refer to the Competition Timeline above for further details.',
        },
        {
            title: 'I emerged as a winner in the contest and received a prize. Can I withdraw or convert it into cash?',
            answer: 'All prizes are provided in the form of coupons or funds, which can be utilized as margin for opening positions or covering fees/losses on real market CoinW trading accounts or CU products purchases. However, prize funds and coupons cannot be withdrawn or converted into cash.',
        }
    ],
}

const CUMembershipFAQ = {
    title: 'General',
    questions: [
        {
            title: 'After I make payment for any of the Alpha Group (CU Membership), how will I be added to the channel?',
            answer: 'You will claim Discord access that will take you to all the Alpha Group in our Crypto University Discord server. If you experienced a tech problem. We do have a group on Discord to assist the new subscribers. Simply head over to our  <a aria-label="CU Account" href="https://discord.com/invite/M9cwwCP49c" target="_blank" class="text-[#FCC229]"> Discord server’s </a> “Get Support” category and open a ticket with as much relevant information as possible. Please include your email, username, a detailed description of the problem, and any screenshots or videos that could help us better understand the issue.',
        },
        {
            title: 'Which exclusive course material do we get once we purchase any of the Alpha Group (CU Membership)?',
            answer: 'You will get all the new course material which is the Web3, AI and Content Creation Masterclass and cover up-to-date course material, such as the Automated Crypto Trading course and Ordinals.',
        },
        {
            title: 'If we stop paying the Alpha Group (CU Membership), will I lose access to our exclusive course materials and the Alpha Group?',
            answer: 'Yes',
        },
        {
            title: 'Do we generate a return daily once we join any of the Alpha Group (CU Membership)?',
            answer: 'Our Alpha Group (CU Membership) is not a return of investment opportunity. Our membership comes with community benefits such as exclusive course material, live coaching, whitelists, trading indicators and ideas.',
        }
    ],
}



export { categories, AffiliateFaq, CoinWCompetitionFAQ, CUMembershipFAQ }
