import ImageShortcut from '@/components/Ui/Image'
import React from 'react'
import Link from 'next/link'

interface Props {
    title: string
    description: string
    link: string
    img: any
}

const Card = ({ title, description, link, img }: Props) => {
    return (
        <Link
            aria-label="Card"
            href={link}
            className="relative flex flex-1 select-none flex-col gap-3 rounded-lg border border-[#E1E1E1] pb-6 pl-6 pr-16 pt-[115px] shadow-md max-md:w-full max-md:pr-0 max-md:pt-5">
            <ImageShortcut
                src={img}
                alt=""
                className="absolute right-3 top-5 max-md:right-3 max-md:top-0 max-sm:right-0"
            />
            <div className="flex flex-col gap-2 max-md:max-w-[230px] max-sm:max-w-[150px]">
                <h1 className="text-b3 font-medium ">{title}</h1>
                <p className="text-sub3 max-md:text-cap2">{description}</p>
            </div>
            <p className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-blue">
                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="15" fill="none" viewBox="0 0 19 15">
                    <path
                        fill="#fff"
                        d="M18.036 8.22a1 1 0 000-1.414l-5.657-5.657a1 1 0 10-1.414 1.414l3.95 3.95H1.672a1 1 0 100 2h13.243l-3.95 3.95a1 1 0 001.414 1.414l5.657-5.657z"></path>
                </svg>
            </p>
        </Link>
    )
}

export default Card
