'use client'
import React, { useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import { MdChevronLeft, MdChevronRight } from 'react-icons/md'
import { fetchCryptoData } from '@/lib/cryptoData'

interface CryptoPrice {
    name: string
    Abr: string
    value: number
    change: number
    image: any
}

const CryptoPrices = () => {
    const [showMore, setShowMore] = useState(false)
    const [scrollValue, setScrollValue] = useState(0)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const [cryptoData, setCryptoData] = useState<CryptoPrice[]>([])
    const [loading, setLoading] = useState<boolean>(true)

    useEffect(() => {
        const getData = async () => {
            setLoading(true)
            const data = await fetchCryptoData()
            setCryptoData(data)
            setLoading(false)
        }

        getData()
    }, [])

    const handleScroll = (delta: number) => {
        if (containerRef.current) {
            const newScrollValue = containerRef.current.scrollLeft + delta
            containerRef.current.scrollTo({
                left: newScrollValue,
                behavior: 'smooth',
            })
        }
    }

    const visibleData = showMore ? cryptoData : cryptoData.slice(0, 5)

    return (
        <div className="mx-auto my-28 max-w-[400px] overflow-hidden text-black sm:max-w-[700px] md:max-w-[1400px]">
            <section className="flex justify-between">
                <h2 className="text-[24px] font-[600] text-[#1A1A1A] md:text-[36px]">👀 Crypto Prices</h2>
                <div className="mt-4 hidden gap-4 scroll-smooth md:flex">
                    <button
                        className="scroll-smoothbg-green-300 hover:bg-green-500 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border text-black hover:bg-[#717171] hover:text-white hover:shadow-lg"
                        onClick={() => handleScroll(-200)}>
                        <MdChevronLeft className="text-lg " />
                    </button>
                    <button
                        className="bg-green-300 hover:bg-green-500 hover:text-whitehover:shadow-lg flex h-8 w-8 cursor-pointer items-center justify-center scroll-smooth rounded-full border text-black transition-all duration-100 ease-in-out hover:bg-[#717171] hover:text-white"
                        onClick={() => handleScroll(200)}>
                        <MdChevronRight className="text-lg " />
                    </button>
                </div>
            </section>

            <div
                ref={containerRef}
                className="mx-auto mt-3 grid gap-2 overflow-x-hidden text-black md:hidden md:space-x-3">
                {visibleData.map(item => {
                    const percentValue = parseFloat(item.change.toString())
                    const percentClass = percentValue >= 0 ? 'green' : 'red'
                    const percentColor = percentValue >= 0 ? '#2EB233' : '#FC2424'

                    return (
                        <section
                            key={item.Abr}
                            className="mx-auto flex w-full flex-shrink-0 justify-between rounded-[8px] border p-2">
                            <section className="flex items-center gap-2">
                                <Image
                                    src={item.image}
                                    alt="crypto"
                                    width={32}
                                    height={32}
                                    className="w-full rounded-full"
                                />
                                <div className="gap-2">
                                    <h2 className="text-[16px] font-[600] text-[#F7931A]">{item.Abr}</h2>
                                    <h3 className="text-[14px] font-[400] text-[#959595]">{item.name}</h3>
                                </div>
                            </section>
                            <div>
                                <h2 className="text-[24px] font-[600]">${item.value.toLocaleString()}</h2>
                                <h2 className={`text-end ${percentClass}`} style={{ color: percentColor }}>
                                    {item.change}%
                                </h2>
                            </div>
                        </section>
                    )
                })}
            </div>

            <div
                ref={containerRef}
                className="mx-auto mt-3 hidden w-[1350px] gap-2 overflow-x-hidden md:flex md:space-x-3">
                {cryptoData.map(item => {
                    const percentValue = parseFloat(item.change.toString())
                    const percentClass = percentValue >= 0 ? 'text-green-500' : 'text-red-500'
                    const percentColor = percentValue >= 0 ? '#2EB233' : '#FC2424'

                    return (
                        <section
                            key={item.Abr}
                            className="mx-auto flex w-[343px] flex-shrink-0 items-center justify-between rounded-[8px] border p-2">
                            <section className="flex items-center gap-2">
                                <Image src={item.image} alt="crypto" width={32} height={32} className="" />
                                <div className="gap-2">
                                    <h2 className="text-[15px] font-[600] text-[#F7931A]">{item.Abr}</h2>
                                    <h3 className="text-[14px] font-[400] text-[#959595]">{item.name}</h3>
                                </div>
                            </section>
                            <div className="items-center">
                                <h2 className="text-[22px] font-[600]">${item.value.toLocaleString()}</h2>
                                <h2 style={{ color: percentColor }} className={`text-end ${percentClass}`}>
                                    {item.change}%
                                </h2>
                            </div>
                        </section>
                    )
                })}
            </div>
            <button
                className="bg-blue-500 hover:bg-blue-600 mx-auto mt-4 w-full rounded border px-4 py-2 text-center font-[500] text-black transition md:hidden"
                onClick={() => setShowMore(!showMore)}>
                {showMore ? 'View Less' : 'View More'}
            </button>
        </div>
    )
}

export default CryptoPrices
