'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import WishlistCard from './WishlistCard'
import { useState, useEffect } from 'react'
import Button from '@/components/Ui/Button'
import useWishlist from '@/hooks/useWishlist'
import ImageShortcut from '@/components/Ui/Image'

const Wishlist = ({ main }: { main?: boolean }) => {
    const wishlist = useWishlist()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])

    if (!isMounted || (!main && wishlist.products.length === 0)) return null

    return (
        <>
            <section id="wishlist" className="container mx-auto min-h-[800px] py-14 pb-20">
                <div className="flex flex-col gap-7 max-md:gap-5">
                    <div
                        className={cn(
                            'flex items-center justify-between',
                            wishlist.products.length == 0 ? 'flex-col' : ''
                        )}>
                        {wishlist.products.length > 0 ? (
                            <>
                                <h1 className="text-h3 max-md:text-sub1">My Wishlist</h1>
                                <div className={cn(!main ? '' : 'hidden')}>
                                    <Link aria-label="Wishlist" href={'/wishlist'}>
                                        <Button className="max-md:!text-cap3" rounded>
                                            See All
                                        </Button>
                                    </Link>
                                </div>
                            </>
                        ) : (
                            <div className="flex flex-col items-center justify-center gap-5 ">
                                <ImageShortcut src="/cart/empty.png" alt="empty" width={430} height={370} />
                                <h1 className="text-headline">Oops! Your Wishlist is Empty</h1>
                                <div>
                                    <Link aria-label="Explore Our Products" href="/products">
                                        <Button className="!px-11" variant="primary" rounded>
                                            Explore Our Products
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )}
                    </div>

                    {wishlist.products.length > 0 && (
                        <div className="grid grid-cols-3 gap-4 max-lg:grid-cols-2 max-md:grid-cols-1">
                            {!main
                                ? wishlist.products
                                      .slice(0, 3)
                                      .map((item, index) => <WishlistCard key={index} course={item} />)
                                : wishlist.products.map((item, index) => <WishlistCard key={index} course={item} />)}
                        </div>
                    )}
                </div>
            </section>
            <div className={cn(!main ? 'border-b border-gray-700' : 'hidden')} />
        </>
    )
}

export default Wishlist
