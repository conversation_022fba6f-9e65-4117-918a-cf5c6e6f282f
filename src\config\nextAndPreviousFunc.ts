import { CourseModel } from '@/types/CourseModel'

interface Props {
    course: CourseModel | undefined
    currentRoute: string
}

// Get the rank of the current lesson
export const currentLesson = ({ course, currentRoute }: Props) => {
    const lesson = course?.course_lessons.find(lesson => {
        return lesson.name.toLowerCase().replace(/\s/g, '-') === currentRoute.split('/')[3]
    })
    return lesson
}

// Get the rank of the current Topic
export const currentTopic = ({ course, currentRoute }: Props) => {
    const topic = currentLesson({ course, currentRoute })?.course_topics.find(topic => {
        return topic.name.toLowerCase().replace(/\s/g, '-') === currentRoute.split('/')[4]
    })
    return topic
}

// Check if the lesson has a video
export const hasVideo = ({ course, currentRoute }: Props) => {
    return (
        currentLesson({ course, currentRoute })?.video_link !== undefined &&
        currentLesson({ course, currentRoute })?.video_link !== null &&
        currentLesson({ course, currentRoute })?.video_link !== ''
    )
}

// Check if the current lesson is the last lesson
export const isLastLesson = ({ course, currentRoute }: Props) => {
    // filter the lessons that have topics and reorder instead of the rank
    const filterLesson = course?.course_lessons
        .filter(lesson => lesson.course_topics.length > 0)
        .sort((a, b) => a.rank - b.rank)
    // find the last lesson in the filtered lessons without relying on the rank
    const lastLesson = filterLesson != undefined ? filterLesson[filterLesson?.length - 1] : undefined

    return lastLesson?.name.toLowerCase().replace(/\s/g, '-') === currentRoute.split('/')[3]
}

// Check if the current lesson is the first lesson
export const isFirstLesson = ({ course, currentRoute }: Props) => {
    const filterLesson = course?.course_lessons
        .filter(lesson => lesson.course_topics.length > 0)
        .sort((a, b) => a.rank - b.rank)
    const firstLesson = filterLesson ? filterLesson[0] : undefined
    return firstLesson?.name.toLowerCase().replace(/\s/g, '-') === currentRoute.split('/')[3]
}

// Check if the current topic is the last topic
export const isLastTopic = ({ course, currentRoute }: Props) => {
    const numberOfTopics = currentLesson({ course, currentRoute })?.course_topics.length
    const lastTopic = currentLesson({ course, currentRoute })?.course_topics.find(topic => {
        return topic.rank === numberOfTopics
    })
    return lastTopic?.name.toLowerCase().replace(/\s/g, '-') === currentRoute.split('/')[4]
}

// Check the next topic
export const nextTopic = ({ course, currentRoute }: Props) => {
    // if the current topic is the last move to the next lesson
    if (isLastTopic({ course, currentRoute })) {
        // if the current lesson is the last lesson move to the next course  if the next lesson has a topic
        const TrueLesson = course?.course_lessons.filter(lesson => lesson.course_topics.length > 0)
        //find the next lesson that has rank greater than the current lesson
        const nextLesson = TrueLesson?.find(lesson => {
            return lesson.rank > (currentLesson({ course, currentRoute })?.rank ?? 0)
        })
        if (nextLesson?.video_link !== undefined && nextLesson?.video_link !== null && nextLesson?.video_link !== '') {
            return '/dashboard/' + currentRoute.split('/')[2] + '/' + nextLesson?.name.toLowerCase().replace(/\s/g, '-')
        }
        const nextTopic = nextLesson?.course_topics.find(topic => {
            return topic.rank === 1
        })
        return (
            '/dashboard/' +
            currentRoute.split('/')[2] +
            '/' +
            nextLesson?.name.toLowerCase().replace(/\s/g, '-') +
            '/' +
            nextTopic?.name.toLowerCase().replace(/\s/g, '-')
        )
    }
    if (currentRoute.split('/').length == 3) {
        return (
            '/dashboard/' +
            currentRoute.split('/')[2] +
            '/' +
            currentLesson({ course, currentRoute })?.name.toLowerCase().replace(/\s/g, '-')
        )
    } else {
        const nextTopic = currentLesson({ course, currentRoute })?.course_topics.find(topic => {
            return topic.rank === (currentTopic({ course, currentRoute })?.rank ?? 0) + 1
        })
        return (
            '/dashboard/' +
            currentRoute.split('/')[2] +
            '/' +
            currentLesson({ course, currentRoute })?.name.toLowerCase().replace(/\s/g, '-') +
            '/' +
            nextTopic?.name.toLowerCase().replace(/\s/g, '-')
        )
    }
}

// Check the previous topic
export const previousTopic = ({ course, currentRoute }: Props) => {
    // if the current topic is the first move to the previous lesson
    if (currentTopic({ course, currentRoute })?.rank === 1 && hasVideo({ course, currentRoute })) {
        return (
            '/dashboard/' +
            currentRoute.split('/')[2] +
            '/' +
            currentLesson({ course, currentRoute })?.name.toLowerCase().replace(/\s/g, '-')
        )
    } else if (
        currentTopic({ course, currentRoute })?.rank === 1 ||
        (currentLesson({ course, currentRoute })?.rank !== 1 && currentRoute.split('/').length == 4)
    ) {
        // reorder the filtered lessons high to low
        const filterLesson = course?.course_lessons
            .filter(lesson => lesson.course_topics.length > 0 && lesson != currentLesson({ course, currentRoute }))
            .sort((a, b) => b.rank - a.rank)
        const previousLesson = filterLesson ? filterLesson[0] : undefined
        const previousTopic = previousLesson?.course_topics.find(topic => {
            return topic.rank === previousLesson?.course_topics.length
        })
        return (
            '/dashboard/' +
            currentRoute.split('/')[2] +
            '/' +
            previousLesson?.name.toLowerCase().replace(/\s/g, '-') +
            '/' +
            previousTopic?.name.toLowerCase().replace(/\s/g, '-')
        )
    } else {
        const previousTopic = currentLesson({ course, currentRoute })?.course_topics.find(topic => {
            return topic.rank === (currentTopic({ course, currentRoute })?.rank ?? 0) - 1
        })
        return (
            '/dashboard/' +
            currentRoute.split('/')[2] +
            '/' +
            currentLesson({ course, currentRoute })?.name.toLowerCase().replace(/\s/g, '-') +
            '/' +
            previousTopic?.name.toLowerCase().replace(/\s/g, '-')
        )
    }
}
