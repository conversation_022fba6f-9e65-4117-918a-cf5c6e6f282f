import Button from '@/components/Ui/Button'
import React from 'react'

interface Props {
    length: number
    total: number
}

const Summary = ({ length, total }: Props) => {
    return (
        <div className="flex flex-col gap-8 border border-gray-700 px-6 py-9 max-md:w-full">
            <h1 className="text-b3 font-medium">Summary</h1>
            <div className="flex flex-col gap-6">
                <div className="flex items-center justify-between text-sub2">
                    <p className=" text-gray-900">
                        Total Product (<span className="text-blue">{length}</span>)
                    </p>
                    <p className="font-semibold">${total}</p>
                </div>
                <div className="border-b border-gray-700 " />
                <div className="flex items-center justify-between text-sub1">
                    <p className="font-medium">Total</p>
                    <p className="font-semibold">${total}</p>
                </div>
            </div>
            <div className="w-[330px] max-md:w-full">
                <Button variant="primary" rounded>
                    Buy Now ({length})
                </Button>
            </div>
            <p className="text-sub3 text-center text-gray-900">Coupon can be applied during checkout.</p>
        </div>
    )
}

export default Summary
