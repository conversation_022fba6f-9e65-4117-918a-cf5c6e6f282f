'use client'
import { useState } from 'react'
import { user } from '@/types/UserModel'
import { useRouter } from 'next/navigation'
import Button from '@/components/Ui/Button'
import { GetCurrentSession } from '@/lib/session'
import Label from '@/components/Ui/Label'
import ButtonSpinner from '@/components/Ui/buttonSpinner'
import { toast } from 'react-toastify'
import useReferral from '@/hooks/useReferral'

type InputV2Props = {
    value: string | number | readonly string[] | undefined
    type: string
    name: string
    required?: boolean;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
    placeholder?: string
    disabled?: boolean
}

// Components
const InputV2 = ({ value, type, name, onChange, placeholder, disabled = false, required = false }: InputV2Props) => {
    return (
        <input
            type={type}
            name={name}
            value={value ?? ''}
            onChange={onChange}
            placeholder={placeholder}
            className="w-full max-w-full rounded-full border border-gray-700 p-4 ring-0 focus:border-blue focus:outline-none disabled:cursor-not-allowed disabled:bg-gray-200 max-md:py-3.5 max-md:text-cap1"
            disabled={disabled}
            required={required}
        />
    )
}
const Item = ({
    children,
    required = false,
    label,
}: {
    children: React.ReactNode
    label: string
    required?: boolean
}) => {
    return (
        <div className="flex w-full flex-col gap-2">
            <Label uppercase={false} required={required}>
                {label}
            </Label>
            {children}
        </div>
    )
}

const RegistrationForm = ({ user, token }: { user: user; token: string; }) => {
    const session = GetCurrentSession()
    const router = useRouter()
    const { registerAffiliate, error, isLoading } = useReferral(token)
    const [isError, setIsError] = useState('')
    const user_id = user.id; 
    const commission_id = 1;

    const [formData, setFormData] = useState<any>({
        ...(user ?? {
            wallet: '',
            promote: '',
            affiliateCode: '', 
            preferred_crypto: '', 
        }),
    });

    const handleChange = (e: any) => {
        const { name, value } = e.target
        setFormData((prevData: any) => ({
            ...prevData,
            [name]: value,
        }))
    }
    const handleSubmit = async (e: any) => {
        e.preventDefault()
        const payload = {
            user_id,
            commission_id,
            affiliateCode: formData.affiliateCode,
            preferred_crypto: "USDT",
            promote: formData.promote,
            wallet: formData.wallet,
        };
        try {
            const res = await registerAffiliate(payload)
            toast.success('Affiliate registered successfully')
            session.update({ ...session, user: res.data.user })
            //window.location.href = '/dashboard/affiliate';
            // router.push('/dashboard/affiliate')
            router.push('/dashboard');
            //window.location.reload();
        } catch (error: any) {
            console.error(error)
            toast.error('Affiliate already exist')
        }
    }
    return (
        <form className="gird-cols-2 grid max-w-[800px] space-y-6 pb-4" onSubmit={handleSubmit}>
            <div className="col-span-2 flex items-center gap-4">
                <Item label="Your First Name" required>
                    <InputV2
                        type="text"
                        name="first_name"
                        value={formData.first_name ?? ''}
                        placeholder="e.g. John"
                        required={true}
                        disabled ={true}
                        onChange={handleChange}
                    />
                </Item>

                <Item label="Your Last Name" required>
                    <InputV2
                        type="text"
                        name="last_name"
                        value={formData.last_name ?? ''}
                        onChange={handleChange}
                        required={true}
                        disabled ={true}
                        placeholder="e.g. Doe"
                    />
                </Item>
            </div>

            <div className="col-span-2 flex w-full">
                <Item label="USDT (TRC20) Wallet Address" required>
                    <InputV2
                        type="text"
                        name="wallet"
                        value={formData.wallet ?? ''}
                        onChange={handleChange}
                        required={true}
                        placeholder="Please enter your USDT (TRC20) Wallet Address to get your affiliate earnings"
                    />
                </Item>
            </div>

            <div className="col-span-2 flex items-center gap-4">
            <Item label="Enter your affiliate reference code" required>
                    <InputV2
                        type="text"
                        name="affiliateCode"
                        value={formData.affiliateCode ?? ''}
                        onChange={handleChange}
                        required={true}
                        placeholder="The code will be appended in your url?"
                    />
                </Item>

                <Item label="How will you promote us?" required>
                    <InputV2
                        type="text"
                        name="promote"
                        value={formData.promote ?? ''}
                        onChange={handleChange}
                        required={true}
                        placeholder="Tell us where and how will you promote us?"
                    />
                </Item>
            </div>
            <div className="max-w-[250px]">
                <Button type="submit" variant="primary" className="" rounded disabled={isLoading}>
                    {isLoading && <ButtonSpinner />}Submit
                </Button>
            </div>
            {(error || isError) && <p className="text-cap1 text-red">{error || isError}</p>}
        </form>
    )
}

export default RegistrationForm

