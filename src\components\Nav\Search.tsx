'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import useSearch from '@/hooks/useSearch'
import { useEffect, useRef } from 'react'

const SearchIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="25"
        height="24"
        viewBox="0 0 25 24"
        fill="none"
        className="absolute left-6">
        <path
            d="M12.5022 20.7549C17.4665 20.7549 21.4908 16.7306 21.4908 11.7664C21.4908 6.80215 17.4665 2.77783 12.5022 2.77783C7.53799 2.77783 3.51367 6.80215 3.51367 11.7664C3.51367 16.7306 7.53799 20.7549 12.5022 20.7549Z"
            stroke="#AAAAAA"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M18.7539 18.4849L22.2779 21.9997"
            stroke="#AAAAAA"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
)
const BlueSearch = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
        <path
            d="M14.9354 16.7228C14.734 16.7228 14.5494 16.6389 14.3983 16.4879L10.4043 12.3932C10.119 12.0912 10.119 11.6212 10.421 11.3359C10.723 11.0507 11.193 11.0507 11.4783 11.3527L15.4723 15.4641C15.7576 15.7661 15.7576 16.2361 15.4556 16.5214C15.3213 16.6557 15.1198 16.7227 14.9352 16.7227L14.9354 16.7228Z"
            fill="#2655FF"
        />
        <path
            d="M6.83015 13.8033C3.05431 13.8033 6.71654e-05 10.7658 6.71654e-05 7.0403C-0.0165552 3.3148 3.05431 0.277344 6.83015 0.277344C10.606 0.277344 13.6602 3.3148 13.6602 7.0403C13.6602 10.7658 10.5892 13.8033 6.83015 13.8033ZM6.83015 1.7709C3.87663 1.7709 1.47684 4.13701 1.47684 7.0403C1.47684 9.94348 3.87654 12.3097 6.83015 12.3097C9.76692 12.3097 12.1667 9.94348 12.1667 7.0403C12.1668 4.13701 9.76698 1.7709 6.83015 1.7709Z"
            fill="#2655FF"
        />
    </svg>
)

const Search = () => {
    const { data, error, isLoading, empty, SetSearch } = useSearch()

    const ref = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const handleOutSideClick = (event: MouseEvent) => {
            if (ref.current && !ref.current.contains(event.target as Node)) {
                SetSearch('')
            }
        }

        window.addEventListener('mousedown', handleOutSideClick)

        return () => {
            window.removeEventListener('mousedown', handleOutSideClick)
        }
    }, [ref, SetSearch])

    return (
        <li className="relative max-w-[150px]">
            <div className="relative flex max-w-[150px] items-center">
                <SearchIcon />
                <input
                    placeholder="Search"
                    name="search"
                    autoComplete="off"
                    onChange={e => {
                        SetSearch(e.target.value)
                    }}
                    type="search"
                    className="max-w-[150px] rounded-full bg-gray-200 px-6 py-[12.5px] pl-14 font-medium text-black outline-none placeholder:text-[#676A73]"
                />
            </div>

            <div
                ref={ref}
                className={cn(
                    'absolute mt-7 flex w-[348px] flex-col rounded-lg bg-white p-6 shadow-[4px_8px_100px_0px_rgba(0,0,0,0.25)]',
                    empty && 'hidden'
                )}>
                {isLoading ? (
                    <div className="text-sub3 font-medium">Searching...</div>
                ) : data.length > 0 ? (
                    <div className="space-y-6">
                        <Link
                            aria-label={data[0].name || data[0].title as string}
                            href={
                                data[0].type === 'course'
                                    ? `/courses/${data[0].slug}`
                                    : data[0].type === 'mentorship'
                                    ? `/mentorships/${data[0].slug}`
                                    : data[0].type === 'bundle'
                                    ? `/bundles/${data[0].slug}`
                                    : data[0].type === 'indicator'
                                    ? `/indicators/${data[0].slug}`
                                    : data[0].type === 'bootcamp'
                                    ? `/bootcamps/${data[0].slug}`
                                    :`/bundles/${data[0].slug}`
                            }
                            onClick={() => SetSearch('')}
                            className="text-sub3 capitalize text-black transition-colors duration-150 hover:text-blue">
                            {data[0].name || data[0].title}
                        </Link>
                        {data.slice(1, 6).length > 0 && (
                            <div className="border-t border-gray-400 pt-6">
                                <div className="flex gap-2 pb-[13.5px] text-sub3 font-semibold">
                                    <BlueSearch /> Similar results
                                </div>
                                <div className="flex flex-col gap-[15px]">
                                    {data.slice(1, 6).map((item: any, index: number) => (
                                        <Link
                                            aria-label={item.name || item.title as string}
                                            href={
                                                item.type === 'course'
                                                    ? `/courses/${item.slug}`
                                                    : item.type == 'mentorship'
                                                    ? `/mentorhips/${item.slug}`
                                                    : item.type === 'bundle'
                                                    ? `/bundles/${item.slug}`
                                                    : item.type === 'indicator'
                                                    ? `/indicators/${item.slug}`
                                                    : item.type === 'bootcamp'
                                                    ? `/bootcamps/${item.slug}`
                                                    :`/bundles/${item.slug}`
                                            }
                                            key={index}
                                            onClick={() => SetSearch('')}
                                            className="text-sub3 capitalize text-black transition-colors duration-150 hover:text-blue">
                                            {item.name || item.title}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                ) : !error ? (
                    <div className="text-sub3 font-semibold">No results</div>
                ) : (
                    <div className="text-sub3 font-semibold">Error</div>
                )}
            </div>
        </li>
    )
}

export default Search
