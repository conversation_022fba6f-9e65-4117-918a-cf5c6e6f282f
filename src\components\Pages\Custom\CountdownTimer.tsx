'use client'
import { useCountdown } from '@/hooks/useCountdown'

interface CountdownTimerProps {
    targetDate: Date
}

const CountdownTimer = ({ targetDate }: CountdownTimerProps): JSX.Element => {
    const [days, hours, minutes, seconds] = useCountdown(targetDate)

    return (
        <div className="countdown-bg relative w-full overflow-hidden py-20">
            <div className="space-y-4 text-white">
                <h3 className="text-center text-b2 font-semibold capitalize max-md:text-sub1">
                    Reserve your spot now!
                </h3>
                <div className="flex items-center justify-center gap-8 max-md:gap-4 max-sm:gap-2">
                    <div className="text-center">
                        <div className="font-bold max-md:text-callout md:text-[3vw]">{days < 0 ? '0' : days}</div>
                        <div className="text-sub3 max-md:text-cap2">Day(s)</div>
                    </div>

                    <p className="font-bold max-md:text-callout md:text-[3vw]">:</p>

                    <div className="text-center">
                        <div className="font-bold max-md:text-callout md:text-[3vw]">{hours < 0 ? '0' : hours}</div>
                        <div className="text-sub3 max-md:text-cap2">Hour(s)</div>
                    </div>

                    <p className="font-bold max-md:text-callout md:text-[3vw]">:</p>

                    <div className="text-center">
                        <div className="font-bold max-md:text-callout md:text-[3vw]">{minutes < 0 ? '0' : minutes}</div>
                        <div className="text-sub3 max-md:text-cap2">Minute(s)</div>
                    </div>

                    <p className="font-bold max-md:text-callout md:text-[3vw]">:</p>

                    <div className="text-center">
                        <div className="font-bold max-md:text-callout md:text-[3vw]">{seconds < 0 ? '0' : seconds}</div>
                        <div className="text-sub3 max-md:text-cap2">Second(s)</div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CountdownTimer
