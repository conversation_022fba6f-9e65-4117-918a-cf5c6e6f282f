
'use client';
import Link from 'next/link';
import useAuth from '@/hooks/useAuth';
import Label from '@/components/Ui/Label';
import { useState } from 'react';
import { Eye, Lock } from '@public/global';
import Button from '@/components/Ui/Button';
import ButtonSpinner from '@/components/Ui/buttonSpinner';
import { useSearchParams } from 'next/navigation'
import { MdClose, MdOutlineKeyboardArrowRight } from 'react-icons/md';

interface TextBoxProps {
    label: string;
    email?: boolean;
    icon: boolean;
    password?: boolean;
    showPassword?: boolean;
    setShowPassword?: any;
    placeholder: string;
    formData: any;
    setFormData: any;
}

const TextBox = ({
    label,
    email,
    icon,
    password,
    showPassword,
    setShowPassword,
    placeholder,
    formData,
    setFormData,
}: TextBoxProps) => {
    return (
        <div className="flex w-full flex-col gap-3">
            <Label required uppercase={false}>
                {label}
            </Label>

            <div className="relative flex select-none items-center">
                <div className="absolute left-4">{email ? <Eye /> : password ? <Lock /> : icon && <Lock />}</div>
                <input
                    type={email ? 'email' : password ? (showPassword ? 'text' : 'password') : 'text'}
                    name={email ? 'email' : password ? 'password' : 'input'}
                    value={email ? formData.email : password ? formData.password : ''}
                    onChange={e =>
                        email
                            ? setFormData({ ...formData, email: e.target.value })
                            : password
                                ? setFormData({ ...formData, password: e.target.value })
                                : ''
                    }
                    className="max-md:w-full w-full rounded-full border border-gray-700 px-4 py-[17.5px] pl-12 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue max-md:py-3"
                    placeholder={placeholder}
                    required
                />
                {password && (
                    <div onClick={() => setShowPassword(!showPassword)} className="absolute right-4 cursor-pointer">
                        <Eye />
                    </div>
                )}
            </div>
        </div>
    );
};

export default function LoginPage() {
    const { login, error, reset, isLoading } = useAuth();
    const [showPassword, setShowPassword] = useState(false);
    const [rememberMe, setRememberMe] = useState(false);
    const [formData, setFormData] = useState({
        email: '',
        password: '',
    });
    const searchParams = useSearchParams()



    const handleChange = () => {
        setRememberMe(!rememberMe);
    };

    const source: any = searchParams.get('source')

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        await login(formData.email, formData.password, source);
    };

     const handleClose = () => {
         console.log('Close button clicked')
     }

    return (
        <div className="flex min-h-screen flex-col bg-cover bg-center bg-no-repeat md:bg-[url('/icons/crypto-university-bg.png')]">
            <div className="flex justify-end p-3">
                <button className="hidden w-[159px] items-center justify-center rounded-full border border-[#FFFFFF] bg-white bg-opacity-10 py-2 text-[14px] font-[600] text-[#FFFFFF] transition duration-300 md:flex">
                    Go Home
                    <MdOutlineKeyboardArrowRight className="h-4 w-4 font-[600]" />
                </button>
                <button className="text-black md:hidden" onClick={handleClose}>
                    <MdClose size={24} />
                </button>
            </div>
            <div className="flex w-full justify-start md:flex-row">
                <div className="w-full p-5 md:ml-[-15px] md:w-1/2 md:p-10">
                    <div className="mx-auto w-full max-w-md bg-white p-4 sm:rounded-[21.6px] sm:p-8 sm:shadow-md">
                        <div className="w-full pb-4 text-center font-manrope text-b3 font-semibold  text-gray-800 max-md:text-sub1">
                            Welcome back, Dear trader!
                        </div>
                        <form onSubmit={handleSubmit} className="flex w-full flex-col gap-4">
                            <TextBox
                                label="Email"
                                icon
                                email
                                formData={formData}
                                setFormData={setFormData}
                                placeholder="<EMAIL>"
                            />
                            <div className="flex w-full flex-col gap-2">
                                <TextBox
                                    label="Password"
                                    showPassword={showPassword}
                                    setShowPassword={setShowPassword}
                                    icon
                                    password
                                    formData={formData}
                                    setFormData={setFormData}
                                    placeholder="enter your password"
                                />
                                {error && (
                                    <p className="rounded-[8px] bg-red/20 p-3 font-sans text-cap1 text-red">{error}</p>
                                )}
                            </div>
                            <div className="flex w-full justify-between">
                                <div className="flex items-center gap-[0.375rem]">
                                    <input
                                        className="h-5 w-5 border-gray-700 outline-none"
                                        onChange={handleChange}
                                        type="checkbox"
                                        name="rememberme"
                                        id="rememberme"
                                    />
                                    <label
                                        htmlFor="rememberme"
                                        className="select-none text-cap1 leading-none text-gray-900">
                                        Keep me signed in
                                    </label>
                                </div>
                                <Link
                                    aria-label="forgot password"
                                    href="/forgot-password"
                                    className="px-2 py-[1px] text-cap1 font-medium text-blue">
                                    Forgot password?
                                </Link>
                            </div>

                            <div className="flex w-full flex-col gap-2">
                                <Button variant="primary" type="submit" rounded disabled={isLoading}>
                                    {isLoading && <ButtonSpinner />}Sign in
                                </Button>
                                <p className="py-[17.5px] text-center text-sub3 font-semibold">
                                    Don&apos;t have an account?{' '}
                                    <Link
                                        aria-label="Register"
                                        className="text-blue"
                                        href={{ pathname: '/register', query: { source: 'coinw' } }}>
                                        Sign up now
                                    </Link>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    )
}
