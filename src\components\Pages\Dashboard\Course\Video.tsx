'use client'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import mix from '@/config/videoLink'
import replaceVideoExtensionOrConvertToEmbedLink from '@/config/replaceVideoLink'

interface Props {
    bannerImage: string | null
    topic:
        | {
              id: number
              name: string
              rank: number
              video_link: string
          }
        | undefined
}

const VideoPlayerIcon = () => (
    <svg fill="white" className='bg-black/30 rounded-full group-hover:bg-black/70 transition-all duration-300' viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
        <path
            clipRule="evenodd"
            fillRule="evenodd"
            d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm14.024-.983a1.125 1.125 0 010 1.966l-5.603 3.113A1.125 1.125 0 019 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113z"
        />
    </svg>
)

const Video = ({ bannerImage, topic }: Props) => {
    const [imageClicked, setImageClicked] = useState(false)
    return (
        <div className="flex w-full flex-col gap-6">
            <h1 className="text-b3 font-medium capitalize">{topic?.name}</h1>
            <div className="flex select-none items-center justify-center">
                <div className="relative flex w-full items-center justify-center rounded-[1rem] bg-[darkgray] group">
                    {!imageClicked ? (
                        <>
                            <div onClick={() => setImageClicked(true)}>
                                <ImageShortcut
                                    src={bannerImage ?? ''}
                                    width={1280}
                                    height={720}
                                    alt="thumbnail"
                                    className="rounded-[1rem] z-[10] aspect-video max-h-[600px] min-h-[575px] w-full cursor-pointer select-none object-cover max-md:min-h-[300px] max-sm:min-h-[220px] md:object-cover"
                                    priority
                                />
                            </div>
                            <div
                                className={'absolute z-[10] h-[100px] w-[100px] cursor-pointer'}
                                onClick={() => setImageClicked(true)}>
                                <VideoPlayerIcon />
                            </div>
                        </>
                    ) : (
                        <>
                            <iframe
                                width="575"
                                height="480"
                                src={replaceVideoExtensionOrConvertToEmbedLink(topic?.video_link ?? '')}
                                title="video player"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                className="rounded-card z-[10] min-h-[600px] w-full select-none rounded-2xl max-md:min-h-[300px] max-sm:min-h-[220px]"
                                allowFullScreen
                            />
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}

export default Video
