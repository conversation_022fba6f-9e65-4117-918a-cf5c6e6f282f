import { Search } from '@public/global'

type AuthorHeroProps = {
    name: string | null
    // firstName?: string | null
    // lastName?: string | null
}

const AuthorHero = ({ name }: AuthorHeroProps) => {
    return (
        <section className="relative mb-5 bg-yellow-light py-[4.5rem] pt-[4.8125rem] max-sm:mb-[5.25rem]">
            <div className="container mx-auto">
                <div className="space-y-8">
                    {/* {name ? (
                        <h1 className="text-h2 font-medium capitalize max-md:text-b2">
                            {name} Blog
                        </h1>
                    ) : ( */}
                        <h1 className="text-h2 font-medium capitalize max-md:text-b2">{name} Blog</h1>
                    {/* )} */}
                    <div className="relative flex items-center max-sm:absolute max-sm:-bottom-6 max-sm:mx-auto max-sm:w-[calc(100vw-32px)]">
                        <input
                            type="search"
                            name="search"
                            id="search_blog"
                            placeholder="Search blogs"
                            className="w-[319px] rounded-full py-[13.5px] pl-14 pr-4 
                        text-sub3 font-medium
                        leading-[20.8px] shadow-[2px_4px_30px_0px_#0000001A] outline-none placeholder:text-black max-md:w-full
                        max-sm:py-[1.125rem] max-sm:text-cap2 max-sm:placeholder:text-[#676A73]"
                        />
                        <div className="absolute left-6 max-sm:hidden">
                            <Search />
                        </div>
                        <div className="absolute left-6 sm:hidden">
                            <Search height={15} width={15} />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default AuthorHero
