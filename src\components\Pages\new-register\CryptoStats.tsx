import React from 'react'; // Add this line
import Image from 'next/image'
import { FaBitcoin, FaEthereum } from 'react-icons/fa'
import { SiTether } from 'react-icons/si'

export default function CryptoStats() {
    return (
        <>
            <div className="text-center">
                <div className="mb-8">
                    <Image src="/logo.png" alt="The Crypto University Logo" width={150} height={150} />
                    <h2 className="text-3xl mt-4 font-bold">THE CRYPTO UNIVERSITY</h2>
                </div>

                <h1 className="text-4xl text-yellow-400 mb-8 font-bold">
                    World’s #1 Cryptocurrency
                    <br />
                    Education Platform
                </h1>

                <div className="mb-8 grid grid-cols-3 gap-8">
                    <div>
                        <p className="text-4xl font-bold">35,000+</p>
                        <p className="text-xl">Members</p>
                    </div>
                    <div>
                        <p className="text-4xl font-bold">10,000+</p>
                        <p className="text-xl">Hours of content</p>
                    </div>
                    <div>
                        <p className="text-4xl font-bold">20+</p>
                        <p className="text-xl">Partners</p>
                    </div>
                </div>

                <div className="mb-8">
                    <p className="text-xl mb-2">Rated 4.5 out of 5 on Trustpilot</p>
                    <div className="flex justify-center">
                        {[...Array(4)].map((_, i) => (
                            <svg key={i} className="text-yellow-400 h-6 w-6 fill-current" viewBox="0 0 24 24">
                                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                            </svg>
                        ))}
                        <svg className="text-yellow-400 h-6 w-6 fill-current" viewBox="0 0 24 24">
                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                        </svg>
                    </div>
                </div>

                <div className="flex justify-center space-x-4">
                    <FaBitcoin size={40} className="text-orange-500" />
                    <FaEthereum size={40} className="text-blue-400" />
                    <SiTether size={40} className="text-green-500" />
                </div>
            </div>
        </>
    )
}
