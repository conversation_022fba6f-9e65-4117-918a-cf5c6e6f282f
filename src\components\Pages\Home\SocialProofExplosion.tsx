"use client";
import { useState, useEffect } from 'react';

interface StudentSuccess {
  name: string;
  profit: string;
  timeframe: string;
  location: string;
  story: string;
}

const SocialProofExplosion = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [liveCounter, setLiveCounter] = useState(47832);

  const successStories: StudentSuccess[] = [
    {
      name: "<PERSON>",
      profit: "$89,432",
      timeframe: "6 months",
      location: "California, USA",
      story: "I was a complete beginner and now I'm making more than my corporate job!"
    },
    {
      name: "<PERSON>",
      profit: "$156,847",
      timeframe: "1 year",
      location: "London, UK", 
      story: "Quit my 9-5 and now trade crypto full-time. Best decision ever!"
    },
    {
      name: "<PERSON>",
      profit: "$247,923",
      timeframe: "18 months",
      location: "Sydney, Australia",
      story: "From $500 to quarter million! This system changed my life completely."
    },
    {
      name: "<PERSON>",
      profit: "$78,291",
      timeframe: "4 months",
      location: "Toronto, Canada",
      story: "Made more in 4 months than my entire previous year salary!"
    },
    {
      name: "<PERSON>",
      profit: "$134,567",
      timeframe: "8 months",
      location: "Berlin, Germany",
      story: "Started with zero knowledge, now I'm financially free!"
    }
  ];

  const liveUpdates = [
    "🔥 <PERSON> just made $3,247 in the last hour!",
    "💰 <PERSON> earned $8,932 today using our signals!",
    "🚀 <PERSON> hit $50K total profits this month!",
    "⚡ <PERSON> made $1,847 while sleeping!",
    "💎 <PERSON> just withdrew $12,500 in profits!",
    "🎯 Rachel earned $6,234 in her first week!",
    "🔥 Tom made $15,678 this weekend!",
    "💰 Jessica hit $25K milestone today!"
  ];

  const [currentUpdate, setCurrentUpdate] = useState(0);

  useEffect(() => {
    const testimonialTimer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % successStories.length);
    }, 4000);

    const updateTimer = setInterval(() => {
      setCurrentUpdate((prev) => (prev + 1) % liveUpdates.length);
    }, 3000);

    const counterTimer = setInterval(() => {
      setLiveCounter((prev) => prev + Math.floor(Math.random() * 3) + 1);
    }, 5000);

    return () => {
      clearInterval(testimonialTimer);
      clearInterval(updateTimer);
      clearInterval(counterTimer);
    };
  }, []);

  return (
    <section className="py-20 bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-40 h-40 bg-yellow-400/10 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-green-400/10 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-60 h-60 bg-primary/5 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto relative z-10 px-4">
        {/* Live Counter Section */}
        <div className="text-center mb-16">
          <div className="bg-red-600 text-white px-8 py-4 rounded-2xl mb-8 border-4 border-yellow-400 animate-pulse inline-block">
            <div className="text-2xl font-black">🔴 LIVE: STUDENTS MAKING MONEY RIGHT NOW!</div>
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-tight">
            🚀 <span className="text-yellow-400 animate-pulse">{liveCounter.toLocaleString()}+</span> Students Already <span className="text-green-400">PROFITING!</span>
          </h1>

          {/* Live Updates Ticker */}
          <div className="bg-black/50 backdrop-blur-sm p-6 rounded-2xl border-2 border-yellow-400 mb-12">
            <div className="text-2xl font-black text-yellow-400 mb-4 animate-pulse">
              📈 LIVE PROFIT UPDATES
            </div>
            <div className="text-xl font-bold text-green-300 animate-pulse">
              {liveUpdates[currentUpdate]}
            </div>
          </div>
        </div>

        {/* Massive Success Stories Grid */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Featured Success Story */}
          <div className="bg-gradient-to-br from-yellow-400 to-yellow-600 p-8 rounded-3xl border-4 border-green-600 text-black relative overflow-hidden">
            <div className="absolute top-4 right-4 animate-bounce">
              <div className="text-4xl">🏆</div>
            </div>

            <div className="relative z-10">
              <div className="bg-green-600 text-white px-4 py-2 rounded-xl mb-6 inline-block">
                <div className="font-black">⭐ STUDENT SPOTLIGHT</div>
              </div>

              <div className="text-center mb-6">
                <div className="text-6xl font-black text-green-800 mb-2">
                  {successStories[currentTestimonial].profit}
                </div>
                <div className="text-2xl font-bold text-green-700">
                  in just {successStories[currentTestimonial].timeframe}!
                </div>
              </div>

              <blockquote className="text-xl font-bold text-center mb-6 italic">
                &quot;{successStories[currentTestimonial].story}&quot;
              </blockquote>

              <div className="text-center">
                <div className="text-2xl font-black text-green-800">
                  - {successStories[currentTestimonial].name}
                </div>
                <div className="text-lg font-semibold text-green-700">
                  {successStories[currentTestimonial].location}
                </div>
              </div>
            </div>
          </div>

          {/* Success Metrics */}
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-purple-600 to-purple-800 p-8 rounded-3xl border-4 border-yellow-400">
              <div className="text-center">
                <div className="text-5xl font-black text-yellow-400 mb-2">$2.4M+</div>
                <div className="text-xl font-bold">Total Student Profits</div>
                <div className="text-lg text-purple-200 mt-2">And growing every day!</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-red-600 to-red-800 p-8 rounded-3xl border-4 border-yellow-400">
              <div className="text-center">
                <div className="text-5xl font-black text-yellow-400 mb-2">97%</div>
                <div className="text-xl font-bold">Success Rate</div>
                <div className="text-lg text-red-200 mt-2">Our students WIN!</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-8 rounded-3xl border-4 border-yellow-400">
              <div className="text-center">
                <div className="text-5xl font-black text-yellow-400 mb-2 animate-pulse">LIVE</div>
                <div className="text-xl font-bold">Active Traders</div>
                <div className="text-lg text-blue-200 mt-2">Making money 24/7!</div>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonial Carousel */}
        <div className="bg-black/50 backdrop-blur-sm p-8 rounded-3xl border-4 border-yellow-400 mb-16">
          <h3 className="text-3xl font-black text-center text-yellow-400 mb-8">
            🎯 WHAT OUR MILLIONAIRE STUDENTS SAY:
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            {successStories.slice(0, 3).map((story, index) => (
              <div key={index} className="bg-gradient-to-br from-green-600 to-green-700 p-6 rounded-2xl border-2 border-yellow-400">
                <div className="text-center">
                  <div className="text-3xl font-black text-yellow-400 mb-2">
                    {story.profit}
                  </div>
                  <div className="text-lg font-bold mb-4">
                    in {story.timeframe}
                  </div>
                  <blockquote className="text-sm font-semibold mb-4 italic">
                    &quot;{story.story}&quot;
                  </blockquote>
                  <div className="font-bold">- {story.name}</div>
                  <div className="text-sm text-green-200">{story.location}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Final Social Proof CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-red-600 via-red-700 to-red-800 p-12 rounded-3xl border-4 border-yellow-400 max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-black text-white mb-6">
              🚨 JOIN THE {liveCounter.toLocaleString()}+ WINNERS TODAY!
            </h3>
            
            <div className="bg-yellow-400 text-black p-6 rounded-2xl mb-8 inline-block">
              <div className="text-2xl font-black">THEY DID IT - SO CAN YOU!</div>
              <div className="text-lg font-bold">Start Your Profit Journey RIGHT NOW!</div>
            </div>

            <p className="text-2xl text-yellow-100 mb-8 font-bold">
              Stop watching others get rich! <span className="text-green-400">Your success story</span> could be next. Join our profitable community today!
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <button className="px-12 py-6 bg-green-600 hover:bg-green-700 text-white text-2xl font-black rounded-2xl hover:scale-110 transform transition-all shadow-2xl">
                🚀 YES! I WANT TO BE NEXT!
              </button>
              <button className="px-12 py-6 bg-yellow-400 hover:bg-yellow-500 text-black text-2xl font-black rounded-2xl hover:scale-110 transform transition-all shadow-2xl">
                💎 SHOW ME THE PROOF!
              </button>
            </div>

            <div className="mt-8 text-yellow-200 font-bold text-lg animate-pulse">
              ⚡ Join {Math.floor(Math.random() * 50) + 100}+ people who signed up in the last hour!
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialProofExplosion;
