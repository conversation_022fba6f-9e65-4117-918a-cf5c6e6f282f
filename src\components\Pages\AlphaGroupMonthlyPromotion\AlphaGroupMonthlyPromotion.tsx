
'use client'
import 'react-responsive-modal/styles.css'
import './Membership.css'
import Check from '../../../../public/icons/Check.svg'
import ImageShortcut from "@/components/Ui/Image"
import Link from "next/link"
import Image from 'next/image'
import { discordChat, cyberSecurity, globalLearning, group, onlineLearning, wallet1 } from "@public/courses/noones"

const AlphaGroupMonthlyPromotion = () => {

    const headerItems = [
        {
            title: 'Web3 & AI Insights'
        },
        {
            title: 'Alpha Trading & Alpha Stream'
        },
        {
            title: 'NFTs and Ordinals'
        }
    ]

    const whyChooseUs = [
        {
            title: 'Web3 & AI Insights',
            description: 'Stay ahead with cutting-edge developments in Web3 and AI technologies.',
            icon: globalLearning
        },
        {
            title: 'Content Creation Masterclass',
            description: 'Learn to create impactful content with our expert-led courses.',
            icon: wallet1
        },
        {
            title: 'Crypto Day Trading',
            description: 'Discover proven trading strategies to maximize your gains.',
            icon: onlineLearning
        },
        {
            title: 'NFTs and Ordinals',
            description: 'Explore the latest trends and opportunities in NFTs and Ordinals.',
            icon: cyberSecurity
        },
        {
            title: 'Alpha Trading & Alpha Stream',
            description: 'Gain access to exclusive trading streams and insights.',
            icon: group
        }

    ]

    return (
        <section>
            <div className="bgg">
                <div className="p-[32px] md:p-[64px] pt-[350px] p">
                    <div className="mt-2 text-[26px] font-bold text-white text-center md:text-left md:text-[54px] max-w-[300px] md:min-w-[700px]">
                        Alpha Group Monthly <span className="text-[#EFB77C]"> 10% Discount</span> Promotion!
                    </div>

                    <div>
                        <ul className="text-left my-3 md:my-7 grid gap-2 text-[13.62px] md:text-[16px] text-white md:grid-cols-1">
                            <li className="flex gap-2">
                                {' '}
                                <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                Web3, AI, Content Creation Masterclass Courses
                            </li>
                            <li className="flex gap-2">
                                {' '}
                                <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                Crypto day Trading
                            </li>
                            <li className="flex gap-2">
                                {' '}
                                <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                NFTs and Ordinals
                            </li>
                            <li className="flex gap-2">
                                {' '}
                                <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                Alpha Trading & Alpha Stream
                            </li>
                            <li className="flex gap-2">
                                {' '}
                                <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                Weekly Live Session
                            </li>
                            <li className="flex gap-2">
                                {' '}
                                <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                Crypto trading Insights
                            </li>
                        </ul>
                    </div>
                    <Link
                        aria-label="View products"
                        href={"/checkout/subscription/alpha-group-monthly-promotion?previous=/subscriptions"}
                        className="!bg-gradient-to-r !from-[#EFB77C] !to-[#6E4C31] text-white hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub1 font-semibold py-3 px-8 mt-6 transition-colors duration-150 inline-block"
                    >
                        Claim 10% Discount{' >'}
                    </Link>
                </div>
            </div>

            <section className="px-2 sm:px-12 py-2 sm:py-12 container mx-auto">
                <div className="text-center sm:mx-[9rem] flex flex-col sm:flex-none items-center sm:items-start">
                    <h1 className="text-b3 text-[#EFB77C] text-center sm:text-left sm:text-headline font-bold my-8 sm:my-16">Why purchasing Alpha Group?</h1>
                    <div className="grid mb-10 grid-cols-1 sm:grid-cols-3 gap-4 justify-center">
                        {whyChooseUs.map((choose) => (
                            <div key={choose.title} className="rounded-3xl border-2 !border-[#EFB77C] p-[38px] m-2 shadow-md  p-2 sm:p-4" style={{ maxWidth: "335px", maxHeight: "280px", minWidth: "200px" }}>
                                <div className="flex justify-center">
                                    <ImageShortcut
                                        src={choose.icon}
                                        width={50}
                                        height={50}
                                        alt="thumbnail"
                                        className="object-cover h-16 w-16"
                                        priority={true}
                                    />
                                </div>
                                <h1 className="text-b3 sm:text-sub1 font-bold text-center mt-4 text-white">{choose.title}</h1>
                                <p className="text-center text-white mt-2">{choose.description}</p>
                            </div>
                        ))}

                        <div className="rounded-3xl border-2 !border-[#EFB77C] p-[38px] bg-gradient-to-r from-[#EFB77C] to-[#6E4C31] p-2" style={{ maxWidth: "335px", maxHeight: "250px", minWidth: "200px" }}>
                            <h1 className="text-b3 sm:text-sub1 text-white font-bold text-center mt-4">Get Alpha Group <br /> Annual 50% Discount!</h1>
                            <Link
                                aria-label="View products"
                                href={"/checkout/subscription/alpha-group-yearly?previous=/subscriptions"}
                                className="bg-[#fff] text-[#EFB77C] mt-6 hover:bg-blue-700 active:bg-blue-800 rounded-lg text-sub2 sm:text-sub2 font-semibold py-3 px-8 sm:px-6 transition-colors duration-150 inline-block"
                            >
                                Claim Discount {' >'}
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            <section className="px-12 py-6 sm:py-12 bg-gradient-to-r from-[#EFB77C] to-[#6E4C31]">
                <div className="sm:mx-[9rem] flex flex-col sm:flex-row items-center justify-center sm:justify-between text-center sm:text-left"> {/* Center items on mobile, align left on larger screens */}
                    <div className="max-w-md sm:mr-6">
                        <div className="flex justify-center sm:justify-start"> {/* Added flex container */}
                            <ImageShortcut
                                src={discordChat}
                                width={300}
                                height={500}
                                alt="thumbnail"
                                className="object-contain h-80 w-auto p-2"
                                priority={true}
                            />
                        </div>
                    </div>
                    <div className="mt-6 sm:mt-0">
                        <h1 className="text-b3 sm:text-headline font-bold mb-4 text-white">Join CU Community!</h1>
                        <p className="mb-6 text-white">Shape the Future of Finance</p>
                        <Link
                            aria-label="View products"
                            href={"https://discord.com/invite/M9cwwCP49c"}
                            className="bg-[#2655FF] text-white hover:bg-blue-700 active:bg-blue-800 rounded-lg  text-sub1 sm:text-sub2 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150 inline-block"
                        >
                            Join CU Discord {'>'}
                        </Link>
                    </div>
                </div>
            </section>
        </section>
    )
}

export default AlphaGroupMonthlyPromotion
