const SideSkeleton = () => {
    return (
        <div className="min-w-[420px] max-w-[420px] animate-pulse space-y-11 max-md:w-full max-sm:min-w-full max-sm:max-w-full">
            <div className="space-y-8">
                <div className="h-[15px] w-[70%] bg-gray-500/60" />
                <div className="flex flex-wrap items-center gap-7">
                    {Array.from({ length: 5 }).map((_, index) => (
                        <div
                            key={index}
                            className="select-none rounded-full bg-gray-500/60 p-4 transition-colors duration-150"></div>
                    ))}
                </div>
            </div>

            <div className="flex items-center justify-between gap-[2px] py-7 rounded-lg bg-gray-500/60 pl-5 pr-4" />

            <div className="aspect-square bg-gray-500/60 max-sm:min-w-full max-sm:max-w-full" />
        </div>
    )
}

export default SideSkeleton
