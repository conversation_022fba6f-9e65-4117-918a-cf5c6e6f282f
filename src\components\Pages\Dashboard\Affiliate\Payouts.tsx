'use client'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import TransactionModel from '@/types/TransactionModel'
import OrderModel from '../Settings/OrderModel'

interface Model {
    amount: number,
    created_at: string,
}

interface Props {
    payout: Model
}

const Payouts = ({ payout }: Props) => {
    const [openModel, setOpenModel] = useState(false)
    return (
        <>
            <div className="grid grid-cols-12 px-6 place-content-center place-self-center border-b border-gray-400 pb-4 text-cap1 font-medium capitalize max-md:hidden">

                <p className="col-span-3 flex items-center">{payout.created_at.split('T')[0]}</p>
                <p className="col-span-3 flex items-center">${payout.amount}</p>
                <p className="col-span-2 flex items-center">USDT (TRC20)</p>
                <p
                    className={cn(
                        'col-span-2 flex items-center text-cap1 text-green-dark'
                    )}>
                    Completed
                </p>
            </div>
            <div className="flex w-full flex-col gap-4 rounded-md p-4 shadow-md md:hidden">
                <div className="border-b border-gray-300" />
                <div className="flex flex-col gap-2">
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Date</p>
                        <p className="font-medium">{payout.created_at.split('T')[0]}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Amount</p>
                        <p className="font-medium">{payout.amount}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Crypto</p>
                        <p className="font-medium">USDT (TRC20)</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Status</p>
                        <p
                            className={cn(
                                'col-span-2 flex items-center text-cap1 text-green-dark'
                            )}>
                            Completed
                        </p>
                    </div>
                </div>
            </div>
        </>
    )
}

export default Payouts
