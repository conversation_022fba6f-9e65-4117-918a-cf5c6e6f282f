import ImageShortcut from "@/components/Ui/Image"
import Link from "next/link"
import { bitcoin, sponsoredBySVG, discordChat, cyberSecurity, globalLearning, group, onlineLearning, p2p, wallet, wallet1 } from "@public/courses/noones"
const NoonesAcademy = () => {


    const courseOffers = [
        {
            title: 'Bitcoin Basics',
            description: 'A beginner-friendly course covering the fundamentals of Bitcoin, blockchain technology, and how to get started with your first transactions.',
            icon: bitcoin
        },
        {
            title: 'P2P Trading Mastery',
            description: 'Master the art of peer-to-peer trading, harnessing the power of our platform to engage in secure and efficient transactions.',
            icon: p2p
        },
        {
            title: 'Security and Wallet Management',
            description: 'Learn the best practices for securing your Bitcoin holdings and managing your digital wallets with confidence.',
            icon: wallet
        }
    ]
    const headerItems = [
        {
            title: 'Bitcoin Basics'
        },
        {
            title: 'Security and Wallet Management'
        },
        {
            title: 'P2P Trading Mastery'
        }
    ]

    const whyChooseUs = [
        {
            title: 'Global Learning Hub',
            description: 'Connect with learners worldwide, creating a diverse community for mastering Bitcoin.',
            icon: globalLearning
        },
        {
            title: '400+ Payment Methods',
            description: 'Enjoy unmatched flexibility - buy, sell, and store Bitcoin with over 400 payment options.',
            icon: wallet1
        },
        {
            title: 'Tailored Courses',
            description: 'Explore courses for all levels, guided by experts. From basics to advanced, we have got you covered.',
            icon: onlineLearning
        },
        {
            title: 'Learn Security First',
            description: 'Your funds are our priority. Benefit from top-notch security measures for worry-free transactions.',
            icon: cyberSecurity
        },
        {
            title: 'Interactive Community',
            description: 'Engage in discussions, connect with experts, and grow in our dynamic learning environment.',
            icon: group
        }

    ]

    return (
        <>
            <section>

                <section>
                    <div className="relative p-12" style={{ height: '750px' }}>
                        {/* Desktop View */}
                        <div className="hidden sm:block absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden" style={{
                            backgroundImage: `url('https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1709582757/Hero_banner_-_desktop_weepdz.jpg')`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                        }}>
                            <div className="my-2 p-6 sm:p-12 text-white sm:mx-[9rem]">
                                <h1 className="text-left text-b2 sm:text-h2 font-bold mt-2">P2P Academy <br /> Global Reach, Local <br /> Experience</h1>

                                <ImageShortcut
                                    src={sponsoredBySVG}
                                    width={40}
                                    height={20} // Let Next.js handle the aspect ratio
                                    alt="thumbnail"
                                    className="object-cover h-[3rem] w-auto my-6"
                                    priority={true}
                                />

                                <ul className="ml-4 my-8">
                                    {headerItems.map(item => (
                                        <li key={item.title} className="flex items-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                <path fill="#FCC229"
                                                    d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                                                </path>
                                                <path fill="#FCC229" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                <path stroke="#000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5"
                                                    d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            </svg>
                                            <div className="mx-2">{item.title}</div>
                                        </li>
                                    ))}
                                </ul>

                                <Link
                                    aria-label="View products"
                                    href={"/courses/noones-academy"}
                                    className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub2 font-semibold py-3 px-8 mt-6 transition-colors duration-150 inline-block"
                                >
                                    Join the P2P Community Today {'>'}
                                </Link>
                            </div>
                        </div>

                        {/* Mobile View */}
                        <div className="block sm:hidden absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden" style={{
                            backgroundImage: `url('/courses/noones/Hero-banner-mobile.jpg')`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                        }}>
                            <div className="my-2 p-6 sm:p-12 text-white">
                                <h1 className="text-left text-b2 sm:text-h2 font-bold mt-2">P2P Academy <br /> Global Reach, Local <br /> Experience</h1>

                                <ImageShortcut
                                    src={sponsoredBySVG}
                                    width={40}
                                    height={20} // Let Next.js handle the aspect ratio
                                    alt="thumbnail"
                                    className="object-cover h-[3rem] w-auto my-6"
                                    priority={true}
                                />

                                <ul className="ml-4 mt-[15rem]">
                                    {headerItems.map(item => (
                                        <li key={item.title} className="flex items-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                <path fill="#FCC229"
                                                    d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                                                </path>
                                                <path fill="#FCC229" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                <path stroke="#000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5"
                                                    d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            </svg>
                                            <div className="mx-2">{item.title}</div>
                                        </li>
                                    ))}
                                </ul>

                                <Link
                                    aria-label="View products"
                                    href={"/courses/noones-academy"}
                                    className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub3 sm:text-sub2 font-semibold py-3 px-8 mt-6 transition-colors duration-150 inline-block"
                                >
                                    Join the P2P Community Today {'>'}
                                </Link>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Desktop Iframe */}
                <section className="hidden sm:block px-12 py-6 sm:py-12 bg-[#F6F6F6]">
                    <div className="sm:mx-[9rem] flex flex-col sm:flex-row items-center justify-center sm:justify-between">
                        <div className="max-w-md sm:mr-6">
                            <h1 className="text-b3 sm:text-b2 mb-4">How To Make <strong> $20K Per Month </strong> Online By Trading Crypto P2P On Noones</h1>
                            <Link
                                aria-label="View products"
                                href={"https://www.youtube.com/watch?v=Yn8tj59yWkg&ab_channel=CryptoHustle"}
                                className="inline-block bg-[#2655FF] mt-6 text-white hover:bg-blue-700 active:bg-blue-800 rounded-lg  text-sub1 sm:text-sub2 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150"
                            >
                                Watch Video {'>'}
                            </Link>
                        </div>
                        <div className="mt-6 sm:mt-0">
                            <iframe className="rounded" width="509" height="287" src="https://www.youtube.com/embed/Yn8tj59yWkg?si=pQrslQwvWEPfjsYs" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                        </div>
                    </div>
                </section>

                {/* Mobile Iframe */}
                <section className="block sm:hidden px-6 sm:px-12 py-6 sm:py-12 bg-[#F6F6F6]">
                    <div className="sm:mx-[9rem] flex flex-col sm:flex-row items-center justify-center sm:justify-between text-center">
                        <div className="max-w-md sm:mr-6">
                            <h1 className="text-b3 sm:text-b2 mb-4">How To Make <strong> $20K Per Month </strong> Online By Trading Crypto P2P On Noones</h1>
                        </div>
                        <div className="mt-6 sm:mt-0 w-full sm:w-auto flex justify-center"> {/* Center the iframe within its container */}
                            <div className="aspect-w-[22rem] aspect-h-[13rem]"> {/* Aspect ratio for responsive iframe */}
                                <iframe className="rounded" src="https://www.youtube.com/embed/Yn8tj59yWkg?si=pQrslQwvWEPfjsYs" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                            </div>
                        </div>
                        <Link
                            aria-label="View products"
                            href={"https://www.youtube.com/watch?v=Yn8tj59yWkg&ab_channel=CryptoHustle"}
                            className="inline-block bg-[#2655FF] mt-6 text-white hover:bg-blue-700 active:bg-blue-800 rounded-lg text-sub1 sm:text-sub2 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150"
                        >
                            Watch Video {'>'}
                        </Link>
                    </div>
                </section>


                <section className="px-12 py-2 sm:py-12">
                    <div className="text-center sm:mx-[9rem]">
                        <h1 className="text-b3 sm:text-headline text-center sm:text-left font-bold my-8 sm:my-16">Course offers</h1>
                        <div className="flex flex-col sm:flex-row items-center sm:items-start">
                            <div className="w-full sm:w-1/3 border border-[#DADADA] flex justify-center">
                                <ImageShortcut
                                    src={'https://res.cloudinary.com/cryptouniversitynetwork/image/upload/v1709665071/c3ec11aa13c4bdd3bc19788af94ec9d4_qqj2z8.png'}
                                    width={300}
                                    height={500}
                                    alt="thumbnail"
                                    className="object-cover h-80 w-60 p-6"
                                    priority={true}
                                />
                            </div>
                            <div className="grid grid-cols-1 gap-2 w-full sm:w-2/3 divide-y space-y-4 py-4">
                                {courseOffers.map((offer, index) => (
                                    <div key={index} className="flex flex-row items-start">
                                        <div className="mr-4">
                                            <ImageShortcut
                                                src={offer.icon}
                                                width={50}
                                                height={50}
                                                alt="thumbnail"
                                                className="object-contain h-13 w-auto mx-3"
                                                priority={true}
                                            />
                                        </div>
                                        <div>
                                            <h1 className="text-b3 sm:text-sub3 pl-2 mb-2 font-bold text-left mt-2">{offer.title}</h1>
                                            <p className="text-left text-sub3 pl-2">{offer.description}</p>
                                        </div>
                                        <hr />
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="sm:ml-auto mt-4"> {/* Move this div to the right side */}
                            <Link
                                aria-label="View products"
                                href={"/courses/noones-academy"}
                                className="bg-[#FFF] text-blue sm:w-[24rem] border border-blue hover:bg-yellow-600 py-3 px-2 sm:px-8 active:bg-yellow-800 rounded-lg text-sub3 sm:text-sub2 font-semibold transition-colors duration-150 inline-block"
                            >
                                Join the P2P Community Today {'>'}
                            </Link>
                        </div>
                    </div>
                </section>


                <section className="px-2 sm:px-12 py-2 sm:py-12 container mx-auto">
                    <div className="text-center sm:mx-[9rem] flex flex-col sm:flex-none items-center sm:items-start">
                        <h1 className="text-b3 text-center sm:text-left sm:text-headline font-bold my-8 sm:my-16">Why choose P2P Academy?</h1>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 justify-center">
                            {whyChooseUs.map((choose) => (
                                <div key={choose.title} className="border border-[#DADADA] p-2 sm:p-4" style={{ maxWidth: "335px", maxHeight: "250px", minWidth: "200px" }}>
                                    <div className="flex justify-center">
                                        <ImageShortcut
                                            src={choose.icon}
                                            width={50}
                                            height={50}
                                            alt="thumbnail"
                                            className="object-cover h-16 w-16"
                                            priority={true}
                                        />
                                    </div>
                                    <h1 className="text-b3 sm:text-sub1 font-bold text-center mt-4">{choose.title}</h1>
                                    <p className="text-center mt-2">{choose.description}</p>
                                </div>
                            ))}

                            <div className="border bg-blue border-[#DADADA] p-4" style={{ maxWidth: "335px", maxHeight: "250px", minWidth: "200px" }}>
                                <h1 className="text-b3 sm:text-sub1 text-white font-bold text-center mt-4">Join the P2P <br /> Community Today!</h1>
                                <Link
                                    aria-label="View products"
                                    href={"https://discord.com/invite/M9cwwCP49c"}
                                    className="bg-[#fff] mt-6 text-blue hover:bg-blue-700 active:bg-blue-800 rounded-lg text-sub1 sm:text-sub3 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150 inline-block"
                                >
                                    Join CU Discord {'>'}
                                </Link>
                            </div>
                        </div>
                    </div>
                </section>

                <section className="px-12 py-6 sm:py-12 bg-[#F6F6F6]">
                    <div className="sm:mx-[9rem] flex flex-col sm:flex-row items-center justify-center sm:justify-between text-center sm:text-left"> {/* Center items on mobile, align left on larger screens */}
                        <div className="max-w-md sm:mr-6">
                            <div className="flex justify-center sm:justify-start"> {/* Added flex container */}
                                <ImageShortcut
                                    src={discordChat}
                                    width={300}
                                    height={500}
                                    alt="thumbnail"
                                    className="object-contain h-80 w-auto p-2"
                                    priority={true}
                                />
                            </div>
                        </div>
                        <div className="mt-6 sm:mt-0">
                            <h1 className="text-b3 sm:text-headline font-bold mb-4">Join the P2P Community Today!</h1>
                            <p className="mb-6">Shape the Future of Finance</p>
                            <Link
                                aria-label="View products"
                                href={"https://discord.com/invite/M9cwwCP49c"}
                                className="bg-[#2655FF] text-white hover:bg-blue-700 active:bg-blue-800 rounded-lg  text-sub1 sm:text-sub2 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150 inline-block"
                            >
                                Join CU Discord {'>'}
                            </Link>
                        </div>
                    </div>
                </section>

            </section>
        </>
    )
}

export default NoonesAcademy
