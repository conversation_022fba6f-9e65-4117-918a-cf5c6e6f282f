import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import Kit from '@/components/Pages/Affiliate/Kit'
import Earn from '@/components/Pages/Affiliate/Earn'
import Hero from '@/components/Pages/Affiliate/Hero'
import Video from '@/components/Pages/Affiliate/Video'
import Benefits from '@/components/Pages/Affiliate/Benefits'
import AffiliateFaqs from '@/components/Pages/Affiliate/FAQs'

export const metadata = {
    title: 'Affiliates',
    description:
        'Get paid directly in cryptocurrency every week! · Affiliates receive 30% of sales, and partners can earn up to 35%.',
    keywords: ['Affiliates', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const AffiliatesPage = () => {
    return (
        <section className="flex w-full flex-col">
            <Hero />
            <Video />
            <Benefits />
            <Earn />
            <AffiliateFaqs />
            <Kit />
            {/* <section id='question' className="container mx-auto pb-[20px] max-md:py-9">
                <div className="flex- flex items-center gap-10 max-md:flex-col max-md:items-start max-md:gap-0">
                    <ImageShortcut
                        src={'/join/contact.png'}
                        width={280}
                        height={280}
                        className={'max-md:hidden max-md:h-auto max-md:w-[190px]'}
                        alt={'Contact Icon'}
                    />
                    <div className="flex flex-col gap-8 max-md:gap-4">
                        <div className="flex flex-col gap-2 max-md:gap-[0.375rem]">
                            <p className="text-headline font-semibold max-md:text-b3">Have Any Questions?</p>
                        </div>
                        <div className="flex flex-col gap-5">
                            <div className="w-[310px] max-md:w-[220px]">
                                <Button gradient variant="primary" rounded>
                                    <Link
                                        href={'mailto:<EMAIL>'}
                                        aria-label="Email crypto university">
                                        Help {'>'}
                                    </Link>
                                </Button>
                            </div>
                            <div className="flex flex-wrap gap-1 text-callout max-md:flex-col max-md:gap-2 max-md:text-sub3">
                                <p>OR Shoot us an email on:</p>{' '}
                                <span className="text-blue">
                                    {' '}
                                    <Link
                                        aria-label="Email crypto university"
                                        href={'mailto:<EMAIL>'}>
                                        <EMAIL>
                                    </Link>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </section> */}
        </section>
    )
}

export default AffiliatesPage
