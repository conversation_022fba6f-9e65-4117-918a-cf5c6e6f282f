import Link from "next/link";
import Marquees from "./Marquees";

const LoopSection = () => {
  return (
    <section className="bg-[#0B0E11] text-white border-t border-[#2B2B2B] border-b">
      {/* Loop */}
      <Marquees />

      {/* CTA */}
      <div className="container mx-auto pt-7 pb-20 flex flex-col items-center gap-7">
        <div className="flex gap-8 flex-col items-center">
          <h3 className="text-headline max-md:text-b3 text-center font-semibold">
            Learn. Trade. Invest. Play
          </h3>
          <p className="text-sub3 max-md:text-[14px] max-md:leading-[1.8em] leading-6 font-medium max-w-[701px] text-center">
            Learn about trading and investing in Cryptocurrencies, Altcoins, Top
            Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other
            cryptocurrencies.
          </p>
        </div>

        <Link
          aria-label="View products"
          href={"/products"}
          className="bg-[#00B24A] hover:bg-[#00a043] active:bg-[#017933] rounded-lg text-sub3 font-semibold py-3 px-[0.938rem] transition-colors duration-150"
        >
          Cryptocurrency Courses
        </Link>
      </div>
    </section>
  );
};

export default LoopSection;
