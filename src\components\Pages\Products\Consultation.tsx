import Link from 'next/link'
import Current from './Current'
import { Whishlist } from '@public/home'
import numberWithCommas from '@/lib/formatNumber'
import ImageShortcut from '@/components/Ui/Image'

const Consultation = ({ consultation }: any) => {
    return (
        <div className="space-y-9">
            <h2 className="text-headline font-medium max-md:text-callout">1 on 1 Consultation</h2>

            <div className="flex flex-col gap-y-7">
                {consultation.length > 0 ? (
                    consultation.map((call: any) => (
                        <div
                            key={call.id}
                            className="relative flex max-w-[1015px] items-center gap-4 rounded-lg border border-gray-700 px-4 pb-8 pt-7 max-md:flex-col max-md:items-start max-md:gap-2 max-md:px-3 max-md:pb-4 max-md:pt-[0.875rem]">
                            <div className="flex items-center gap-[0.625rem]">
                                <ImageShortcut
                                    src={call.image}
                                    height={283}
                                    width={244}
                                    className="h-auto w-[244px] object-cover max-md:w-[115px]"
                                    alt={call.name}
                                />
                                <div className="space-y-[0.375rem]">
                                    <h3 className="text-sub1 font-medium capitalize md:hidden">{call.name}</h3>
                                    {/* Mentorship price */}
                                    {call.sale ? (
                                        <>
                                            <h5 className="flex items-center gap-1 text-callout font-medium max-md:text-sub2">
                                                ${call.finalPrice - call.tax}{' '}
                                                <span className="text-cap1 text-green-dark max-md:text-cap3">
                                                    (-{call.sale}%)
                                                </span>
                                            </h5>
                                            <p className="text-sub2 text-gray-900 line-through max-md:text-sub3">
                                                ${parseInt(call.price || '0') - call.tax}
                                            </p>
                                        </>
                                    ) : (
                                        <p className="text-sub2 font-semibold md:hidden">
                                            ${numberWithCommas(call.price)}
                                        </p>
                                    )}
                                </div>
                            </div>

                            <div className="flex flex-col gap-10 max-md:gap-4">
                                <div className="space-y-3">
                                    <h3 className="text-b3 font-medium capitalize max-md:hidden">{call.name}</h3>
                                    <p className="max-w-[670px] text-sub3">{call.short_description}</p>
                                </div>

                                <div className="flex items-center justify-between">
                                    <p className="text-headline font-semibold max-md:hidden">
                                        ${numberWithCommas(call.price - call.tax)}
                                    </p>
                                    <div className='hidden sm:block'>
                                        <Link
                                            aria-label="Learn More"
                                            href={`/mentorships/${call.slug}`}
                                            className="text-sub2 text-red fornt-normal underline-offset-4 focus:underline active:underline">
                                            Learn More &gt;
                                        </Link>
                                    </div>
                                    <Current {...call} />
                                </div>

                                <div className="flex items-center justify-between md:hidden">
                                    <div className="flex items-center gap-1 text-cap1">
                                        <Whishlist active={false} /> Wishlist
                                    </div>
                                    <Link
                                        aria-label="Learn More"
                                        href={`/mentorships/${call.slug}`}
                                        className="text-cap1 font-semibold underline-offset-4 focus:underline active:underline">
                                        Learn More &gt;
                                    </Link>
                                </div>
                            </div>
                            <div className="absolute right-6 top-6 flex select-none items-center gap-1 text-sub3 leading-none max-md:hidden">
                                <Whishlist active={false} /> Wishlist
                            </div>
                        </div>
                    ))
                ) : (
                    <p>No consultations available</p>
                )}
            </div>
        </div>
    )
}

export default Consultation
