"use client"

import Image from 'next/image'
import React, { useEffect, useRef, useState } from 'react'
import { star, line } from '@public/memberships'
import StudentTestimonies from './StudentTestimonies'
import { MdChevronLeft, MdChevronRight } from 'react-icons/md'
import Link from 'next/link'


const OurStudents = () => {

const [scrollValue, setScrollValue] = useState(0);
  const containerRef = useRef<HTMLDivElement | null>(null)

  const handleScroll = (delta: number) => {
    setScrollValue((prevScrollValue) => prevScrollValue + delta);
  };

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollLeft = scrollValue;
    }
  }, [scrollValue]);

    return (
        <section className="overflow-hidden border-b-[1px] border-t-[1px] p-[32px] md:p-[64px]">
            <h2 className="mb-1 text-center text-[18px] font-[600] text-[#222222] md:mb-3 md:hidden md:text-left md:text-[28px] ">
                Our students love us!
            </h2>
            <div className="mb-3 hidden items-center justify-between text-center md:flex">
                <h2 className="mb-1 text-center text-[18px] font-[600] text-[#222222] md:mb-3 md:text-left md:text-[28px]">
                    Our students love us!
                </h2>
                <div className="mt-4 hidden gap-4 scroll-smooth md:flex">
                    <button
                        className="scroll-smoothbg-green-300 hover:bg-green-500 flex h-8 w-8 cursor-pointer items-center  justify-center rounded-full border text-black hover:bg-[#717171] hover:text-white hover:shadow-lg"
                        onClick={() => handleScroll(-200)}>
                        <MdChevronLeft className="text-lg " />
                    </button>
                    <button
                        className="bg-green-300 hover:bg-green-500 hover:text-whitehover:shadow-lg flex h-8 w-8 cursor-pointer items-center justify-center scroll-smooth rounded-full border text-black transition-all duration-100 ease-in-out hover:bg-[#717171] hover:text-white"
                        onClick={() => handleScroll(200)}>
                        <MdChevronRight className="text-lg " />
                    </button>
                </div>
            </div>
            <div className="flex items-center md:hidden">
                <Image src={star} alt="video" width={36.88} height={3.07} className=" rounded-[4.0px]" />
                <h2 className="text-[16px] text-[#191919] md:text-[28px]">Trustpilot</h2>
                <Image src={line} alt="video" width={2} height={1} className="mx-2 " />
                <h3 className=" text-[12px] font-[400] md:text-[18px]">
                    Rated <span className="font-[600]">4.5</span> out of 5 Based
                </h3>
            </div>
            <section className="w-full gap-6" id="testimony-container" ref={containerRef}>
                <StudentTestimonies scrollValue={scrollValue} flag={true} />
            </section>

            <div className="mx-auto mt-4 flex items-center justify-center gap-4 scroll-smooth text-center  md:hidden">
                <button
                    className="bg-green-300 hover:bg-green-500 flex h-8 w-8 cursor-pointer items-center  justify-center scroll-smooth rounded-full border text-black hover:bg-[#717171] hover:text-white hover:shadow-lg "
                    onClick={() => handleScroll(-200)}>
                    <MdChevronLeft className="text-lg " />
                </button>
                <button
                    className="bg-green-300 hover:bg-green-500 flex h-8 w-8 cursor-pointer items-center justify-center scroll-smooth rounded-full border text-black transition-all duration-100 ease-in-out hover:bg-[#717171] hover:text-white hover:shadow-lg"
                    onClick={() => handleScroll(200)}>
                    <MdChevronRight className="text-lg " />
                </button>
            </div>

            <section className="my-3 flex justify-between">
                <div className="hidden items-center md:flex">
                    <Image
                        src={star}
                        alt="video"
                        width={36.88}
                        height={3.07}
                        className="hidden rounded-[4.0px] md:block"
                    />
                    <h2 className="text-[28px] text-[#191919]">Trustpilot</h2>
                    <Image src={line} alt="video" width={3} height={2} className="mx-3 " />
                    <h3 className=" text-[18px] font-[400]">
                        Rated <span className="font-[600]">4.5</span> out of 5 Based
                    </h3>
                </div>

                <Link
                    href={'https://uk.trustpilot.com/review/cryptouniversity.network'}
                    className="h-[48px] w-[343px] rounded-[8px] border-[1px] p-3 text-[14px] font-[600] justify-center text-center">
                    Read More reviews on Trustpilot{' '}
                </Link>
            </section>
        </section>
    )
}

export default OurStudents
