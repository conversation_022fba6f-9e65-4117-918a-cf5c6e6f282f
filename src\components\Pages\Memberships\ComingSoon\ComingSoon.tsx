
'use client'
import { useState } from 'react'
import 'react-responsive-modal/styles.css'
import './Membership.css'
import Image from 'next/image'

export const ComingSoon = () => {
    const [email, setEmail] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [message, setMessage] = useState('')
    const [messageType, setMessageType] = useState<'success' | 'error' | ''>('')

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!email) {
            setMessage('Please enter your email.')
            setMessageType('error')
            return
        }

        setIsLoading(true)
        setMessage('')
        setMessageType('')

        try {
            const res = await fetch(`${process.env.API_URL}/subscription/alpha-group/waitlist`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            })

            const data = await res.json()

            if (res.ok && data.success) {
                setMessage('You have successfully been added to the waitlist!')
                setMessageType('success')
                setEmail('')
            } else {
                throw new Error(data?.message || 'Something went wrong')
            }
        } catch (error: any) {
            setMessage(error.message || 'An unexpected error occurred.')
            setMessageType('error')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <section>
            <div className="bgg h-[35em] relative">
                {/* Floating background elements */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-indigo-500 opacity-20 floating"></div>
                    <div className="absolute top-40 right-20 w-32 h-32 rounded-full bg-purple-500 opacity-20 floating-slow"></div>
                    <div className="absolute bottom-20 left-1/4 w-24 h-24 rounded-full bg-pink-500 opacity-20 floating-slower"></div>
                    <div className="absolute top-1/3 right-1/3 w-16 h-16 rounded-full bg-indigo-300 opacity-20 floating"></div>
                </div>

                <div className="container mx-auto px-4 py-12 relative z-10">
                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div className="text-center md:text-left">
                            <h1 className="text-headline md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-white">
                                Alpha Group <span className="text-transparent bg-clip-text bg-[#EFB77C]">Subscription</span> Update!
                            </h1>
                            <p className="text-lg md:text-xl text-indigo-100 mb-8 max-w-lg mx-auto md:mx-0">
                                We’ve reached the limit of available Alpha Group seats. To maintain service quality, we’re no longer accepting new subscribers. New spots will open soon. Thank you for your patience.
                            </p>

                            <div className="max-w-md mx-auto md:mx-0">
                                {/* Alert Message */}
                                {message && (
                                    <div
                                        className={`mb-4 p-3 rounded-lg text-sm font-medium border ${
                                            messageType === 'success'
                                                ? 'bg-[#f8f8f8] text-[#22BB33] border-[#22BB33]'
                                                : 'bg-[#f8f8f8] text-[#BB2124] border-[#BB2124]'
                                        }`}
                                    >
                                        {message}
                                    </div>
                                )}

                                <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
                                    <input
                                        type="email"
                                        placeholder="Join the waitlist"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        className="flex-grow px-4 py-3 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#EFB77C] bg-white/10 backdrop-blur-sm border border-white/20"
                                    />
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="px-6 py-3 bg-gradient-to-r from-[#EFB77C] to-[#6E4C31] 
                                        hover:from-[#f1c68d] hover:to-[#5a3c27]
                                        rounded-lg font-medium transition-all duration-200 whitespace-nowrap flex items-center justify-center gap-2 disabled:opacity-50"
                                    >
                                        {isLoading ? (
                                            <>
                                                <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                                                Loading...
                                            </>
                                        ) : (
                                            'Notify Me'
                                        )}
                                    </button>
                                </form>

                                <p className="text-xs text-[#EFB77C] mt-3">
                                    We’ll email you as soon as a spot opens up.
                                </p>
                            </div>

                            <div className="mt-10 flex justify-center md:justify-start space-x-6">
                                <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
                                    <i className="fab fa-twitter text-xl"></i>
                                </a>
                                <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
                                    <i className="fab fa-instagram text-xl"></i>
                                </a>
                                <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
                                    <i className="fab fa-facebook text-xl"></i>
                                </a>
                                <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
                                    <i className="fab fa-linkedin-in text-xl"></i>
                                </a>
                            </div>
                        </div>

                        {/* Optional image section */}
                        {/* <div className="hidden md:block">
                            <div className="relative">
                                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full opacity-20 blur-3xl"></div>
                                <Image
                                    width={80}
                                    height={100}
                                    src="https://cdn.pixabay.com/photo/2018/11/29/21/51/social-media-3846597_1280.png"
                                    alt="Illustration"
                                    className="relative z-10 mx-auto floating-slow"
                                />
                            </div>
                        </div> */}
                    </div>
                </div>
            </div>
        </section>
    )
}

// 'use client'
// import { useState } from 'react'
// import 'react-responsive-modal/styles.css'
// import './Membership.css'
// import Image from 'next/image'

// export const ComingSoon = () => {
//     const [email, setEmail] = useState('')
//     const [isLoading, setIsLoading] = useState(false)
//     const [message, setMessage] = useState('')
//     const [messageType, setMessageType] = useState<'success' | 'error' | ''>('')

//     const handleSubmit = (e: React.FormEvent) => {
//         e.preventDefault()

//         if (!email) {
//             setMessage('Please enter your email.')
//             setMessageType('error')
//             return
//         }

//         setIsLoading(true)
//         setMessage('')
//         setMessageType('')

//         // Simulate async API call
//         setTimeout(() => {
//             setIsLoading(false)
//             setMessage('You have successfully been added to the waitlist!')
//             setMessageType('success')
//             setEmail('')
//         }, 2000)
//     }

//     return (
//         <section>
//             <div className="bgg h-[35em]">
//                 {/* Floating background elements */}
//                 <div className="absolute inset-0 overflow-hidden pointer-events-none">
//                     <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-indigo-500 opacity-20 floating"></div>
//                     <div className="absolute top-40 right-20 w-32 h-32 rounded-full bg-purple-500 opacity-20 floating-slow"></div>
//                     <div className="absolute bottom-20 left-1/4 w-24 h-24 rounded-full bg-pink-500 opacity-20 floating-slower"></div>
//                     <div className="absolute top-1/3 right-1/3 w-16 h-16 rounded-full bg-indigo-300 opacity-20 floating"></div>
//                 </div>

//                 <div className="container mx-auto px-4 py-12 relative z-10">
//                     <div className="grid md:grid-cols-2 gap-12 items-center">
//                         <div className="text-center md:text-left">
//                             <h1 className="text-headline md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-white">
//                                 Alpha Group <span className="text-transparent bg-clip-text bg-[#EFB77C]">Subscription</span> Update!
//                             </h1>
//                             <p className="text-lg md:text-xl text-indigo-100 mb-8 max-w-lg mx-auto md:mx-0">
//                                 We’ve reached the limit of available Alpha Group seats. To maintain service quality, we’re no longer accepting new subscribers. New spots will open soon. Thank you for your patience.
//                             </p>

//                             <div className="max-w-md mx-auto md:mx-0">
//                                 {/* Alert Message */}
//                                 {message && (
//                                     <div
//                                         className={`mb-4 p-3 rounded-lg text-sm font-medium border ${
//                                             messageType === 'success'
//                                                 ? 'bg-[#f8f8f8] text-[#22BB33] border-[#22BB33]'
//                                                 : 'bg-[#f8f8f8] text-[#BB2124] border-[#BB2124]'
//                                         }`}
//                                     >
//                                         {message}
//                                     </div>
//                                 )}

//                                 <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
//                                     <input
//                                         type="email"
//                                         placeholder="Join the waitlist"
//                                         value={email}
//                                         onChange={(e) => setEmail(e.target.value)}
//                                         className="flex-grow px-4 py-3 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#EFB77C] bg-white/10 backdrop-blur-sm border border-white/20"
//                                     />
//                                     <button
//                                         type="submit"
//                                         disabled={isLoading}
//                                         className="px-6 py-3 bg-gradient-to-r from-[#EFB77C] to-[#6E4C31] 
//                                         hover:from-[#f1c68d] hover:to-[#5a3c27]
//                                         rounded-lg font-medium transition-all duration-200 whitespace-nowrap disabled:opacity-50"
//                                     >
//                                         {isLoading ? 'Loading...' : 'Notify Me'}
//                                     </button>
//                                 </form>

//                                 <p className="text-xs text-[#EFB77C] mt-3">
//                                     We’ll email you as soon as a spot opens up.
//                                 </p>
//                             </div>

//                             <div className="mt-10 flex justify-center md:justify-start space-x-6">
//                                 <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
//                                     <i className="fab fa-twitter text-xl"></i>
//                                 </a>
//                                 <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
//                                     <i className="fab fa-instagram text-xl"></i>
//                                 </a>
//                                 <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
//                                     <i className="fab fa-facebook text-xl"></i>
//                                 </a>
//                                 <a href="#" className="text-indigo-300 hover:text-white transition-colors duration-200">
//                                     <i className="fab fa-linkedin-in text-xl"></i>
//                                 </a>
//                             </div>
//                         </div>

//                         {/* Right side image - optional */}
//                         {/* <div className="hidden md:block">
//                             <div className="relative">
//                                 <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full opacity-20 blur-3xl"></div>
//                                 <Image
//                                     width={80}
//                                     height={100}
//                                     src="https://cdn.pixabay.com/photo/2018/11/29/21/51/social-media-3846597_1280.png"
//                                     alt="Illustration"
//                                     className="relative z-10 mx-auto floating-slow"
//                                 />
//                             </div>
//                         </div> */}
//                     </div>
//                 </div>
//             </div>
//         </section>
//     )
// }
