'use client'
import { useState } from 'react'
import { user } from '@/types/UserModel'
import { useRouter } from 'next/navigation'
import Button from '@/components/Ui/Button'
import { GetCurrentSession } from '@/lib/session'
import useProfileSetting from '@/hooks/useProfileSetting'
import Label from '@/components/Ui/Label'
import useAuth from '@/hooks/useAuth'
import ButtonSpinner from '@/components/Ui/buttonSpinner'
import { toast } from 'react-toastify'
import Select, { GroupBase, StylesConfig } from 'react-select'
import { Country } from '@/types/CountryModel'

type InputV2Props = {
    value: string | number | readonly string[] | undefined
    type: string
    name: string
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
    placeholder?: string
    disabled?: boolean
}

// Icons
const NotVerified = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#FC0019" className="h-8 w-8">
        <path
            fillRule="evenodd"
            d="M11.484 2.17a.75.75 0 011.032 0 11.209 11.209 0 007.877 ********** 0 01.722.515 12.74 12.74 0 01.635 3.985c0 5.942-4.064 10.933-9.563 12.348a.749.749 0 01-.374 0C6.314 20.683 2.25 15.692 2.25 9.75c0-1.39.223-2.73.635-3.985a.75.75 0 01.722-.516l.143.001c2.996 0 5.718-1.17 7.734-3.08zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zM12 15a.75.75 0 00-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 00.75-.75v-.008a.75.75 0 00-.75-.75H12z"
            clipRule="evenodd"
        />
    </svg>
)
const Verified = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="green" className="h-8 w-8">
        <path
            fillRule="evenodd"
            d="M12.516 2.17a.75.75 0 00-1.032 0 11.209 11.209 0 01-7.877 ********** 0 00-.722.515A12.74 12.74 0 002.25 9.75c0 5.942 4.064 10.933 9.563 12.348a.749.749 0 00.374 0c5.499-1.415 9.563-6.406 9.563-12.348 0-1.39-.223-2.73-.635-3.985a.75.75 0 00-.722-.516l-.143.001c-2.996 0-5.717-1.17-7.734-3.08zm3.094 8.016a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
            clipRule="evenodd"
        />
    </svg>
)

// Components
const InputV2 = ({ value, type, name, onChange, placeholder, disabled = false }: InputV2Props) => {
    return (
        <input
            type={type}
            name={name}
            value={value ?? ''}
            onChange={onChange}
            placeholder={placeholder}
            className="w-full max-w-full rounded-full border border-gray-700 p-4 ring-0 focus:border-blue focus:outline-none disabled:cursor-not-allowed disabled:bg-gray-200 max-md:py-3.5 max-md:text-cap1"
            disabled={disabled}
        />
    )
}
const Item = ({
    children,
    required = false,
    label,
}: {
    children: React.ReactNode
    label: string
    required?: boolean
}) => {
    return (
        <div className="flex w-full flex-col gap-2">
            <Label uppercase={false} required={required}>
                {label}
            </Label>
            {children}
        </div>
    )
}

const UpdateForm = ({ user, token, countries }: { user: user; token: string; countries: Country[] }) => {
    const session = GetCurrentSession()
    const router = useRouter()

    const { updateProfile, error, isLoading } = useProfileSetting(token)
    const countryNames = countries.map((country: Country) => ({
        value: country.name.common,
        label: country.name.common,
    }))
    //   Verify Email
    const { verifyEmail } = useAuth()
    const [loading, setLoading] = useState(false)
    const [isError, setIsError] = useState('')
    const handleSubmitEmail = async () => {
        try {
            setLoading(true)
            await verifyEmail(user.email)
            setLoading(false)
        } catch (error: any) {
            setLoading(false)
            setIsError(error.response.data.message)
        }
    }

    const [formData, setFormData] = useState<any>({
        ...(user ?? {
            email: '',
            metamask: '',
            bio: '',
            image: '',
            first_name: '',
            last_name: '',
            display_name: '',
            country: '',
            address: '',
            town: '',
            region: '',
            postal_code: '',
            phone: '',
            discord: '',
            telegram: '',
        }),
    })

    const handleChange = (e: any) => {
        const { name, value } = e.target
        setFormData((prevData: any) => ({
            ...prevData,
            [name]: value,
        }))
    }
    const handleChangeCountry = (e:any) => {
        const {value} = e
        setFormData((prevData: any) => ({
            ...prevData,
            country: value,
        }))
    }
    const handleSubmit = async (e: any) => {
        e.preventDefault()
        try {
            const res = await updateProfile(formData)
            toast.success('Profile updated successfully')
            session.update({ ...session, user: res.data.user })
            router.refresh()
        } catch (error: any) {
            console.error(error)
            toast.error('Email already exist')
        }
    }
    return (
        <form className="gird-cols-2 grid max-w-[800px] space-y-6 pb-4" onSubmit={handleSubmit}>
            <div className="col-span-2 flex items-center gap-4">
                <Item label="Your First Name" required>
                    <InputV2
                        type="text"
                        name="first_name"
                        value={formData.first_name ?? ''}
                        placeholder="e.g. John"
                        onChange={handleChange}
                    />
                </Item>

                <Item label="Your Last Name" required>
                    <InputV2
                        type="text"
                        name="last_name"
                        value={formData.last_name ?? ''}
                        onChange={handleChange}
                        placeholder="e.g. Doe"
                    />
                </Item>
            </div>

            <div className="col-span-2 flex w-full gap-2">
                <Item label="Display Name (Username)" required>
                    <InputV2
                        type="text"
                        name="display_name"
                        value={formData.display_name ?? ''}
                        onChange={handleChange}
                    />
                </Item>
                <div className="col-span-1 flex w-full" />
            </div>

            <div className="col-span-2 flex w-full gap-4">
                <Item label="Email" required>
                    <div className="relative rounded-full border border-gray-700">
                        <input
                            type="text"
                            name="email"
                            value={formData.email ?? ''}
                            onChange={handleChange}
                            placeholder="e.g. <EMAIL>"
                            className="w-full max-w-full rounded-full p-4 pr-14 ring-0 focus:border-blue focus:outline-none disabled:cursor-not-allowed max-md:py-3.5 max-md:text-cap1"
                        />
                        <span className="qs absolute right-4 top-1/2 -translate-y-1/2 transform cursor-pointer">
                            {user.is_email_verified ? (
                                <button type="button" className="pt-1">
                                    <Verified />
                                    <span className="popover above">Email verified</span>
                                </button>
                            ) : (
                                <button type="button" className="pt-1" onClick={handleSubmitEmail}>
                                    <NotVerified />
                                    <span className="popover above">{loading ? 'Loading' : 'Email not verified'}</span>
                                </button>
                            )}
                        </span>
                    </div>
                </Item>
                <Item label="Country" required>
                    <div className="relative flex items-center rounded-full">
                        <Select
                            name="country"
                            onChange={handleChangeCountry}
                            options={countryNames}
                            instanceId="country"
                            value={countryNames.find(option => option.value === formData.country)}
                            theme={theme => ({ ...theme, borderRadius: 0 })}
                            placeholder={"Select your country"}
                            isSearchable={true}
                            styles={{
                                control: (provided, state) => ({
                                    width: '100%',
                                    paddingTop: '14px',
                                    paddingBottom: '14px',
                                    height: '100%',
                                    border: state.isFocused
                                        ? '1px solid #2563EB'
                                        : ''
                                        ? '1px solid red'
                                        : '1px solid #AAAAAA',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    paddingRight: '0px',
                                    paddingLeft: '0.5rem',
                                    borderRadius: '50px',
                                }),
                                valueContainer: (provided, state) => ({
                                    padding: '0pxs',
                                    paddingLeft: '35px',
                                    alignItems: 'center',
                                    display: 'grid',
                                    flex: '1',
                                    flexWrap: 'wrap',
                                    position: 'relative',
                                    overflow: 'hidden',
                                    boxSizing: 'border-box',
                                }),
                            }}
                            openMenuOnFocus
                            className="relative h-[62px] w-full rounded-[50px] px-0 outline-none transition-[border] duration-150 placeholder:text-gray-700 max-md:max-h-[50px] max-md:text-cap1"
                        />
                    </div>
                </Item>
            </div>

            <div className="col-span-2 flex w-full">
                <Item label="Bio" required>
                    <InputV2
                        type="text"
                        name="bio"
                        value={formData.bio ?? ''}
                        onChange={handleChange}
                        placeholder="what you want people to know about you"
                    />
                </Item>
            </div>

            <div className="col-span-2 flex w-full gap-4">
                <Item label="Town" required>
                    <InputV2
                        type="text"
                        name="town"
                        value={formData.town ?? ''}
                        onChange={handleChange}
                        placeholder="what you want people to know about you"
                    />
                </Item>
                <Item label="Address" required>
                    <InputV2
                        type="text"
                        name="address"
                        value={formData.address ?? ''}
                        onChange={handleChange}
                        placeholder="what you want people to know about you"
                    />
                </Item>
            </div>

            <div className="col-span-2 flex w-full">
                <Item label="Discord" required>
                    <InputV2
                        type="text"
                        name="discord"
                        value={formData.discord ?? ''}
                        onChange={handleChange}
                        placeholder="john#1234"
                    />
                </Item>
            </div>
            <div className="max-w-[250px]">
                <Button type="submit" variant="primary" className="" rounded disabled={isLoading}>
                    {isLoading && <ButtonSpinner />}Update
                </Button>
            </div>
            {(error || isError) && <p className="text-cap1 text-red">{error || isError}</p>}
        </form>
    )
}

export default UpdateForm
