'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import InstructorModel from '@/types/InstructorModel'
import MarkdownComponent from '@/components/Ui/MarkdownComponent'
import { Facebook, Instagram, LinkedIn, Twitter, Youtube } from '@public/instructor'

interface Props {
    instructor: InstructorModel
}

const Instructors = ({ instructor }: Props) => {
    const [showFullText, setShowFullText] = useState(false)
    const data = [
        ...(instructor?.facebook ? [{ socials: <Facebook />, instructor: instructor.facebook, socialMedia: "Facebook" }] : []),
        ...(instructor?.instagram ? [{ socials: <Instagram />, instructor: instructor.instagram, socialMedia: "Instagram" }] : []),
        ...(instructor?.linkedin ? [{ socials: <LinkedIn />, instructor: instructor.linkedin, socialMedia: "LinkedIn" }] : []),
        ...(instructor?.twitter ? [{ socials: <Twitter />, instructor: instructor.twitter, socialMedia: "Twitter" }] : []),
        ...(instructor?.youtube ? [{ socials: <Youtube />, instructor: instructor.youtube, socialMedia: "Youtube" }] : []),
    ]
    return (
        <div className=" pr-11 max-md:container max-md:mx-auto">
            <div className="flex flex-col gap-6 border-gray-700 pb-16 max-md:pb-6 ">
                <h1 className="text-b3 max-md:text-sub1">Instructors</h1>
                <div className="flex flex-col items-start gap-6">
                    <div className="flex items-center gap-4 ">
                        <ImageShortcut
                            src={instructor?.image}
                            width={48}
                            height={48}
                            className={
                                'min-h-[48px] min-w-[48px] rounded-full object-cover opacity-0 transition-[opacity] duration-150 max-md:h-auto'
                            }
                            alt={'Profile Picture'}
                        />
                        <div className="flex flex-col gap-1">
                            <h1 className="text-sub1 font-semibold max-md:text-sub2">{instructor?.name}</h1>
                        </div>
                    </div>
                    <div className="flex items-start gap-3 max-md:flex-col max-md:gap-5">
                        <div className="flex flex-col gap-5 px-[14px] max-md:hidden max-md:flex-row max-md:items-end">
                            {data.map((item, index) => (
                                <Link
                                    aria-label="Instructor Socials"
                                    target={'_blank'}
                                    key={index}
                                    href={item?.instructor ?? ''}>
                                    {item?.socials}
                                </Link>
                            ))}
                        </div>
                        <div className="hidden sm:block flex-col gap-5 px-[14px] font-sans text-sub2 text-gray-900 max-md:text-cap1">

                            {data.map((item2, index) => (
                                <Link
                                    className='flex flex-col items-start gap-6'
                                    aria-label="Instructor Socials"
                                    target={'_blank'}
                                    key={index}
                                    href={item2?.instructor ?? ''}>
                                    {item2?.socialMedia}
                                </Link>
                            ))}

                            {/* {instructor?.description.length > 300 && !showFullText ? (
                                <div className="flex flex-col items-start gap-6">
                                    <MarkdownComponent text={instructor?.description.slice(0, 300) as string} />
                                    <button className="text-blue" onClick={() => setShowFullText(true)}>
                                        Show More
                                    </button>
                                </div>
                            ) : (
                                <div className="flex flex-col items-start gap-6">
                                    <MarkdownComponent text={instructor?.description} />
                                    <button
                                        className={cn(
                                            'text-blue',
                                            instructor?.description.length > 300 ? '' : 'hidden'
                                        )}
                                        onClick={() => setShowFullText(false)}>
                                        Show less
                                    </button>
                                </div>
                            )} */}
                        </div>
                        <div className="flex flex-col gap-5 max-md:flex-row max-md:items-end md:hidden">
                            {data.map((item, index) => (
                                <Link
                                    aria-label="Instructor Socials"
                                    target={'_blank'}
                                    key={index}
                                    href={item?.instructor ?? ''}>
                                    {item?.socials}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Instructors
