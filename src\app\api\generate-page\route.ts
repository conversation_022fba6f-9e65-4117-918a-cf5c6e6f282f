import { NextResponse } from 'next/server';
import { savePageData } from '@/lib/pageData';

export async function POST(request: Request) {
  try {
    const { pageType, pageData } = await request.json();
    
    if (!pageType || !pageData || !pageData.title) {
      return NextResponse.json({ error: 'Invalid input data' }, { status: 400 });
    }

    // Generate a unique slug for the new page
    const slug = generateSlug(pageData.title);
    
    // Save the page data
    await savePageData(slug, { pageType, pageData });
    
    return NextResponse.json({ slug }, { status: 200 });
  } catch (error) {
    console.error('Error in generate-page API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)+/g, '');
}


export async function GET() {
    return NextResponse.json({ message: 'Hello, API is working!' })
}
