import { Blogs, Blog } from '@/types/BlogModel'
import Hero from '@/components/Pages/Blog/Hero'
import Recent from '@/components/Pages/Blog/Recent'
import Sections from '@/components/Pages/Blog/Sections'
import Pagination from '@/components/Pages/Blog/Pagination'
import Card from '@/components/Pages/Blog/Card'
import { notFound } from 'next/navigation'
import CryptoNewsView from '@/components/Pages/Blog/NewBlogFiles/CryptoNewsView'
import CryptoPrices from '@/components/Pages/Blog/NewBlogFiles/CryptoPrices'
import FeaturedVideos from '@/components/Pages/Blog/NewBlogFiles/FeaturedVideo'
import Banner from '@/components/Pages/Blog/NewBlogFiles/Banner'

export const metadata = {
    title: 'Blog',
    description:
        'Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
    keywords: ['Blog', 'Crypto University', 'Cryptocurrency', 'Bitcoin', 'Ethereum', 'Trading', 'Investing'],
}

async function GetAdverts(search: string) {
    try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/ads?search=${search}&pageNumber=1&pageSize=10`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.response
    } catch (error) {
        console.error(error)
        return null
    }
}

async function GetRecent() {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/recent?pageNumber=1&pageSize=3`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.posts
    } catch (error) {
        console.error(error)
        return null
    }
}

async function GetFeatured() {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/featured?pageNumber=1&pageSize=3`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.posts
    } catch (error) {
        console.error(error)
        return null
    }
}

async function GetPageCount() {
    try {
        const res = await fetch(`${process.env.API_URL}/blog/posts/total/pages?pageSize=9`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.totalPages
    } catch (error) {
        console.error(error)
        return null
    }
}

async function GetPagePosts(page?: number) {
    try {
        if (page === undefined) page = 1
        const res = await fetch(`${process.env.API_URL}/blog/posts?pageNumber=${page}&pageSize=9`, {
            next: { revalidate: 10 },
        })
        if (!res.ok) return null
        const data = await res.json()
        return data.posts
    } catch (error) {
        console.error(error)
        return null
    }
}

const BlogPage = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const count: number = await GetPageCount()
    const recent: Blogs = await GetRecent()
    const featured: Blogs = await GetFeatured()
    const adverts = await GetAdverts('blog')
    const blogs: Blogs = await GetPagePosts(
        searchParams && searchParams.page ? parseInt(searchParams.page as string) : 1
    )

    if (!blogs || blogs.length === 0) notFound()

    return (
        <>
            <Hero />
            <CryptoNewsView blogs={featured} />
            <CryptoPrices />
            <FeaturedVideos />
            <Banner />
            <main className="container mx-auto mb-8 max-lg:mb-20 max-lg:space-y-14">
                {!searchParams.page && (
                    <Sections>
                        {recent && <Recent blogs={recent} />}
                    </Sections>
                )}

                <Sections>
                    <h3 className="mb-8 text-b3 font-semibold">All Blog Posts</h3>
                    <div className="grid grid-cols-3 gap-6 max-lg:grid-cols-2 max-md:grid-cols-1">
                    {(blogs || []).map((blog, index) => (
                        <Card key={index} {...blog} />
                    ))}
                    </div>
                </Sections>

                <Pagination page={searchParams.page} count={count} />
            </main>
        </>
    )
}

export default BlogPage
