'use client'
import { cn } from '@/lib/cn'
import { Email } from '@public/global'
import { toast } from 'react-toastify'
import fetchInstance from '@/lib/fetch'
import Label from '@/components/Ui/Label'
import { ProfileBlack } from '@public/home'
import Button from '@/components/Ui/Button'
import { Country } from '@/types/CountryModel'
import React, { useRef, useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import CustomSelect from '../GetInTouch/CustomSelect'
import { Country as CountrySVG } from '@public/checkout'
import ButtonSpinner from '@/components/Ui/buttonSpinner'
import { FormikProps, Yup, useFormik, FormikErrors } from '@/lib/formik'

type ValidationSchemaKeys = 'full_name' | 'phone_number' | 'country_code' | 'email' | 'country'
interface TextBoxProps {
    setEmail?: any
    placeholder: string
    error?: string | undefined | string[] | FormikErrors<any> | FormikErrors<any>[]
    label: string
    className?: string
    Icon?: React.ReactNode
    CodeSelect?: React.ReactNode
    formik: FormikProps<any>
    id: ValidationSchemaKeys
    value: any
    onChange: any
    innerRef: React.RefObject<HTMLInputElement>
    type?: string
    isSelectFocus?: boolean
}
const showError = (
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>,
    key: ValidationSchemaKeys,
    formik: FormikProps<any>
) => {
    if (ref.current) {
        ref.current.onfocus = () => {
            formik.setFieldTouched(key, false)
        }
    }
    return formik.touched[key] && (formik.errors as { [key: string]: string })[key]?.length > 0
}
const TextBox = ({
    placeholder,
    error,
    label,
    className,
    Icon,
    CodeSelect,
    id,
    value,
    innerRef,
    formik,
    type,
}: TextBoxProps) => {
    const [focus, setFocus] = useState(false)

    return (
        <div className={cn('flex h-fit w-full flex-col gap-3', className)}>
            <Label className="" required uppercase={false}>
                {label}
            </Label>

            <div className="flex flex-col gap-2 max-md:w-full">
                <div
                    onBlur={() => {
                        setFocus(false)
                    }}
                    onClick={() => setFocus(true)}
                    className={cn(
                        'relative flex flex-1 select-none flex-row items-center rounded-[50px] border border-gray-700 bg-white text-black ',
                        focus && 'border-blue',
                        (id != 'phone_number'
                            ? showError(innerRef, id, formik)
                            : showError(innerRef, 'country_code', formik) || showError(innerRef, id, formik)) &&
                            'border-red focus:ring-red'
                    )}>
                    {Icon ? <div className="absolute left-4">{Icon}</div> : ''}
                    {CodeSelect ? (
                        <div
                            className={cn(
                                'flex w-[120px] items-center rounded-full rounded-r-none border-r-0',
                                (showError(innerRef, 'country_code', formik) ||
                                    (showError(innerRef, id, formik) && !focus)) &&
                                    'border-red focus:ring-red'
                            )}>
                            {CodeSelect}
                            <div
                                className={cn(
                                    'z-30 mx-1 h-full border-l  border-gray-700 py-1.5',
                                    (showError(innerRef, 'country_code', formik) ||
                                        (showError(innerRef, id, formik) && !focus)) &&
                                        'border-red',
                                    focus && 'border-l border-blue'
                                )}></div>
                        </div>
                    ) : (
                        ''
                    )}
                    <input
                        autoComplete="new-password"
                        ref={innerRef}
                        type={id == 'email' ? 'email' : id == 'phone_number' ? 'number' : type ?? 'text'}
                        name={id}
                        id={id}
                        value={value}
                        onBlur={formik.handleBlur}
                        onChange={formik.handleChange}
                        className={cn(
                            'w-full rounded-full border border-gray-700 border-transparent px-4  py-[17.5px] pr-2 ring-0 focus:outline-none max-md:py-3.5 max-md:text-cap1',

                            Icon && 'pl-12',
                            CodeSelect && '!h-[62px] min-w-0 max-w-full grow rounded-l-none border-l-0'
                        )}
                        placeholder={placeholder}
                    />
                </div>
                {showError(innerRef, id, formik) && (
                    <div className="rounded-lg bg-red/20 p-3">
                        <p className="text-cap1 text-red">{error?.toString()}</p>
                    </div>
                )}
            </div>
        </div>
    )
}

const Subscribe = ({ countries }: { countries: Country[] }) => {
    const [isLoading, setIsLoading] = useState(false)
    const countryNames = countries.map((country: Country) => ({
        value: country.name.common,
        label: country.name.common,
    }))
    const codes = countries.map((country: Country) => ({
        value: country.idd.root + country.idd.suffixes,
        label: (
            <span className="max-md:gap0.5 flex gap-2 text-black">
                <ImageShortcut
                    src={country.flags.svg}
                    height={24}
                    alt={country.name.common}
                    width={24}
                    className="object-contain"
                />
                {country.idd.suffixes?.length == 1 ? country.idd.root + country.idd.suffixes : country.idd.root}
            </span>
        ),
    }))

    const formik = useFormik({
        initialValues: {
            full_name: '',
            email: '',
            phone_number: '',
            country: '',
            country_code: codes[34].value,
        },
        validationSchema: Yup.object({
            email: Yup.string().email('Invalid email address').required('Work email is required'),
            full_name: Yup.string().required('Full name is required'),
            phone_number: Yup.string().required('Phone number is required'),
            country: Yup.string().required('Country is required'),
            country_code: Yup.string().required('Phone number code is required'),
        }),
        onSubmit: async (values: any) => {
            setIsLoading(true)
            alert(JSON.stringify(values, null, 2))
            // const data = await fetchInstance('/mail/get-in-touch', {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //     },
            //     body: JSON.stringify(values),
            // })
            // if (data.success == true) {
            //     setIsLoading(false)
            //     toast('✅ Email Sent!', {
            //         position: 'bottom-right',
            //         progress: undefined,
            //         theme: 'light',
            //     })
            //     formik.setValues({
            //         full_name: '',
            //         email: '',
            //         phone_number: '',
            //         country_code: codes[34].value,
            //     })
            //     formik.setTouched({
            //         full_name: false,
            //         email: false,
            //         phone_number: false,
            //         country_code: false,
            //     })
            // } else {
            //     setIsLoading(false)
            //     toast('❌ Error submitting the form!', {
            //         position: 'bottom-right',
            //         progress: undefined,
            //         theme: 'light',
            //     })
            // }
            setIsLoading(false)
        },
        initialErrors: {
            full_name: '',
            email: '',
            country: '',
            phone_number: '',
            country_code: '',
        },
    })

    const [isFocus, setIsFocus] = useState(false)
    const nameRef = useRef<HTMLInputElement>(null)
    const emailRef = useRef<HTMLInputElement>(null)
    const phoneSuffixRef = useRef<HTMLInputElement>(null)
    const countryRef = useRef<HTMLInputElement>(null)

    return (
        <form
            className="grid h-fit w-full max-w-[600px] grid-cols-2 place-content-start gap-6 font-sans max-lg:flex max-lg:flex-col"
            onSubmit={formik.handleSubmit}>
            <TextBox
                error={formik.errors.full_name}
                innerRef={nameRef}
                formik={formik}
                value={formik.values.full_name}
                onChange={formik.handleChange}
                id="full_name"
                placeholder="e.g John Smith"
                label="Your Full Name"
                Icon={<ProfileBlack />}
            />
            <TextBox
                error={formik.errors.email}
                innerRef={emailRef}
                formik={formik}
                value={formik.values.email}
                onChange={formik.handleChange}
                id="email"
                placeholder="e.g <EMAIL>"
                label="Email"
                Icon={<Email />}
            />
            <TextBox
                isSelectFocus={isFocus}
                error={`${formik.errors.country_code ?? ''} ${formik.errors.country_code ? '|' : ''} ${
                    formik.errors.phone_number ?? ''
                }`}
                innerRef={phoneSuffixRef}
                formik={formik}
                id="phone_number"
                label="Phone Number"
                placeholder="Enter mobile no."
                value={formik.values.phone_number}
                onChange={formik.handleChange}
                CodeSelect={
                    <CustomSelect
                        setIsFocus={setIsFocus}
                        style={{
                            control: (provided, state) => ({
                                width: '120px',
                                borderWidth: '0px',
                                height: 'auto',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingRight: '0px',
                                paddingLeft: '1rem',
                                borderRadius: '50px',
                                borderTopRightRadius: '0px',
                                borderBottomRightRadius: '0px',
                            }),
                            indicatorSeparator: (provided, state) => ({
                                display: 'none',
                            }),
                            dropdownIndicator: (provided, state) => ({
                                padding: '0px',
                            }),
                            valueContainer: (provided, state) => ({
                                padding: '0px',
                                alignItemd: 'center',
                                alignSelf: 'center',
                                display: 'flex',
                                flex: '1',
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                flexWrap: 'wrap',
                                position: 'relative',
                                overflow: 'hidden',
                                boxSizing: 'border-box',
                            }),
                            singleValue: (provided, state) => ({
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }),
                        }}
                        isSearchable={true}
                        formik={formik}
                        options={codes}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.country_code}
                        name="country_code"
                        id="country_code"
                        className="rounded-[50px]=relative w-full border-gray-700 px-0 placeholder:text-gray-700 focus:outline-none focus:ring-2 max-md:max-h-max  max-md:text-cap1"
                    />
                }
            />
            <div className="flex h-fit w-full flex-col gap-3">
                <Label required uppercase={false}>
                    Country
                </Label>
                <div className={'relative flex items-center rounded-full bg-white text-black'}>
                    <CustomSelect
                        style={{
                            control: (provided, state) => ({
                                width: '100%',
                                paddingTop: '14px',
                                paddingBottom: '14px',
                                height: '100%',
                                border: state.isFocused
                                    ? '1px solid #2563EB'
                                    : showError(countryRef, 'country', formik)
                                    ? '1px solid red'
                                    : '1px solid #AAAAAA',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingRight: '0px',
                                paddingLeft: '0.5rem',
                                borderRadius: '50px',
                            }),
                            valueContainer: (provided, state) => ({
                                padding: '0pxs',
                                paddingLeft: '35px',
                                alignItems: 'center',
                                display: 'grid',
                                flex: '1',
                                flexWrap: 'wrap',
                                position: 'relative',
                                overflow: 'hidden',
                                boxSizing: 'border-box',
                            }),
                        }}
                        innerRef={countryRef}
                        isSearchable={true}
                        formik={formik}
                        label="Select"
                        options={countryNames}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.country}
                        defaultValue={countryNames[34]}
                        name="country"
                        id="country"
                        setIsFocus={() => {}}
                        className="relative h-[62px] w-full rounded-[50px] px-0 outline-none transition-[border] duration-150 placeholder:text-gray-700 max-md:max-h-[50px] max-md:text-cap1"
                    />
                    <div className="absolute left-4">
                        <CountrySVG />
                    </div>
                </div>
                <div
                    className={cn(
                        'bg mb-1 hidden min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                        showError(countryRef, 'country', formik) && 'block'
                    )}>
                    <p className="select-none text-cap1">{formik.errors.country + ''}</p>
                </div>
            </div>

            <span className="w-full">
                <Button variant="primary" rounded type="submit" disabled={formik.isSubmitting || !formik.isValid}>
                    {isLoading && <ButtonSpinner />}Submit
                </Button>
            </span>
        </form>
    )
}

export default Subscribe
