'use client'
import <PERSON><PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import UserSubscriptionModel from '@/types/UserSubscriptionModel'
import { Dialog, Transition } from '@headlessui/react'
import { Coinbase, PayPal, Gift, Stripe, Atlos } from '@public/checkout'
import { Close } from '@public/home'
import Link from 'next/link'
import { Fragment } from 'react'

interface Props {
    product: UserSubscriptionModel
    openModel: boolean
    setOpenModel: any
    tax: number
    user: any
}

const OrderSubscriptionModel = ({ product, openModel, setOpenModel, user, tax }: Props) => {

    const handleCancel = async () => {
        // Confirmation prompt (optional)
        if (window.confirm('Are you sure you want to cancel this subscription?')) {
            try {
                if (product) {
                    // Hit the external endpoint to cancel the order
                    const data: any = {
                        subscriptionID: product.subscription_id,
                        user_id: product.user_id
                    }
                    const response = await fetch(`${process.env.API_URL}/payment/subscription/cancel`, {
                        method: 'POST',
                        headers: {
                            Authorization: `Bearer ${user?.access_token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data),
                    });

                    const responseData = await response.json(); // Parse the JSON response

                    if (responseData.cancelled) {
                        // Handle successful cancellation (e.g., close modal, display confirmation)
                        setOpenModel(false);
                        console.log('Subscription cancelled successfully!'); // Or display user-friendly message
                        // Refresh the page
                        window.location.reload(); // Simpler approach for immediate refresh
                    } else {
                        // Handle cancellation failure
                        console.error('Failed to cancel subscription:', responseData);
                    }
                }
            } catch (error) {
                console.error('Error cancelling subscription:', error);
                // Handle errors gracefully (e.g., display error message to user)
            }
        }
    };

    const handleRenewal = async () => {
        // Confirmation prompt (optional)
        if (window.confirm('Are you sure you want to renew this subscription?')) {
            try {
                if (product) {
                    // Hit the external endpoint to cancel the order
                    const data: any = {
                        subscriptionID: product.subscription_id,
                        user_id: product.user_id
                    }
                    const response = await fetch(`${process.env.API_URL}/payment/subscription/renew`, {
                        method: 'POST',
                        headers: {
                            Authorization: `Bearer ${user?.access_token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data),
                    });

                    const responseData = await response.json(); // Parse the JSON response

                    if (responseData.renewed) {
                        // Handle successful cancellation (e.g., close modal, display confirmation)
                        setOpenModel(false);
                        console.log('Subscription renewed successfully!'); // Or display user-friendly message
                        // Refresh the page
                        window.location.reload(); // Simpler approach for immediate refresh
                    } else {
                        // Handle cancellation failure
                        console.error('Failed to renewed subscription:', responseData);
                    }
                }
            } catch (error) {
                console.error('Error renewing subscription:', error);
                // Handle errors gracefully (e.g., display error message to user)
            }
        }
    };



    return (
        <Transition show={openModel || false} as={Fragment}>
            <Dialog
                as="div"
                onClose={() => setOpenModel(false)}
                className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                <div className="fixed  inset-0 flex items-center justify-center max-md:block max-md:p-0">
                    <Transition.Child
                        as={Fragment}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0">
                        <Dialog.Panel className="flex h-full items-center justify-center  max-md:block max-md:max-w-full">
                            <div className="relative flex w-[570px] flex-col gap-9 rounded-[1.5rem] bg-white px-[47px] pb-10 pt-[29px] font-sans max-md:h-full max-md:w-full  max-md:gap-6 max-md:rounded-none max-md:px-5 max-md:pt-[30px]">
                                <button
                                    type="button"
                                    onClick={() => setOpenModel(false)}
                                    className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                    <Close />
                                </button>
                                <div className="flex flex-col items-start gap-3">
                                    <p className="text-sub2 font-semibold">Order #{product.order_id}</p>
                                    <p className="text-cap1 max-md:text-cap2">{product.created_at.split('T')[0]}</p>
                                    <p
                                        className={cn(
                                            'rounded-md px-3 py-1 text-cap2 text-white max-md:text-cap3',
                                            product.status == 'pending'
                                                ? 'bg-yellow'
                                                : product.status == 'active'
                                                    ? 'bg-green-dark'
                                                    : 'bg-red'
                                        )}>
                                        {product.status}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-[18px] ">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <ImageShortcut
                                                src={
                                                    product.subscriptions.image
                                                }
                                                alt="product"
                                                width={60}
                                                height={60}
                                                className="object-cover max-md:h-[40px] max-md:w-[40px]"
                                            />
                                            <p className="text-sub3 font-medium">
                                                {product.subscriptions.name}
                                            </p>
                                        </div>
                                        <p className="text-sub2 font-medium max-md:text-cap2">
                                            $
                                            {product.subscriptions.price}
                                        </p>
                                    </div>
                                    <div className="border-b border-gray-700" />
                                    <div className="flex items-center justify-between">
                                        <p className="text-sub3  font-medium max-md:text-cap2">Price</p>
                                        <p className="text-sub1 font-medium max-md:text-cap1">
                                            $
                                            {product.subscriptions.price}
                                        </p>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <p className="text-sub3 font-medium max-md:text-cap2">Discount</p>
                                        <p className="text-sub1 font-medium max-md:text-cap1">
                                            -$
                                            {parseInt(product.subscriptions.price ?? '0')}
                                        </p>
                                    </div>
                                    <div className="border-b border-gray-400" />

                                    <div className="flex items-center justify-between">
                                        <p className="text-sub3 font-medium max-md:text-cap2">Tax</p>
                                        <p className="text-sub1 font-medium max-md:text-cap1">
                                            +${Math.round(+product.subscriptions.price * (tax / 100))}
                                        </p>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <p className="text-b3 font-semibold max-md:text-sub3">Total</p>
                                        <p className="text-b3 font-semibold max-md:text-sub3">${product.subscriptions.price}</p>
                                    </div>
                                    <div className="border-b border-gray-700" />
                                </div>
                                <div className="flex flex-col gap-6">
                                    <h1 className="text-sub3 font-medium max-md:text-cap3">Payment Method</h1>
                                    {product.paymentMethod == 'stripe' ? (
                                        <Stripe />
                                    ) : product.paymentMethod == 'coinbase' ? (
                                        <Coinbase />
                                    ) : product.paymentMethod == 'paypal' ? (
                                        <PayPal />
                                    ) : product.paymentMethod == 'atlos' ? (
                                        <Atlos />
                                    ): (
                                        <Gift />
                                    )}
                                </div>
                                <div className="flex flex-col gap-6">
                                    <h1 className="text-sub3 font-semibold max-md:text-cap2">
                                        Having troubles with your order?
                                    </h1>
                                    <div className="w-[380px] max-md:w-full">
                                        <div className="flex justify-between">
                                            <Link href={'/help'}>
                                                <Button variant="primary" rounded>
                                                    Get Help {'>'}
                                                </Button>
                                            </Link>
                                            <div>
                                                {product.auto_renewal_status === 'active' ? (
                                                    <button 
                                                    type="button" 
                                                    onClick={handleCancel} 
                                                    className="py-[17.5px] flex items-center gap-2 justify-center font-sans max-md:py-4 max-md:text-sub3 select-none w-full px-6 font-semibold min-w-fit transition-[background-color] duration-150 disabled:cursor-not-allowed rounded-full bg-[#FF4D4D] hover:bg-[#CC0000] active:bg-[#990000] text-white disabled:bg-[#E9EEFF] disabled:text-[#BCCAFF]" data-dashlane-rid="f8240f6ab898db6f" 
                                                    data-form-type="" 
                                                    data-dashlane-label="true">
                                                        Cancel Subscription &gt;
                                                    </button>
                                                ) : (
                                                    <button
                                                        type="button"
                                                        onClick={handleRenewal}
                                                        className="py-[17.5px] flex items-center gap-2 justify-center font-sans max-md:py-4 max-md:text-sub3 select-none w-full px-6 font-semibold min-w-fit transition-[background-color] duration-150 disabled:cursor-not-allowed rounded-full bg-[#28a745] hover:bg-[#218838] active:bg-[#1e7e34] text-white disabled:bg-[#E9EEFF] disabled:text-[#BCCAFF]"
                                                        data-dashlane-rid="f8240f6ab898db6f"
                                                        data-form-type=""
                                                        data-dashlane-label="true"
                                                    >
                                                        Renew Subscription &gt;
                                                    </button>
                                                )}

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    )
}

export default OrderSubscriptionModel
