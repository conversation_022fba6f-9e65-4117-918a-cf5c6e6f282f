import Link from 'next/link'
import ImageShortcut from '../Ui/Image'
import LoopSection from './LoopSection'
import { LogoDark } from '@public/marketing'
import Dubai from 'public/home/<USER>/dubai-black.png'
import { Discord, Facebook, Instagram, Telegram, Twitter, Youtube } from '@public/footer'

const company = [
    {
        name: 'Style & Gear',
        link: 'https://degenwear.store',
    },
    {
        name: 'About us',
        link: '/about',
    },
    {
        name: 'Blog',
        link: '/blog',
    },
    {
        name: 'Join Our Community',
        link: '/join-our-community',
    },
    {
        name: 'Terms of Service',
        link: '/terms-of-service',
    },
    {
        name: 'Our Privacy Policy',
        link: '/privacy-policy',
    },
    {
        name: 'Returns & Exchange Policy',
        link: '/returns-exchange-policy',
    },
]
const memberships = [
    {
        name: 'CU Membership',
        link: 'https://whop.com/orders/products/',
    },
    {
        name: 'Become A Member',
        link: '/membership',
    },
]
const courses = [
    {
        name: 'Web3 Masterclass',
        link: '/courses/web3-masterclass',
    },
    {
        name: 'AI Masterclass',
        link: '/courses/ai-masterclass',
    },
]
const resources = [
    {
        name: 'Crypto Resources',
        link: '/resources',
    },
    {
        name: 'Cryptoguide',
        link: '/cryptoguide',
    },
    {
        name: 'FAQs',
        link: '/faqs',
    },
    {
        name: 'Affiliate Program',
        link: '/affiliates',
    },
    {
        name: 'Exchanges',
        link: '/resources/exchanges',
    },
    {
        name: 'Wallets',
        link: '/resources/wallets',
    },
    // {
    //     name: 'Events',
    //     link: '/404',
    // },
    // {
    //     name: 'Arbitrage',
    //     link: '/404',
    // },
    // {
    //     name: 'Jobs',
    //     link: '/404',
    // },
    {
        name: 'Earn',
        link: '/earn',
    },
]

const indicators = [
    {
        name: 'EMA Trading',
        link: '/indicators',
    },
    {
        name: 'Nitros Bull',
        link: '/indicators',
    },
    {
        name: 'Trend Momentum Strategy',
        link: '/indicators',
    },
    {
        name: 'Visual Assistance',
        link: '/indicators',
    },
]
const coaching = [
    {
        name: 'Discord Membership',
        link: '/membership',
    },
    {
        name: '1 on 1 Consulting',
        link: '/consultation',
    },
]
const community = [
    {
        href: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=b65931f0b4&e=8f6f07dee9',
        icon: <Facebook />,
    },
    {
        href: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=cc7ec96606&e=8f6f07dee9',
        icon: <Twitter />,
    },
    {
        href: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=e587f742d2&e=8f6f07dee9',
        icon: <Instagram />,
    },
    {
        href: 'https://discord.com/invite/M9cwwCP49c',
        icon: <Discord />,
    },
    {
        href: 'https://www.youtube.com/@thecryptouniversity',
        icon: <Youtube />,
    },
    {
        href: 'https://t.me/thecryptou',
        icon: <Telegram />,
    },
]

const Links = ({ href, children, target }: { href: string; children: React.ReactNode; target?: boolean }) => (
    <div className="md:flex">
        <Link href={href} target={target ? '_blank' : ''} aria-label="Social media link">
            <p className="text-sub3 transition-[color] duration-150 hover:text-yellow">{children}</p>
        </Link>
    </div>
)

const Footer = () => {
    return (
        <>
            <LoopSection />
            <section className="flex flex-col overflow-hidden bg-[#0B0E11] font-sans text-white">
                <div className="container mx-auto space-y-16 pb-20 pt-16 max-md:pb-8 max-md:pt-12">
                    {/* Description */}
                    <div className="flex items-center justify-between">
                        <div className="flex flex-col gap-6 max-md:w-full">
                            <div className="flex max-md:justify-center">
                                <Link
                                    aria-label="Crypto University Home page"
                                    href="/"
                                    className="flex w-full select-none items-center gap-2 max-md:w-auto max-md:flex-col max-md:items-center max-md:gap-6">
                                    <LogoDark />
                                    <p className="flex gap-1 text-callout font-semibold max-md:text-b2 max-md:font-normal">
                                        <span className="hidden max-md:block">The</span> Crypto University
                                    </p>
                                </Link>
                            </div>
                            <p className="text-sub2 max-md:hidden">Join the #1 Crypto Community in the World</p>
                        </div>
                        {/* <ImageShortcut
                            src={Dubai}
                            alt={'Dubai Knowledge Logo'}
                            className="h-auto min-h-[60px] w-auto object-cover max-md:hidden"
                            width={1080}
                            height={720}
                        /> */}
                    </div>

                    {/* Links */}
                    <div className="flex justify-between gap-14 max-md:flex-col">
                        {/* Company */}
                        <div className="flex flex-col gap-6">
                            <p className="font-manrope text-callout font-semibold max-md:text-sub1">Company</p>
                            <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                {company.map((i, index) => (
                                    <Links key={index} href={i.link}>
                                        {i.name}
                                    </Links>
                                ))}
                            </div>
                        </div>

                        {/* Courses */}
                        <div className="space-y-6">
                            <div className="flex flex-col gap-6">
                                <p className="font-manrope text-callout font-semibold max-md:text-sub1">Memberships</p>
                                <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                    {memberships.map((i, index) => (
                                        <Links
                                            key={index}
                                            href={i.link}
                                            target={i.name === 'CU Membership' ? true : false}>
                                            {i.name}
                                        </Links>
                                    ))}
                                </div>
                                <hr className="h-[1px] w-full text-gray-400" />
                            </div>
                            <div className="flex flex-col gap-6">
                                <p className="font-manrope text-callout font-semibold max-md:text-sub1">Courses</p>
                                <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                    {courses.map((i, index) => (
                                        <Links key={index} href={i.link}>
                                            {i.name}
                                        </Links>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Resources */}
                        <div className="flex flex-col gap-6">
                            <p className="font-manrope text-callout font-semibold max-md:text-sub1">Resources</p>
                            <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                {resources.map((i, index) => (
                                    <Links key={index} href={i.link}>
                                        {i.name}
                                    </Links>
                                ))}
                            </div>
                        </div>

                        {/* Indicators */}
                        <div className="space-y-6">
                            <div className="flex flex-col gap-6">
                                <p className="font-manrope text-callout font-semibold max-md:text-sub1">Indicators</p>
                                <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                    {indicators.map((item, index) => (
                                        <Links key={index} href={item.link}>
                                            {item.name}
                                        </Links>
                                    ))}
                                </div>
                                <hr className="h-[1px] w-full text-gray-400" />
                            </div>
                            <div className="space-y-6">
                                <p className="font-manrope text-callout font-semibold max-md:text-sub1">Coaching</p>
                                <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                    {coaching.map((item, index) => (
                                        <Links key={index} href={item.link}>
                                            {item.name}
                                        </Links>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Newsletter */}
                        <div className="space-y-6">
                            <div className="flex flex-col gap-6">
                                <p className="font-manrope text-callout font-semibold max-md:text-sub1">Newsletter</p>
                                <div className="flex max-w-[225px] flex-col gap-5 max-md:max-w-full">
                                    <Links href="/newsletter">Weekly Widget &gt;</Links>
                                </div>
                                <hr className="h-[1px] w-full text-gray-400" />
                            </div>
                            <div className="space-y-6">
                                <p className="font-manrope text-callout font-semibold max-md:text-sub1">Community</p>
                                <div className="flex flex-wrap items-center gap-4">
                                    {community.map((item, index) => (
                                        <Link
                                            aria-label="Socials"
                                            key={index}
                                            target="_blank"
                                            className="transition-[transform] duration-150 md:hover:-translate-y-1"
                                            href={item.href}>
                                            {item.icon}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="container mx-auto">
                    <div className="mx-auto flex justify-between gap-4 border-gray-400 pb-10 max-md:flex-col max-md:border-t max-md:py-8">
                        <p className="text-cap3 max-md:text-cap3">
                            Copyright © {new Date().getFullYear()} WEB THREE LEARNING LTD, All rights reserved.
                        </p>
                    </div>
                </div>

            </section>
        </>
    )
}

export default Footer
 