const Menu = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path fill={'#081228'} d="M3 15v-2h18v2H3zm0-5V8h18v2H3z"></path>
    </svg>
)
const Myclass = ({ active = false }: any) =>
    active ? (
        <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                d="M16.076 2h3.386A2.549 2.549 0 0122 4.56v3.415a2.549 2.549 0 01-2.538 2.56h-3.386a2.549 2.549 0 01-2.539-2.56V4.56A2.549 2.549 0 0116.076 2z"
                opacity="0.4"></path>
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M4.539 2h3.385a2.549 2.549 0 012.539 2.56v3.415a2.549 2.549 0 01-2.539 2.56H4.54A2.549 2.549 0 012 7.974V4.56A2.549 2.549 0 014.539 2zm0 11.466h3.385a2.549 2.549 0 012.539 2.56v3.414A2.55 2.55 0 017.924 22H4.54A2.55 2.55 0 012 19.44v-3.415a2.549 2.549 0 012.539-2.56zm14.923 0h-3.386a2.549 2.549 0 00-2.539 2.56v3.414A2.55 2.55 0 0016.076 22h3.386A2.55 2.55 0 0022 19.44v-3.415a2.549 2.549 0 00-2.538-2.56z"
                clipRule="evenodd"></path>
        </svg>
    ) : (
        <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill={'#081228'}
                d="M16.076 2h3.386A2.549 2.549 0 0122 4.56v3.415a2.549 2.549 0 01-2.538 2.56h-3.386a2.549 2.549 0 01-2.539-2.56V4.56A2.549 2.549 0 0116.076 2z"
                opacity="0.4"></path>
            <path
                fill={'#081228'}
                fillRule="evenodd"
                d="M4.539 2h3.385a2.549 2.549 0 012.539 2.56v3.415a2.549 2.549 0 01-2.539 2.56H4.54A2.549 2.549 0 012 7.974V4.56A2.549 2.549 0 014.539 2zm0 11.466h3.385a2.549 2.549 0 012.539 2.56v3.414A2.55 2.55 0 017.924 22H4.54A2.55 2.55 0 012 19.44v-3.415a2.549 2.549 0 012.539-2.56zm14.923 0h-3.386a2.549 2.549 0 00-2.539 2.56v3.414A2.55 2.55 0 0016.076 22h3.386A2.55 2.55 0 0022 19.44v-3.415a2.549 2.549 0 00-2.538-2.56z"
                clipRule="evenodd"></path>
        </svg>
    )
const Coaching = ({ active = false }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M3 16.87V9.257h18v7.674C21 20.07 19.024 22 15.863 22H8.127C4.996 22 3 20.03 3 16.87zm4.96-2.46a.822.822 0 01-.85-.8c0-.46.355-.84.81-.86.444 0 .81.35.82.8a.822.822 0 01-.78.86zm4.06 0a.822.822 0 01-.85-.8c0-.46.356-.84.81-.86.445 0 .81.35.82.8a.822.822 0 01-.78.86zm4.03 3.68a.847.847 0 01-.82-.85.831.831 0 01.81-.85h.01c.465 0 .84.38.84.85s-.375.85-.84.85zm-4.88-.85c.02.46.395.82.85.8a.821.821 0 00.78-.86.817.817 0 00-.82-.8.855.855 0 00-.81.86zm-4.07 0c.02.46.395.82.85.8a.821.821 0 00.78-.86.817.817 0 00-.82-.8.855.855 0 00-.81.86zm8.14-3.64c0-.46.356-.83.81-.84.445 0 .8.36.82.8a.82.82 0 01-.79.85.814.814 0 01-.84-.8v-.01z"
                clipRule="evenodd"></path>
            <path
                fill="#fff"
                d="M3.003 9.257c.013-.587.063-1.752.156-2.127.474-2.109 2.084-3.449 4.386-3.64h8.911c2.282.201 3.912 1.55 4.386 3.64.092.365.142 1.54.155 2.127H3.003z"
                opacity="0.4"></path>
            <path
                fill="#fff"
                d="M8.305 6.59c.434 0 .76-.329.76-.77V2.771A.748.748 0 008.305 2c-.435 0-.761.33-.761.771V5.82c0 .441.326.77.76.77zM15.695 6.59c.425 0 .76-.329.76-.77V2.771a.754.754 0 00-.76-.771c-.435 0-.76.33-.76.771V5.82c0 .441.325.77.76.77z"></path>
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#929292"
                fillRule="evenodd"
                d="M3 16.87V9.259h18v7.674C21 20.07 19.024 22 15.863 22H8.127C4.996 22 3 20.03 3 16.87zm4.96-2.46a.822.822 0 01-.85-.798c0-.46.355-.84.81-.861.444 0 .81.35.82.8a.822.822 0 01-.78.86zm4.06 0a.822.822 0 01-.85-.798c0-.46.356-.84.81-.861.445 0 .81.35.82.8a.822.822 0 01-.78.86zm4.03 3.68a.847.847 0 01-.82-.85.831.831 0 01.81-.848h.01c.465 0 .84.38.84.849 0 .47-.375.85-.84.85zm-4.88-.85c.02.46.395.822.85.8a.821.821 0 00.78-.858.817.817 0 00-.82-.801.855.855 0 00-.81.86zm-4.07 0c.02.46.395.822.85.8a.821.821 0 00.78-.858.817.817 0 00-.82-.801.855.855 0 00-.81.86zm8.14-3.638c0-.46.356-.83.81-.84.445 0 .8.359.82.8a.82.82 0 01-.79.849.814.814 0 01-.84-.8v-.01z"
                clipRule="evenodd"></path>
            <path
                fill="#081228"
                d="M3.003 9.257c.013-.587.063-1.752.156-2.127.474-2.109 2.084-3.449 4.386-3.64h8.911c2.282.201 3.912 1.55 4.386 3.64.092.365.142 1.54.155 2.127H3.003z"
                opacity="0.4"></path>
            <path
                fill="#081228"
                d="M8.305 6.59c.435 0 .76-.329.76-.77V2.771A.748.748 0 008.306 2c-.435 0-.76.33-.76.771V5.82c0 .441.325.77.76.77M15.695 6.59c.425 0 .76-.329.76-.77V2.771a.754.754 0 00-.76-.771c-.435 0-.76.33-.76.771V5.82c0 .441.325.77.76.77"></path>
        </svg>
    )
const Indicators = ({ active }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 20 18" fill="none">
            <path
                opacity="0.4"
                d="M4.70506 9.89062C4.18895 9.89062 3.77114 10.3146 3.77114 10.8384L3.51367 15.4172C3.51367 16.0847 4.04734 16.6251 4.70506 16.6251C5.36279 16.6251 5.89529 16.0847 5.89529 15.4172L5.63898 10.8384C5.63898 10.3146 5.22118 9.89062 4.70506 9.89062"
                fill="white"
            />
            <path
                d="M5.98037 0.673447C5.98037 0.673447 5.71236 0.397892 5.54618 0.277931C5.30509 0.0926435 5.00783 0 4.71173 0C4.37936 0 4.0704 0.104521 3.81877 0.301685C3.77313 0.348007 3.57886 0.522605 3.41852 0.685325C2.41204 1.6367 0.765393 4.12026 0.262153 5.42083C0.182571 5.618 0.0105329 6.11685 0 6.38409C0 6.63827 0.0561757 6.88294 0.170868 7.11455C0.331202 7.40436 0.582823 7.63715 0.880085 7.76424C1.08606 7.84619 1.70282 7.97328 1.71453 7.97328C2.38981 8.10156 3.48757 8.17045 4.70003 8.17045C5.85514 8.17045 6.90727 8.10156 7.59308 7.99704C7.60478 7.98516 8.37017 7.85807 8.6335 7.71792C9.11333 7.46255 9.41177 6.96371 9.41177 6.43041V6.38409C9.40006 6.03608 9.10163 5.30444 9.09109 5.30444C8.58785 4.07394 7.02079 1.64858 5.98037 0.673447Z"
                fill="white"
            />
            <path
                opacity="0.4"
                d="M15.2949 8.10962C15.811 8.10962 16.2288 7.68558 16.2288 7.16178L16.4851 2.58296C16.4851 1.91543 15.9526 1.375 15.2949 1.375C14.6372 1.375 14.1035 1.91543 14.1035 2.58296L14.361 7.16178C14.361 7.68558 14.7788 8.10962 15.2949 8.10962"
                fill="white"
            />
            <path
                d="M19.8288 10.885C19.6685 10.5952 19.4168 10.3636 19.1196 10.2353C18.9136 10.1534 18.2957 10.0263 18.2851 10.0263C17.6098 9.89799 16.5121 9.8291 15.2996 9.8291C14.1445 9.8291 13.0924 9.89799 12.4066 10.0025C12.3949 10.0144 11.6295 10.1427 11.3662 10.2816C10.8852 10.537 10.5879 11.0359 10.5879 11.5704V11.6167C10.5996 11.9647 10.8969 12.6952 10.9086 12.6952C11.4118 13.9257 12.9777 16.3523 14.0193 17.3263C14.0193 17.3263 14.2873 17.6018 14.4535 17.7206C14.6934 17.9071 14.9907 17.9997 15.2891 17.9997C15.6203 17.9997 15.9281 17.8952 16.1809 17.698C16.2265 17.6517 16.4208 17.4771 16.5811 17.3156C17.5864 16.363 19.2343 13.8794 19.7363 12.58C19.8171 12.3828 19.9891 11.8827 19.9997 11.6167C19.9997 11.3613 19.9435 11.1166 19.8288 10.885"
                fill="white"
            />
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 20 18" fill="none">
            <path
                opacity="0.4"
                d="M4.70506 9.89062C4.18895 9.89062 3.77114 10.3146 3.77114 10.8384L3.51367 15.4172C3.51367 16.0847 4.04734 16.6251 4.70506 16.6251C5.36279 16.6251 5.89529 16.0847 5.89529 15.4172L5.63898 10.8384C5.63898 10.3146 5.22118 9.89062 4.70506 9.89062"
                fill="#081228"
            />
            <path
                d="M5.98037 0.673447C5.98037 0.673447 5.71236 0.397892 5.54618 0.277931C5.30509 0.0926435 5.00783 0 4.71173 0C4.37936 0 4.0704 0.104521 3.81877 0.301685C3.77313 0.348007 3.57886 0.522605 3.41852 0.685325C2.41204 1.6367 0.765393 4.12026 0.262153 5.42083C0.182571 5.618 0.0105329 6.11685 0 6.38409C0 6.63827 0.0561757 6.88294 0.170868 7.11455C0.331202 7.40436 0.582823 7.63715 0.880085 7.76424C1.08606 7.84619 1.70282 7.97328 1.71453 7.97328C2.38981 8.10156 3.48757 8.17045 4.70003 8.17045C5.85514 8.17045 6.90727 8.10156 7.59308 7.99704C7.60478 7.98516 8.37017 7.85807 8.6335 7.71792C9.11333 7.46255 9.41177 6.96371 9.41177 6.43041V6.38409C9.40006 6.03608 9.10163 5.30444 9.09109 5.30444C8.58785 4.07394 7.02079 1.64858 5.98037 0.673447"
                fill="#081228"
            />
            <path
                opacity="0.4"
                d="M15.2949 8.10962C15.811 8.10962 16.2288 7.68558 16.2288 7.16178L16.4851 2.58296C16.4851 1.91543 15.9526 1.375 15.2949 1.375C14.6372 1.375 14.1035 1.91543 14.1035 2.58296L14.361 7.16178C14.361 7.68558 14.7788 8.10962 15.2949 8.10962"
                fill="#081228"
            />
            <path
                d="M19.8288 10.885C19.6685 10.5952 19.4168 10.3636 19.1196 10.2353C18.9136 10.1534 18.2957 10.0263 18.2851 10.0263C17.6098 9.89799 16.5121 9.8291 15.2996 9.8291C14.1445 9.8291 13.0924 9.89799 12.4066 10.0025C12.3949 10.0144 11.6295 10.1427 11.3662 10.2816C10.8852 10.537 10.5879 11.0359 10.5879 11.5704V11.6167C10.5996 11.9647 10.8969 12.6952 10.9086 12.6952C11.4118 13.9257 12.9777 16.3523 14.0193 17.3263C14.0193 17.3263 14.2873 17.6018 14.4535 17.7206C14.6934 17.9071 14.9907 17.9997 15.2891 17.9997C15.6203 17.9997 15.9281 17.8952 16.1809 17.698C16.2265 17.6517 16.4208 17.4771 16.5811 17.3156C17.5864 16.363 19.2343 13.8794 19.7363 12.58C19.8171 12.3828 19.9891 11.8827 19.9997 11.6167C19.9997 11.3613 19.9435 11.1166 19.8288 10.885"
                fill="#081228"
            />
        </svg>
    )

const Subscriptions = ({ active }: any) =>
    active ? (
        <svg className="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#ffffff" viewBox="0 0 24 24">
            <path fill="#ffffff" d="M12 14a3 3 0 0 1 3-3h4a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-4a3 3 0 0 1-3-3Zm3-1a1 1 0 1 0 0 2h4v-2h-4Z" clip-rule="evenodd" />
            <path fill="#ffffff" d="M12.293 3.293a1 1 0 0 1 1.414 0L16.414 6h-2.828l-1.293-1.293a1 1 0 0 1 0-1.414ZM12.414 6 9.707 3.293a1 1 0 0 0-1.414 0L5.586 6h6.828ZM4.586 7l-.056.055A2 2 0 0 0 3 9v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2h-4a5 5 0 0 1 0-10h4a2 2 0 0 0-1.53-1.945L17.414 7H4.586Z" clip-rule="evenodd" />
        </svg>
    ) : (
        <svg className="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M12 14a3 3 0 0 1 3-3h4a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-4a3 3 0 0 1-3-3Zm3-1a1 1 0 1 0 0 2h4v-2h-4Z" clip-rule="evenodd" />
            <path fill-rule="evenodd" d="M12.293 3.293a1 1 0 0 1 1.414 0L16.414 6h-2.828l-1.293-1.293a1 1 0 0 1 0-1.414ZM12.414 6 9.707 3.293a1 1 0 0 0-1.414 0L5.586 6h6.828ZM4.586 7l-.056.055A2 2 0 0 0 3 9v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2h-4a5 5 0 0 1 0-10h4a2 2 0 0 0-1.53-1.945L17.414 7H4.586Z" clip-rule="evenodd" />
        </svg>
    )


const Affiliate = ({ active }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M15.243 4.738a4.024 4.024 0 004.027 4.021c.245-.001.49-.025.73-.07v7.973C20 20.015 18.021 22 14.662 22H7.346C3.98 22 2 20.016 2 16.662V9.355C2 6.002 3.979 4 7.346 4h7.967c-.047.243-.07.49-.07.738zM13.15 14.897l2.858-3.688v-.018a.754.754 0 00-.14-*********** 0 00-1.039.15l-2.409 3.1-2.743-2.16a.74.74 0 00-1.047.14l-2.954 3.81a.72.72 0 00-.159.457.738.738 0 001.363.43l2.471-3.196 2.744 2.151a.74.74 0 001.055-.131z"
                clipRule="evenodd"></path>
            <circle cx="19.5" cy="4.5" r="2.5" fill="#fff" opacity="0.4"></circle>
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#929292"
                fillRule="evenodd"
                d="M15.243 4.738a4.024 4.024 0 004.027 4.021c.245-.001.49-.025.73-.07v7.973C20 20.015 18.021 22 14.662 22H7.346C3.98 22 2 20.016 2 16.662V9.355C2 6.002 3.979 4 7.346 4h7.967c-.047.243-.07.49-.07.738zM13.15 14.897l2.858-3.688v-.018a.754.754 0 00-.14-*********** 0 00-1.039.15l-2.409 3.1-2.743-2.16a.74.74 0 00-1.047.14l-2.954 3.81a.72.72 0 00-.159.457.738.738 0 001.363.43l2.471-3.196 2.744 2.151a.74.74 0 001.055-.131z"
                clipRule="evenodd"></path>
            <circle cx="19.5" cy="4.5" r="2.5" fill="#929292" opacity="0.4"></circle>
        </svg>
    )
const Statistic = ({ active }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                d="M16.333 22H7.666C4.276 22 2 19.623 2 16.084V7.917C2 4.378 4.277 2 7.666 2h8.668C19.724 2 22 4.378 22 7.917v8.167C22 19.623 19.723 22 16.333 22"
                opacity="0.4"></path>
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M11.245 8.675a2.488 2.488 0 01-2.485 2.486 2.487 2.487 0 01-2.485-2.486A2.488 2.488 0 018.76 6.19a2.489 2.489 0 012.485 2.486zm8.156 5.413c.233.226.4.484.51.76.332.831.16 1.831-.197 2.655a3.638 3.638 0 01-2.247 2.046 4.578 4.578 0 01-1.4.207h-8.38c-.835 0-1.573-.194-2.178-.558-.379-.228-.446-.753-.165-1.095.47-.57.934-1.142 1.402-1.719.892-1.104 1.493-1.424 2.162-************.543.291.822.475.746.494 1.784 1.172 3.15.436.933-.51 1.475-1.384 1.947-2.145l.01-.015.094-.152c.16-.26.319-.515.498-.75.221-.29 1.045-1.2 2.11-.553.68.408 1.25.96 1.861 1.551z"
                clipRule="evenodd"></path>
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#929292"
                d="M16.333 22H7.666C4.276 22 2 19.623 2 16.084V7.917C2 4.378 4.277 2 7.666 2h8.668C19.724 2 22 4.378 22 7.917v8.167C22 19.623 19.723 22 16.333 22z"
                opacity="0.4"></path>
            <path
                fill="#929292"
                fillRule="evenodd"
                d="M11.245 8.675a2.488 2.488 0 01-2.485 2.486 2.487 2.487 0 01-2.485-2.486A2.488 2.488 0 018.76 6.19a2.489 2.489 0 012.485 2.486zm8.155 5.413c.233.226.4.484.51.76.333.831.16 1.831-.196 2.655a3.638 3.638 0 01-2.248 2.046 4.578 4.578 0 01-1.399.207h-8.38c-.835 0-1.573-.194-2.178-.558-.379-.228-.446-.753-.165-1.095.47-.57.934-1.142 1.402-1.719.892-1.104 1.493-1.424 2.161-1.143.271.116.543.291.823.475.746.494 1.783 1.172 3.15.436.933-.51 1.475-1.384 1.947-2.145l.01-.015.094-.152c.16-.26.318-.515.497-.75.222-.29 1.046-1.2 2.111-.553.68.408 1.25.96 1.861 1.551z"
                clipRule="evenodd"></path>
        </svg>
    )
const Setting = ({ active }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                d="M12.012 14.83c-1.604 0-2.902-1.25-2.902-2.82 0-1.57 1.298-2.83 2.902-2.83 1.605 0 2.872 1.26 2.872 2.83s-1.267 2.82-2.872 2.82z"></path>
            <path
                fill="#fff"
                d="M21.23 14.37c-.194-.3-.47-.6-.828-.79-.286-.14-.47-.37-.633-.64-.521-.86-.215-1.99.654-2.5a2.027 2.027 0 00.756-2.83l-.685-1.18a2.112 2.112 0 00-2.871-.76c-.9.48-2.055.16-2.576-.69-.163-.28-.255-.58-.235-.88.03-.39-.092-.76-.276-1.06A2.152 2.152 0 0012.717 2h-1.44c-.747.02-1.431.42-1.81 1.04-.194.3-.306.67-.286 *********-.071.6-.235.88-.521.85-1.676 1.17-2.565.69a2.124 2.124 0 00-2.882.76l-.685 1.18c-.582.99-.255 2.26.757 2.83.868.51 1.175 1.64.664 2.5-.174.27-.358.5-.644.64-.347.19-.654.49-.817.79-.379.62-.358 1.4.02 2.05l.705 1.2a2.134 2.134 0 001.82 1.04c.347 0 .755-.1 1.083-.3.255-.17.562-.23.899-.23 1.012 0 1.86.83 1.88 1.82 0 1.15.94 2.05 2.126 2.05h1.39c1.175 0 2.115-.9 2.115-2.05.03-.99.88-1.82 1.89-1.82.328 0 .634.06.9.23.327.2.726.3 1.084.3.725 0 1.43-.4 1.808-1.04l.716-1.2c.368-.67.398-1.43.02-2.05z"
                opacity="0.4"></path>
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#081228"
                d="M12.012 14.83c-1.604 0-2.902-1.25-2.902-2.82 0-1.57 1.298-2.83 2.902-2.83s2.872 1.26 2.872 2.83-1.268 2.82-2.872 2.82z"></path>
            <path
                fill="#081228"
                d="M21.23 14.37c-.194-.3-.47-.6-.828-.79-.286-.14-.47-.37-.633-.64-.522-.86-.215-1.99.654-2.5a2.027 2.027 0 00.756-2.83l-.685-1.18a2.112 2.112 0 00-2.872-.76c-.899.48-2.054.16-2.575-.69-.163-.28-.255-.58-.235-.88.03-.39-.092-.76-.276-1.06A2.152 2.152 0 0012.717 2h-1.44c-.747.02-1.432.42-1.81 1.04-.194.3-.306.67-.286 *********-.071.6-.235.88-.521.85-1.676 1.17-2.565.69a2.124 2.124 0 00-2.882.76l-.685 1.18c-.582.99-.255 2.26.757 2.83.868.51 1.175 1.64.664 2.5-.174.27-.358.5-.644.64-.347.19-.654.49-.818.79-.378.62-.357 1.4.02 2.05l.706 1.2a2.134 2.134 0 001.82 1.04c.347 0 .755-.1 1.082-.3.256-.17.562-.23.9-.23 1.011 0 1.86.83 1.88 1.82 0 1.15.94 2.05 2.126 2.05h1.39c1.175 0 2.115-.9 2.115-2.05.03-.99.879-1.82 1.89-1.82.328 0 .634.06.9.23.327.2.726.3 1.083.3.726 0 1.431-.4 1.81-1.04l.715-1.2c.367-.67.398-1.43.02-2.05z"
                opacity="0.4"></path>
        </svg>
    )
const Help = ({ active }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                d="M22 12c0 5.524-4.477 10-10 10S2 17.524 2 12C2 6.478 6.477 2 12 2s10 4.478 10 10"
                opacity="0.4"></path>
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M12.87 12.63a.877.877 0 01-.875.876.877.877 0 01-.875-.875V8.21c0-.482.393-.875.875-.875s.875.393.875.875v4.42zm-1.745 3.174a.878.878 0 011.755 0 .876.876 0 01-.875.875.879.879 0 01-.88-.875z"
                clipRule="evenodd"></path>
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#929292"
                d="M22 12c0 5.524-4.477 10-10 10S2 17.524 2 12C2 6.478 6.477 2 12 2s10 4.478 10 10z"
                opacity="0.4"></path>
            <path
                fill="#929292"
                fillRule="evenodd"
                d="M12.87 12.63a.877.877 0 01-.875.876.877.877 0 01-.875-.875V8.21c0-.482.393-.875.875-.875s.875.393.875.875v4.42zm-1.745 3.174a.878.878 0 011.755 0 .876.876 0 01-.875.875.879.879 0 01-.88-.875z"
                clipRule="evenodd"></path>
        </svg>
    )
const FAQ = ({ active }: any) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#fff"
                d="M22 12c0 5.524-4.477 10-10 10S2 17.524 2 12C2 6.478 6.477 2 12 2s10 4.478 10 10z"
                opacity="0.4"></path>
            <path
                fill="#fff"
                d="M11.35 13.97c-.01-.379.05-.755.177-1.107.09-.249.242-.465.438-.625.175-.129.364-.258.57-.387l.568-.344c.197-.117.357-.298.456-.519.117-.268.171-.564.16-.862a1.65 1.65 0 00-.49-1.25 1.677 1.677 0 00-1.224-.48 1.582 1.582 0 00-1.24.543c-.32.358-.486.849-.456 1.35H9.006c-.053-.873.245-1.726.814-2.338.286-.313.627-.56 1.003-.723.376-.163.778-.24 1.182-.226.787-.021 1.55.3 2.123.893.571.57.889 1.384.87 2.231.015.458-.067.914-.239 1.331-.125.315-.32.59-.569.8a6.327 6.327 0 01-.677.45 2.27 2.27 0 00-.569.469c-.16.184-.247.432-.239.687v.919h-1.354v-.813zm.08 3.749a1.025 1.025 0 01-.264-.69c0-.26.093-.51.26-.693a.85.85 0 01.63-.286.87.87 0 01.736.434c.165.27.196.612.083.912a.913.913 0 01-.644.584.837.837 0 01-.8-.261z"></path>
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
                fill="#929292"
                d="M22 12c0 5.524-4.477 10-10 10S2 17.524 2 12C2 6.478 6.477 2 12 2s10 4.478 10 10z"
                opacity="0.4"></path>
            <path
                fill="#130F26"
                d="M11.35 13.97c-.01-.379.05-.755.177-1.107.09-.249.242-.465.438-.625.175-.129.364-.258.57-.387l.568-.344c.197-.117.357-.298.456-.519.117-.268.171-.564.16-.862a1.65 1.65 0 00-.49-1.25 1.677 1.677 0 00-1.224-.48 1.582 1.582 0 00-1.24.543c-.32.358-.486.849-.456 1.35H9.006c-.053-.873.245-1.726.814-2.338.286-.313.627-.56 1.003-.723.376-.163.778-.24 1.182-.226.787-.021 1.55.3 2.123.893.571.57.889 1.384.87 2.231.015.458-.067.914-.239 1.331-.125.315-.32.59-.569.8a6.327 6.327 0 01-.677.45 2.27 2.27 0 00-.569.469c-.16.184-.247.432-.239.687v.919h-1.354v-.813zm.08 3.749a1.025 1.025 0 01-.264-.69c0-.26.093-.51.26-.693a.85.85 0 01.63-.286.87.87 0 01.736.434c.165.27.196.612.083.912a.913.913 0 01-.644.584.837.837 0 01-.8-.261z"></path>
        </svg>
    )
const Pause = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M10.65 19.11V4.89c0-1.35-.57-1.89-2.01-1.89H5.01C3.57 3 3 3.54 3 4.89v14.22C3 20.46 3.57 21 5.01 21h3.63c1.44 0 2.01-.54 2.01-1.89zM21 19.11V4.89C21 3.54 20.43 3 18.99 3h-3.63c-1.43 0-2.01.54-2.01 1.89v14.22c0 1.35.57 1.89 2.01 1.89h3.63c1.44 0 2.01-.54 2.01-1.89z"></path>
    </svg>
)
const Play = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            strokeWidth="1.5"
            d="M4 12V8.44c0-4.42 3.13-6.23 6.96-4.02l3.09 1.78 3.09 1.78c3.83 2.21 3.83 5.83 0 8.04l-3.09 1.78-3.09 1.78C7.13 21.79 4 19.98 4 15.56V12z"></path>
    </svg>
)
const RightArrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20">
        <path
            stroke="#676A73"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.25"
            d="M7.084 4.167L12.917 10l-5.833 5.834"></path>
    </svg>
)
const RightLightArrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path stroke="#081228" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8.5 5l7 7-7 7"></path>
    </svg>
)
const LeftLightArrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path
            stroke="#081228"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M15.5 19l-7-7 7-7"></path>
    </svg>
)

export {
    Affiliate,
    Statistic,
    Coaching,
    Myclass,
    Menu,
    Help,
    Setting,
    FAQ,
    Pause,
    Play,
    RightArrow,
    RightLightArrow,
    LeftLightArrow,
    Indicators,
    Subscriptions,
}
