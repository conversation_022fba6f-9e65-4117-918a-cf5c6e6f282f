import axios from 'axios';
import {  USDT,ETH,BTC,BNB,ADA,XRP,SOL,AVAX,DOGE,PEPE,STETH, LTC  } from '@public/resources/cryptos';
import { StaticImageData } from 'next/image';

interface CryptoAPIResponse {
  data: {
    id: string;
    rank: string;
    symbol: string;
    name: string;
    supply: string;
    maxSupply: string | null;
    marketCapUsd: string;
    volumeUsd24Hr: string;
    priceUsd: string;
    changePercent24Hr: string;
    vwap24Hr: string;
    explorer: string;
  }[];
}

interface Crypto {
  name: string;
  Abr: string;
  image: StaticImageData;
}

interface ProcessedCryptoData {
  name: string;
  Abr: string;
  value: number;
  change: number;
  image: StaticImageData;
}

const cryptoSymbols: Crypto[] = [
  { name: 'Bitcoin', Abr: 'BTC', image: BTC },
  { name: 'Ethereum', Abr: 'ETH', image: ETH },
  { name: 'Binance Coin', Abr: 'BNB', image: BNB },
  { name: '<PERSON><PERSON>', Abr: 'SOL', image: SOL },
  { name: 'Tether', Abr: 'USDT', image: USDT },
  { name: 'Cardano', Abr: 'ADA', image: ADA },
  { name: 'Ripple', Abr: 'XRP', image: XRP },
  { name: 'Litecoin', Abr: 'LTC', image: LTC },
  { name: 'Dogecoin', Abr: 'DOGE', image: DOGE },
  { name: 'Avalanche', Abr: 'AVAX', image: AVAX },
  { name: 'Lido Staked Ether', Abr: 'STETH', image: STETH },
  { name: 'PEPE', Abr: 'PEPE', image: PEPE },
];

export const fetchCryptoData = async (): Promise<ProcessedCryptoData[]> => {
  try {
    const response = await axios.get<CryptoAPIResponse>('https://api.coincap.io/v2/assets');
    const data = response.data.data;

    const filteredData: ProcessedCryptoData[] = cryptoSymbols.map(symbol => {
      const crypto = data.find(item => item.symbol === symbol.Abr);
      if (!crypto) {
        return {
          name: symbol.name,
          Abr: symbol.Abr,
          value: 0,
          change: 0,
          image: symbol.image,
        };
      }

      return {
        name: symbol.name,
        Abr: symbol.Abr,
        value: parseFloat(crypto.priceUsd),
        change: parseFloat(parseFloat(crypto.changePercent24Hr).toFixed(1)),
        image: symbol.image,
      };
    });

    return filteredData;
  } catch (error) {
    console.error('Error fetching crypto data:', error);
    return [];
  }
};

