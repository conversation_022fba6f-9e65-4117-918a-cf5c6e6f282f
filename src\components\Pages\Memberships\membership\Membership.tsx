'use client'
import Link from 'next/link'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import { AlphaGroupCounter } from '@/components/Ui/AlphaGroupCounter'
import 'react-responsive-modal/styles.css'
import { newdesktopBanner, newMobileBanner, discount50 } from '@public/alpha-group'
import './Membership.css'
// import Check from '../../../../public/icons/Check.svg'
import Check from '../../../../../public/icons/Check.svg'
import Plastic from '../../../../public/icons/Plastic.svg'
import Image from 'next/image'
import { MdKeyboardArrowRight } from 'react-icons/md'


const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault()
    const href = e.currentTarget.href
    const targetId = href.replace(/.*\#/, '')
    const elem = document.getElementById(targetId)
    window.scrollTo({
        top: elem?.getBoundingClientRect().top! + window.scrollY - 100,
        behavior: 'smooth',
    })
}
const Membership = ({ main }: { main?: boolean }) => {
    const [isOpen, setIsOpen] = useState(false)
    const openModal = () => {
        setIsOpen(true)
    }

    const closeModal = () => {
        setIsOpen(false)
    }
    const headerItems = [
        {
            title: 'Web3, AI, Content Creation Masterclass Courses'
        },
        {
            title: 'Alpha Trading & Alpha Stream'
        },
        {
            title: 'Weekly Live Session'
        },
        {
            title: 'Trading indicators'
        }
        ,
        {
            title: 'Live Support'
        }
    ]

    return (
        <section>
            <div className="bgg">

                    <div className="hidden md:block md:pt-[64px]">
                        <div className="via-brown w-1/2 bg-gradient-to-r from-[#EFB77C66] to-[#6e4c3100] p-4">
                            <h1 className=" text-[24.97px] font-[450] leading-[37.45px] text-[#EFB77C]">
                                ALPHA GROUP MEMBERSHIP
                            </h1>
                            <div className="border-blue-500 h-full border-l-4"></div>
                        </div>
                    </div>
                    <div className="p-[32px] md:p-[64px] pt-[350px] p">
                        <div className="mt-2 text-[28px] font-bold text-white text-center md:text-left md:text-[54px] max-w-[300px] md:min-w-[700px]">
                            Join the <span className="text-[#EFB77C]"> Elite</span> in Crypto Intelligence
                        </div>

                        <div>
                            <ul className="text-left my-3 md:my-7 grid gap-2 text-[13.62px] md:text-[16px] text-white md:grid-cols-2">
                                <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    Web3, AI, Content Creation Masterclass Courses
                                </li>
                                <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    Crypto day Trading
                                </li>
                                <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    NFTs and Ordinals
                                </li>
                                <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    Alpha Trading & Alpha Stream
                                </li>
                                <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    Weekly Live Session
                                </li>
                                <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    Crypto trading Insights
                                </li>
                                {/* <li className="flex gap-2">
                                    {' '}
                                    <Image src={Check} width={20} height={20} alt="Check" className='mb-1' />
                                    Signals, calls & Alpha
                                </li> */}
                            </ul>
                        </div>

                        {/* <Link
                            aria-label="View products"
                            href={'/aplha-group'}
                            className="items-center justify-center hover:bg-yellow-600 active:bg-yellow-800 mt-10 md:mt-7 flex h-[54px] w-[341px] rounded-lg border border-[#EFB77C] text-[#EFB77C] sm:text-sub3 md:w-[24rem] md:px-12 md:text-sub2">
                            Learn More <MdKeyboardArrowRight className="mb-[2px] h-5 w-5 md:mb-0 md:h-6 md:w-6" />
                        </Link> */}
                    </div>
                </div>
        </section>
    )
}

export default Membership
