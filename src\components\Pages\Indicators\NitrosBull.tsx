
"use client"
import ImageShortcut from "@/components/Ui/Image";
import Link from "next/link";
const NitrosBull = () => {

  return (
    <>
         <section>
                <div className="relative overflow-hidden bg-cover bg-no-repeat p-12"
                    style={{
                        backgroundImage: `url('/indicators/indicatorbg.jpg')`,
                        height: '750px'
                    }}>
                    <div className="absolute bottom-0 left-0 right-0 top-0 h-full w-full overflow-hidden bg-fixed"
                        style={{
                            backgroundColor: 'rgba(0, 0, 0, 0.6)'
                        }}>
                       <div className="my-12 p-12 sm:p-36">
                            <div>
                                <h1 className="text-b2 sm:text-h2 font-bold mt-10 text-white text-center">Nitros Bull Crypto Indicator
                                </h1>
                                <p className="text-lg my-4 text-white font-semibold text-center">THE BEST AUTOMATED CRYPTO INDICATOR
                                </p>
                                <div className="">

                                </div>
                                <div className="container py-10 px-10 mx-0 min-w-full flex flex-col items-center">
                                    <Link
                                        aria-label="View products"
                                        href={"/checkout/indicator/nitros-bull-crypto-indicator?previous=/indicators"}
                                        className="bg-[#00ce63] mb-4  hover:bg-[#00a043]  text-white active:bg-[#017933] rounded-lg text-sub2 font-semibold py-3 px-12 mt-6 transition-colors duration-150"
                                    >
                                        Buy Now
                                    </Link>
                                </div>
                                <h1 className="text-white text-h3 my-4 font-semibold text-center">$200</h1>
                                <p className="text-lg my-4 text-white font-semibold text-center">THE BEST AUTOMATED CRYPTO INDICATOR
                                </p>
                                <p className="text-white text-center">NOTE : You will need a Tradingview account to use Nitros Bull. If you don\’t have one already
                                    please sign up using the link belowradingview</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="mb-4">
                <div className="">
                    <h1 className="text-b2 sm:text-h2 font-bold mt-10 text-[#00394F] text-center my-16">HOW IT WORKS</h1>

                    <div className="flex justify-center rounded mt-10 mx-16">
                        <ul className="list-disc">
                            <li>This indicator shows you when’s a good time to buy or sell.</li>
                            <li>BULL = Buy alert</li>
                            <li>BEAR = Sell alert</li>
                            <li>This indicator works on all cryptocurrencies, stocks, and forex.</li>
                            <li>This indicator runs in REAL TIME</li>
                        </ul>
                    </div>
                    <div className="flex justify-center rounded mt-10 p-6">
                        <iframe  width="707" height="398" src="https://media.publit.io/file/Indicators-tutorial-videos/Nitros-Bull-Tutorial.html?player=PLAYER1"
                            title="Grey Jabesi Presentation" frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            allowFullScreen></iframe>
                    </div>
                </div>
            </section>
            <section className="p-12">
                <div className="">
                    <h1 className="text-b2 sm:text-h2 font-bold mt-10 text-[#00394F] text-center my-16">SCREENSHOTS</h1>
                    <div className="grid grid-cols-2 gap-3">
                        <div className="p-4">
                            <ImageShortcut
                                width={500}
                                height={25}
                                src={'/indicators/Screen-Shot-2020-07-27-at-2.13.41-AM-1280x556.png'}
                                alt="thumbnail"
                                className="object-cover"
                                priority
                            />
                        </div>
                        <div className="p-4">
                            <ImageShortcut
                                width={500}
                                height={25}
                                src={'/indicators/Screen-Shot-2020-07-27-at-3.43.11-AM.png'}
                                alt="thumbnail"
                                className="object-cover"
                                priority
                            />
                        </div>
                    </div>
                </div>
            </section>

            <section className="px-12 py-2 sm:py-12 flex flex-col items-center">
                <div className="">
                    <ImageShortcut
                        width={700}
                        height={500}
                        src={'/indicators/Artboard.jpg'}
                        alt="thumbnail"
                        className="object-cover"
                        priority
                    />
                </div>
                <h1 className="text-h3 my-4 font-semibold text-[#002f68]">$200</h1>
                <Link
                    aria-label="View products"
                    href={"/checkout/indicator/nitros-bull-crypto-indicator?previous=/indicators"}
                    className="bg-[#002f68] mb-4  hover:bg-[#00a043]  text-white active:bg-[#017933] rounded-lg text-sub2 font-semibold py-3 px-12 mt-6 transition-colors duration-150"
                >
                    Buy Now
                </Link>
            </section>

            <section className="bg-slate-900 p-8">
                <h1 className="text-h2 font-bold text text-white text-center my-4">Disclaimer</h1>
                <p className="text-white text-center text-sub4">The website https://web3learning.network/ cannot and does not contain
                    financial, tax, investment or legal advice. The financial, tax, investment or legal information is provided
                    for general informational and educational purposes only and is not a substitute for professional advice.
                    Accordingly, before taking any actions based upon such information, we encourage you to consult with the
                    appropriate professionals. We do not provide any kind of financial, tax, investment or legal advice.</p>
                <p>THE USE OR RELIANCE OF ANY INFORMATION OR PRODUCTS CONTAINED ON THIS WEBSITE IS SOLELY AT YOUR OWN RISK.</p>
            </section>
    </>
  );
};

export default NitrosBull;