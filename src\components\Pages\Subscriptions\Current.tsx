'use client'

import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import { usePathname } from 'next/navigation'
import { SubscriptionModel } from '@/types/SubscriptionModel'

const Current = (subscription: SubscriptionModel) => {
    const router = usePathname()

    return (
        <Link
            aria-label="Start Now"
            href={'/checkout/subscription/' + subscription.slug + '?previous=' + router}
            className="w-[200px] max-md:w-[188px]">
            <Button variant="primary" rounded>
                Subscribe Now &gt;
            </Button>
        </Link>
    )
}

export default Current
