'use client'

import Link from 'next/link'
import But<PERSON> from '@/components/Ui/Button'
import { usePathname } from 'next/navigation'
import { IndicatorModel } from '@/types/IndicatorModel'

const Current = (indicator: IndicatorModel) => {
    const router = usePathname()

    return (
        <Link
            aria-label="Start Now"
            href={'/checkout/indicator/' + indicator.slug + '?previous=' + router}
            className="w-[200px] max-md:w-[188px]">
            <Button variant="primary" rounded>
                Start Now &gt;
            </Button>
        </Link>
    )
}

export default Current
