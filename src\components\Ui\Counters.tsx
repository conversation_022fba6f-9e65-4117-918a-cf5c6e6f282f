import Counter from './Counter'
import { Profile, Rating, RatingMobile, TrustPilot, TrustPilotMobile, Video, Heart } from '@public/home'

export const Counters = () => {
    return (
        <div className="flex grid-cols-2 flex-wrap gap-9 max-md:grid max-md:gap-y-[1.125rem]">
            <Counter icon={<Profile />} number={30000} unit={'Members'} className="max-md:w-full" />
            <Counter
                icon={<Video />}
                number={10000}
                unit={'Hours Of Content'}
                className="max-md:w-full max-md:border-none"
            />
            <Counter icon={<Heart />} number={20} unit={'Partners'} className="max-md:w-full" />
            <div className="flex gap-[0.875rem] max-md:gap-2">
                <div className="flex flex-col items-center max-md:items-start">
                    <span className="text-cap1 max-md:text-cap4">Rated</span>
                    <p className="text-b1 font-semibold max-md:text-sub1">4.5</p>
                    <span className="text-cap1 max-md:text-cap4">Out of 5</span>
                </div>
                <div className="hidden flex-col justify-end gap-1 py-[2px] max-md:flex">
                    <TrustPilotMobile />
                    <RatingMobile />
                </div>
                <div className="flex flex-col justify-end gap-3 py-[2px] max-md:hidden">
                    <TrustPilot />
                    <Rating />
                </div>
            </div>
        </div>
    )
}
