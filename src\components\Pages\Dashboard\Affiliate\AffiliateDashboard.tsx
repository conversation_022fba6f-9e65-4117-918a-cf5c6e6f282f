"use client"
import Link from "next/link"
import StatisticsCard from "./StatisticsCard"
import { Discord, Facebook, Instagram, Telegram, Twitter, Youtube, RedIt, CopyIcon } from '@public/affiliate'
import { toast } from 'react-toastify'
import { useState } from "react"
import Label from '@/components/Ui/Label'
import useReferral from '@/hooks/useReferral'
import { GetCurrentSession } from "@/lib/session"
import { useRouter } from 'next/navigation'
import Button from "@/components/Ui/Button"
import ButtonSpinner from "@/components/Ui/buttonSpinner"


type StatisticProps = {
    title: string
    value: string
    subValue: string | null
}

interface Model {
    affiliate: {
        total_unpaid: number
        total_withdrawn: number
        referral_count: number
        totalPages: number
        commission_rate: number
        earnings: number
        affiliateCode: string
    }
}

type InputV2Props = {
    value: string | number | readonly string[] | undefined
    type: string
    name: string
    required?: boolean;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
    placeholder?: string
    disabled?: boolean
}

// Components
const InputV2 = ({ value, type, name, onChange, placeholder, disabled = false, required = false }: InputV2Props) => {
    return (
        <input
            type={type}
            name={name}
            value={value ?? ''}
            onChange={onChange}
            placeholder={placeholder}
            className="w-full max-w-full rounded-full border border-gray-700 p-4 ring-0 focus:border-blue focus:outline-none disabled:cursor-not-allowed disabled:bg-gray-200 max-md:py-3.5 max-md:text-cap1"
            disabled={disabled}
            required={required}
        />
    )
}

const Item = ({
    children,
    required = false,
    label,
}: {
    children: React.ReactNode
    label: string
    required?: boolean
}) => {
    return (
        <div className="flex w-full flex-col gap-2">
            <Label uppercase={false} required={required}>
                {label}
            </Label>
            {children}
        </div>
    )
}

const community = [
    {
        href: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=b65931f0b4&e=8f6f07dee9',
        icon: <Facebook />,
    },
    {
        href: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=cc7ec96606&e=8f6f07dee9',
        icon: <Twitter />,
    },
    {
        href: 'https://imojimotion.us14.list-manage.com/track/click?u=6fd1f8435bbd04508ed77cc88&id=e587f742d2&e=8f6f07dee9',
        icon: <Instagram />,
    },
    {
        href: 'https://discord.com/invite/M9cwwCP49c',
        icon: <Discord />,
    },
    {
        href: 'https://www.youtube.com/@thecryptouniversity',
        icon: <Youtube />,
    },
    {
        href: 'https://t.me/thecryptou',
        icon: <Telegram />,
    },
    {
        href: 'https://t.me/thecryptou',
        icon: <RedIt />,
    },
]



const AffiliateDashboard = ({ affiliateStats, token }: { affiliateStats: Model, token: string; }) => {
    const [isError, setIsError] = useState('')
    const { updateReferralCode, error, isLoading } = useReferral(token)
    const router = useRouter()
    const affiliate = affiliateStats.affiliate;
    const session = GetCurrentSession()
    const statistics: StatisticProps[] = [
        {
            title: "Total Earnings",
            value: "$" + affiliate.earnings.toString(),
            subValue: "$" + affiliate.total_unpaid.toString() + " Unpaid"
        },
        {
            title: "Total Referrals/Visits",
            value: affiliate.referral_count.toString(),
            subValue: null
        },
        {
            title: "Commision Rate",
            value: affiliate.commission_rate.toString() + "%",
            subValue: null
        },
        {
            title: "Total Withdrawn",
            value: "$" + affiliate.total_withdrawn.toString(),
            subValue: null
        }
    ]

    const copylink = () => {
        const url = "https://cryptouniversity.network/ref/" + affiliate.affiliateCode;
        navigator.clipboard.writeText(url)
        toast.success('Affiliate URL is copied successfully')
    }

    const copyCouponCode = () => {
        const couponCode = "BULLRUN";
        navigator.clipboard.writeText(couponCode)
        toast.success('Coupon Code is copied successfully')
    }

    const [formData, setFormData] = useState<any>({
        ...({
            affiliateCode: '',
        }),
    });

    const handleChange = (e: any) => {
        const { name, value } = e.target
        setFormData((prevData: any) => ({
            ...prevData,
            [name]: value,
        }))
    }
    const handleSubmit = async (e: any) => {
        e.preventDefault()
        try {
            const res = await updateReferralCode(formData)
            toast.success('Affiliate reference code updated successfully')
            session.update({ ...session, user: res.data.user })
            router.refresh()

        } catch (error: any) {
            console.error(error)
            toast.error(error)
        }
    }

    return (
        <div className="mx-8">
            <h1 className="text-callout font-normal capitalize text-black mt-10">Statistics</h1>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                {
                    statistics.map((statistic) => (
                        <StatisticsCard key={`${statistic.title}-${Math.random()}`} statistic={statistic} />
                    ))
                }
            </div>
            <div>
                <h1 className="text-callout font-normal capitalize text-black mt-10 mb-4">Affiliate URL</h1>
                <div className="h-auto sm:h-[72px] gap-2 grid grid-cols-1 sm:grid-cols-2">
                    <div className="flex-col px-[47px] justify-center items-center gap-2 flex">
                        <div className="h-[55px] pl-8 pr-[34px] pt-6 pb-[25px] bg-amber-50 rounded-lg border border-dashed border-black justify-center items-center flex">
                            <div className="w-auto sm:w-[351px] text-center text-gray-950 text-cap1 sm:text-2xl sm:text-lg font-semibold  leading-tight">cryptouniversity.network/ref/{affiliate.affiliateCode}</div>
                        </div>
                    </div>
                    <div onClick={copylink} className=" cursor-pointer w-auto sm:w-[210px] mt-auto sm:mt-2 h-14 px-[47px] py-4 rounded-[50px] border border-black  gap-2 flex">
                        <CopyIcon /> <div className="text-gray-950 text-base font-medium  leading-tight">  Copy URL </div>
                    </div>
                </div>
            </div>
          
            <div className='border-b-4 border-[#E2E2E2] my-6 sm:my-2 mr-2 sm:mr-4 pr-2 sm:pr-4' />
            <div className="my-4 sm:my-6">
                <h1 className="text-callout font-normal capitalize text-black mt-10 mb-4">Custom Link Generator</h1>
                <form className="grid-cols-1 gap-4 sm:grid-cols-2 grid max-w-auto sm:w-[800px] pb-auto sm:pb-4" onSubmit={handleSubmit}>
                    <div className="flex items-center">
                        <Item label="Enter your affiliate reference code to generate a custom link" required>
                            <InputV2
                                type="text"
                                name="affiliateCode"
                                value={formData.affiliateCode ?? ''}
                                onChange={handleChange}
                                required={true}
                                placeholder="The code will be appended in your url?"
                            />
                        </Item>
                    </div>
                    <div className="flex items-center">
                        <div className="">
                            <Button type="submit" variant="primary" className="w-auto sm:w-[270px] h-[53px] px-[47px] mt-2 sm:mt-12 text-white text-base font-medium  leading-tight" rounded disabled={isLoading}>
                                {isLoading && <ButtonSpinner />}Create Custom Link
                            </Button>
                        </div>
                    </div>
                    {(error || isError) && <p className="text-cap1 text-red">{error || isError}</p>}
                </form>

            </div>
              
            <div className='border-b-4 border-[#E2E2E2] my-2 mr-4 pr-4' />
            <div className="mb-10">
                <h1 className="text-callout font-normal capitalize text-black mt-10 mb-4">Coupon Code</h1>
                <div className="h-auto sm:h-[72px]  grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex-col justify-center items-center inline-flex">
                        <div className="h-[55px] pl-8 pr-[34px] pt-6 pb-[25px] bg-amber-50 rounded-lg border border-dashed border-black justify-center items-center inline-flex">
                            <div className="w-auto sm:w-[351px] text-center text-gray-950 text-lg font-semibold  leading-normal">BULLRUN (10% Discount)</div>
                        </div>
                    </div>
                    <div onClick={copyCouponCode} className=" cursor-pointer w-auto mt-auto sm:mt-2 sm:w-[300px] h-14 px-[47px] py-4 rounded-[50px] border border-black  gap-2 flex">
                        <CopyIcon /> <div className="text-gray-950 text-base font-medium  leading-tight">  Copy Coupon Code </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default AffiliateDashboard
