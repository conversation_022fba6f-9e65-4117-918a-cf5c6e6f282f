import Header from '@/components/Nav/Header'
import { getCurrentUser } from '@/lib/session'
import HeaderMobile from '@/components/Nav/HeaderMobile'
import { Sidebar } from '@/components/Pages/Dashboard/Home/Sidebar'
import SidebarMobile from '@/components/Pages/Dashboard/Home/SidebarMobile'
import { siteConfig } from '@/config/site'

export const metadata = {
    title: {
        default: siteConfig.name,
        template: `%s | ${siteConfig.name}`,
    },
    description: 'Dashboard page for your Crypto University account.',
}

interface DashboardLayotuProps {
    children: React.ReactNode
}

// ? Dynamic forcing or it will give deployment error
export const dynamic = 'force-dynamic'

export default async function DashboardLayotu({ children }: DashboardLayotuProps) {
    const user = await getCurrentUser()
    if (!user) return null

    return (
        <>
            <Header />
            <HeaderMobile />
            <section className="flex h-screen flex-grow pt-20 max-lg:h-auto max-lg:flex-col ">
                <Sidebar user={user} />
                <SidebarMobile user={user} />
                <main className="flex flex-1 overflow-y-auto overflow-x-hidden">{children}</main>
            </section>
        </>
    )
}
