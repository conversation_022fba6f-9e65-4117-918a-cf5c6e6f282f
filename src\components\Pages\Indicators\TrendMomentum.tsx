"use client"
import { Search } from "@public/global";
import ImageShortcut from "@/components/Ui/Image";
import Link from "next/link";
import { Disclosure, Transition } from '@/lib/headlessui'
import PortableText from '@/components/Ui/portable'
import { cn } from "@/lib/cn";

const TrendMomentum = () => {
    const headerDetails = [
        {
            image: "/cgu/man-climbing-stairs2x.png",
            title: 'Beginner Friendly',
            description: 'I have learned so much and refined so much of my knowledge about Bitcoin and Altcoins…and just being a more intentional investor than others'
        },
        {
            image: "/cgu/reading-book2x.png",
            title: 'Learn at your pace',
            description: 'We have endless amount of content. You pick and choose which area to start with based on your goals or prior experience. You can also refer back to our lessons whenever faced with a challenge.'
        },
        {
            image: "/cgu/chat-bubble2x.png",
            title: 'Beginner Friendly',
            description: 'You’ll be part of our <a href="https://discord.gg/M9cwwCP49c" className="text-yellow">Discord</a> server where you can ask questions to experts and other learners.'
        }
    ]
    const headerItems = [
        {
            title: 'This indicator plots trades on your chart to follow'
        },
        {
            title: 'Adjustable stop loss and take profit targets'
        },
        {
            title: 'Multiple customizable options for strategy configuration'
        }
        ,
        {
            title: 'Works on all cryptocurrency, stock, and forex pairs'
        }

    ]

    const courseOutline = [
        {
            title: "VISUALS",
            content: "<ul><li>🔼 The blue upwards pointing arrows indicate long entries.</li><li>🔽 The pink downward pointing arrows indicate close signals from the previous long entry signal.</li><li>🔽 The red downward pointing arrows indicate short entries.</li><li>🔼 The pink upwards pointing arrows indicate close signals from the previous short entry signal.</li><li>The numbers under the arrows indicate how much of the asset was purchased. By default, this value is 1,000 USD equivalent but can be changed by the user in the “Properties” tab.</li><li>✰ The green stars under the candlestick represent bullish engulfing candlesticks. The visibility, color, and image can be edited in the “Style” tab.</li><li>✰ The red stars on top of the candlesticks indicate bearish engulfing candlesticks. The visibility, color, and image can be edited in the “Style” tab.</li><li>The blue line is the SMA (Simple Moving Average). The value can be adjusted in the “Inputs” tab. The visibility and color can be edited in the “Style” tab.</li><li>The white lines indicate the upper, middle, and lower ranges of the Bollinger Bands. The “Middle Band Length”, “BB Source”, and “Standard Deviation” values can be adjusted in the “Inputs” tab. The visibility and color can be edited in the “Style” tab.</li></ul>"
        },
        {
            title: "INPUTS",
            content: "<ul><li>Apply Stoch – By checking this box you apply a stochastic filter to the conditions for long entries.</li><li>Short Engulfing – By checking this box you enable short trade entry conditions for short engulfing patterns that occur after an uptrend swing has ended.</li><li>RSI Long – By checking this box you apply an RSI SMA filter to the conditions for long entries.</li><li>Trailing Stop Loss – By checking this box you apply a trailing stop loss to the strategy back tester. If this is not enabled, then the back tester will use the values of “Stop Loss %” and “Profit Take %”.</li><li>Stop Loss % – The strategy will close a trade for a loss at this value which can be edited by the user.</li><li>Profit Take % – The strategy will close a trade for a profit at this value which can be edited by the user.</li><li>Trailing Stop Long % – The strategy will implement a trailing stop for long positions at this value if the “Trailing Stop Loss” box is checked. If the value is 100, the trailing stop loss is disabled and only the strategy close condition will be used.</li><li>Trailing Stop Short % – The strategy will implement a trailing stop for Short positions at this value if the “Trailing Stop Loss” box is checked. If the value is 100, the trailing stop loss is disabled and only the strategy close condition will be used.</li><li>Start Month – Input the month number for the back test beginning here.</li><li>Start Day – Input the day number for the back test beginning here.</li><li>Start Year – Input the year number for the back test beginning here.</li><li>End Month – Input the month number for the back test ending here.</li><li>End Day – Input the day number for the back test ending here.</li><li>End Year – Input the year number for the back test ending here.</li><li>SMA Source – Input the number to use as the simple moving average source value.</li><li>SMA Length – Input the number to use as the simple moving average source length.</li><li>RSI Source – Input the number to use as relative strength index source value.</li><li>BB Middle Band Length – Input the number to take the middle band of the bollinger bands value from.</li><li>BB Source – Input the number to take the bollinger bands source value from.</li><li>Standard Deviation – Input the number to take the standard deviation of the bollinger bands from.</li><li>Close After Sessions LONG – This value will force close a long position after the number of candlesticks specified regardless of profits or loss.</li><li>Close After Sessions SHORT – This value will force close a short position after the number of candlesticks specified regardless of profits or loss.</li></ul>"

        },
        {
            title: "PROPERTIES",
            content: "<ul><li><strong>Initial Capital</strong> – The amount of capital in the total portfolio. 1,000 by default.</li><li><strong>Base Currency</strong> – The currency to be used as base. USD by default.</li><li><strong>Order Size</strong> – The amount of funds per trade; base currency, percent equity, or contracts.</li><li><strong>Pyramiding</strong> – How many orders for a single trade. 1 by default.</li><li><strong>Commission</strong> – Exchange/broker commission rates. 0 by default.</li><li><strong>Verify Price For Limit Orders</strong> – Ticks past limit for an order fill. 0 by default.</li><li><strong>Slippage</strong> – Ticks added to fill price of market stop orders. 0 by default.</li><li><strong>Margin For Long Positions</strong> – Percent of equity for long positions. 0 by default.</li><li><strong>Margin For Short Positions</strong> – Percent of equity for short positions. 0 by default.</li><li><strong>Recalculate</strong> – Post-order fill recalculations; real time bar updates.</li></ul>"

        },
        {
            title: "STYLE",
            content: "<ul class=\"m-4 flex flex-wrap gap-4\"><li><strong>SMA</strong> – Simple moving average as a line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>Middle Band</strong> – Middle band of the bollinger bands as a line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>Upper Band</strong> – Upper band of the bollinger bands as a line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>Lower Band</strong> – Lower band of the bollinger bands as a line. Visibility and color changeable. Check box to enable/disable.</li><li><strong>Bearish Engulfing</strong> – Plots bearish engulfing candlesticks. Visibility, color, and image changeable. Check box to enable/disable.</li><li><strong>Bullish Engulfing</strong> – Plots bullish engulfing candlesticks. Visibility, color, and image changeable. Check box to enable/disable.</li><li><strong>Trades On Chart</strong> – Add/remove trade arrows, signal labels, entry quantities from chart. Check box to toggle.</li><li><strong>Signal Labels</strong> – Add/remove strategy direction text from trades on chart. Check box to toggle.</li><li><strong>Quantity</strong> – Add/remove quantity of asset purchased from trades on chart. Check box to toggle.</li><li><strong>Precision</strong> – N/A Value.</li><li><strong>Labels On Price Scale</strong> – Add/remove labels from the price scale. Check box to toggle.</li><li><strong>Values In The Status Line</strong> – Remove values from the status line of the indicator. Check box to toggle.</li></ul>"

        }
        ,
        {
            title: "VISIBILITY",
            content: "<ul className=\"list-disc\"><li>Set the time frames you would like the strategy back tester to be visible on.</li></ul>"

        }
    ]

    const faqs = [
        {
            title: "How does the Trend Momentum Strategy work?",
            content: "The strategy allows the user to take positions based on the presence and strength of an uptrend."
        },
        {
            title: "How do I set up the Trend Momentum Strategy in TradingView?",
            content: "It comes with default values but is fully customizable. Please watch this video to understand your new indicator fully. Tutorial video"

        },
        {
            title: "Do I have to pay for TradingView to use the Trend Momentum Strategy?",
            content: "With a free TradingView account you can use up to three indicators simultaneously at no cost."

        },
        {
            title: "I bought the Trend Momentum Strategy but I don’t have access to it. What now?",
            content: "Access is typically granted within 1-24 hours after purchase. If it has been longer than 24 hours since your purchase, Please join our discord server here and visit our #get-support channel to get a ticket, or send an <NAME_EMAIL>"

        }
        ,
        {
            title: "I provided the wrong TradingView username at checkout. How can I change this?",
            content: "Please join our discord server here and visit our #get-support channel to get a ticket, or send an <NAME_EMAIL>"

        }
        ,
        {
            title: "How do I open the Trend Momentum Strategy in TradingView?",
            content: "Once logged into your TradingView account, select “charts” > “indicators” > “invite only scripts” > add your new indicator to your chart. (Press the star icon to favorite the indicator and make it appear in your favorites section.)"

        }
        ,
        {
            title: "What if I change my TradingView username or lose. access to my TradingView account that has the indicator?",
            content: "Please join our discord server here and visit our #get-support channel to get a ticket, or send an <NAME_EMAIL>"

        }
    ]
    const followers = [
        {
            url: "https://twitter.com/deadlycrypto",
            icon: "/cgu/icons8-twitterx-48.png",
            name: "X"
        },
        {
            url: "https://www.youtube.com/c/DeadlyCrypto",
            icon: "/cgu/icons8-youtube-48.png",
            name: "YouTube"
        }
    ]
    const followers2 = [
        {
            url: "https://www.instagram.com/greybtc/",
            icon: "/cgu/icons8-instagram-48.png",
            name: "Instagram"
        },
        {
            url: "http://deadlycrypto.com/",
            icon: "/indicators/Logo-v2.webp",
            name: "DeadlyCrypto"
        }
    ]
    return (
        <>
            <section className="bg-slate-900 p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 pt-12">
                    <div className="mt-10">
                        <ImageShortcut
                            src={'/indicators/trendmomentum.png'}
                            width={400}
                            height={700}
                            alt="thumbnail"
                            className=" object-contain h-700 w-400"
                            priority
                        />
                    </div>

                    <div>
                        <h1 className="text-white text-h2 font-bold mb-4 mt-4 ">Trend Momentum Strategy Indicator</h1>
                        <p className="text-white pt-4 mb-4">Trend Momentum Strategy” is a momentum indicator that plots trades for you. Just pick your settings and follow the trade signals!</p>
                        <ul className="text-white ml-4">
                            <li>
                                {
                                    headerItems.map(item => (
                                        <div key={item.title} className="flex">
                                            <div className="flex-none w-9 h-14">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                                                    <path fill="#00BF77"
                                                        d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                                                    </path>
                                                    <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                    <path stroke="#fff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5"
                                                        d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                </svg>
                                            </div>
                                            <div className="flex-initial w-90">
                                                {item.title}
                                            </div>
                                        </div>
                                    ))
                                }

                            </li>
                        </ul>
                        <p className="text-white text-headline my-6">$200</p>
                        <Link
                            aria-label="View products"
                            href={"/checkout/indicator/trend-momentum-strategy-indicator?previous=/indicators"}
                            className="bg-yellow hover:bg-[#00a043]  active:bg-[#017933] rounded-lg text-sub3 font-semibold py-3 px-6 mt-6 transition-colors duration-150"
                        >
                            Buy Now
                        </Link>
                    </div>
                </div>

            </section>
            <section className="mb-4">
                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold"> Introduction Video </h1>
                </div>
                <div className="flex justify-center rounded mt-10 p-6">
                    <iframe className="rounded" width="720" height="500" src="https://media.publit.io/file/Indicators-tutorial-videos/Trend-Momentum-Strategy.html?player=PLAYER1" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
                </div>
            </section>
            <section className="mb-4 p-6">
                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold">Your Instructor</h1>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 m-10">
                    <div>
                        <ImageShortcut
                            src={'/indicators/deadly-crypto.webp'}
                            width={1280}
                            height={720}
                            alt="thumbnail"
                            className=" object-contain h-80 w-96 pt-8"
                            priority
                        />

                    </div>
                    <div>
                        <h1 className="text-headline">Edward Gonzales</h1>
                        <p className="mt-10 text-lg">Is a technical analyst, professional day trader and content creator that has been involved in cryptocurrency since April 2017.
                            Through his passion for cryptocurrency education, he was able to connect with Crypto University and join the team in January 2021. He works closely with the students in our private group to assist in learning and strategy development.
                            Edward also writes blogs for the university website. Specializing in passive income methods, Edward aims to bring a deeper understanding of algorithmic trading to Crypto University and our students. He’s also the creator of <a href="https://cryptouniversity.network/courses/crypto-automated-trading-course">Crypto Automated Trading Course.</a></p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 mt-4 gap-8 ">
                            {followers.map(follower => (
                                <Link key={follower.icon}
                                    aria-label="View products"
                                    href={follower.url}
                                    className=""
                                >
                                    <div className="border-gray-600 border rounded-full p-2">
                                        <div className="flex justify-center">
                                            <div className="flex flex-row ...">
                                                <div> <ImageShortcut
                                                    src={follower.icon}
                                                    width={5}
                                                    height={5}
                                                    alt="thumbnail"
                                                    className=" object-contain h-5 w-5"
                                                    priority
                                                /></div>
                                                <div className="ml-2">{follower.name}</div>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            ))}
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 mt-4 gap-8 ">
                            {followers2.map(follower => (
                                <Link key={follower.icon}
                                    aria-label="View products"
                                    href={follower.url}
                                    className=""
                                >
                                    <div className="border-gray-600 border rounded-full p-2">
                                        <div className="flex justify-center">
                                            <div className="flex flex-row ...">
                                                <div> <ImageShortcut
                                                    src={follower.icon}
                                                    width={5}
                                                    height={5}
                                                    alt="thumbnail"
                                                    className=" object-contain h-5 w-5"
                                                    priority
                                                /></div>
                                                <div className="ml-2">{follower.name}</div>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </section>
            <section className="mb-4 p-6">
                <h1 className="text-center mt-10 text-h3 font-bold">About the Indicator</h1>
                <div className="grid grid-cols-1 sm:grid-cols-1 m-10 gap-8">
                    <p className="mt-10 text-lg">Trend Momentum Strategy” is a momentum indicator that plots trades for you. Just pick your settings and follow the trade signals!
                        There is an option to add/remove stochastic measurements to the strategy which can improve its performance in some assets/time frames. The user can input desired take profits and stop loss goals or trailing take profit and trailing stop loss goals. The range to be back tested can be specified by the user. The overlays are titled, optional, and can be turned on/off.
                        This strategy primarily looks for long positions in an area of upwards momentum, however there is an option to include bearish engulfing shorts entries. This bearish engulfing short entry mechanism was added to potentially short the ends of the upwards momentum moves without being involved in a downtrend for too long. Although the strategy was not designed for shorts alone, the user can remove all long entries and only see short bearish engulfing patterns.</p>
                </div>

                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold">How it Functions</h1>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-1 m-10 gap-8">
                    {courseOutline.map((question, index) => (
                        <Disclosure key={index}>
                            {({ open }) => (
                                <div
                                    key={index}
                                    className={cn(
                                        'px-6 py-5 max-md:min-w-full max-md:px-[10px] max-md:py-4',
                                        index + 1 !== question.title.length && 'border-b border-gray-700',
                                        true ? 'max-lg:min-w-full' : ''
                                    )}>
                                    <div
                                        key={index}
                                        className="flex w-full flex-col gap-4 bg-white transition-all duration-300 px-6">
                                        <Disclosure.Button
                                            className={
                                                'relative flex w-full cursor-pointer select-none items-center justify-between gap-4 text-left text-sub3 max-md:text-sub4'
                                            }>
                                            <div
                                                className={cn(
                                                    'max-w-[700px] font-medium text-black transition-[color] duration-300',
                                                    open && 'text-blue'
                                                )}>
                                                {question.title}
                                            </div>
                                            <span
                                                className={cn(
                                                    'flex min-h-[24px] min-w-[24px] flex-col items-center justify-center rounded-full text-white transition-[transform_background] duration-300 ',
                                                    open ? 'bg-blue' : 'rotate-180 bg-black'
                                                )}>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="10"
                                                    height="7"
                                                    fill="none"
                                                    viewBox="0 0 10 7">
                                                    <path
                                                        stroke="#fff"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth="1.5"
                                                        d="M9.242 5.5l-4.12-4.083L1 5.5"
                                                    />
                                                </svg>
                                            </span>
                                        </Disclosure.Button>
                                        <Transition
                                            show={open}
                                            enter="transition duration-100 ease-out"
                                            enterFrom="transform scale-95 opacity-0"
                                            enterTo="transform scale-100 opacity-100"
                                            leave="transition duration-75 ease-out"
                                            leaveFrom="transform scale-100 opacity-100"
                                            leaveTo="transform scale-95 opacity-0">
                                            <div className="font-sans text-sub3 transition-[max-height_opacity] duration-300 max-md:text-sub4">
                                                <div dangerouslySetInnerHTML={{ __html: question.content }} />
                                            </div>
                                        </Transition>
                                    </div>
                                </div>
                            )}
                        </Disclosure>
                    ))}
                </div>
            </section>
            <section className="mb-4 p-6">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mx-10 pb-4 mt-10">
                    {
                        headerDetails.map(header => (
                            <div key={header.title} className="h-100 w-100 bg-gray-750 text-white rounded-md">
                                <ImageShortcut
                                    src={header.image}
                                    width={20}
                                    height={20}
                                    alt="thumbnail"
                                    className="mx-2  object-scale-down  h-20 w-20"
                                    priority
                                />
                                <h1 className="py-4 mx-2 text-2xl font-bold">{header.title}</h1>
                                <div className="p-2" dangerouslySetInnerHTML={{ __html: header.description }} />
                            </div>
                        ))
                    }


                </div>
            </section>

            <section className="mb-4 p-6">
                <div>
                    <h1 className="text-center mt-10 text-h3 font-bold">FAQs</h1>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-1 m-10 gap-8">
                    {faqs.map((question, index) => (
                        <Disclosure key={index}>
                            {({ open }) => (
                                <div
                                    key={index}
                                    className={cn(
                                        'px-6 py-5 max-md:min-w-full max-md:px-[10px] max-md:py-4',
                                        index + 1 !== question.title.length && 'border-b border-gray-700',
                                        true ? 'max-lg:min-w-full' : ''
                                    )}>
                                    <div
                                        key={index}
                                        className="flex w-full flex-col gap-4 bg-white transition-all duration-300 px-6">
                                        <Disclosure.Button
                                            className={
                                                'relative flex w-full cursor-pointer select-none items-center justify-between gap-4 text-left text-sub3 max-md:text-sub4'
                                            }>
                                            <div
                                                className={cn(
                                                    'max-w-[700px] font-medium text-black transition-[color] duration-300',
                                                    open && 'text-blue'
                                                )}>
                                                {question.title}
                                            </div>
                                            <span
                                                className={cn(
                                                    'flex min-h-[24px] min-w-[24px] flex-col items-center justify-center rounded-full text-white transition-[transform_background] duration-300 ',
                                                    open ? 'bg-blue' : 'rotate-180 bg-black'
                                                )}>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="10"
                                                    height="7"
                                                    fill="none"
                                                    viewBox="0 0 10 7">
                                                    <path
                                                        stroke="#fff"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth="1.5"
                                                        d="M9.242 5.5l-4.12-4.083L1 5.5"
                                                    />
                                                </svg>
                                            </span>
                                        </Disclosure.Button>
                                        <Transition
                                            show={open}
                                            enter="transition duration-100 ease-out"
                                            enterFrom="transform scale-95 opacity-0"
                                            enterTo="transform scale-100 opacity-100"
                                            leave="transition duration-75 ease-out"
                                            leaveFrom="transform scale-100 opacity-100"
                                            leaveTo="transform scale-95 opacity-0">
                                            <div className="font-sans text-sub3 transition-[max-height_opacity] duration-300 max-md:text-sub4">
                                                <div dangerouslySetInnerHTML={{ __html: question.content }} />
                                            </div>
                                        </Transition>
                                    </div>
                                </div>
                            )}
                        </Disclosure>
                    ))}
                </div>
            </section>
        </>
    );
};

export default TrendMomentum;