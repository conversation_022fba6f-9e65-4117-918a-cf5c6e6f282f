import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { user } from '@/types/UserModel'

const Avatar = ({ user }: { user: user }) => {
    return (
        <div className="flex items-end gap-9">
            <ImageShortcut
                src={user.image == null ? 'https://i.ibb.co/tzrYqNX/default.png' : user.image}
                alt="avatar"
                width={1080}
                height={720}
                className={'h-24 w-24 rounded-full'}
            />
            <div className="flex gap-5">
                <label className="w-[290px]">
                    <span className="flex w-full min-w-fit select-none items-center justify-center gap-2 rounded-full border border-gray-600 bg-white px-6 py-[17.5px] font-sans text-sub2 font-semibold text-black transition-[background-color] duration-150 hover:border-gray-500 hover:bg-gray-500 active:border-gray-600 active:bg-gray-600 disabled:cursor-not-allowed disabled:opacity-30 max-md:py-4 max-md:text-sub3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path
                                stroke="#081228"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="1.5"
                                d="M7.39 8.983h-.934a3.685 3.685 0 00-3.685 3.685v4.875a3.685 3.685 0 003.685 3.684h11.13a3.685 3.685 0 003.686-3.684v-4.885a3.675 3.675 0 00-3.674-3.675h-.944M12.021 2.19v12.04M9.106 5.117l2.915-2.928 2.916 2.928"></path>
                        </svg>
                        Upload A New Avatar
                    </span>
                    <input type="file" className="hidden" accept="image/*" />
                </label>
                <div className="w-[290px]">
                    <Button disabled={user.image == null} className="flex gap-2" rounded>
                        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" fill="none" viewBox="0 0 25 24">
                            <path
                                stroke="#FC0019"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="1.5"
                                d="M19.825 9.469s-.543 6.735-.858 9.572c-.15 1.355-.987 2.149-2.358 2.174-2.61.047-5.221.05-7.83-.005-1.318-.027-2.141-.831-2.288-2.162-.317-2.862-.857-9.58-.857-9.58M21.208 6.238H4.25M17.94 6.239a1.648 1.648 0 01-1.614-1.324l-.243-1.216a1.28 1.28 0 00-1.237-.949h-4.233a1.28 1.28 0 00-1.237.949l-.243 1.216a1.648 1.648 0 01-1.615 1.324"></path>
                        </svg>
                        Remove Current Avatar
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default Avatar
