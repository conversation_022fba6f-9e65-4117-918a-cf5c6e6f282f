"use client";
import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'

const TABS = [
  { label: 'Courses', key: 'courses' },
  { label: '1-on-1 Mentorship', key: 'mentorship' },
  { label: 'Trading Tools', key: 'tools' },
  { label: 'Bundles', key: 'bundles' },
]

// Example data structure for demonstration
const PRODUCTS = [
  {
    key: 'courses',
    items: [
      {
        image: '/courses/crypto101.png',
        title: 'Crypto 101 Masterclass',
        price: 99,
        videoCount: 42,
        benefits: [
          'Understand blockchain fundamentals',
          'Learn to trade safely',
          'Access to private Discord',
        ],
        cta: 'Enroll Now',
        slug: 'crypto-101-masterclass',
      },
      // ...more courses
    ],
  },
  {
    key: 'mentorship',
    items: [
      {
        image: '/courses/mentorship.png',
        title: '1-on-1 Mentorship',
        price: 499,
        videoCount: 10,
        benefits: [
          'Personalized trading guidance',
          'Direct access to experts',
          'Weekly live calls',
        ],
        cta: 'Book Session',
        slug: '1-on-1-mentorship',
      },
      // ...more mentorships
    ],
  },
  {
    key: 'tools',
    items: [
      {
        image: '/courses/trading-tool.png',
        title: 'Pro Trading Indicator',
        price: 149,
        videoCount: 5,
        benefits: [
          'Real-time market signals',
          'Easy TradingView integration',
          'Lifetime updates',
        ],
        cta: 'Get Tool',
        slug: 'pro-trading-indicator',
      },
      // ...more tools
    ],
  },
  {
    key: 'bundles',
    items: [
      {
        image: '/courses/bundle.png',
        title: 'Masterclass + Mentorship Bundle',
        price: 549,
        videoCount: 52,
        benefits: [
          'Full course access',
          '1-on-1 mentorship',
          'Save $50 with bundle',
        ],
        cta: 'Get Bundle',
        slug: 'masterclass-mentorship-bundle',
      },
      // ...more bundles
    ],
  },
]

function ProductCard({ product }: { product: any }) {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-2xl hover:-translate-y-1 transition p-6 flex flex-col gap-4">
      <Image src={product.image} alt={product.title} width={320} height={180} className="rounded-lg mb-4" />
      <h3 className="text-h3 font-semibold text-black">{product.title}</h3>
      <div className="flex items-center gap-2 text-sub2 text-gray-700 mb-2">
        <span>{product.videoCount} videos</span>
      </div>
      <ul className="list-disc pl-5 text-b3 text-gray-800 mb-2">
        {product.benefits.map((b: string, i: number) => (
          <li key={i}>{b}</li>
        ))}
      </ul>
      <div className="flex items-center justify-between mt-auto">
        <span className="text-h3 font-bold text-yellow">${product.price}</span>
        <Link href={`/courses/${product.slug}`}>
          <button className="bg-yellow text-black rounded-full px-6 py-2 font-bold hover:bg-yellow-400 transition">
            {product.cta}
          </button>
        </Link>
      </div>
    </div>
  )
}

export default function EnhancedCourses() {
  const [activeTab, setActiveTab] = useState('courses')
  const currentProducts = PRODUCTS.find((p) => p.key === activeTab)?.items || []

  return (
    <section className="container mx-auto py-16">
      <h2 className="text-h2 font-bold text-black mb-8">Our Offerings</h2>
      <div className="flex gap-4 mb-8">
        {TABS.map((tab) => (
          <button
            key={tab.key}
            className={`px-6 py-2 rounded-full font-bold text-b3 border-2 transition ${
              activeTab === tab.key
                ? 'bg-yellow text-black border-yellow shadow'
                : 'bg-white text-gray-700 border-gray-200 hover:bg-yellow/20'
            }`}
            onClick={() => setActiveTab(tab.key)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {currentProducts.map((product, idx) => (
          <ProductCard key={idx} product={product} />
        ))}
      </div>
      <div className="flex justify-center mt-12">
        <Link href="/memberships">
          <button className="bg-yellow text-black rounded-full px-8 py-4 text-b1 font-bold shadow-lg hover:bg-yellow-400">
            CU Membership – Unlock All Access
          </button>
        </Link>
      </div>
    </section>
  )
}