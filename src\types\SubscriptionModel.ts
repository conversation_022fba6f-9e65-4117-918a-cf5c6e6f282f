export interface SubscriptionModel {
    id: 3
    name: string
    short_description: string
    slug?: string
    description: string
    image: string
    price: string
    sale: number
    status: string
    instructor_id: number
    created_at: string
    updated_at: string
    priceWithoutTax: number
    stripe_price_id: string | null
    paypal_subscription_plan_id: string | null
    subscription_interval: string
    interval_count: number
    tax: number
    finalPrice: number
    transactions: Transactions[]
}

interface Transactions {
    created_at: string;
}