import { deskTool } from 'sanity/desk'
import schemas from '../sanity/schemas'
import { defineConfig } from './lib/sanity'

const config = defineConfig({
    projectId: process.env.SANITY_PROJECT_ID as string,
    dataset: 'production',
    title: 'Crypto University',
    apiVersion: '2023-06-08',
    basePath: '/cu-admin',
    plugins: [deskTool()],
    schema: { types: schemas }
})

export default config