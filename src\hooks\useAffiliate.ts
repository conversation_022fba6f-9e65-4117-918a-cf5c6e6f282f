import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

interface ReferralStore {
    referral: string
    startDate: Date
    endDate: Date
    setReferral: (referral: string) => void
    setStartDate: (date: Date) => void
    setEndDate: (date: Date) => void
}

const useAffiliate = create(
    persist<ReferralStore>(
        (set, get) => ({
            referral: '',
            startDate: new Date(),
            endDate: new Date(),
            setReferral: (referral: string) => {
                set({ referral })
            },
            setStartDate: (startDate: Date) => {
                set({ startDate })
            },
            setEndDate: (endDate: Date) => {
                endDate.setHours(endDate.getHours() + 2);
                set({ endDate });
            }
        }),
        {
            name: 'affiliate-storage',
            storage: createJSONStorage<ReferralStore>(() => localStorage),
        }
    )
)

export default useAffiliate
