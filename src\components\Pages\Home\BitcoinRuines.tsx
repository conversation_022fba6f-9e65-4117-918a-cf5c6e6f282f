import Link from "next/link"

const BitcoinRuines = () => {
    return (
        <section id='crypto' className="overflow-hidden bg-[#eec9100a] border border-[#EECA1080] py-28 text-black max-md:py-[2.125rem]">
            <div className="mx-auto flex max-w-[1130px] flex-wrap items-center gap-[4.625rem] px-4 max-md:flex-col max-md:gap-[1.125rem]">
                <div className="max-w-[600px] space-y-4">
                    <Link
                        aria-label="Watch Video"
                        href={"#"}
                        className="inline-block disabled bg-[#eec9101f] mb-2 border border-[#FCC229] mt-6 text-[#BD921F] hover:bg-blue-700 active:bg-blue-800 rounded-full  text-sub3 sm:text-sub4 font-semibold py-2 px-8 sm:px-6 transition-colors duration-150"
                    >
                        Featured Video
                    </Link>
                    <h2 className="font-medium text-b3 sm:text-b2 text-left">
                        Bitcoin Runes - All You Need to Know
                    </h2>
                    <p className="text-sub3 text-left max-md:text-sub4">
                        Bitcoin Runes are a novel fungible token protocol on the Bitcoin network, utilising a model that builds upon the social consensus mechanisms that was first introduced with the Ordinals protocol. To get involved in the evolution of the Bitcoin ecosystem, learn more about Bitcoin Runes in our video below.
                    </p>
                    <Link
                        aria-label="Watch Video"
                        href={"https://www.youtube.com/watch?v=rGHZXDS5h80&ab_channel=CryptoUniversity"}
                        className="inline-block bg-[#222222] mt-6 text-white hover:bg-blue-700 active:bg-blue-800 rounded-full  text-sub2 sm:text-sub3 font-semibold py-3 px-8 sm:px-12 transition-colors duration-150"
                    >
                        Watch Video {'>'}
                    </Link>
                </div>
                <div className="flex max-h-[209px] sm:max-h-[369px]">
                    <iframe className="rounded-md" width="360" height="260" src="https://www.youtube.com/embed/rGHZXDS5h80?si=HiWVQKcSTgE_S6Hb" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
                </div>
            </div>
        </section>
    )
}

export default BitcoinRuines
