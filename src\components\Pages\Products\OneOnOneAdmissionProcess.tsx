import React from 'react'
import Link from 'next/link'

const OneOnOneAdmissionProcess = () => {
  const process = [
      {
          step: 'STEP 1',
          title: 'Registration',
          description: (
              <p className=" md:text-[18px] text-left text-[14px] font-[500] text-[#5B5B5B]">
                  Click on the
                  <Link href="https://cryptouniversity.network/affiliates" className="register-link cursor-pointer text-[#D4A21C] underline mx-1">
                      Register Now
                  </Link>
                  button to access the application form.
              </p>
          ),
      },
      {
          step: 'STEP 2',
          title: 'Application Review and Selection',
          description:
              'During the review process, we look for applicants who are genuinely passionate about crypto trading and commit to participate in the assignments.',
      },
      {
          step: 'STEP 3',
          title: 'Admission and Enrollment',
          description:
              'Upon completion of the application review process, successful candidates will be notified of their admission to The Crypto Trading Bootcamp via email which contains important enrollment details.',
      },
  ]
  return (
      <div className="bg-white p-[32px] ">
          <h2 className="mb-5 mt-5 w-[343px] text-[24px] font-[600] text-[#222222] md:mt-10 md:w-full md:text-center md:text-[48px]">
              Mentorship Admission Process
          </h2>
          <div className="my-4 mt-6 gap-4">
              {process.map((item, index) => (
                  <div
                      key={index}
                      className="border-[#F2F2F2] mx-auto mb-[7px] max-w-[375px] justify-center
gap-2 rounded-[8px] border from-transparent to-[#F0F0F0] p-3 md:mb-[20px] md:max-w-[866px] md:p-6">
                      <h3 className="text-[14px] font-[600] text-[#40389E] md:text-[16px]">{item.step}</h3>
                      <h2 className="items-center text-[16px] font-[600] md:my-3 md:text-[22px]">{item.title}</h2>

                      <p className="text-left text-[12px] font-[400] text-[#5B5B5B]  md:text-[15px]">
                          {item.description}{' '}
                      </p>
                  </div>
              ))}
          </div>
      </div>
  )
}

export default OneOnOneAdmissionProcess
