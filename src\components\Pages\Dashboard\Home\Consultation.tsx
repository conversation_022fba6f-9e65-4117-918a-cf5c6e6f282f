import { MentorshipModel } from "@/types/MentorshipModel";
import CardConsultation from "./CardConsultation";

interface props {
  mentorships: MentorshipModel[];
}

const Consultation = ({ mentorships }: props) => {
  return (
    <div className="w-full flex gap-8 px-8 max-md:px-5 items-start flex-wrap">
      {mentorships && mentorships.map((item, index) => (
        <CardConsultation key={index} mentorship={item} />
      ))}
    </div>
  );
};

export default Consultation;
