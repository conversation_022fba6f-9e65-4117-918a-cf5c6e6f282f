'use client'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import Label from '@/components/Ui/Label'
import { FormikProps } from '@/lib/formik'

interface TextBoxProps {
    setEmail?: any
    placeholder: string
    error?: string
    label: string
    className?: string
    Icon?: React.ReactNode
    CodeSelect?: React.ReactNode
    formik: FormikProps<any>
    id: any
    disabled?: boolean
    value: any
    onChange: any
    innerRef: React.RefObject<HTMLInputElement>
    ValidationSchemaKeys: any
    type?: string
    required?: boolean
    pattern?: string
    isSelectFocus?: boolean
}

const showError = (
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>,
    key: any,
    formik: FormikProps<any>
) => {
    if (ref.current) {
        ref.current.onfocus = () => {
            formik.setFieldTouched(key, false)
        }
    }
    return formik.touched[key] && (formik.errors as { [key: string]: string })[key]?.length > 0
}

export const TextBox = ({
    placeholder,
    required,
    label,
    className,
    Icon,
    CodeSelect,
    disabled,
    id,
    value,
    innerRef,
    formik,
    type,
    onChange,
}: TextBoxProps) => {
    const [focus, setFocus] = useState(false)

    return (
        <div className={cn('flex h-fit w-full flex-col gap-3', className)}>
            <Label className="" required uppercase={false}>
                {label}
            </Label>

            <div className={cn('flex flex-col gap-2 max-md:w-full rounded-full', disabled && "bg-gray-400")}>
                <div
                    onBlur={() => {
                        setFocus(false)
                    }}
                    onClick={() => disabled ?? setFocus(true)}
                    className={cn(
                        'relative z-30 flex flex-1 select-none flex-row items-center rounded-[50px] border border-gray-700 ',
                        (showError(innerRef, 'phonePreffix', formik) || showError(innerRef, id, formik)) &&
                            'border-red',
                        focus && 'border border-blue'
                    )}>
                    {Icon ? <div className="absolute left-4">{Icon}</div> : ''}
                    {CodeSelect ? (
                        <div className="flex  w-[120px] items-center rounded-full rounded-r-none border-r-0">
                            {CodeSelect}
                            <div
                                className={cn(
                                    'z-30 mx-1 h-full border-l  border-gray-700 py-1.5 ',
                                    (showError(innerRef, 'phonePreffix', formik) ||
                                        (showError(innerRef, id, formik) && !focus)) &&
                                        'border-red',
                                    focus && 'border-l border-blue'
                                )}></div>
                        </div>
                    ) : (
                        ''
                    )}
                    <input
                        required={required}
                        autoComplete="new-password"
                        ref={innerRef}
                        type={id == 'email' ? 'email' : type ?? 'text'}
                        name={id}
                        id={id}
                        value={value}
                        onBlur={formik.handleBlur}
                        onChange={onChange}
                        disabled={disabled}
                        className={cn(
                            'w-full rounded-full border border-gray-700 border-transparent px-4 py-[17.5px] pr-2 outline-none ring-0 focus:outline-none max-md:py-3.5 max-md:text-cap1',
                            disabled && 'bg-gray-400',
                            Icon && 'pl-12',
                            CodeSelect && 'min-w-0 max-w-full grow rounded-l-none border-l-0 max-sm:!h-[46.8px]'
                        )}
                        placeholder={placeholder}
                    />
                </div>
                {showError(innerRef, id, formik) && !focus && (
                    <div className="rounded-lg bg-red/20 p-3">
                        <p className="text-cap1 text-red">{formik.errors[id]?.toString()}</p>
                    </div>
                )}
            </div>
        </div>
    )
}
