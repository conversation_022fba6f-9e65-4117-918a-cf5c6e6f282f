const PostSkeleton = () => {
    return (
        <section className="w-full animate-pulse text-black">
            <div className="mb-11">
                <div className="mb-5 space-y-2">
                    <div className="h-[25px] w-full bg-gray-500/60" />
                    <div className="h-[25px] w-[10%] bg-gray-500/60" />
                </div>

                <div className="mb-4 h-[8px] w-[140px] bg-gray-500/60" />

                <div className="mb-9 h-[408px] w-full bg-gray-500/60 max-md:mb-8 max-md:h-[185px]" />

                <div className="flex flex-wrap items-center space-x-2">
                    <div className="select-none rounded-full bg-gray-500/60 px-3 py-1 transition-colors duration-150">
                        <div className="h-[12px] w-[50px]" />
                    </div>
                </div>
            </div>

            <div className="space-y-2">
                {Array.from({ length: 12 }).map((_, index) => (
                    <div key={index} className=" h-[15px] w-full bg-gray-500/60" />
                ))}
            </div>
        </section>
    )
}

export default PostSkeleton
