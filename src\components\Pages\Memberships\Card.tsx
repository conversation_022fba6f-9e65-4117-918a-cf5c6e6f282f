// import Link from 'next/link'
// import { cn } from '@/lib/cn'
// import Button from '@/components/Ui/Button'
// import ImageShortcut from '@/components/Ui/Image'
// import { MembershipModel } from '@/types/MembershipModel'

// interface Props {
//     item: MembershipModel
//     index: number
// }

// const Card = ({ item, index }: Props) => {

//     return (
//         <div
//             className={cn(
//                 'relative flex flex-col items-start gap-7 rounded-3xl border border-gray-300 p-[38px] shadow-md max-sm:p-6',
//                 index == 1 && 'membershipcard'
//             )}>
//             <div className={cn('flex flex-col gap-[21px] max-sm:gap-3', index == 1 ? 'gap-10 max-sm:gap-6' : '')}>
//                 <div className="flex flex-col gap-[14px]">
//                     <div className="flex flex-col gap-1 ">
//                         <div className="relative flex w-full max-sm:static ">
//                             <h1 className={cn(`text-cap1 capitalize max-sm:text-cap4`)}>{item.title.text}</h1>
//                             <ImageShortcut
//                                 src={item.icon.src}
//                                 height={150}
//                                 width={150}
//                                 className="absolute left-[68%] mt-[-117px] h-auto object-cover max-sm:right-0 max-sm:mt-[-70px] max-sm:w-[95px]"
//                                 alt={item.icon.alt}
//                             />
//                         </div>
//                         <p className={`text-b3 font-semibold max-sm:text-sub3 text-${item.sub.color}`}>
//                             {item.sub.text}
//                         </p>
//                     </div>
//                     <p className={`font-sans text-h2 font-semibold max-sm:text-b1 text-${item.price.color}`}>
//                         ${parseInt(item.price.text)}{' '}
//                         <span className="text-sub1 font-normal max-sm:text-cap2">/monthly</span>
//                     </p>
//                 </div>
//                 <div className={cn('flex flex-col gap-3 max-sm:gap-2 ', index == 1 ? 'gap-6 max-sm:gap-3' : '')}>
//                     <p
//                         className={cn(
//                             'font-sans text-sub2 font-semibold max-sm:text-cap2',
//                             index == 1 ? 'text-white' : ''
//                         )}>
//                         What&apos;s included:
//                     </p>
//                     <div className="flex flex-col gap-2">
//                         {item.icon.alt === "Basic Membership Icon" ? (
//                             <>
//                                 {item.cards.text.slice(0,5).map((card: string, i: number) => (
//                                     <div key={i} className="flex items-center gap-3 max-sm:gap-2 opacity-50 cursor-not-allowed">
//                                     <span className="flex max-sm:w-[12px]">

//                                             <svg
//                                                 xmlns="http://www.w3.org/2000/svg"
//                                                 width="23"
//                                                 height="22"
//                                                 fill="none"
//                                                 viewBox="0 0 23 22">
//                                                 <path
//                                                     fill="#00BF77"
//                                                     d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
//                                                 <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
//                                                 <path
//                                                     stroke="#fff"
//                                                     strokeLinecap="round"
//                                                     strokeLinejoin="round"
//                                                     strokeWidth="1.5"
//                                                     d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
//                                             </svg>
//                                         </span>
//                                         <p className={`text-sub3 max-sm:text-cap3 text-gray-400`}>{card}</p>
//                                     </div>
//                                 ))}
//                                  {item.cards.text.slice(5).map((card: string, i: number) => (
//                                  <div key={i} className="flex items-center gap-3 max-sm:gap-2 opacity-50 cursor-not-allowed">
//                                  <span className="flex max-sm:w-[12px]">
//                                     <svg xmlns="http://www.w3.org/2000/svg"
//                                         width="23"
//                                         height="22"
//                                         fill="#FF0000"
//                                         viewBox="0 0 36 36">
//                                         <g>
//                                             <circle cx="17.89" cy="18.07" r="17.33" fill="rgba(255, 255, 255, 1)" />
//                                             <path d="m18,0h0C8.06,0,0,8.06,0,18h0c0,9.94,8.06,18,18,18h0c9.94,0,18-8.06,18-18h0C36,8.06,27.94,0,18,0Zm6.17,21.59c.92.86,1.24,1.89.29,2.85s-1.97.67-2.85-.24l-3.66-3.77c-1.29,1.37-2.41,2.58-3.55,3.78-.88.92-1.91,1.19-2.85.22s-.6-1.99.31-2.85c1.16-1.1,2.32-2.21,3.97-3.77-1.41-1.2-2.69-2.24-3.91-3.33-.98-.88-1.35-1.96-.29-2.97.98-.93,2-.55,2.85.36,1.09,1.17,2.18,2.34,3.54,3.8,1.41-1.5,2.53-2.72,3.67-3.92.83-.87,1.83-1.09,2.73-.23.98.93.73,1.96-.18,2.85-1.19,1.15-2.4,2.27-3.88,3.66,1.41,1.31,2.62,2.43,3.82,3.56Z" />
//                                         </g>
//                                     </svg>
//                                     </span>
//                                     <p className={`text-sub3 max-sm:text-cap3 text-gray-400`}>{card}</p>
//                                 </div>
//                             ))}
//                             </>
//                         ) : item.icon.alt === "Advanced Membership Icon" ? (
//                             <>
//                                 {item.cards.text.slice(0,6).map((card: string, i: number) => (
//                                     <div key={i} className="flex items-center gap-3 max-sm:gap-2">
//                                         <span className="flex max-sm:w-[12px]">

//                                             <svg
//                                                 xmlns="http://www.w3.org/2000/svg"
//                                                 width="23"
//                                                 height="22"
//                                                 fill="none"
//                                                 viewBox="0 0 23 22">
//                                                 <path
//                                                     fill="#00BF77"
//                                                     d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
//                                                 <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
//                                                 <path
//                                                     stroke="#fff"
//                                                     strokeLinecap="round"
//                                                     strokeLinejoin="round"
//                                                     strokeWidth="1.5"
//                                                     d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
//                                             </svg>
//                                         </span>
//                                         <p className={`text-sub3 max-sm:text-cap3 text-${item.cards.color}`}>{card}</p>
//                                     </div>
//                                 ))}
//                                  {item.cards.text.slice(6).map((card: string, i: number) => (
//                                 <div key={i} className="flex items-center gap-3 max-sm:gap-2">
//                                     <span className="flex max-sm:w-[12px]">
//                                     <svg xmlns="http://www.w3.org/2000/svg"
//                                         width="23"
//                                         height="22"
//                                         fill="#FF0000"
//                                         viewBox="0 0 36 36">
//                                         <g>
//                                             <circle cx="17.89" cy="18.07" r="17.33" fill="rgba(255, 255, 255, 1)" />
//                                             <path d="m18,0h0C8.06,0,0,8.06,0,18h0c0,9.94,8.06,18,18,18h0c9.94,0,18-8.06,18-18h0C36,8.06,27.94,0,18,0Zm6.17,21.59c.92.86,1.24,1.89.29,2.85s-1.97.67-2.85-.24l-3.66-3.77c-1.29,1.37-2.41,2.58-3.55,3.78-.88.92-1.91,1.19-2.85.22s-.6-1.99.31-2.85c1.16-1.1,2.32-2.21,3.97-3.77-1.41-1.2-2.69-2.24-3.91-3.33-.98-.88-1.35-1.96-.29-2.97.98-.93,2-.55,2.85.36,1.09,1.17,2.18,2.34,3.54,3.8,1.41-1.5,2.53-2.72,3.67-3.92.83-.87,1.83-1.09,2.73-.23.98.93.73,1.96-.18,2.85-1.19,1.15-2.4,2.27-3.88,3.66,1.41,1.31,2.62,2.43,3.82,3.56Z" />
//                                         </g>
//                                     </svg>
//                                     </span>
//                                     <p className={`text-sub3 max-sm:text-cap3 text-${item.cards.color}`}>{card}</p>
//                                 </div>
//                             ))}
//                             </>
//                         ) : item.icon.alt === "Supreme Membership Icon" ? (
//                             <>
//                                 {item.cards.text.map((card: string, i: number) => (
//                                     <div key={i} className="flex items-center gap-3 max-sm:gap-2">
//                                         <span className="flex max-sm:w-[12px]">

//                                             <svg
//                                                 xmlns="http://www.w3.org/2000/svg"
//                                                 width="23"
//                                                 height="22"
//                                                 fill="none"
//                                                 viewBox="0 0 23 22">
//                                                 <path
//                                                     fill="#00BF77"
//                                                     d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
//                                                 <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
//                                                 <path
//                                                     stroke="#fff"
//                                                     strokeLinecap="round"
//                                                     strokeLinejoin="round"
//                                                     strokeWidth="1.5"
//                                                     d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
//                                             </svg>
//                                         </span>
//                                         <p className={`text-sub3 max-sm:text-cap3 text-${item.cards.color}`}>{card}</p>
//                                     </div>
//                                 ))}
//                             </>
//                         ) : ( <div>

//                         </div>)}

//                     </div>
//                 </div>
//             </div>
//             <Link aria-label="Membership page" href={item.plans} target="_blank" className="w-full">
//                 <Button
//                     className={cn(
//                         '',
//                         index == 1
//                             ? '!bg-white !text-blue hover:!bg-white/80 hover:!text-blue/80 active:!bg-white/90 active:!text-blue/90'
//                             : ''
//                     )}
//                     variant="primary"
//                     rounded>
//                     Get Membership
//                 </Button>
//             </Link>
//         </div>
//     )
// }

// export default Card

import Link from 'next/link'
import { cn } from '@/lib/cn'
import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { MembershipModel } from '@/types/MembershipModel'

interface Props {
    item: MembershipModel
    index: number
}

const Card = ({ item, index }: Props) => {
    // Check if the plan is disabled
    const isDisabled = item.icon.alt === "Basic Membership Icon" || item.icon.alt === "Supreme Membership Icon";

    return (
        <div
            className={cn(
                'relative flex flex-col items-start gap-7 rounded-3xl border border-gray-300 p-[38px] shadow-md max-sm:p-6',
                index == 1 && 'membershipcard',
                isDisabled && 'opacity-50 cursor-not-allowed'
            )}>
            <div className={cn('flex flex-col gap-[21px] max-sm:gap-3', index == 1 ? 'gap-10 max-sm:gap-6' : '')}>
                <div className="flex flex-col gap-[14px]">
                    <div className="flex flex-col gap-1 ">
                        <div className="relative flex w-full max-sm:static ">
                            <h1 className={cn(`text-cap1 capitalize max-sm:text-cap4`)}>{item.title.text}</h1>
                            <ImageShortcut
                                src={item.icon.src}
                                height={150}
                                width={150}
                                className="absolute left-[68%] mt-[-117px] h-auto object-cover max-sm:right-0 max-sm:mt-[-70px] max-sm:w-[95px]"
                                alt={item.icon.alt}
                            />
                        </div>
                        <p className={`text-b3 font-semibold max-sm:text-sub3 text-${item.sub.color}`}>
                            {item.sub.text}
                        </p>
                    </div>
                    <p className={`font-sans text-h2 font-semibold max-sm:text-b1 text-${item.price.color}`}>
                        ${parseInt(item.price.text)}{' '}
                        <span className="text-sub1 font-normal max-sm:text-cap2">/monthly</span>
                        {/* {parseInt(item.price.text)===500 ? <>
                            ${parseInt(item.price.text)}{' '}
                         <span className="text-sub1 font-normal max-sm:text-cap2">/monthly</span>
                        </>: <>
                        <span className="text-sub1 font-normal max-sm:text-cap2">Coming Soon!!!</span>
                        </>
                     
                    } */}

                    </p>
                </div>
                <div className={cn('flex flex-col gap-3 max-sm:gap-2 ', index == 1 ? 'gap-6 max-sm:gap-3' : '')}>
                    <p
                        className={cn(
                            'font-sans text-sub2 font-semibold max-sm:text-cap2',
                            index == 1 ? 'text-white' : ''
                        )}>
                        What&apos;s included:
                    </p>
                    <div className="flex flex-col gap-2">
                        {item.icon.alt === "Basic Membership Icon" ? (
                            <>
                                {item.cards.text.slice(0, 5).map((card: string, i: number) => (
                                    <div key={i} className="flex items-center gap-3 max-sm:gap-2 opacity-50 cursor-not-allowed">
                                        <span className="flex max-sm:w-[12px]">

                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="23"
                                                height="22"
                                                fill="none"
                                                viewBox="0 0 23 22">
                                                <path
                                                    fill="#00BF77"
                                                    d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                                <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                <path
                                                    stroke="#fff"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            </svg>
                                        </span>
                                        <p className={cn(`text-sub3 max-sm:text-cap3`, isDisabled ? "text-gray-400" : `text-${item.cards.color}`)}>{card}</p>
                                    </div>
                                ))}
                                {item.cards.text.slice(5).map((card: string, i: number) => (
                                    <div key={i} className="flex items-center gap-3 max-sm:gap-2 opacity-50 cursor-not-allowed">
                                        <span className="flex max-sm:w-[12px]">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                width="23"
                                                height="22"
                                                fill="#FF0000"
                                                viewBox="0 0 36 36">
                                                <g>
                                                    <circle cx="17.89" cy="18.07" r="17.33" fill="rgba(255, 255, 255, 1)" />
                                                    <path d="m18,0h0C8.06,0,0,8.06,0,18h0c0,9.94,8.06,18,18,18h0c9.94,0,18-8.06,18-18h0C36,8.06,27.94,0,18,0Zm6.17,21.59c.92.86,1.24,1.89.29,2.85s-1.97.67-2.85-.24l-3.66-3.77c-1.29,1.37-2.41,2.58-3.55,3.78-.88.92-1.91,1.19-2.85.22s-.6-1.99.31-2.85c1.16-1.1,2.32-2.21,3.97-3.77-1.41-1.2-2.69-2.24-3.91-3.33-.98-.88-1.35-1.96-.29-2.97.98-.93,2-.55,2.85.36,1.09,1.17,2.18,2.34,3.54,3.8,1.41-1.5,2.53-2.72,3.67-3.92.83-.87,1.83-1.09,2.73-.23.98.93.73,1.96-.18,2.85-1.19,1.15-2.4,2.27-3.88,3.66,1.41,1.31,2.62,2.43,3.82,3.56Z" />
                                                </g>
                                            </svg>
                                        </span>
                                        <p className={cn(`text-sub3 max-sm:text-cap3`, isDisabled ? "text-gray-400" : `text-${item.cards.color}`)}>{card}</p>
                                    </div>
                                ))}
                            </>
                        ) : item.icon.alt === "Advanced Membership Icon" ? (
                            <>
                                {item.cards.text.slice(0, 6).map((card: string, i: number) => (
                                    <div key={i} className="flex items-center gap-3 max-sm:gap-2">
                                        <span className="flex max-sm:w-[12px]">

                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="23"
                                                height="22"
                                                fill="none"
                                                viewBox="0 0 23 22">
                                                <path
                                                    fill="#00BF77"
                                                    d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                                <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                <path
                                                    stroke="#fff"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            </svg>
                                        </span>
                                        <p className={cn(`text-sub3 max-sm:text-cap3`, isDisabled ? "text-gray-400" : `text-${item.cards.color}`)}>{card}</p>
                                    </div>
                                ))}
                                {item.cards.text.slice(6).map((card: string, i: number) => (
                                    <div key={i} className="flex items-center gap-3 max-sm:gap-2">
                                        <span className="flex max-sm:w-[12px]">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                width="23"
                                                height="22"
                                                fill="#FF0000"
                                                viewBox="0 0 36 36">
                                                <g>
                                                    <circle cx="17.89" cy="18.07" r="17.33" fill="rgba(255, 255, 255, 1)" />
                                                    <path d="m18,0h0C8.06,0,0,8.06,0,18h0c0,9.94,8.06,18,18,18h0c9.94,0,18-8.06,18-18h0C36,8.06,27.94,0,18,0Zm6.17,21.59c.92.86,1.24,1.89.29,2.85s-1.97.67-2.85-.24l-3.66-3.77c-1.29,1.37-2.41,2.58-3.55,3.78-.88.92-1.91,1.19-2.85.22s-.6-1.99.31-2.85c1.16-1.1,2.32-2.21,3.97-3.77-1.41-1.2-2.69-2.24-3.91-3.33-.98-.88-1.35-1.96-.29-2.97.98-.93,2-.55,2.85.36,1.09,1.17,2.18,2.34,3.54,3.8,1.41-1.5,2.53-2.72,3.67-3.92.83-.87,1.83-1.09,2.73-.23.98.93.73,1.96-.18,2.85-1.19,1.15-2.4,2.27-3.88,3.66,1.41,1.31,2.62,2.43,3.82,3.56Z" />
                                                </g>
                                            </svg>
                                        </span>
                                        <p className={cn(`text-sub3 max-sm:text-cap3`, isDisabled ? "text-gray-400" : `text-${item.cards.color}`)}>{card}</p>
                                    </div>
                                ))}
                            </>
                        ) : item.icon.alt === "Supreme Membership Icon" ? (
                            <>
                                {item.cards.text.map((card: string, i: number) => (
                                    <div key={i} className="flex items-center gap-3 max-sm:gap-2">
                                        <span className="flex max-sm:w-[12px]">

                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="23"
                                                height="22"
                                                fill="none"
                                                viewBox="0 0 23 22">
                                                <path
                                                    fill="#00BF77"
                                                    d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z"></path>
                                                <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                                <path
                                                    stroke="#fff"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                                            </svg>
                                        </span>
                                        <p className={cn(`text-sub3 max-sm:text-cap3`, isDisabled ? "text-gray-400" : `text-${item.cards.color}`)}>{card}</p>
                                    </div>
                                ))}
                            </>
                        ) : (<div>

                        </div>)}

                    </div>

                    {/* </div> */}
                </div>
            </div>
            <Link
                aria-label="Membership page"
                href={isDisabled ? '#' : 'https://whop.com/crypto-university/?pass=prod_BaUOMN9EsXukr'}
                target="_blank"
                className="w-full"
            >


                <Button
                    className={cn(
                        '',
                        index == 1
                            ? '!bg-white !text-blue hover:!bg-white/80 hover:!text-blue/80 active:!bg-white/90 active:!text-blue/90'
                            : '',
                        isDisabled && '!bg-gray-400 !text-gray-500 cursor-not-allowed'
                    )}
                    variant="primary"
                    rounded
                    disabled={isDisabled}
                >
                    <div className="flex justify-between">
                        Join (Pay with Credit Card)
                        <ImageShortcut
                            src={'/memberships/credit-card-icon.png'}
                            height={35}
                            width={35}
                            className="flex justify-end  object-cover h-35 w-35 ml-4" // Added ml-auto here
                            alt={item.icon.alt}
                        />
                    </div>
                </Button>
            </Link>
            <Link
                aria-label="Membership page"
                href={isDisabled ? '#' : 'https://whop.com/checkout/9d98aca6-8bb8-4613-b680-e76a7dd2b627'}
                target="_blank"
                className="w-full"
            >


                <Button
                    className={cn(
                        '',
                        index == 1
                            ? '!bg-white !text-blue hover:!bg-white/80 hover:!text-blue/80 active:!bg-white/90 active:!text-blue/90'
                            : '',
                        isDisabled && '!bg-gray-400 !text-gray-500 cursor-not-allowed'
                    )}
                    variant="primary"
                    rounded
                    disabled={isDisabled}
                >
                    <div className="flex justify-between">
                        Join (Pay with Crypto)
                        <ImageShortcut
                            src={'/memberships/bitcoin-icon.png'}
                            height={35}
                            width={35}
                            className="flex justify-end  object-cover h-35 w-35 ml-4" // Added ml-auto here
                            alt={item.icon.alt}
                        />
                    </div>
                </Button>
            </Link>

            {/* <Link aria-label="Membership page" href={isDisabled ? '#' : 'https://whop.com/crypto-university/?pass=prod_BaUOMN9EsXukr'} target="_blank" className="w-full">
                <Button
                    className={cn(
                        '',
                        index == 1
                            ? '!bg-white !text-blue hover:!bg-white/80 hover:!text-blue/80 active:!bg-white/90 active:!text-blue/90'
                            : '',
                        isDisabled && '!bg-gray-400 !text-gray-500 cursor-not-allowed'
                    )}
                    variant="primary"
                    rounded
                    disabled={isDisabled}>
                     <ImageShortcut
                                src={'/memberships/bitcoin-icon.png'}
                                height={50}
                                width={50}
                                className="absolute object-cover h-50 w-50"
                                alt={item.icon.alt}
                            />Join (Pay with Crypto) 
                </Button>
            </Link> */}
        </div>
    )
}

export default Card

