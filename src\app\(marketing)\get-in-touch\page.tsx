import { Country } from '@/types/CountryModel'
import ReachUsHidden from '@/components/Ui/ReachUsHidden'
import Sidebar from '@/components/Pages/GetInTouch/Sidebar'
import { InfoForm } from '@/components/Pages/GetInTouch/InfoForm'

export const metadata = {
    title: 'Get in touch',
    description:
        "We're looking forward to your contact. Fill out your name, email, and inquiry below and a member of our team will get back with you ASAP.",
    keywords: ['Get in touch', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

async function getCountryInfo(): Promise<Country[]> {
    const countries = await fetch('https://restcountries.com/v3.1/all', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        next: { revalidate: false },
    })

    const countriesData: Country[] = await countries.json()

    const countryInfo: Country[] = []

    for (const country of countriesData) {
        const { name, flags, idd } = country
        countryInfo.push({
            name: { common: name.common },
            flags: { svg: flags.svg },
            idd: idd,
        })
    }
    return countryInfo
}

export default async function GetInTouchPage() {
    const countries: Country[] = await getCountryInfo()

    return (
        <section className="relative flex w-full items-center max-md:flex-wrap">
            <Sidebar />

            <div className="h-fit grow px-20 py-14 max-xl:px-4 max-lg:pb-[46px] max-lg:pt-7 max-md:min-w-full">
                <InfoForm countries={countries} />
            </div>

            <ReachUsHidden />
        </section>
    )
}
