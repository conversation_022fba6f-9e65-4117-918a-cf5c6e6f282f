import Link from 'next/link'
import { cn } from '@/lib/cn'
import <PERSON>ton<PERSON><PERSON> from '../Ui/ButtonNav'
import { Menu, Transition } from '@/lib/headlessui'
import { Noones, COinW, Brazil, UAE, USA, Zambia, UK, Africa } from '@public/countries'
import Image from "../Ui/Image"

interface Props {
    img: any
    name: string
    link: string
    blank: boolean
}

const LinkComponent = ({ href, children, target }: { href: string; children: React.ReactNode; target?: boolean }) => {
    return (
        <Link
            aria-label={children + ' page'}
            href={href}
            target={target ? '_blank' : ''}
            className={'transition-[color] duration-150'}>
            <Menu.Item as="li">
                {({ active }) => <span className={active ? 'text-blue' : ''}>{children}</span>}
            </Menu.Item>
        </Link>
    )
}

const Country = ({ img, name, link, blank }: Props) => (
    <Link aria-label="Country page" href={link} target={blank ? "_blank" : undefined} className="w-full">
        <Menu.Item as="li">
            {({ active }) => (
                <div className="w-full flex items-center gap-[0.625rem] transition-[color] duration-150">
                    <Image
                        src={img}
                        alt={name}
                        width={31}
                        height={30}
                        className="rounded-full"
                    />
                    <span className={cn("text-sub3", active && "text-blue")}>{name}</span>
                </div>
            )}
        </Menu.Item>
    </Link>
)

const cohorts = [
    {
        img: Africa,
        name: "Developer pro",
        link: "/web3-dev-program",
        blank: false
    },
    // {
    //     img: COinW,
    //     name: 'CoinW Trading Competition',
    //     link: '/coinw-trading-competition',
    //     blank: false,
    // },
    {
        img: Noones,
        name: 'P2P Academy',
        link: '/noones-p2p-academy',
        blank: false,
    },
    // {
    //     img: Zambia,
    //     name: "Zambia Dev Program",
    //     link: "/zambia-web3-developer-programme",
    //     blank: false
    // }
    // ,
    // {
    //     img: Zambia,
    //     name: "Zambia Bootcamp",
    //     link: "/bootcamp",
    //     blank: false
    // },
    // {
    //     img: USA,
    //     name: "USA",
    //     link: '/get-in-touch',
    //     blank: false
    // },
    // {
    //     img: UK,
    //     name: "UK",
    //     link: '/get-in-touch',
    //     blank: false
    // },
    // {
    //     img: Brazil,
    //     name: "Brazil",
    //     link: '/get-in-touch',
    //     blank: false
    // },
    // {
    //     img: UAE,
    //     name: "UAE",
    //     link: '/get-in-touch',
    //     blank: false
    // },
]

const countries = [
    {
        img: UAE,
        name: "UAE",
        link: '#',
        blank: false
    },
    {
        img: Zambia,
        name: "Zambia",
        link: "/zambia-web3-developer-programme",
        blank: false
    },
    // ,
    // {
    //     img: Zambia,
    //     name: "Zambia Bootcamp",
    //     link: "/bootcamp",
    //     blank: false
    // },
    {
        img: UK,
        name: "UK",
        link: '#',
        blank: false
    },
    {
        img: USA,
        name: "USA",
        link: '#',
        blank: false
    },
    {
        img: Brazil,
        name: "Brazil",
        link: '#',
        blank: false
    },

]


const ExploreDropdown = () => {
    return (
        <Menu as="li">
            {({ open, close }) => (
                <>
                    <Menu.Button
                        className={
                            'flex cursor-pointer items-center gap-[0.625rem] text-sub3 transition-[font-weight] duration-300'
                        }>
                        Explore
                        <svg
                            className={cn(open && 'rotate-180', 'transition[transform] duration-150')}
                            xmlns="http://www.w3.org/2000/svg"
                            width="15"
                            height="8"
                            fill="none"
                            viewBox="0 0 15 8">
                            <path
                                stroke="#2655FF"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="1.5"
                                d="M13.736 1l-6 6-6-6"
                            />
                        </svg>
                    </Menu.Button>
                    <Transition
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95">
                        <Menu.Items
                            className={
                                'absolute mt-10 flex max-w-[820px] rounded-lg bg-white font-sans shadow-[0px_40px_80px_rgba(0,0,0,0.1)]'
                            }>
                            {/* Part 1 */}
                            <div className="flex h-full w-[340px] flex-col gap-6 p-6 max-2xl:gap-4 max-xl:w-[300px]">
                                <h3 className="text-sub3 font-semibold">Courses</h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    <LinkComponent href="/courses/web3-masterclass">Web3 Masterclass</LinkComponent>
                                    <LinkComponent href="/courses/web3-masterclass">Crypto Trading</LinkComponent>
                                    <LinkComponent href="/courses/web3-masterclass">Crypto Investing</LinkComponent>
                                    {/* <LinkComponent href="/courses/ai-masterclass">AI Masterclass</LinkComponent> */}
                                    {/* <LinkComponent href="/courses/content-creation-masterclass">
                                        Content Creation Masterclass
                                    </LinkComponent> */}
                                </ul>
                                <div className="w-[320px] max-xl:w-full" onClick={close}>
                                    <ButtonNav href="/products">All products</ButtonNav>
                                </div>
                                <h3 className="border-t border-gray-400 pt-6 text-sub3 font-semibold">
                                    Cohort Programs
                                </h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    {cohorts.map((cohort, i) => (
                                        <Country
                                            key={i}
                                            img={cohort.img}
                                            name={cohort.name}
                                            link={cohort.link}
                                            blank={cohort.blank}
                                        />
                                    ))}
                                    {/* <LinkComponent href="/zambia-web3-developer-programme">Zambia Dev Program</LinkComponent>
                                    <LinkComponent href="/bootcamp">Zambia Bootcamp</LinkComponent>
                                    <LinkComponent href="/get-in-touch">USA</LinkComponent>
                                    <LinkComponent href="/get-in-touch">UK</LinkComponent>
                                    <LinkComponent href="/get-in-touch">Brazil</LinkComponent>
                                    <LinkComponent href="/get-in-touch">UAE</LinkComponent> */}
                                </ul>
                                {/* <h3 className="border-t border-gray-400 pt-6 text-sub3 font-semibold">
                                    Cohort Programs
                                </h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    {countries.map((country, i) => (
                                        <Country
                                            key={i}
                                            img={country.img}
                                            name={country.name}
                                            link={country.link}
                                            blank={country.blank}
                                        />
                                    ))}
                                </ul> */}
                                <h3 className="border-t border-gray-400 pt-6 text-sub3 font-semibold">Explore</h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    <LinkComponent href="https://degenwear.store" target={true}>Style & Gear</LinkComponent>
                                    <LinkComponent href="/courses">All Courses</LinkComponent>
                                    {/* <LinkComponent href="/cgucourse">CGU Course</LinkComponent> */}
                                    {/* <LinkComponent href="/courses/private">Private Courses</LinkComponent> */}
                                    {/* /cgucourse */}
                                    <LinkComponent href="/blog">Blog</LinkComponent>
                                    <LinkComponent href="/about">About us</LinkComponent>
                                    <LinkComponent href="/join-our-community">Community</LinkComponent>
                                </ul>
                            </div>
                            {/* Part 2 */}
                            <div className="flex h-full w-[340px] flex-col gap-6 p-6 max-2xl:gap-4 max-2xl:p-5 max-xl:w-[300px]">
                                <h3 className="text-sub3 font-semibold">Coaching</h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    <LinkComponent href="/consultation">1 On 1 Mentorship</LinkComponent>
                                    <LinkComponent href="/grey-jabesi-mentorship">Grey Jabesi Mentorship</LinkComponent>
                                    <LinkComponent href="/membership">CU Membership</LinkComponent>
                                </ul>
                                <h3 className="border-t border-gray-400 pt-6 text-sub3 font-semibold">CU Membership</h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    <LinkComponent href="https://whop.com/orders/products/" target>
                                        Manage Membership
                                    </LinkComponent>
                                    <LinkComponent href="/join-our-community">Join Our Community</LinkComponent>
                                </ul>
                                <h3 className="border-t border-gray-400 pt-6 text-sub3 font-semibold">Trade Tools</h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    <LinkComponent href="/affiliates">Affiliates</LinkComponent>
                                    <LinkComponent href="/resources/exchanges">Exchanges</LinkComponent>
                                    <LinkComponent href="/cryptoguide">Cryptoguide</LinkComponent>
                                    <LinkComponent href="/faqs">FAQs</LinkComponent>
                                    <LinkComponent href="/indicators">Indicators</LinkComponent>
                                    <LinkComponent href="/resources/extra-resources">Resources</LinkComponent>
                                </ul>
                            </div>
                            {/* Part 3 */}
                            <div className="flex h-full w-[340px] flex-col gap-6 p-6 max-2xl:gap-4 max-xl:w-[300px]">
                                <h3 className="text-sub3 font-semibold">
                                    Web3 Dev Programs
                                </h3>
                                <ul className="flex flex-col gap-[1.125rem] max-2xl:gap-2">
                                    {countries.map((country, i) => (
                                        <Country
                                            key={i}
                                            img={country.img}
                                            name={country.name}
                                            link={country.link}
                                            blank={country.blank}
                                        />
                                    ))}
                                </ul>

                            </div>
                        </Menu.Items>
                    </Transition>
                </>
            )}
        </Menu>
    )
}

export default ExploreDropdown
