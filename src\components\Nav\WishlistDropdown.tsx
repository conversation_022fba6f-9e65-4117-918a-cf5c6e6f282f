'use client'

import { Menu, Transition } from '@/lib/headlessui'
import { Whishlist as WhishlistIcon } from '@public/home'
import WishlistCard from '../Pages/Cart/WishlistCard'
import useWishlist from '@/hooks/useWishlist'
import Button from '../Ui/Button'
import { useEffect, useState } from 'react'
import { cn } from '@/lib/cn'
import Link from 'next/link'

const Wishlist = () => {
    const wishlist = useWishlist()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])
    if (!isMounted) return null
    return (
        <Menu className={'list-none'} as="li">
            {({ open, close }) => (
                <>
                    <Menu.Button
                        aria-label="Wishlist Button"
                        className={
                            'relative flex cursor-pointer items-center gap-[0.625rem] text-sub3 transition-[font-weight] duration-300 '
                        }>
                        <WhishlistIcon black={wishlist.products.length > 0} />
                        <div
                            className={cn(
                                'absolute inset-0 left-3 top-3 flex h-[20px] w-[20px] items-center justify-center rounded-full bg-blue ',
                                wishlist.products.length > 0 ? '' : 'hidden'
                            )}>
                            <p className="text-cap2 text-white"> {wishlist.products.length}</p>
                        </div>
                    </Menu.Button>
                    <Transition
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95">
                        <Menu.Items className="-px-[24px] absolute right-0 mt-10 max-w-[760px] select-none space-y-3 rounded-[0.5rem] bg-white p-6 font-sans text-sub3 shadow-[0px_40px_80px_rgba(0,0,0,0.1)]">
                            {wishlist.products.length > 0 ? (
                                <ul className="flex flex-col gap-4">
                                    {wishlist.products.slice(0, 3).map(course => (
                                        <WishlistCard key={course.id} course={course} removeBorder />
                                    ))}
                                </ul>
                            ) : (
                                <div className="flex flex-col items-center gap-4">
                                    <WhishlistIcon />
                                    <p className="text-sub2 capitalize">Your wishlist is empty</p>
                                </div>
                            )}

                            <div
                                className={cn(
                                    'border-b border-gray-700 px-6',
                                    wishlist.products.length == 0 ? 'opacity-0' : ''
                                )}
                            />
                            <div className="w-[343px] border-gray-700 pb-2" onClick={close}>
                                <Link aria-label="Wishlist" href={'/wishlist'}>
                                    <Button className="!py-2" rounded variant="primary">
                                        Go To Wishlist
                                    </Button>
                                </Link>
                            </div>
                        </Menu.Items>
                    </Transition>
                </>
            )}
        </Menu>
    )
}

export default Wishlist
