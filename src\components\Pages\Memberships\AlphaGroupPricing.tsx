import { AlphaGroupModel } from '@/types/AlphaGroupModel'
import AlphaCard from './AlphaCard'
import AlphaSwiperCards from './AlphaSwiperCards'
import AlphaCardMobile from './AlphaCardMobile'

const AlphaGroupPricing = async ({ data }: { data: AlphaGroupModel[] }) => {


    return (
        <div id="plans" className=" relative mx-auto flex flex-col gap-[106px] max-sm:gap-10 mb-10 py-auto sm:py-[5rem] bg-[#222121] p-[64px]">
            <div className="flex flex-col items-center gap-4">
                <h1 className="text-callout font-semibold uppercase text-[#EFB77C] max-sm:text-cap2">One Membership. Unlimited Returns</h1>
                <p className="text-center text-white sm:font-medium sm:leading-[142%] text-b2 max-sm:text-sub3">
                    Pick Your Alpha Group <br/> Membership Plan
                </p>
            </div>
            <div id='plans' className="flex w-full items-center justify-between max-md:hidden">
                {data.map((item, index) => (
                    <AlphaCard key={index} item={item} index={index} />
                ))}
            </div>
            {/* <AlphaSwiperCards data={data} /> */}
            <div className="md:hidden">
            {data.map((item, index) => (
                    <AlphaCardMobile key={index} item={item} index={index} />
                ))}
            </div>
        </div>
    )
}

export default AlphaGroupPricing
