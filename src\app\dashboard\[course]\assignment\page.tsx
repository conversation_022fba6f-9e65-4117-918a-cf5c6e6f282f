import { GetCourses, GetOneCourse, getCourseById } from '@/config/validationCourseDashboard'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { CourseModelV2 } from '@/types/CourseModel'
import { redirect } from 'next/navigation'

interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

type Props = {
    searchParams: { [key: string]: string | string[] | undefined }
    params: {
        course: string
        quiz: string
    }
}

const page = async ({ searchParams, params }: Props) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }
    const { filter, pageNumber } = searchParams
    const booleanFilter = filter == 'inProgress' || filter === 'all' ? true : false

    const courses: Model = await GetCourses({
        user,
        booleanFilter,
        filter,
        pageNumber,
    })
    const courseId = getCourseById({ courses, params })
    const course = await GetOneCourse(user, courseId?.slug ?? '')
    if (courseId === null || course === undefined) {
        redirect('/dashboard')
    }
    redirect('/dashboard/' + courseId?.slug)
}

export default page
