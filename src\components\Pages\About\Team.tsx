import Link from 'next/link'
import teamCards from '@/config/teamCards'
import AboutCard from '@/components/Ui/AboutCard'
import ImageShortcut from '@/components/Ui/Image'

const Team = () => {
    return (
        <section
            id="team"
            className="container mx-auto space-y-11 pb-16 pt-[77px] max-md:space-y-9 max-md:pb-9 max-md:pt-[30px]">
            <div className="flex flex-col items-center gap-[10px] max-md:gap-2">
                <h3 className="text-center text-h3 font-semibold max-md:text-b3">Our Team</h3>
                <p className="max-w-[1027px] text-center text-sub3 max-md:hidden">
                    <span className="text-headline font-medium leading-none">In 2018</span> as a way to help newcomers
                    avoid scams in the cryptocurrency space. In addition, we also wanted to provide advanced education
                    for investors, traders and professionals. Cryptocurrency is the ultimate financial tool. At Crypto
                    U, we aim to educate and empower people all over the world through Cryptocurrency and Blockchain
                    knowledge.
                </p>
                <p className="max-w-[1027px] text-center text-sub4 md:hidden">
                    <span className="text-b3 font-medium leading-none">In 2018</span> as a way to help newcomers avoid
                    scams in the cryptocurrency space. In addition, we also wanted to provide advanced education for
                    investors, traders and professionals.
                </p>
            </div>

            <div className="max-md:gap-x-[18px]max-md:gap-y-5 grid grid-cols-4 gap-x-6 gap-y-11 max-xl:grid-cols-3 max-lg:grid-cols-2">
                {teamCards.map((card, index) => (
                    <div key={index} className="space-y-4">
                        <AboutCard className="!max-md:max-w-full !max-md:min-w-full relative">
                            <ImageShortcut
                                src={card.image}
                                className="rounded-lg mix-blend-luminosity"
                                quality={100}
                                alt={card.name}
                            />
                            <ImageShortcut
                                src={card.country}
                                className="absolute right-4 top-5 max-md:right-2 max-md:top-[11px] max-md:h-[26.19px] max-md:w-[34.57px]"
                                quality={100}
                                alt={card.countryName}
                            />
                        </AboutCard>
                        <div className="flex flex-col items-center justify-center gap-1">
                            <p className="text-center text-callout font-semibold max-md:text-cap2">{card.name}</p>
                            <p className="text-center text-sub4 font-medium max-md:text-cap3">{card.title}</p>
                            <Link
                                href={card.link}
                                target="_blank"
                                aria-label={card.name + ' Linked in'}
                                className="p-[10px] max-md:p-[5px]">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                                </svg>
                            </Link>
                        </div>
                    </div>
                ))}
            </div>
        </section>
    )
}

export default Team
