import { cn } from '@/lib/cn'
import { CourseModelV2 } from '@/types/CourseModel'
import Link from 'next/link'

interface Model {
    courses: CourseModelV2[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}
interface props {
    courses: Model
    filter: string
    pageNumber: string
}

const Pagination = ({ courses, filter, pageNumber }: props) => {
    const pageArray = courses?.meta?.totalPages
        ? Array.from({ length: courses.meta.totalPages }, (_, index) => index + 1)
        : []
    return (
        <div className="flex items-center justify-center gap-4 pb-14 pt-6 text-b2">
            <Link
                aria-label="Previous page"
                className={cn(
                    ' select-none',
                    courses.meta && courses.meta.currentPage <= 1 ? '  cursor-not-allowed ' : ''
                )}
                href={
                    '/dashboard?filter=' +
                    filter +
                    '&pageNumber=' +
                    (typeof pageNumber == 'string' && pageNumber == '1'
                        ? '1'
                        : parseInt(typeof pageNumber == 'string' ? pageNumber : '1') - 1)
                }>
                <button
                    className={cn(
                        'flex h-[52px] min-w-[52px] max-w-[52px] select-none items-center justify-center rounded-full border border-gray-700  hover:bg-blue hover:text-white active:bg-blue/80',
                        courses.meta && courses.meta.currentPage <= 1
                            ? 'cursor-not-allowed text-gray-700 hover:bg-white hover:text-gray-700 active:bg-white'
                            : ''
                    )}>
                    &#60;
                </button>
            </Link>
            {pageArray.map((item, index) => (
                <Link
                    aria-label={'Page ' + item}
                    key={index}
                    href={'/dashboard?filter=' + filter + '&pageNumber=' + item}
                    className={'text-sub3'}>
                    <button
                        className={cn(
                            'flex  h-[42px] min-w-[42px] max-w-[42px] select-none items-center justify-center rounded-full  border  hover:bg-gray-300  active:bg-gray-300/80',
                            item == courses.meta.currentPage ? 'bg-gray-300 text-black' : ''
                        )}>
                        {item}
                    </button>
                </Link>
            ))}
            <Link
                aria-label="Next page"
                className={cn(
                    'select-none',
                    courses.meta && courses.meta.currentPage <= courses.meta.totalPages ? 'cursor-not-allowed' : ''
                )}
                href={
                    '/dashboard?filter=' +
                    filter +
                    '&pageNumber=' +
                    (courses.meta && typeof pageNumber == 'string' && parseInt(pageNumber) >= courses.meta.totalPages
                        ? pageNumber
                        : parseInt(typeof pageNumber == 'string' ? pageNumber : '1') + 1)
                }>
                <button
                    className={cn(
                        'flex h-[52px] min-w-[52px] max-w-[52px] select-none items-center justify-center rounded-full border border-gray-700  hover:bg-blue hover:text-white active:bg-blue/80',
                        courses.meta && courses.meta.currentPage >= courses.meta.totalPages
                            ? 'cursor-not-allowed text-gray-700 hover:bg-white hover:text-gray-700 active:bg-white'
                            : ''
                    )}>
                    &#62;
                </button>
            </Link>
        </div>
    )
}

export default Pagination
