import fetchInstance from '@/lib/fetch';
import { CoursesSlugs } from '@/types/CourseModel';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
    const cacheControlHeaders = new Headers(request.headers);
    cacheControlHeaders.set('Vercel-CDN-Cache-Control', 'max-age=3600');
    cacheControlHeaders.set('CDN-Cache-Control', 'max-age=60');
    cacheControlHeaders.set('Cache-Control', 'max-age=10');

    try {
        const response = await fetchInstance("/course/slugs", { next: { revalidate: 60 } }) as CoursesSlugs;
        return NextResponse.json(response.slugs, { headers: cacheControlHeaders });
    } catch (error) {
        console.error("Error:", error);
        return error;
    }
}