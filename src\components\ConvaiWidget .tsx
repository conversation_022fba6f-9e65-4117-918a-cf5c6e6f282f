"use client";
import Script from "next/script";

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'elevenlabs-convai': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & { 'agent-id': string };
    }
  }
}

const ConvaiWidget = () => {
  return (
    <>
      <elevenlabs-convai agent-id="4njcqX0tEeUFl8IcpSy5"></elevenlabs-convai>
      <Script id="convai-script" strategy="afterInteractive">
        {`
          const script = document.createElement('script');
          script.src = "https://elevenlabs.io/convai-widget/index.js";
          script.async = true;
          script.type = "text/javascript";
          document.body.appendChild(script);
        `}
      </Script>
    </>
  );
};

export default ConvaiWidget;
