"use client"

import Provider from '@/components/provider';
import {QueryClient, QueryClientProvider} from 'react-query'

const queryClient = new QueryClient()

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
        <body>
        <QueryClientProvider client={queryClient}>
      <Provider>
          {children}
          </Provider>
    </QueryClientProvider>
        </body>
    </html>
  )
}
