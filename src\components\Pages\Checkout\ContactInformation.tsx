'use client'
import { cn } from '@/lib/cn'
import { TextBox } from './TextBox'
import { Email } from '@public/global'
import React, { useState } from 'react'
import Label from '@/components/Ui/Label'
import CustomSelect from './CustomSelect'
import { ProfileBlack } from '@public/home'
import { FormikProps } from '@/lib/formik'
import { Country } from '@/types/CountryModel'
import ImageShortcut from '@/components/Ui/Image'
import { Country as CountrySVG } from '@public/checkout'

type Props = {
    user: any
    countries: Country[]
    formik: FormikProps<any>
}

type ValidationSchemaKeys = 'fullname' | 'phoneSuffix' | 'phonePreffix' | 'country' | 'email'

const showError = (
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>,
    key: ValidationSchemaKeys,
    formik: FormikProps<any>
) => {
    if (ref.current) {
        ref.current.onfocus = () => {
            formik.setFieldTouched(key, false)
        }
    }
    return formik.touched[key] && (formik.errors as { [key: string]: string })[key]?.length > 0
}

export const ContactInformation = ({ user, countries, formik }: Props) => {
    const phoneSuffixRef = React.useRef(null)
    const countryNames = countries.map((country: Country) => ({
        value: country.name.common,
        label: country.name.common,
    }))

    const codes = countries.map((country: Country) => ({
        value: country.idd.root + country.idd.suffixes,
        label: (
            <span className="flex items-center justify-center gap-1">
                <ImageShortcut src={country.flags.svg} height={32} alt={country.name.common} width={32} />
                <span>
                    {' '}
                    {country.idd.suffixes?.length == 1 ? country.idd.root + country.idd.suffixes : country.idd.root}
                </span>
            </span>
        ),
    }))

    const fullnameRef = React.useRef(null)
    const emailRef = React.useRef(null)
    const countryRef = React.useRef(null)
    const [isFocus, setIsFocus] = useState(false)

    return (
        <div className="grid h-fit grid-cols-2 gap-[21px] max-lg:grid-cols-1">
            {/* Name Select */}
            <TextBox
                required={true}
                formik={formik}
                Icon={<ProfileBlack />}
                innerRef={fullnameRef}
                ValidationSchemaKeys={'fullname' as ValidationSchemaKeys}
                id="fullname"
                label="Your Full Name"
                disabled={user?.first_name ? true : false}
                value={formik.values.fullname}
                type="text"
                placeholder="e.g John Smith"
                onChange={formik.handleChange}
            />
            {/* Email Input */}
            <TextBox
                formik={formik}
                innerRef={emailRef}
                Icon={<Email />}
                ValidationSchemaKeys={'email' as ValidationSchemaKeys}
                id="email"
                label="Your Email"
                value={formik.values.email}
                type="email"
                disabled={user?.email ? true : false}
                placeholder="e.g <EMAIL>"
                onChange={formik.handleChange}
                required={true}
            />
            {/* Phone Select */}
            <TextBox
                formik={formik}
                ValidationSchemaKeys={'phoneSuffix' as ValidationSchemaKeys}
                innerRef={phoneSuffixRef}
                id="phoneSuffix"
                label="Phone Number"
                placeholder="Enter mobile no."
                value={formik.values.phoneSuffix}
                onChange={formik.handleChange}
                required={true}
                disabled={user?.phone ? true : false}
                CodeSelect={
                    <CustomSelect
                        style={{
                            control: (provided, state) => ({
                                width: '120px',
                                borderWidth: '0px',
                                height: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingRight: '0px',
                                paddingLeft: '0.5rem',
                                borderRadius: '50px',
                                borderTopRightRadius: '0px',
                                borderBottomRightRadius: '0px',
                            }),
                            indicatorSeparator: (provided, state) => ({
                                display: 'none',
                            }),
                            dropdownIndicator: (provided, state) => ({
                                padding: '0px',
                            }),
                            valueContainer: (provided, state) => ({
                                padding: '0pxs',
                                alignItemd: 'center',
                                alignSelf: 'center',
                                display: 'flex',
                                flex: '1',
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                flexWrap: 'wrap',
                                position: 'relative',
                                overflow: 'hidden',
                                boxSizing: 'border-box',
                            }),
                            singleValue: (provided, state) => ({
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }),
                        }}
                        setIsFocus={setIsFocus}
                        isDisabled={user?.phone ? true : false}
                        isSearchable={true}
                        formik={formik}
                        label="Country"
                        options={codes}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.phonePreffix}
                        defaultValue={codes.find(code => code?.value === user?.country_code) ?? codes[34]}
                        name="phonePreffix"
                        id="phonePreffix"
                        className="relative w-full rounded-[50px] border-0 px-0 placeholder:text-gray-700 disabled:bg-gray-400 max-md:max-h-max  max-md:text-cap1"
                    />
                }
            />
            {/* Country Select */}
            <div className="flex h-fit w-full flex-col gap-3">
                <Label required uppercase={false}>
                    Country
                </Label>
                <div className={cn('relative flex items-center rounded-full', user?.country && 'bg-gray-400')}>
                    <CustomSelect
                        isDisabled={user?.country ? true : false}
                        style={{
                            control: (provided, state) => ({
                                width: '100%',
                                paddingTop: '14px',
                                paddingBottom: '14px',
                                height: '100%',
                                border: state.isFocused
                                    ? '1px solid #2563EB'
                                    : showError(countryRef, 'country', formik)
                                    ? '1px solid red'
                                    : '1px solid #AAAAAA',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingRight: '0px',
                                paddingLeft: '0.5rem',
                                borderRadius: '50px',
                            }),
                            valueContainer: (provided, state) => ({
                                padding: '0pxs',
                                paddingLeft: '35px',
                                alignItems: 'center',
                                display: 'grid',
                                flex: '1',
                                flexWrap: 'wrap',
                                position: 'relative',
                                overflow: 'hidden',
                                boxSizing: 'border-box',
                            }),
                        }}
                        innerRef={countryRef}
                        isSearchable={true}
                        formik={formik}
                        label="Select"
                        options={countryNames}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.country}
                        defaultValue={countryNames.find(country => country.value === user?.country) ?? countryNames[34]}
                        name="country"
                        id="country"
                        setIsFocus={() => {}}
                        className="relative h-[62px] w-full rounded-[50px] px-0 outline-none transition-[border] duration-150 placeholder:text-gray-700 max-md:max-h-[50px] max-md:text-cap1"
                    />
                    <div className="absolute left-4">
                        <CountrySVG />
                    </div>
                </div>
                <div
                    className={cn(
                        'bg mb-1 hidden min-h-[40.89px] rounded-lg bg-red/20 p-3 text-red',
                        showError(countryRef, 'country', formik) && 'block'
                    )}>
                    <p className="select-none text-cap1">{formik.errors.country + ''}</p>
                </div>
            </div>
        </div>
    )
}
