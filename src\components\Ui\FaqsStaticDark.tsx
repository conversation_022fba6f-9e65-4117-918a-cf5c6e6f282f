import Link from 'next/link'
import Button from './Button'
import { categories } from '@/content/faqs/faqs'
import DisclosureDark from '../Pages/Faqs/DisclosureDark'

const FaqsStaticDark = () => {
    return (
        <div className="container mx-auto w-full">
            <div className="flex justify-between py-11 max-lg:flex-col max-lg:justify-start max-lg:gap-5">
                <div className="flex max-w-[320px] flex-col items-start gap-6">
                    <h1 className="text-h3 font-medium max-lg:text-callout text-white">FAQ&apos;S</h1>
                    <p className="text-sub1 max-lg:hidden text-white">Have more questions? Feel free to get in touch with us!</p>
                    <div className="w-[185px] max-lg:hidden">
                        <Link href="/get-in-touch">
                            <Button rounded variant="primary" className='!bg-gradient-to-r !from-[#EFB77C] !to-[#6E4C31]'>
                                Contact Us
                            </Button>
                        </Link>
                    </div>
                </div>
                <div className="max-w-[829px] flex-col gap-[70px] max-lg:flex">
                    <DisclosureDark notmain category={categories} />
                </div>
            </div>
        </div>
    )
}

export default FaqsStaticDark
