"use client"
import Link from 'next/link'
import { BlogPost } from '@/types/BlogPost'
import formatDate from '@/lib/formatBlogDate'
import ImageShortcut from '@/components/Ui/Image'
import { Ad } from '@/types/AdModel'
import { useEffect, useState } from 'react'
import axios from 'axios';
import { capitalizeWords } from '@/lib/capitalize'



async function fetchAllAdverts(search: string) {
    try {
        const response = await axios.get(`${process.env.API_URL}/ads?search=${search}&pageNumber=1&pageSize=10`);
        console.error(response.data.data);

        return response.data.data;
    } catch (error) {
        console.error(error);
        return null;
    }
}

function filterAdsByLocationAndLevel(ads: any[], location: string, level: number) {
    return ads.filter(ad => ad.location === location && ad.level === level);
}

const Post = ({ post }: { post: BlogPost }) => {

    const [ads, setAds] = useState([]);
    const [filteredAdvertCenter, setFilteredAdvertCenter] = useState<Ad>();
    const [filteredAdvertCenter2, setFilteredAdvertCenter2] = useState<Ad>();
    const [filteredAdvertCenter3, setFilteredAdvertCenter3] = useState<Ad>();

    useEffect(() => {
        async function fetchAds() {
            const adsData = await fetchAllAdverts('post');
            console.log(adsData)
            if (adsData) {
                setAds(adsData);
            }
        }

        fetchAds();
    }, []);

    useEffect(() => {
        if (ads.length > 0) {
            const filteredLocationCenter1 = filterAdsByLocationAndLevel(ads, 'center', 1);
            const filteredLocationCenter2 = filterAdsByLocationAndLevel(ads, 'center', 2);
            const filteredLocationCenter3 = filterAdsByLocationAndLevel(ads, 'center', 3);

            setFilteredAdvertCenter(filteredLocationCenter1[0]);
            setFilteredAdvertCenter2(filteredLocationCenter2[0]);
            setFilteredAdvertCenter3(filteredLocationCenter3[0]);
        }

    }, [ads]);

    return (
        <section className="w-full text-black">
            {/* Header */}
            <div className="mb-11">
                {filteredAdvertCenter?.url ? (
                    <Link target="_blank" href={filteredAdvertCenter.url}>
                        <ImageShortcut
                            src={filteredAdvertCenter.image}
                            alt={filteredAdvertCenter.name}
                            width={980}
                            height={120}
                            priority
                            className="mt-11 mb-11"
                        />
                    </Link>
                ) : (<span>No Adverts are available</span>)}

                <h1 className="mb-5 text-headline font-medium max-md:text-sub2">{capitalizeWords(post.title)}</h1>
                <p className="mb-4 text-[14px] font-semibold leading-[20px] text-blue">
                    <Link
                        aria-label="Author page"
                        href={`/blog/author/${post.author.slug}`}
                        className="underline-offset-2 hover:underline">
                        {post.author.name}
                    </Link>{' '}
                    • {formatDate(post.published_date)}
                </p>
                {post.image !== null ? (
                    <ImageShortcut
                        src={post.image}
                        alt="blog"
                        width={408}
                        height={755}
                        priority
                        className="mb-9 h-[408px] w-full bg-black object-contain max-md:mb-8 max-md:h-[185px]"
                    />
                ) : (
                    <div className="mb-9 h-[408px] w-full bg-blue/60 object-contain max-md:mb-8 max-md:h-[185px] animate-pulse" />
                )}

                {/* Categories */}
                <div className="flex flex-wrap items-center space-x-2">
                    {post.categories.map((category, index) => (
                        <Link
                            aria-label={category.category.name + ' page'}
                            key={index}
                            href={`/blog/category/${category.category.slug}`}
                            className="select-none rounded-full bg-gray-300 px-3 py-1 text-[14px] font-semibold leading-[20px] transition-colors duration-150 hover:bg-gray-400">
                            {category.category.name}
                        </Link>
                    ))}
                </div>
                {filteredAdvertCenter2?.url ? (
                    <Link target="_blank" href={filteredAdvertCenter2.url}>
                        <ImageShortcut
                            src={filteredAdvertCenter2.image}
                            alt={filteredAdvertCenter2.name}
                            width={980}
                            height={120}
                            priority
                            className="mt-11"
                        />
                    </Link>
                ) : (<span>No Adverts are available</span>)}

            </div>

            {/* Content */}
            <div
                dangerouslySetInnerHTML={{ __html: post.content }}
                className="prose-lg max-md:prose prose-h1:font-semibold prose-h2:font-semibold prose-h3:font-semibold prose-h4:font-semibold prose-h5:font-semibold prose-h6:font-semibold prose-a:text-blue prose-a:no-underline prose-li:list-disc"
            />
            {filteredAdvertCenter3?.url ? (
                <Link target="_blank" href={filteredAdvertCenter3.url}>
                    <ImageShortcut
                        src={filteredAdvertCenter3.image}
                        alt={filteredAdvertCenter3.name}
                        width={980}
                        height={120}
                        priority
                        className="mt-11"
                    />
                </Link>
            ) : (<span>No Adverts are available</span>)}

        </section>
    )
}

export default Post
