'use client'
import Link from 'next/link'
import ImageShortcut from '@/components/Ui/Image'
import numberWithCommas from '@/lib/formatNumber'
import { MentorshipModel } from '@/types/MentorshipModel'
import { usePathname } from 'next/navigation'

  // 👇️ if you only need to capitalize the first letter
  const capitalizeFirst = (str:string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

const Mentorship = ({ mentorship }: { mentorship: MentorshipModel }) => {
    const router = usePathname()
    return (
        <section className="w-full text-black">
            <div className="container px-10">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-2 gap-2 pb-4">
                <div> {mentorship.image !== null ? (
                    <ImageShortcut
                        src={mentorship.image}
                        alt="mentorship"
                        width={408}
                        height={755}
                        priority
                        className=" h-[350px] w-full rounded-md  object-contain p-2"
                    />
                ) : (
                    <div className=" h-[350px] w-full bg-blue/60 object-contain p-8" />
                )}</div>
                <div className="pt-1 md:pt-8 lg:pt-8 xl:pt-8 2xl:pt-8">
                    <h2 className="text-sub1 font-medium capitalize text-blue-600 md:text-green-600">1 on 1 Call – {capitalizeFirst(mentorship.name)}</h2>
                    <p className="pt-4">{mentorship.short_description}</p>
                    <h2 className="text-headline font-semibold max-md:hidden text-indigo-600 p-4"> ${numberWithCommas(mentorship.price - mentorship.tax)}</h2>
                    <a className="bg-indigo-900 text-white rounded-md mt-8 px-3 py-2 pb-4" href={'/checkout/mentorship/' + mentorship.slug + '?previous=' + router}>START NOW</a>
                </div>
            </div>
            <hr className="m-4"/>
            <div>
                <h2 className="font-bold pt-4 text-indigo-900">Additional Information:</h2>
                <p className="">{mentorship.description}</p>
                {/* <h3 className="font-bold pt-4 text-indigo-900">This program includes:</h3>
                <ul className="">
                    <li>Zoom call with {capitalizeFirst(mentorship.name)} (2 hours)</li>
                </ul>
                <p className="">Hey everyone my name is {capitalizeFirst(mentorship.name)} and I’m a full time P2P trader. If you’d like to learn how to make money from anywhere in the world consistently sign up for my 1 on 1 sessions!🙌🏾😊</p> */}
                {/* <h3 className="font-bold pt-4 text-indigo-900">Follow me on:</h3>
                <ul className="pb-4">
                    <li>Instagram ({mentorship.name})</li>
                </ul> */}
            </div>
            
            </div>


        </section>
    )
}

export default Mentorship
