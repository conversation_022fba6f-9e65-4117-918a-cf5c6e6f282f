import { NextResponse } from 'next/server'

export async function GET() {
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.WHOP_BEARER}`,
        },
    }

    const response = await (
        await fetch((process.env.WHOP_PRODUCT_API as string) + 'prod_IfeSDx5xbTC2I?expand=plans', options)
    ).json()

    return NextResponse.json(
        {
            icon: {
                src: '/memberships/card2.png',
                alt: 'Alpha Group 12 Months Plan Icon',
            },
            billed: {
                text: '12 Months',
                color: 'white',
            },
            price: {
                text: "249",
                color: 'white',
            },
            sub: {
                text: "Billed <strong>$2,999</strong> at once",
                color: 'white',
            },
            button: {
                text: "Get One Year",
                color: 'white',
            },
            plan: {
                text: '50% off',
                color: 'white',
            },
          
            // plans: response.plans[0].direct_link,
            plans: '/checkout/subscription/' + 'alpha-group-yearly' + '?previous=' + '/subscriptions'
        },
        { status: 200 }
    )
}
