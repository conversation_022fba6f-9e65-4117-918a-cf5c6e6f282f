'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { useState } from 'react'
import PopupVideo from './PopupVideo'
import { CourseModel } from '@/types/CourseModel'
import { Disclosure, Transition } from '@headlessui/react'
import { Pause, Play } from '@public/dashboard'
import { usePathname } from 'next/navigation'

interface DisclosureLessonProps {
    course: CourseModel | undefined
    isPurchasing: boolean
    handelClose?: () => void
    quizzes?: QuizModel[]
}

const DisclosureLesson = ({ course, isPurchasing, handelClose, quizzes }: DisclosureLessonProps) => {
    const currentRoute = usePathname()
    const [isOpen, setIsOpen] = useState(false)
    const openModal = () => {
        setIsOpen(true)
    }
    const closeModal = () => {
        setIsOpen(false)
    }

    const topicClassName =
        'relative flex w-full cursor-pointer select-none items-center gap-5 border-b border-gray-700 bg-white p-4 font-sans text-sub3 leading-none  transition-[max-height_opacity] duration-300'

    return (
        <>
            {course?.course_lessons
                .sort((a, b) => a.rank - b.rank)
                // .filter(lesson => lesson.course_topics.length > 0)
                .map((lesson, index) => (
                    <Disclosure key={'lesson' + lesson.name + lesson.id}>
                        {({ open }) => (
                            <div className="flex w-full flex-col transition-all duration-300">
                                <Disclosure.Button className="relative flex w-full cursor-pointer select-none items-center gap-5 border-b border-gray-700 bg-gray-300 p-4 leading-none">
                                    <span
                                        className={cn(
                                            'flex flex-col items-center justify-center rounded-full transition-[transform_background] duration-300',
                                            open ? '' : 'rotate-180'
                                        )}>
                                        <svg
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M19 15.5L12 8.5L5 15.5"
                                                stroke="#081228"
                                                strokeWidth="1.5"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </span>
                                    <div className="flex w-full items-center justify-between font-sans">
                                        <h3 className="text-left text-sub3 font-semibold capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                            {lesson.name}
                                        </h3>
                                        {
                                        lesson.course_topics.length === 1 
                                        ? (<p className="text-cap2">
                                            {lesson.course_topics.length} Topic • {lesson.quizzes.length} Quiz
                                        </p>) 
                                        :lesson.course_topics.length > 1 
                                        ? (<p className="text-cap2">
                                            {lesson.course_topics.length} Topics • {lesson.quizzes.length} Quiz
                                        </p>) 
                                        : (<p className="text-cap2">
                                            Introductory Topic • {lesson.quizzes.length} Quiz
                                        </p>)}

                                    </div>
                                </Disclosure.Button>
                                <Transition
                                    show={open}
                                    enter="transition duration-100 ease-out"
                                    enterFrom="transform scale-95 opacity-0"
                                    enterTo="transform scale-100 opacity-100"
                                    leave="transition duration-75 ease-out"
                                    leaveFrom="transform scale-100 opacity-100"
                                    leaveTo="transform scale-95 opacity-0">
                                    {lesson.video_link && !isPurchasing ? (
                                        <>
                                            <button
                                                onClick={() => openModal()}
                                                className={cn(topicClassName, 'text-blue')}>
                                                <svg
                                                    width="24"
                                                    height="24"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        fillRule="evenodd"
                                                        clipRule="evenodd"
                                                        d="M12 2.5C17.2459 2.5 21.5 6.75315 21.5 12C21.5 17.2469 17.2459 21.5 12 21.5C6.75315 21.5 2.5 17.2469 2.5 12C2.5 6.75315 6.75315 2.5 12 2.5Z"
                                                        stroke="#081228"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                    <path
                                                        fillRule="evenodd"
                                                        clipRule="evenodd"
                                                        d="M15 11.9951C15 11.184 10.8425 8.58912 10.3709 9.0557C9.8993 9.52228 9.85395 14.424 10.3709 14.9346C10.8879 15.4469 15 12.8063 15 11.9951Z"
                                                        stroke="#081228"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                </svg>
                                                <div className="flex w-full items-center justify-between font-sans">
                                                    <h3 className="text-sub3 capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                                        Introcution of lessons
                                                    </h3>
                                                    <p className="text-cap2"></p>
                                                </div>
                                            </button>
                                            <PopupVideo
                                                isOpen={isOpen}
                                                closeModal={closeModal}
                                                link={lesson.video_link}
                                            />
                                        </>
                                    ) : (
                                        ''
                                    )}
                                    {lesson.video_link && isPurchasing ? (
                                        <Link
                                            aria-label="Play"
                                            onClick={() => {
                                                handelClose ? handelClose() : ''
                                            }}
                                            href={
                                                !isPurchasing
                                                    ? '/checkout/course/' + course.slug
                                                    : '/dashboard/' +
                                                    course?.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                    '/'
                                            }
                                            className={cn(
                                                topicClassName,
                                                '/dashboard/' +
                                                    course.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') ==
                                                    currentRoute
                                                    ? 'bg-gray-400'
                                                    : ''
                                            )}>
                                            <div>
                                                {'/dashboard/' +
                                                    course.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') ==
                                                    currentRoute ? (
                                                    <Pause />
                                                ) : (
                                                    <Play />
                                                )}
                                            </div>
                                            <div className="flex w-full items-center justify-between font-sans">
                                                <h3 className="text-sub3 capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                                    Introducton of {lesson.name}
                                                </h3>
                                                <p className="text-cap2"></p>
                                            </div>
                                        </Link>
                                    ) : (
                                        ''
                                    )}
                                    {lesson.course_topics.map(topic => (
                                        <Link
                                            aria-label="Play"
                                            onClick={() => {
                                                handelClose ? handelClose() : ''
                                            }}
                                            href={
                                                !isPurchasing
                                                    ? '/checkout/course/' + course.slug
                                                    : '/dashboard/' +
                                                    course?.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                    '/' +
                                                    topic.name.toLowerCase().replace(/\s/g, '-')
                                            }
                                            key={'topics' + topic.name + topic.id}
                                            className={cn(
                                                topicClassName,
                                                '/dashboard/' +
                                                    course.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                    '/' +
                                                    topic.name.toLowerCase().replace(/\s/g, '-') ==
                                                    currentRoute
                                                    ? 'bg-gray-400'
                                                    : ''
                                            )}>
                                            <div>
                                                {'/dashboard/' +
                                                    course.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                    '/' +
                                                    topic.name.toLowerCase().replace(/\s/g, '-') ==
                                                    currentRoute ? (
                                                    <Pause />
                                                ) : (
                                                    <Play />
                                                )}
                                            </div>
                                            <div className="flex w-full items-center justify-between font-sans">
                                                <h3 className="text-sub3 capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                                    {topic.name}
                                                </h3>
                                                <p className="text-cap2"></p>
                                            </div>
                                        </Link>
                                    ))}
                                    {lesson.quizzes.map(quiz => (
                                        <Link
                                            aria-label="Play"
                                            href={
                                                !isPurchasing
                                                    ? '/checkout/course/' + course.slug
                                                    : '/dashboard/' +
                                                    course?.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                    '/quiz'
                                            }
                                            onClick={() => {
                                                handelClose ? handelClose() : ''
                                            }}
                                            key={'quiz' + quiz.question + quiz.id}
                                            className={cn(
                                                'relative flex w-full cursor-pointer select-none items-center gap-5 bg-[#FFF9EA] p-4 font-sans text-sub3 leading-none  transition-[max-height_opacity] duration-300',
                                                '/dashboard/' +
                                                    course.slug +
                                                    '/' +
                                                    lesson.name.toLowerCase().replace(/\s/g, '-') +
                                                    '/' +
                                                    'quiz' ==
                                                    currentRoute
                                                    ? 'bg-yellow/60'
                                                    : ''
                                            )}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                fill="none"
                                                viewBox="0 0 24 24">
                                                <path
                                                    fillRule="evenodd"
                                                    stroke="#081228"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M16.334 2.75H7.665c-3.021 0-4.915 2.139-4.915 5.166v8.168c0 3.027 1.885 5.166 4.915 5.166h8.668c3.031 0 4.917-2.139 4.917-5.166V7.916c0-3.027-1.886-5.166-4.916-5.166z"
                                                    clipRule="evenodd"></path>
                                                <path
                                                    stroke="#081228"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M8.44 12l2.373 2.373 4.746-4.746"></path>
                                            </svg>
                                            <div className="flex w-full items-center justify-between font-sans">
                                                <h3 className="text-sub3 capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                                    Quiz
                                                </h3>
                                                <p className="text-cap2"></p>
                                            </div>
                                        </Link>
                                    ))}
                                </Transition>
                            </div>
                        )}
                    </Disclosure>
                ))}
            {quizzes && quizzes.length > 0 && isPurchasing && (
                <div className="flex w-full flex-col transition-all duration-300">
                    <Disclosure>
                        {({ open }) => (
                            <>
                                <Disclosure.Button className="relative flex w-full cursor-pointer select-none items-center gap-5 border-b border-gray-700 bg-gray-300 p-4 leading-none">
                                    <span
                                        className={`flex flex-col items-center justify-center rounded-full transition-[transform_background] duration-300 ${open ? '' : 'rotate-180'
                                            }`}>
                                        <svg
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M19 15.5L12 8.5L5 15.5"
                                                stroke="#081228"
                                                strokeWidth="1.5"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </span>
                                    <div className="flex w-full items-center justify-between font-sans">
                                        <h3 className="text-left text-sub3 font-semibold capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                            Course Quizzes
                                        </h3>
                                        <p className="text-cap2">{quizzes.length} Quiz</p>
                                    </div>
                                </Disclosure.Button>
                                <Transition
                                    show={open}
                                    enter="transition duration-100 ease-out"
                                    enterFrom="transform scale-95 opacity-0"
                                    enterTo="transform scale-100 opacity-100"
                                    leave="transition duration-75 ease-out"
                                    leaveFrom="transform scale-100 opacity-100"
                                    leaveTo="transform scale-95 opacity-0">
                                    {quizzes.map((quiz, index) => (
                                        <Link
                                            aria-label="Play"
                                            href={`/dashboard/${course?.slug}/quiz/${quiz.id}`}
                                            onClick={() => {
                                                handelClose ? handelClose() : ''
                                            }}
                                            key={'quiz' + quiz.question + quiz.id}
                                            className={cn(
                                                'relative flex w-full cursor-pointer select-none items-center gap-5 bg-yellow-light p-4 font-sans text-sub3 leading-none  transition-[max-height_opacity] duration-300',
                                                '/dashboard/' + course?.slug + '/quiz/' + quiz.id == currentRoute
                                                    ? 'bg-yellow/60'
                                                    : ''
                                            )}>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                fill="none"
                                                viewBox="0 0 24 24">
                                                <path
                                                    fillRule="evenodd"
                                                    stroke="#081228"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M16.334 2.75H7.665c-3.021 0-4.915 2.139-4.915 5.166v8.168c0 3.027 1.885 5.166 4.915 5.166h8.668c3.031 0 4.917-2.139 4.917-5.166V7.916c0-3.027-1.886-5.166-4.916-5.166z"
                                                    clipRule="evenodd"></path>
                                                <path
                                                    stroke="#081228"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth="1.5"
                                                    d="M8.44 12l2.373 2.373 4.746-4.746"></path>
                                            </svg>
                                            <div className="flex w-full items-center justify-between font-sans">
                                                <h3 className="text-sub3 capitalize max-md:flex max-md:flex-1 max-md:text-cap1">
                                                    Quiz {index + 1}
                                                </h3>
                                                <p className="text-cap2"></p>
                                            </div>
                                        </Link>
                                    ))}
                                </Transition>
                            </>
                        )}
                    </Disclosure>
                </div>
            )}
        </>
    )
}

export default DisclosureLesson
