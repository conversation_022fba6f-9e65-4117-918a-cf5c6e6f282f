'use client'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { LuDot } from 'react-icons/lu'
import p2p from '../../../../../public/icons/p2p.svg'
import Link from 'next/link'
import { Markdown } from '@/lib/markdown'

interface Author {
    node: {
        avatar: {
            url: string
        }
        name: string
    }
}

interface Category {
    name: string
    slug: string
}

interface FeaturedImage {
    node: {
        sourceUrl: string
        altText: string
    }
}

interface Post {
    author: Author
    categories: {
        nodes: Category[]
    }
    date: string
    excerpt: string
    featuredImage: FeaturedImage
    slug: string
    title: string
}

async function GetPostsByCategory(category: string): Promise<Post[] | null> {
    try {
        const res = await fetch(process.env.NEXT_PUBLIC_BASE_URL + '/api/blog/recent', {
            next: { revalidate: 10 },
        })
        if (res.headers.get('content-type') !== 'application/json') return null
        const data = await res.json()
        console.log(data) // Log the data here
        return data.response
    } catch (error) {
        console.error(error)
        return null
    }
}

// const bitcoinNews = [
//     {
//         image: p2p,
//         tags: ['BITCOIN', 'BITCOIN', 'BITCOIN'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
//     {
//         image: p2p,
//         tags: ['BITCOIN', 'BITCOIN', 'BITCOIN'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
//     {
//         image: p2p,
//         tags: ['BITCOIN', 'BITCOIN', 'BITCOIN'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
//     {
//         image: p2p,
//         tags: ['BITCOIN', 'BITCOIN', 'BITCOIN'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
// ]

// const ethereumNews = [
//     {
//         image: p2p,
//         tags: ['ETHEREUM', 'ETHEREUM', 'ETHEREUM'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
// ]

// const tradingNews = [
//     {
//         image: p2p,
//         tags: ['TRADING', 'TRADING', 'TRADING'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
// ]

// const investingNews = [
//     {
//         image: p2p,
//         tags: ['INVESTING', 'INVESTING', 'INVESTING'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
// ]

// const industryNews = [
//     {
//         image: p2p,
//         tags: ['INDUSTRY', 'INDUSTRY', 'INDUSTRY'],
//         title: 'How to make P2P transactions safer: understand wallet signatures',
//         paragraph:
//             'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis, dictum est a, mattis tellus. Sed dignissim, metus nec metus...',
//     },
// ]

const TrendingInCrypto = () => {
    const [currentPage, setCurrentPage] = useState(1)
    const postsPerPage = 2
    const [activeCategory, setActiveCategory] = useState('Bitcoin')
    const [cryptoNews, setCryptoNews] = useState<Post[]>([])

    useEffect(() => {
        const fetchData = async () => {
            const newsData = await GetPostsByCategory('jjsjsjsj')
            if (newsData) setCryptoNews(newsData)
        }

        fetchData()
    }, [])

    const categories: any = {
        Bitcoin: cryptoNews,
        Ethereum: cryptoNews,
        Trading: cryptoNews,
        Investing: cryptoNews,
        Industry: cryptoNews,
    }

    const currentNews = categories[activeCategory]

    const indexOfLastPost = currentPage * postsPerPage
    const indexOfFirstPost = indexOfLastPost - postsPerPage
    const currentPosts = currentNews.slice(indexOfFirstPost, indexOfLastPost)

    const totalPages = Math.ceil(currentNews.length / postsPerPage)

    const paginate = (pageNumber: any) => setCurrentPage(pageNumber)

    const renderNews = (newsArray: any) => {
        return (
            <section className="mt-5 grid md:grid-cols-2 ">
                {cryptoNews.slice(0, 2).map((news, index) => (
                    <div key={index} className="grid justify-start justify-items-start p-3 align-top md:flex">
                        <Image src={news.featuredImage.node.sourceUrl}
                            alt={news.featuredImage.node.altText} width={249} height={140} className="self-start" />
                        <section className=" mt-3 md:mx-4 md:mt-0">
                            <div className="space-y-2">
                                <ol className="flex items-center gap-[8px] text-[14px] font-[600] text-[#2655FF]">
                                    {/* {news.tags.map((tag: any, idx: any) => (
                                        <React.Fragment key={idx}>
                                            <li className="underline">{tag}</li>
                                            {idx < news.tags.length - 1 && <LuDot className="h-6 w-6 text-[#2655FF]" />}
                                        </React.Fragment>
                                    ))}
                                     */}

                                    {news.categories.nodes.map((category, catIndex) => (
                                        <React.Fragment key={catIndex}>
                                            {catIndex > 0 && <LuDot className="h-6 w-6 text-[#2655FF]" />}
                                            <li className="underline"><Link aria-label="Post page" href={`/blog/category/${category.slug}`}>{category.name} </Link></li>
                                        </React.Fragment>
                                    ))}

                                </ol>



                                {/* <ol className="flex items-center gap-[8px] text-[14px] font-[600] text-[#2655FF]">
                                    {news.categories.nodes.map((category: any, catIndex: any) => (
                                        <React.Fragment key={catIndex}>
                                            <li className="underline"><Link aria-label="Post page" href={`/blog/category/${news.slug}`}>{category.name} </Link></li>
                                            {catIndex < news.tags.length - 1 && <LuDot className="h-6 w-6 text-[#2655FF]" />}
                                        </React.Fragment>
                                    ))}
                                </ol> */}

                                <h2 className="text-[18px] font-[600] md:text-[24px]">{news.title}</h2>

                                {/* <p className="text-[12px] font-[400] text-[#97989F] md:text-[14px]">{news.paragraph}</p> */}
                                {/* <p
                                    className="text-[12px] font-[400] text-[#97989F] md:text-[14px]"
                                    dangerouslySetInnerHTML={{ __html: news.excerpt }}
                                /> */}
                                <div className="text-[12px] font-[400] text-[#97989F] md:text-[14px]">
                                    <Markdown>
                                        {news.excerpt
                                            ?.replace(/(<([^>]+)>)/gi, '')
                                            .replace(/&hellip;/g, '...')
                                            .replace(/[\[\]']+/g, '')
                                            .replace(/&nbsp;/g, ' ')
                                            .replace(/&#8217;/g, "'")
                                            .replace(/&#8220;/g, '"')
                                            .replace(/&#8221;/g, '"')
                                            .replace(/&#8211;/g, '-')
                                            .replace(/&#038;/g, '&')
                                            .replace(/&#8230;/g, '...')
                                            .replace(/&#8216;/g, "'")
                                            .replace(/&#8218;/g, "'")
                                            .slice(0, 230) + ''}
                                    </Markdown>
                                </div>

                                <div className="flex items-center gap-2 text-center">
                                    <Image
                                        src={news.featuredImage.node.sourceUrl}
                                        alt="author"
                                        width={36.88}
                                        height={3.07}
                                        className="rounded-full"
                                    />
                                    <h2 className="text-[12px] text-[#CA9B21]">{news.author.node.name}</h2>
                                    <h3 className="text-[12px] font-[500] text-[#97989F]">
                                        {new Date(news.date).toLocaleDateString()}
                                    </h3>
                                    {/* <h2 className="text-[12px] text-[#2655FF]">{news.author.node.name}</h2> */}
                                    {/* <h3 className="text-[12px] font-[500] text-[#97989F]">{new Date(news.date).toLocaleDateString()}</h3> */}

                                </div>
                            </div>
                        </section>
                    </div>
                ))}
            </section>
        )
    }

    return (
        <div className="mx-auto my-5 max-w-[400px] overflow-hidden sm:max-w-[700px] md:my-[60px] md:max-w-[1300px]">
            <h2 className="text-[18px] font-[600] md:mb-5 md:text-[36px]">Trending in Crypto</h2>
            <ul className="mt-4 flex h-8 gap-4 border-b text-[14px] font-[600] md:mt-10 md:text-[20px]">
                {Object.keys(categories).map(category => (
                    <li
                        key={category}
                        className={`cursor-pointer ${activeCategory === category ? 'border-b-2 border-[#2655FF] font-bold text-[#2655FF]' : ''
                            }`}
                        onClick={() => {
                            setActiveCategory(category)
                            setCurrentPage(1) // Reset to the first page when category changes
                        }}>
                        {category}
                    </li>
                ))}
            </ul>
            {renderNews(currentPosts)}
            <div className="mt-4 flex ">
                {Array.from({ length: totalPages }, (_, index) => (
                    <button
                        key={index + 1}
                        onClick={() => paginate(index + 1)}
                        className={`mx-1 px-4 py-2 text-[14px] hover:rounded-full hover:bg-[#2655FF1A] hover:text-[#2655FF] md:text-[18px] ${currentPage === index + 1 ? 'rounded-full bg-[#2655FF1A] text-[#2655FF]' : ''
                            }`}>
                        {index + 1}
                    </button>
                ))}
            </div>
        </div>
    )
}

export default TrendingInCrypto
