'use client'
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

interface Partner {
  name: string;
  logo: string;
}

const PageGenerator: React.FC = () => {
  const router = useRouter();
  const [pageType, setPageType] = useState<string>('');
  const [pageData, setPageData] = useState<{ [key: string]: any }>({});
  const [checklistItems, setChecklistItems] = useState<string[]>([]);
  const [newChecklistItem, setNewChecklistItem] = useState<string>('');
  const [partners, setPartners] = useState<Partner[]>([]);
  const [newPartner, setNewPartner] = useState<Partner>({ name: '', logo: '' });
  const [error, setError] = useState<string | null>(null);
  const [courseOffers, setCourseOffers] = useState<Array<{ title: string; description: string; icon: string }>>([])
  const [newCourseOffer, setNewCourseOffer] = useState<{ title: string; description: string; icon: string }>({ title: '', description: '', icon: '' })
  const [whyChooseUs, setWhyChooseUs] = useState<Array<{ title: string; description: string; icon: string }>>([])
  const [newWhyChooseUs, setNewWhyChooseUs] = useState<{ title: string; description: string; icon: string }>({ title: '', description: '', icon: '' })
  const [headerItems, setHeaderItems] = useState<Array<{ title: string }>>([])
  const [newHeaderItem, setNewHeaderItem] = useState<string>('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!pageType) {
      setError("Please select a page type");
      return;
    }

    let formattedPageData = { 
      ...pageData,
      headerItems: headerItems,
      courseOffers: courseOffers,
      whyChooseUs: whyChooseUs,
    };

    if (pageType === 'academy') {
      formattedPageData = {
        ...formattedPageData,
        // stats: [
        //   { value: pageData.stat1Value, label: pageData.stat1Label },
        //   { value: pageData.stat2Value, label: pageData.stat2Label },
        // ],
      };
    }

    try {
      const response = await fetch('/api/generate-page', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pageType, pageData: formattedPageData }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      router.push(`/generated/${data.slug}`);
    } catch (err) {
      console.error("Error generating page:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setPageData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddChecklistItem = () => {
    if (newChecklistItem.trim()) {
      setChecklistItems(prev => [...prev, newChecklistItem.trim()]);
      setNewChecklistItem('');
    }
  };

  const handleRemoveChecklistItem = (index: number) => {
    setChecklistItems(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddPartner = () => {
    if (newPartner.name.trim() && newPartner.logo.trim()) {
      setPartners(prev => [...prev, newPartner]);
      setNewPartner({ name: '', logo: '' });
    }
  };

  const handleRemovePartner = (index: number) => {
    setPartners(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddCourseOffer = () => {
    if (newCourseOffer.title && newCourseOffer.description && newCourseOffer.icon) {
      setCourseOffers(prev => [...prev, newCourseOffer]);
      setNewCourseOffer({ title: '', description: '', icon: '' });
    }
  };

  const handleRemoveCourseOffer = (index: number) => {
    setCourseOffers(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddWhyChooseUs = () => {
    if (newWhyChooseUs.title && newWhyChooseUs.description && newWhyChooseUs.icon) {
      setWhyChooseUs(prev => [...prev, newWhyChooseUs]);
      setNewWhyChooseUs({ title: '', description: '', icon: '' });
    }
  };

  const handleRemoveWhyChooseUs = (index: number) => {
    setWhyChooseUs(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddHeaderItem = () => {
    if (newHeaderItem) {
      setHeaderItems(prev => [...prev, { title: newHeaderItem }]);
      setNewHeaderItem('');
    }
  };

  const handleRemoveHeaderItem = (index: number) => {
    setHeaderItems(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && <div className="text-red-500">{error}</div>}
      <div>
        <label htmlFor="pageType" className="block text-sm font-medium text-gray-700">Page Type</label>
        <select id="pageType" value={pageType} onChange={(e) => setPageType(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
          <option value="">Select Page Type</option>
          <option value="event">Event Page</option>
          <option value="academy">Academy Page</option>
        </select>
      </div>
      {pageType === 'event' && (
        <>
          <input type="text" name="title" placeholder="Event Title" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="subtitle" placeholder="Event Subtitle" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <textarea name="mission" placeholder="Event Mission" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="enrollLink" placeholder="Enroll Link" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="videoUrl" placeholder="Video URL" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="videoThumbnail" placeholder="Video Thumbnail URL" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          
          {/* Lead Dev Information */}
          <input type="text" name="leadDevName" placeholder="Lead Dev Name" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="leadDevTitle" placeholder="Lead Dev Title" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="leadDevCompany" placeholder="Lead Dev Company" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="leadDevImage" placeholder="Lead Dev Image URL" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          
          {/* Stats */}
          <div className="space-y-2">
            <input type="text" name="stat1Value" placeholder="Stat 1 Value" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
            <input type="text" name="stat1Label" placeholder="Stat 1 Label" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          </div>
          <div className="space-y-2">
            <input type="text" name="stat2Value" placeholder="Stat 2 Value" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
            <input type="text" name="stat2Label" placeholder="Stat 2 Label" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          </div>

          {/* Partners */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Partners</label>
            {partners.map((partner, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span>{partner.name}</span>
                <button type="button" onClick={() => handleRemovePartner(index)} className="text-red-500">Remove</button>
              </div>
            ))}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newPartner.name}
                onChange={(e) => setNewPartner(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Partner Name"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <input
                type="text"
                value={newPartner.logo}
                onChange={(e) => setNewPartner(prev => ({ ...prev, logo: e.target.value }))}
                placeholder="Partner Logo URL"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button type="button" onClick={handleAddPartner} className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Add
              </button>
            </div>
          </div>

          {/* Ready to Start Section */}
          <input type="text" name="readyToStartTitle" placeholder="Ready to Start Title" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <textarea name="readyToStartDescription" placeholder="Ready to Start Description" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          
          {/* Checklist Items */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Ready to Start Checklist</label>
            {checklistItems.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span>{item}</span>
                <button type="button" onClick={() => handleRemoveChecklistItem(index)} className="text-red-500">Remove</button>
              </div>
            ))}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newChecklistItem}
                onChange={(e) => setNewChecklistItem(e.target.value)}
                placeholder="New checklist item"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button type="button" onClick={handleAddChecklistItem} className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Add
              </button>
            </div>
          </div>

          <input type="text" name="networkDescription" placeholder="Network Description" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <textarea name="collaborationDescription" placeholder="Collaboration Description" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </>
      )}
      {pageType === 'academy' && (
        <>
          <input type="text" name="title" placeholder="Academy Title" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="subtitle" placeholder="Academy Subtitle" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          
          {/* Header Items */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Header Items</label>
            {headerItems.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span>{item.title}</span>
                <button type="button" onClick={() => handleRemoveHeaderItem(index)} className="text-red-500">Remove</button>
              </div>
            ))}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newHeaderItem}
                onChange={(e) => setNewHeaderItem(e.target.value)}
                placeholder="New header item"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button type="button" onClick={handleAddHeaderItem} className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Add
              </button>
            </div>
          </div>

          {/* Course Offers */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Course Offers</label>
            {courseOffers.map((offer, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span>{offer.title}</span>
                <button type="button" onClick={() => handleRemoveCourseOffer(index)} className="text-red-500">Remove</button>
              </div>
            ))}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newCourseOffer.title}
                onChange={(e) => setNewCourseOffer(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Course Title"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <input
                type="text"
                value={newCourseOffer.description}
                onChange={(e) => setNewCourseOffer(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Course Description"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <input
                type="text"
                value={newCourseOffer.icon}
                onChange={(e) => setNewCourseOffer(prev => ({ ...prev, icon: e.target.value }))}
                placeholder="Course Icon URL"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button type="button" onClick={handleAddCourseOffer} className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Add
              </button>
            </div>
          </div>

          {/* Why Choose Us */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Why Choose Us</label>
            {whyChooseUs.map((reason, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span>{reason.title}</span>
                <button type="button" onClick={() => handleRemoveWhyChooseUs(index)} className="text-red-500">Remove</button>
              </div>
            ))}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newWhyChooseUs.title}
                onChange={(e) => setNewWhyChooseUs(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Reason Title"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <input
                type="text"
                value={newWhyChooseUs.description}
                onChange={(e) => setNewWhyChooseUs(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Reason Description"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <input
                type="text"
                value={newWhyChooseUs.icon}
                onChange={(e) => setNewWhyChooseUs(prev => ({ ...prev, icon: e.target.value }))}
                placeholder="Reason Icon URL"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button type="button" onClick={handleAddWhyChooseUs} className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Add
              </button>
            </div>
          </div>

          <input type="text" name="videoThumbnail" placeholder="Video Thumbnail URL" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
          <input type="text" name="discordChatImage" placeholder="Discord Chat Image URL" onChange={handleInputChange} className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </>
      )}
      <button type="submit" className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Generate Page
      </button>
    </form>
  );
};

export default PageGenerator;
