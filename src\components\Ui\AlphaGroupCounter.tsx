import AlphaCounter from './AlphaCounter'
import { Profile2, Rating, RatingMobile, TrustPilot, TrustPilotMobile, Video2, Heart2 } from '@public/home'

export const AlphaGroupCounter = () => {
    return (
        <div className="flex grid-cols-2 flex-wrap py-6 gap-9 max-md:grid max-md:gap-y-[1.125rem]">
            <AlphaCounter icon={<Profile2 />} number={30000} unit={'Members'} className="max-md:w-full" />
            <AlphaCounter
                icon={<Video2 />}
                number={10000}
                unit={'Hours Of Content'}
                className="max-md:w-full max-md:border-none text-[#EFB77C]"
            />
            <AlphaCounter icon={<Heart2 />} number={20} unit={'Partners'} className="max-md:w-full text-[#EFB77C]" />
            <div className="flex gap-[0.875rem] max-md:gap-2 text-[#EFB77C]">
                <div className="flex flex-col items-center max-md:items-start">
                    <span className="text-cap1 max-md:text-cap4 text-[#EFB77C]">Rated</span>
                    <p className="text-b1 font-semibold max-md:text-sub1 text-[#EFB77C]">4.5</p>
                    <span className="text-cap1 max-md:text-cap4 text-[#EFB77C]">Out of 5</span>
                </div>
                <div className="hidden !text-[#EFB77C] flex-col justify-end gap-1 py-[2px] max-md:flex">
                    <TrustPilotMobile />
                    <RatingMobile />
                </div>
                <div className="flex !text-[#EFB77C] flex-col justify-end gap-3 py-[2px] max-md:hidden">
                    <TrustPilot />
                    <Rating />
                </div>
            </div>
        </div>
    )
}
