import ImageShortcut from '@/components/Ui/Image'
import HeroImage from '@public/newsletter/hero.png'
import Subscribe from './Subscribe'
import { Country } from '@/types/CountryModel'

const Hero = ({ countries }: { countries: Country[] }) => {
    return (
        <section className="bg-[#060E1E] pb-[5.625rem] pt-16 text-white max-md:pb-16 ">
            <div className="container mx-auto flex items-center justify-between max-md:flex-col-reverse">
                <div className="space-y-16 max-md:space-y-11 max-md:pt-4">
                    {/* Title */}
                    <div className="space-y-[10px] max-md:space-y-2">
                        <h1 className="text-h3 font-medium text-yellow max-md:text-center max-md:text-headline">
                            Weekly Digest
                        </h1>
                        <p className="max-w-[460px] text-sub3 max-md:text-center max-md:text-cap2">
                            Sign up to our newsletter to receive weekly crypto news, events, community updates & MORE.
                        </p>
                    </div>
                    {/* Form */}
                    <Subscribe countries={countries} />
                </div>

                <ImageShortcut src={HeroImage} className="h-auto max-md:w-[261px]" alt="Hero image" />
            </div>
        </section>
    )
}

export default Hero
