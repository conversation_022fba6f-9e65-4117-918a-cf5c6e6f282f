import {
    binance,
    bybit,
    coinbase,
    crypto,
    noones,
    huobi,
    kucoin,
    luno,
    okx,
    paxful,
    valr,
    coinw
} from '@public/resources/wallets'
import { Remitano } from '@public/home/<USER>'
import Skins from '@/components/Pages/Resources/Skins'

export const metadata = {
    title: 'Exchanges',
    description:
        'Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
    keywords: ['Exchanges', 'Crypto University', 'Cryptocurrency', 'Bitcoin', 'Ethereum', 'Trading', 'Investing'],
}

const ExchangesPage = () => {
    const data = [
        {
            category: 'Futures',
            items: [
                {
                    name: 'Binance',
                    img: binance,
                    link: 'https://www.binance.com/en/futures/ref?code=greybtc',
                },
                {
                    name: 'CoinW',
                    img: coinw,
                    link: 'https://www.coinw.com/front/affiliate?agentKey=CryptoUniversity&lang=en_US',
                },
                {
                    name: 'Bybit',
                    img: bybit,
                    link: 'https://www.bybit.com/en/sign-up?affiliate_id=97446&group_id=0&group_type=1',
                },
                {
                    name: 'Crypto.com',
                    img: crypto,
                    link: 'http://platinum.crypto.com/r/hardcorecrypto?utm_campaign=Crypto.com',
                },
                {
                    name: 'Huobi',
                    img: huobi,
                    link: 'https://www.huobi.io/en-us/topic/invited/?invite_code=hpm35',
                },
                {
                    name: 'Kucoin',
                    img: kucoin,
                    link: 'https://www.kucoin.com/ucenter/signup?rcode=2N2Qece&utm_source=kucoin',
                },
                {
                    name: 'OKX',
                    img: okx,
                    link: 'https://www.okx.com/join/2028981',
                },
            ],
        },
        {
            category: 'Spot',
            items: [
                {
                    name: 'CoinBase',
                    img: coinbase,
                    link: 'https://www.coinbase.com/join/580beab974462c332874e6f0',
                },
                {
                    name: 'Luno',
                    img: luno,
                    link: 'https://www.luno.com/invite/3Z4ZU',
                },
                {
                    name: 'Valr',
                    img: valr,
                    link: 'https://www.valr.com/invite/VAURZWMT',
                },
            ],
        },
        {
            category: 'P2P',
            items: [
                {
                    name: 'Paxful',
                    img: paxful,
                    link: 'https://paxful.com/?r=ZxQbQW1qYkW',
                },
                {
                    name: 'Remitano',
                    img: Remitano,
                    link: 'https://remitano.com/btc/za',
                },
                {
                    name: 'Noones',
                    img: noones,
                    link: 'https://noones.com/?r=ninja265',
                },
            ],
        },
    ]

    return (
        <section className="w-full">
            <Skins data={data} />
        </section>
    )
}

export default ExchangesPage
