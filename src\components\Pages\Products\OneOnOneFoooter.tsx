import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import yellowbutton from '../../../../public/icons/yellowbutton.svg'
import './OneOnOneFooter.css'

const OneOnOneFoooter = () => {
  return (
      <div className="bg p-[32px] md:p-0">
        <section className='mx-auto max-w-[400px] md:max-w-[700px] md:pt-20 lg:max-w-[1400px]'>
            <div className="flex ">
              <div className="w-[215px] md:w-[750px] md:p-20 ">
                  <h3 className="text-[18px] font-[600] text-[#00FF9E] md:text-[36px]">Exclusive</h3>
                  <h2 className="text-[28px] font-[600] text-white md:text-[40px]">
                      One-On-One Mentorship with <PERSON>
                  </h2>
                  <h3 className="w-[166px] text-[11.62px] font-[600] text-[#E9D41F] md:hidden">
                      Founder & CEO of Crypto University
                  </h3>
                  <Link aria-label="View products" target='_blank' href={'https://noteforms.com/forms/mentorship-request-d8avmm?notionforms=1'} className="my-3">
                      <Image
                          src={yellowbutton}
                          height={60}
                          width={342}
                          alt="crown"
                          className="w-full md:max-w-full h-[60px] md:h-[68px] my-5" // Add mx-auto class here
                      />
                  </Link>
              </div>
              <div className="hidden p-20 pl-[220px] md:block">
                  <h3 className=" hidden h-[40px] w-[87px] items-center justify-center rounded-full bg-[#FCC229] text-center text-[22px] md:block">
                      with
                  </h3>
                  <h2 className="hidden text-[40px] font-[600] text-white md:block">Grey Jabesi</h2>
                  <h3 className="hidden text-[18px] font-[600] text-[#E9D41F] md:block">
                      Founder & CEO of Crypto University
                  </h3>
              </div>
          </div>
        </section>
          
      </div>
  )
}

export default OneOnOneFoooter
