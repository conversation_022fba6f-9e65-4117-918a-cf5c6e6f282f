'use client'
import Link from 'next/link'
import useAuth from '@/hooks/useAuth'
import Label from '@/components/Ui/Label'
import { Fragment, useState } from 'react'
import { Eye, Lock } from '@public/global'
import Button from '@/components/Ui/Button'
import { Close } from '@public/home'
import ButtonMobile from '../Ui/ButtonMobile'
import { Dialog, Transition } from '@/lib/headlessui'
import ButtonSpinner from '@/components/Ui/buttonSpinner'

interface TextBoxProps {
    label: string
    email?: boolean
    icon: boolean
    password?: boolean
    showPassword?: boolean
    setShowPassword?: any
    placeholder: string
    formData: any
    setFormData: any
}
type Props = {
    isOpen: boolean
    setIsOpen: any
    handleOpen: any
}

const TextBox = ({
    label,
    email,
    icon,
    password,
    showPassword,
    setShowPassword,
    placeholder,
    formData,
    setFormData,
}: TextBoxProps) => {
    return (
        <div className="flex w-full flex-col gap-3">
            <Label required uppercase={false}>
                {label}
            </Label>

            <div className="relative flex select-none items-center">
                <div className="absolute left-4">{email ? <Eye /> : password ? <Lock /> : icon && <Lock />}</div>
                <input
                    type={email ? 'email' : password ? (showPassword ? 'text' : 'password') : 'text'}
                    name={email ? 'email' : password ? 'password' : 'input'}
                    value={email ? formData.email : password ? formData.password : ''}
                    onChange={e =>
                        email
                            ? setFormData({ ...formData, email: e.target.value })
                            : password
                            ? setFormData({ ...formData, password: e.target.value })
                            : ''
                    }
                    className="max-md:wfull w-full rounded-full border border-gray-700 px-4 py-[17.5px] pl-12 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue max-md:py-3"
                    placeholder={placeholder}
                    required
                />
                {password && (
                    <div onClick={() => setShowPassword(!showPassword)} className="absolute right-4 cursor-pointer">
                        <Eye />
                    </div>
                )}
            </div>
        </div>
    )
}

export default function MyDialog({ isOpen, handleOpen, setIsOpen }: Props) {
    const { login, error, reset, isLoading } = useAuth()
    const [showPassowrd, setShowPassowrd] = useState(false)
    const [rememberMe, setRememberMe] = useState(false)
    const [formData, setFormData] = useState({
        email: '',
        password: '',
    })

    // get event with typescript in the handleChange function
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setRememberMe(!rememberMe)
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        await login(formData.email, formData.password, "")
    }
    const fun = () => {
        reset()
        setFormData({ email: '', password: '' })
        setIsOpen(false)
    }

    return (
        <>
            <Button
                className="max-md:hidden"
                nav
                variant="default"
                onClick={() => {
                    setIsOpen(true)
                    handleOpen()
                }}
                rounded>
                Login
            </Button>
            <ButtonMobile
                onClick={() => {
                    setIsOpen(true)
                    handleOpen()
                }}>
                Login
            </ButtonMobile>

            <Transition show={isOpen || false} as={Fragment}>
                <Dialog as="div" onClose={() => fun()} className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                    <div className="fixed inset-0 flex items-center justify-center p-4 max-md:block max-md:p-0">
                        <Transition.Child
                            as={Fragment}
                            enter="transition duration-100 ease-out"
                            enterFrom="transform scale-95 opacity-0"
                            enterTo="transform scale-100 opacity-100"
                            leave="transition duration-75 ease-out"
                            leaveFrom="transform scale-100 opacity-100"
                            leaveTo="transform scale-95 opacity-0">
                            <Dialog.Panel className="flex items-center justify-center max-md:block max-md:h-full">
                                <div className="relative flex flex-col gap-4 rounded-[1.5rem] bg-white p-10 pt-16 font-sans max-md:h-full max-md:justify-center max-md:rounded-none max-md:px-8 max-md:py-12">
                                    <button
                                        type="button"
                                        onClick={() => fun()}
                                        className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                        <Close />
                                    </button>

                                    <Dialog.Title className="w-full pb-4 text-center font-manrope text-b3 font-semibold  max-md:text-sub1">
                                        Welcome back, Dear trader!
                                    </Dialog.Title>

                                    <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                                        <div className="flex flex-col gap-4">
                                            <TextBox
                                                label="Email"
                                                icon
                                                email
                                                formData={formData}
                                                setFormData={setFormData}
                                                placeholder="<EMAIL>"
                                            />
                                            <div className="flex flex-col gap-2">
                                                <TextBox
                                                    label="Password"
                                                    showPassword={showPassowrd}
                                                    setShowPassword={setShowPassowrd}
                                                    icon
                                                    password
                                                    formData={formData}
                                                    setFormData={setFormData}
                                                    placeholder="enter your password"
                                                />
                                                {error && (
                                                    <p className="rounded-[8px] bg-red/20 p-3 font-sans text-cap1 text-red">
                                                        {error}
                                                    </p>
                                                )}
                                            </div>
                                            <div className="flex justify-between">
                                                <div className="flex items-center gap-[0.375rem]">
                                                    <input
                                                        className="h-5 w-5 border-gray-700 outline-none"
                                                        onChange={handleChange}
                                                        type="checkbox"
                                                        name="rememberme"
                                                        id="rememberme"
                                                    />
                                                    <label
                                                        htmlFor="rememberme"
                                                        className="select-none text-cap1 leading-none text-gray-900">
                                                        Keep me signed in
                                                    </label>
                                                </div>
                                                <Link
                                                    aria-label="forgot password"
                                                    href="/forgot-password"
                                                    onClick={() => fun()}
                                                    className="px-2 py-[1px] text-cap1 font-medium text-blue">
                                                    Forgot password?
                                                </Link>
                                            </div>
                                        </div>

                                        <div className="flex flex-col gap-2">
                                            <Button variant="primary" type="submit" rounded disabled={isLoading}>
                                                {isLoading && <ButtonSpinner />}Sign in
                                            </Button>
                                            <p className="py-[17.5px] text-center text-sub3 font-semibold max-md:py-3">
                                                Don&apos;t have an account?{' '}
                                                <Link aria-label='Register' className="text-blue" href="/register" onClick={() => fun()}>
                                                    Sign up now
                                                </Link>
                                            </p>
                                        </div>
                                    </form>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
        </>
    )
}
