import Image from 'next/image'
import { LuDot } from 'react-icons/lu'
import Link from 'next/link'
import { Markdown } from '@/lib/markdown'
import { Blogs } from '@/types/BlogModel'

const CryptoNewsView = ({ blogs }: { blogs: Blogs }) => {
   
    return (
        <div className="mx-auto max-w-[400px] overflow-hidden sm:max-w-[700px] md:max-w-[1400px]">
            <section className="flex items-center justify-between md:my-7">
                <h2 className="hidden text-[36px] font-[600] md:block">Crypto News & Blogs</h2>
            </section>
            <div className="relative">
                <h2 className="absolute top-[10px] p-3 text-[18px] font-[600] text-white md:hidden">
                    Crypto News & Blogs
                </h2>
            </div>

            <section className="grid justify-center gap-5 md:flex">
                {blogs.slice(0, 2).map((news, index) => (
                    <div key={index} className="rounded-[12px] border">
                        <div className="aspect-w-16 aspect-h-9"> {/* Set a fixed aspect ratio */}
                            <Image
                                src={news.image}
                                alt={news.title}
                                width={375}
                                height={230}
                                className="w-full h-full object-cover rounded-[0px]" /* Use object-cover */
                            />
                        </div>
                        <section className="p-5">
                            <div className="space-y-2">
                                <ol className="flex items-center gap-[8px] text-[14px] font-[600] text-[#2655FF]">
                                    {news.categories.map((category, catIndex) => (
                                        <div key={catIndex} className="flex items-center">
                                            {catIndex > 0 && <LuDot className="h-6 w-6 text-[#2655FF]" />}
                                            <li className="underline">
                                                <Link aria-label="Post page" href={`/blog/category/${category.category.slug}`}>
                                                    {category.category.name}
                                                </Link>
                                            </li>
                                        </div>
                                    ))}
                                </ol>
                                <Link aria-label="Post page" href={`/blog/${news.slug}`}>
                                    <h2 className="text-[18px] font-[600] md:text-[24px]">{news.title}</h2>
                                </Link>

                                <div className="text-[12px] font-[400] text-[#97989F] md:text-[14px]">
                                    <Markdown>
                                        {news.excerpt
                                            ?.replace(/(<([^>]+)>)/gi, '')
                                            .replace(/&hellip;/g, '...')
                                            .replace(/[\[\]']+/g, '')
                                            .replace(/&nbsp;/g, ' ')
                                            .replace(/&#8217;/g, "'")
                                            .replace(/&#8220;/g, '"')
                                            .replace(/&#8221;/g, '"')
                                            .replace(/&#8211;/g, '-')
                                            .replace(/&#038;/g, '&')
                                            .replace(/&#8230;/g, '...')
                                            .replace(/&#8216;/g, "'")
                                            .replace(/&#8218;/g, "'")
                                            .slice(0, 230) + ''}
                                    </Markdown>
                                </div>
                                <div className="flex items-center gap-2 text-center">
                                    <div className="h-9 w-9 overflow-hidden rounded-full">
                                        <Image
                                            src={news.image}
                                            alt="author"
                                            width={36.88}
                                            height={36.88}
                                            className="h-9 w-9 object-cover"
                                        />
                                    </div>
                                    <h2 className="text-[12px] text-[#CA9B21]">{news.author.name}</h2>
                                    <h3 className="text-[12px] font-[500] text-[#97989F]">
                                        {new Date(news.published_date).toLocaleDateString()}
                                    </h3>
                                </div>
                            </div>
                        </section>
                    </div>
                ))}
            </section>
        </div>
    )
}

export default CryptoNewsView