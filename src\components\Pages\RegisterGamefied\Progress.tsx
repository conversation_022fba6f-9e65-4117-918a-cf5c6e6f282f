import { cn } from "@/lib/cn";

type ProgressProps = {
  currentStep: number;
  steps: number;
};
const ProgressBar = ({ currentStep, steps }: ProgressProps) => {
  const interval = 100 / steps;
  const progress = (currentStep * interval).toFixed(2);

  const names = [
    "Your username",
    "Your full name",
    "Email",
    "Country",
    "Phone Number",
    "Password",
  ];

  const Names = names?.map((name, index) => {
    return (
      <h1
        key={index}
        className={cn(
          "text-xs text-center absolute transition-colors duration-150 w-fit overflow-visible whitespace-nowrap -translate-x-[48%]",
          index === currentStep ? "text-black" : "text-gray-600"
        )}
        style={{
          left: ` calc( ${index * interval + "%"} )`,
          whiteSpace: "nowrap",
        }}
      >
        {name}
      </h1>
    );
  });

  return (
    <div className="flex flex-col w-full gap-3 text-cap1 font-medium font-manrope max-w-2xl">
      <div className="w-full bg-transparent h-fit overflow-visible  relative">
        <div className="bg-[#B4B4B4] h-[1px]  z-50 w-full flex flex-row items-center">
          <div
            className="bg-blue h-0.5 transition-all duration-150 rounded-full"
            style={{ width: progress + "%" }}
          ></div>
          <div className="w-3 h-3 bg-blue rounded-full text-red  -ml-2"></div>
        </div>
      </div>
      <div className="relative">{Names}</div>
    </div>
  );
};

export const ProgressBarMob = ({ currentStep, steps }: ProgressProps) => {
  const interval = 100 / steps;
  const progress = (currentStep * interval).toFixed(2);

  const names = [
    "Your username",
    "Your full name",
    "Email",
    "Country",
    "Phone Number",
    "Password",
  ];

  return (
    <div className="flex flex-col w-full gap-3 text-cap1 font-medium font-manrope max-w-2xl">
      <div className="w-full bg-transparent h-fit overflow-visible  relative">
        <div className="bg-[#B4B4B4] h-[1px]  z-50 w-full flex flex-row items-center">
          <div
            className="bg-blue h-0.5 transition-all duration-150 rounded-full"
            style={{ width: progress + "%" }}
          />
          <div className="w-3 h-3 bg-blue rounded-full text-red -ml-2" />
        </div>
      </div>
      <div className="relative">
        <h1
          className={cn(
            "text-xs text-center absolute transition-colors duration-150 w-fit overflow-visible whitespace-nowrap -translate-x-[48%]",
            "text-black"
          )}
          style={{
            left: ` calc( ${currentStep * interval + "%"} )`,
            whiteSpace: "nowrap",
          }}
        >
          {names[currentStep]}
        </h1>
      </div>
    </div>
  );
};
export default ProgressBar;
