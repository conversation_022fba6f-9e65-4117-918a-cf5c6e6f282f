'use client'
import Link from 'next/link'
import { Close } from '@public/home'
import But<PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { Dialog, Transition } from '@/lib/headlessui'
import { Fragment, useEffect, useState } from 'react'
import successPayment from '@public/checkout/successPayment.png'

type Props = {
    success: boolean | undefined
    type?: string | undefined
}

const DiscordSuccessModel = ({ success, type }: Props) => {
    const [isOpen, setIsOpen] = useState(false)

    useEffect(() => {
        const modal = () => {
            if (success) setIsOpen(true)
        }
        modal()
    }, [success])

    return (
        <Transition show={isOpen || false} as={Fragment}>
            <Dialog
                as="div"
                onClose={() => setIsOpen(false)}
                className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                <div className="fixed inset-0 flex items-center justify-center p-4 max-md:block max-md:p-0">
                    <Transition.Child
                        as={Fragment}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0">
                        <Dialog.Panel className="flex h-full items-center justify-center max-md:block">
                            <div className="relative flex flex-col gap-9 rounded-[1.5rem] bg-white p-[52px] pt-[3.25rem] font-sans max-md:h-full max-md:justify-center max-md:gap-0 max-md:rounded-none max-md:px-8 max-md:py-12">
                                <button
                                    type="button"
                                    onClick={() => setIsOpen(false)}
                                    className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                    <Close />
                                </button>

                                <ImageShortcut
                                    className="maxmd:h-[188px] self-center max-md:w-[241]"
                                    width={226}
                                    height={171}
                                    src={successPayment}
                                    alt="purchase completed successfully"
                                />
                                <div className="flex max-w-[347px] flex-col items-center gap-4 self-center max-md:mt-6 max-md:max-w-full max-md:gap-1">
                                    <Dialog.Title className="text-center font-manrope text-headline font-medium max-md:text-b3">
                                        Thank you for order
                                    </Dialog.Title>
                                    <Dialog.Description className="text-center text-sub2 text-gray-900 max-md:max-w-[334px] max-md:text-black">
                                        {type === 'course' ? (
                                            <>
                                                You can access your course in{' '}
                                                <b className="font-semibold">My Courses</b> from your account.
                                            </>
                                        ) : type === 'mentorship' ? (
                                            <>
                                                You can check your mentorships in{' '}
                                                <b className="font-semibold">Mentorships</b> from your account.
                                            </>
                                        ) : type === 'indicator' ? (
                                            <>
                                                An invoice has been sent to your email and someone will contact you soon
                                                for more details.
                                            </>
                                        ) : type === 'bootcamp' ? (
                                            <>
                                                The bootcamp link and details will be sent to your email.
                                            </>
                                        ) : (
                                            <>
                                                You can access your course in{' '}
                                                <b className="font-semibold">My Courses</b> from your account.
                                            </>
                                        )}
                                    </Dialog.Description>
                                </div>
                                <div className="flex w-[373px] flex-col gap-5 max-md:mt-14 max-md:w-full">
                                    <Link aria-label="My Courses" href="/courses" className="w-full rounded-full">
                                        <Button variant="primary" rounded>
                                            Discover More Courses
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    )
}

export default DiscordSuccessModel
