"use client"
import ImageShortcut from '@/components/Ui/Image'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import axios from 'axios';
import { Ad } from '@/types/AdModel'

async function fetchAllAdverts(search: string) {
    try {
        const response = await axios.get(`${process.env.API_URL}/ads?search=${search}&pageNumber=1&pageSize=10`);
        console.error(response.data.data);

        return response.data.data;
    } catch (error) {
        console.error(error);
        return null;
    }
}

function filterAdsByLocationAndLevel(ads: any[], location: string, level: number) {
    return ads.filter(ad => ad.location === location && ad.level === level);
}

const Banner = () => {

    const [ads, setAds] = useState([]);
    const [filteredAdvert, setFilteredAdvert] = useState<Ad>();
    const [filteredAdvert2, setFilteredAdvert2] = useState<Ad>();

    useEffect(() => {
        async function fetchAds() {
            const adsData = await fetchAllAdverts('blog');
            console.log(adsData)
            if (adsData) {
                setAds(adsData);
            }
        }

        fetchAds();
    }, []);

    useEffect(() => {
        if (ads.length > 0) {
            const filteredLocation1 = filterAdsByLocationAndLevel(ads, 'center', 2);
            const filteredLocation2 = filterAdsByLocationAndLevel(ads, 'center', 3);
            setFilteredAdvert(filteredLocation1[0]);
            setFilteredAdvert2(filteredLocation2[0]);
        }

    }, [ads]);

    return (
        <div className="mx-auto my-5 grid max-w-[400px] overflow-hidden p-[32px] sm:max-w-[700px] md:max-w-[1400px] md:grid-cols-2 md:gap-4">
            <section className="my-auto mt-3 grid h-full w-full items-center justify-center rounded-[8px] md:mx-4 md:h-[170px]">

                {filteredAdvert?.url ? (
                    <Link target="_blank" href={filteredAdvert.url}>
                        <ImageShortcut
                            src={filteredAdvert.image}
                            alt={filteredAdvert.name}
                            width={980}
                            height={120}
                            priority
                            className="object-cover"
                        />
                    </Link>
                ) : (<span>No Adverts are available</span>)}


            </section>
            <section className="mx-4 my-auto mt-3 hidden h-full w-full items-center justify-center rounded-[8px] md:grid md:h-[170px]">
                {filteredAdvert2?.url ? (
                    <Link target="_blank" aria-label="Cart" href={filteredAdvert2.url}>
                        <ImageShortcut
                         width={980}
                         height={120}
                            src={filteredAdvert2.image}
                            alt={filteredAdvert2.name}
                            className="bg-[hsl(0,0%,98.4%,0.2)] bg-fixed object-cover opacity-0 transition duration-300 ease-in-out hover:opacity-100 max-sm:min-w-full max-sm:max-w-full"
                        />
                    </Link>
                ) : (<span>No Adverts are available</span>)}

            </section>
        </div>
    )
}

export default Banner
