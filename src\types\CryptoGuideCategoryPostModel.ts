export interface CryptoGuidePost {
    title: string;
    slug: string;
}

export interface CryptoGuideCategoryPost {
    id: number;
    name: string;
    slug: string;
    posts: CryptoGuidePost[]; // Update to allow an array of posts
}

export interface CryptoGuideResponse {
    categories: CryptoGuideCategoryPost[];
    success: boolean;
    statusCode: number;
}

export interface CryptoGuide {
    id: number;
    name: string;
    title: string | null;
    slug: string;
    content: string;
    previous: string | null;
    next: string | null;
}

export interface CryptoGuideResponse {
    cryptoguide: CryptoGuide;
    success: boolean;
    statusCode: number;
}
