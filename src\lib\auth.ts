import axios from "./axios"
import { NextAuthOptions } from "next-auth"
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"
import Discord<PERSON>rovider from 'next-auth/providers/discord';

interface CredentialsProps {
    email: string
    password: string
}

export const authOptions: NextAuthOptions = {
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: {
                    label: "email",
                    type: "email",
                    required: true
                },
                password: {
                    label: "Password",
                    type: "password",
                    required: true
                }
            },
            async authorize(credentials) {
                const res = await axios.post("/auth/email/login", credentials as CredentialsProps)
                const data = res.data;
                const user = data.user;

                if (user) {
                    return { ...user, access_token: data.access_token }
                }

                return null;
            }
        }),
        DiscordProvider({
            clientId: process.env.DISCORD_CLIENT_ID,
            clientSecret: process.env.DISCORD_CLIENT_SECRET,
        } as any),
    ],
    pages: {
        signIn: "/",
        error: "/",
        verifyRequest: '/dashboard/settings/verify-email'
    },
    session: {
        // Set maxAge to 24 hours
        maxAge: 24 * 60 * 60,
    },
    secret: process.env.NEXTAUTH_SECRET,
    callbacks: {
        async jwt({ token, user, trigger, session, account }) {
            if (trigger === "update") {
                return { ...token, ...session.user };
            }

            if (user) {
                token = { id: user.id  , provider:account?.provider}
            }

            return {
                ...token,
                ...user
            }
        },

        async session({ session, token }) {
            session.user = token as any;
            return session;
        }
    }
}