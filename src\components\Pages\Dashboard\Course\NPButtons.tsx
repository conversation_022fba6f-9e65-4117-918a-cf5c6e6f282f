'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { usePathname } from 'next/navigation'
import { CourseModel } from '@/types/CourseModel'
import { RightLightArrow, LeftLightArrow } from '@public/dashboard'
import {
    currentTopic,
    hasVideo,
    isFirst<PERSON>esson,
    isLastLesson,
    isLastTopic,
    nextTopic,
    previousTopic,
} from '@/config/nextAndPreviousFunc'

interface Props {
    course: CourseModel | undefined
}

const NPButtons = ({ course }: Props) => {
    const currentRoute = usePathname()

    // Get the rank of the current lesson

    return (
        <div className="flex items-center gap-10 max-md:gap-12">
            <Link
                aria-label="Previous lesson"
                href={
                    (isFirstLesson({ course, currentRoute }) &&
                        currentTopic({ course, currentRoute })?.rank === 1 &&
                        !hasVideo({ course, currentRoute })) ||
                    (hasVideo({ course, currentRoute }) &&
                        isFirstLesson({ course, currentRoute }) &&
                        currentRoute.split('/').length == 4)
                        ? currentRoute
                        : previousTopic({ course, currentRoute })
                }>
                <button
                    className={cn(
                        'flex items-center gap-2 text-sub3 text-blue',
                        (isFirstLesson({ course, currentRoute }) &&
                            currentTopic({ course, currentRoute })?.rank === 1 &&
                            !hasVideo({ course, currentRoute })) ||
                            (hasVideo({ course, currentRoute }) &&
                                isFirstLesson({ course, currentRoute }) &&
                                currentRoute.split('/').length == 4)
                            ? 'cursor-not-allowed text-black opacity-70'
                            : ''
                    )}
                    disabled={
                        (isFirstLesson({ course, currentRoute }) &&
                            currentTopic({ course, currentRoute })?.rank === 1 &&
                            !hasVideo({ course, currentRoute })) ||
                        (hasVideo({ course, currentRoute }) &&
                            isFirstLesson({ course, currentRoute }) &&
                            currentRoute.split('/').length == 4)
                    }>
                    <LeftLightArrow />
                    Previous
                </button>
            </Link>
            <Link
                aria-label="Next lesson"
                href={
                    isLastLesson({ course, currentRoute }) && isLastTopic({ course, currentRoute })
                        ? currentRoute
                        : nextTopic({ course, currentRoute })
                }>
                <button
                    className={cn(
                        'flex items-center gap-2 text-sub3 text-blue',
                        isLastLesson({ course, currentRoute }) && isLastTopic({ course, currentRoute })
                            ? 'cursor-not-allowed text-black opacity-70'
                            : ''
                    )}
                    disabled={isLastLesson({ course, currentRoute }) && isLastTopic({ course, currentRoute })}>
                    Next
                    <RightLightArrow />
                </button>
            </Link>
        </div>
    )
}

export default NPButtons
