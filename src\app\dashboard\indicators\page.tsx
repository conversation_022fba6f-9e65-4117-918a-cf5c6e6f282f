import fetchInstance from '@/lib/fetch'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import { IndicatorModel } from '@/types/IndicatorModel'
import HeaderWithNoAction from '@/components/Pages/Dashboard/Home/HeaderWithNoAction'
import Indicators from '@/components/Pages/Dashboard/Home/Indicators'
import NoIndicator from '@/components/Pages/Dashboard/Home/NoIndicator'

interface Model {
    indicators: IndicatorModel[]
    meta: {
        totalCount: number
        pageSize: number
        currentPage: number
        totalPages: number
    }
}

export const metadata = {
    title: 'My Indicators',
    description: 'Indicators page for your Crypto University account.',
}

const IndicatorPage = async ({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) => {
    const user = await getCurrentUser()
    if (!user) {
        redirect(authOptions?.pages?.signIn || '/')
    }

  

    const GetIndicators = async () => {
        try {
            const response = await fetchInstance(
                '/indicator/all?search=i&pageNumber=1&pageSize=10',
                {
                    headers: {
                        Authorization: `Bearer ${user?.access_token}`,
                    },
                }
            )
            return response
        } catch (error) {
            console.error('Error indicators: ', error)
            return error
        }
    }

    const indicators: Model = await GetIndicators()
    return (
        <section className="flex w-full flex-col gap-14 text-black">
            <HeaderWithNoAction user={user} />
            {indicators.meta && indicators.meta.totalCount == 0 ? (
                  <NoIndicator />
                  ) : (
                      <div className="flex h-full flex-col justify-between">
                          <Indicators indicators={indicators.indicators} />
                      </div>
                  )}
        </section>
    )
}

export default IndicatorPage
