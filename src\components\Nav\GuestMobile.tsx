import Link from 'next/link'
import { useState } from 'react'
import GuestDropdown from './GuestDropdown'
import { NavList } from '@/config/mobileNav'
import ButtonMobile from '../Ui/ButtonMobile'
import LoginModal from '@/components/Nav/LoginModal'
import SignoutBtn from './SignoutBtn'
import CartIconsMobile from './CartIconsMobile'

const GuestMobile = ({ user, handleOpen }: any) => {
    const [openLogin, setOpenLogin] = useState(false)
    const nav = NavList.mobileNav

    if (user)
        return (
            <div className="flex flex-col items-end gap-[50px] pb-24">
                <div className="flex w-full flex-col items-center gap-5">
                    <Link aria-label='Dashboard' href="/dashboard" onClick={handleOpen} className="w-full">
                        <ButtonMobile>
                            <svg
                                width="10"
                                height="14"
                                viewBox="0 0 10 14"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    d="M8.30873 3.97195C8.30873 5.80759 6.83693 7.27948 4.99999 7.27948C3.16367 7.27948 1.69125 5.80759 1.69125 3.97195C1.69125 2.1363 3.16367 0.665039 4.99999 0.665039C6.83693 0.665039 8.30873 2.1363 8.30873 3.97195ZM5 13.165C2.28898 13.165 0 12.7243 0 11.0243C0 9.32369 2.30336 8.89868 5 8.89868C7.71164 8.89868 10 9.33931 10 11.0393C10 12.74 7.69664 13.165 5 13.165Z"
                                    fill="#2655FF"
                                />
                            </svg>{' '}
                            My account
                        </ButtonMobile>
                    </Link>

                    {nav.map((item, index) => (
                        <GuestDropdown handleOpen={handleOpen} key={index} nav={item} border={index !== 0 && true} />
                    ))}

                    <div className="w-full">
                        <SignoutBtn nav />
                    </div>
                </div>
                <CartIconsMobile handleOpen={handleOpen} />
            </div>
        )

    return (
        <div className="flex flex-col items-end gap-[50px] pb-24">
            <div className="flex w-full flex-col gap-5 ">
                <Link aria-label='Register' href="/register" onClick={handleOpen} className="w-full">
                    <ButtonMobile fill>Register</ButtonMobile>
                </Link>
                <LoginModal isOpen={openLogin} handleOpen={handleOpen} setIsOpen={setOpenLogin} />

                {nav.map((item, index) => (
                    <GuestDropdown handleOpen={handleOpen} key={index} nav={item} border={index !== 0 && true} />
                ))}
            </div>
            <CartIconsMobile  handleOpen={handleOpen} />
        </div>
    )
}

export default GuestMobile
