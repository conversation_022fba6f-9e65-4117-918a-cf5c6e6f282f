'use client'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { RightArrow } from '@public/dashboard'

const BreadCrumb = () => {
    const currentRoute = usePathname()

    return (
        <div className="flex items-center gap-2 text-cap2 font-medium text-blue">
            <Link href="/dashboard">My Courses</Link>
            <RightArrow />
            <Link className="capitalize" href={'/dashboard/' + currentRoute.split('/')[2]}>
                {currentRoute.split('/')[2].split('-').join(' ')}
            </Link>
            <div className="flex items-center gap-2 max-md:hidden">
                <RightArrow />
                <span className="capitalize text-black">
                    {currentRoute?.split('/')[2]?.split('-').join(' ') + ' Assignment ' + currentRoute?.split('/')[4]}
                </span>
            </div>
        </div>
    )
}

export default BreadCrumb
