import axios from '@/lib/axios'
import { useState, useEffect } from 'react'

export default function useCryptoguide() {
    const [data, setData] = useState<any>([])
    const [error, setError] = useState<any>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [search, setSearch] = useState('')
    const [empty, setEmpty] = useState(true)

    const SetSearch = (searchTerm: string) => {
        setSearch(searchTerm)
    }

    useEffect(() => {
        const fetchData = async () => {
            if (!search) {
                setData([])
                setEmpty(true)
                return
            }
            setIsLoading(true)
            setEmpty(false)
            try {
                const response = await axios.get(`${process.env.API_URL}/cryptoguide/posts`, {
                    params: {
                        pageNumber:1,
                        pageSize:6,
                        search: search,
                    },
                })
                setData(response.data.cryptoguides)
            } catch (error) {
                setError(error)
            }
            setIsLoading(false)
        }

        fetchData()
    }, [search])

    return { data, error, isLoading, SetSearch, empty }
}

