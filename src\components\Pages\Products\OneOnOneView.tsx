import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import './OneOnOneView.css'
import yellowbutton from '../../../../public/icons/yellowbutton.svg'
import number from '../../../../public/icons/number.svg'
import investor from '../../../../public/icons/investor.svg'
import greenCheckCircle from '../../../../public/icons/greenCheckCircle.svg'

const OneOnOneView = () => {
  return (
      <div className="bg-[rgb(31,31,50)]">
          <div className="bgg">
              <section className="mx-auto max-w-[400px] md:max-w-[700px] md:pt-20 lg:max-w-[1400px]">
                <div className="grid justify-between p-[32px] md:flex  md:p-0 ">
                  <div className="col-span-2">
                      <h2 className="text-[20px] font-[600] text-white md:text-[30px]">Exclusive</h2>
                      <h1 className="my-4 hidden text-headline font-[600] text-white md:block md:max-w-[676px] md:text-[48px]">
                          <span className="text-[#00FF9E]">One-On-One Mentorship</span> with Grey Jabesi
                      </h1>
                      <div className=" my-4 w-[251px] text-[36px] font-[600] text-white md:hidden md:max-w-[676px] md:text-[54px]">
                          <h2 className="text-[#00FF9E]">One-On-One Mentorship</h2>{' '}
                          <h3 className="w-[47px] justify-center rounded-[8px] border border-[#FCC229] p-1 text-center text-[12px] text-[#FCC229]">
                              with
                          </h3>
                          <h2>Grey Jabesi</h2>
                      </div>
                      <h3 className="text-[11.62px] text-[#E9D41F] md:hidden">Founder & CEO of Crypto University</h3>

                      <section className="my-8 grid gap-3 text-white md:flex">
                          <div className="flex h-[50px]  items-center gap-2 rounded-[8px] border border-[#3C3C63] p-2 text-start md:h-[80px]">
                              <Image
                                  src={number}
                                  alt="video"
                                  width={36.88}
                                  height={3.07}
                                  className=" rounded-[4.0px]"
                              />
                              <h3 className="max-w-[316px] text-[12px] font-[600] md:text-[20px]">
                                  4 Weeks Of Trading With The Pros
                              </h3>
                          </div>
                          <div className="flex h-[50px]  items-center gap-2 rounded-[8px]  border border-[#3C3C63] p-2 text-start md:h-[80px]">
                              {' '}
                              <Image
                                  src={investor}
                                  alt="video"
                                  width={36.88}
                                  height={3.07}
                                  className=" rounded-[4.0px]"
                              />
                              <h3 className="max-w-[316px] text-[12px] font-[600] md:text-[20px]">
                                  Become a full time trader
                              </h3>
                          </div>
                      </section>

                      <ul className="mt-8 gap-6 text-white">
                          <li className="flex gap-2">
                              <Image
                                  src={greenCheckCircle}
                                  width={20}
                                  height={20}
                                  alt="greenCheckCircle"
                                  className="mb-1"
                              />
                              Complimentary Masterclass and Free Signals
                          </li>
                          <li className="my-2 flex gap-2">
                              <Image
                                  src={greenCheckCircle}
                                  width={20}
                                  height={20}
                                  alt="greenCheckCircle"
                                  className="mb-1"
                              />
                              Hands-On Trading Experience
                          </li>
                          <li className="flex gap-2">
                              <Image
                                  src={greenCheckCircle}
                                  width={20}
                                  height={20}
                                  alt="greenCheckCircle"
                                  className="mb-1"
                              />
                              Dedicated Mentorship
                          </li>
                      </ul>
                      <div className="mt-8">
                          <Link
                              aria-label="View products"
                              target="_blank"
                              href={'https://noteforms.com/forms/mentorship-request-d8avmm?notionforms=1'}
                              className="my-8">
                              <Image src={yellowbutton} height={54} width={341} alt="crown" className=" my-5" />
                          </Link>
                      </div>
                  </div>
                  <div>
                      <div className="hidden">
                          <h3 className=" hidden h-[40px] w-[87px] items-center justify-center rounded-full bg-[#FCC229] text-center text-[22px] md:block">
                              with
                          </h3>
                          <h2 className="hidden text-[54px] font-[600] text-white md:block">Grey Jabesi</h2>
                          <h3 className="hidden text-[24px] font-[600] text-[#E9D41F] md:block">
                              Founder & CEO of Crypto University
                          </h3>
                      </div>

                      <section className="mt-5 grid items-end gap-4 text-white md:ml-48 md:mt-[320px]">
                          <div className="h-[40px] w-[342px] items-center justify-center rounded-[8px] border border-[#3C3C63] p-2 text-[14px] font-[500] md:h-[66px] md:w-full md:p-2 md:text-[20px]">
                              <h2 className="max-w-[218px] text-sub3">Intensive Training with Experts</h2>
                          </div>
                          <div className="h-[40px] w-[342px] items-center justify-center rounded-[8px] border border-[#3C3C63] p-2 text-[14px] font-[500] md:h-[66px] md:w-full md:p-2 md:text-[20px]">
                              <h2 className="max-w-[218px] text-sub3">Designed for All Skill Levels</h2>{' '}
                          </div>
                          <div className="h-[40px] w-[342px] items-center justify-center rounded-[8px] border border-[#3C3C63] p-2 text-[14px] font-[500] md:h-[40px] md:w-full md:p-2 md:text-[20px]">
                              <h2 className="max-w-[218px] text-sub3">Monthly Cohort</h2>
                          </div>
                      </section>
                  </div>
              </div>
              </section>
              
          </div>
      </div>
  )
}

export default OneOnOneView
