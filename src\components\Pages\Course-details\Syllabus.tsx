"use client";
import About from "./(Syllabus)/About";
import Lessons from "./(Syllabus)/Lessons";
import KeyPoints from "./(Syllabus)/KeyPoints";
import { CourseModel } from "@/types/CourseModel";
import Instructors from "../../Ui/Instructors";
import InstructorModel from "@/types/InstructorModel";

interface Props {
  course: CourseModel;
  instructor: InstructorModel;
  TopicsCount : number
}

const Syllabus = ({ course, instructor, TopicsCount }: Props) => {
  return (
    <div className=" pt-16  flex flex-col gap-8 w-full max-md:px-0 max-md:pt-6 ">
      <Lessons TopicsCount={TopicsCount} course={course} />
      <KeyPoints points={course.key_points} />
      <About description={course.description} />
      <Instructors instructor={instructor} />
    </div>
  );
};

export default Syllabus;
