'use client'
import useCart from '@/hooks/useCart'
import { Heart } from '@public/global'
import { useState, useEffect } from 'react'
import Button from '@/components/Ui/Button'
import useWishlist from '@/hooks/useWishlist'
import { CourseModel } from '@/types/CourseModel'
import { Certificate, Global, Mobile, Translate, Unlimited } from '@public/briefly'

interface Props {
    reward: boolean
    course: CourseModel
    setOpenModel: any
    isEnrolled: boolean
}

const data = [
    {
        title: 'Certificate completion',
        description: 'Get a certificate after completing',
        icon: <Certificate />,
    },
    { title: 'Access on smartphone and tabs', description: '', icon: <Mobile /> },
    { title: 'Lifetime full access', description: '', icon: <Unlimited /> },
    {
        title: '100% Online',
        description: 'Start learning right away and set it according to your schedule',
        icon: <Global />,
    },
    { title: 'English Language', description: '', icon: <Translate /> },
]

const Briefly = ({ reward, course, setOpenModel, isEnrolled }: Props) => {
    const cart = useCart()
    const wishlist = useWishlist()
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
    }, [])
    const OnAddToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.addproduct(course)
    }
    const OnAddToCart = (e: any) => {
        e.preventDefault()
        cart.addproduct(course)
    }
    const OnRemoveToCart = (e: any) => {
        e.preventDefault()
        cart.removeproduct(course.id)
    }
    const OnRemoveToWishlist = (e: any) => {
        e.preventDefault()
        wishlist.removeproduct(course.id)
    }

    return (
        <div className="px-16 max-md:px-4">
            <div className="sticky top-20 flex max-w-[520px] flex-col gap-6  border-gray-700 py-10 max-md:w-full max-md:min-w-full max-md:border-b">
                <div className="absolute inset-0"></div>
                {data.map((item, index) =>
                    index === 0 && reward ? (
                        <div key={index} className="flex items-center gap-4">
                            <div className="flex min-h-[52px] min-w-[52px] items-center justify-center rounded-full border border-gray-700">
                                {item.icon}
                            </div>
                            <div className="flex flex-col gap-1 font-sans">
                                <div className="text-sub2 font-medium">{item.title}</div>
                                <div className="text-sub3 text-gray-900">{item.description}</div>
                            </div>
                        </div>
                    ) : index != 0 ? (
                        <div key={index} className="flex items-center gap-4">
                            <div className="flex min-h-[52px] min-w-[52px] items-center justify-center rounded-full border border-gray-700 max-md:min-h-[45px] max-md:min-w-[45px]">
                                {item.icon}
                            </div>
                            <div className="flex flex-col gap-1 font-sans">
                                <div className="text-sub2 font-medium">{item.title}</div>
                                <div className="text-sub3 text-gray-900">{item.description}</div>
                            </div>
                        </div>
                    ) : (
                        ''
                    )
                )}
                <div className="sticky top-24 space-y-6">
                    {isMounted ? (
                        cart.productExists(course) ? (
                            <>
                                <Button
                                    onClick={e => {
                                        OnRemoveToCart(e)
                                    }}
                                    variant="primary"
                                    disabled={true}
                                    rounded>
                                    In Cart
                                </Button>
                            </>
                        ) : (
                            <Button
                                onClick={e => {
                                    OnAddToCart(e), setOpenModel(true)
                                }}
                                variant="primary"
                                disabled={isEnrolled}
                                rounded>
                                {isEnrolled ? "Course owned" : "Add to Cart"}
                            </Button>
                        )
                    ) : (
                        ''
                    )}
                    {isMounted ? (
                        wishlist.productExists(course) ? (
                            <>
                                <Button className="flex items-center" rounded onClick={OnRemoveToWishlist}>
                                    <span>
                                        <Heart active={true} />
                                    </span>
                                    Wishlisted
                                </Button>
                            </>
                        ) : (
                            <Button className="flex items-center" rounded onClick={OnAddToWishlist}>
                                <span>
                                    <Heart active={false} />
                                </span>
                                Add to wishlist
                            </Button>
                        )
                    ) : (
                        ''
                    )}
                </div>
            </div>
        </div>
    )
}

export default Briefly
