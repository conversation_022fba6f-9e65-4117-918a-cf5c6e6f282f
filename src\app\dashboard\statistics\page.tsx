import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/session'
import fetchInstance from '@/lib/fetch'
import StatisticsDashboard from '@/components/Pages/Dashboard/Statistics/StatisticsDashboard'

export const metadata = {
    title: 'Affiliate',
    description: 'Affiliate page for your Crypto University account.',
}

interface Model {
    statistics: StatisticsModel
  }

interface StatisticsModel {
    totalCourses: number
    totalTopics: number
    totalQuizTaken: number
    averageQuizScore: number
    coursesStats: CoursesStat[]
  }
  
  interface CoursesStat {
    name: string
    image: string
    isEnrolled: boolean
    notStarted: boolean
    videosCount: number
    lessonsCount: number
    userWatchedVideosCount: number
    certificate: string
    validForCertificate: boolean
  }

const StatisticsPage = async () => {
    const user = await getCurrentUser()
    if (!user) redirect(authOptions?.pages?.signIn || '/')

    const GetACourseStatistics = async () => {
        try {
            const response = await fetchInstance(
                '/user-course/statistics',
                {
                    headers: {
                        Authorization: `Bear<PERSON> ${user?.access_token}`,
                    },
                }
            )
            return response
        } catch (error) {
            console.error('Error Users: ', error)
            return error
        }
    }
    const accessToken = user?.access_token || ''
    const courseStats: Model = await GetACourseStatistics()
    return (
        <section className="flex w-full flex-col gap-14 text-black">
                <StatisticsDashboard coursesStats={courseStats} token={accessToken} />
        </section>
    )
}

export default StatisticsPage
