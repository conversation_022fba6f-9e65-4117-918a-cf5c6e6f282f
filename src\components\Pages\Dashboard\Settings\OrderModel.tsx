'use client'
import <PERSON><PERSON> from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import TransactionModel from '@/types/TransactionModel'
import { Dialog, Transition } from '@headlessui/react'
import { Coinbase, Gift, Stripe } from '@public/checkout'
import { Close } from '@public/home'
import Link from 'next/link'
import { Fragment } from 'react'

interface Props {
    product: TransactionModel
    openModel: boolean
    setOpenModel: any
    tax: number
}

const OrderModel = ({ product, openModel, setOpenModel, tax }: Props) => {
    return (
        <Transition show={openModel || false} as={Fragment}>
            <Dialog
                as="div"
                onClose={() => setOpenModel(false)}
                className="fixed inset-0 z-50 bg-black/30 max-md:bg-transparent">
                <div className="fixed  inset-0 flex items-center justify-center max-md:block max-md:p-0">
                    <Transition.Child
                        as={Fragment}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0">
                        <Dialog.Panel className="flex h-full items-center justify-center  max-md:block max-md:max-w-full">
                            <div className="relative flex w-[570px] flex-col gap-9 rounded-[1.5rem] bg-white px-[47px] pb-10 pt-[29px] font-sans max-md:h-full max-md:w-full  max-md:gap-6 max-md:rounded-none max-md:px-5 max-md:pt-[30px]">
                                <button
                                    type="button"
                                    onClick={() => setOpenModel(false)}
                                    className="absolute right-8 top-8 rounded-lg transition-[background] duration-150 hover:bg-gray-400 max-md:right-4 max-md:top-4">
                                    <Close />
                                </button>
                                <div className="flex flex-col items-start gap-3">
                                    <p className="text-sub2 font-semibold">Order {product.tx_hash}</p>
                                    <p className="text-cap1 max-md:text-cap2">{product.created_at.split('T')[0]}</p>
                                    <p
                                        className={cn(
                                            'rounded-md px-3 py-1 text-cap2 text-white max-md:text-cap3',
                                            product.status == 'pending'
                                                ? 'bg-yellow'
                                                : product.status == 'succeeded'
                                                ? 'bg-green-dark'
                                                : 'bg-red'
                                        )}>
                                        {product.status}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-[18px] ">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <ImageShortcut
                                                src={
                                                    product.courses
                                                        ? product.courses?.image
                                                        : product.bundles
                                                        ? product.bundles?.image
                                                        : product.indicators
                                                        ? product.indicators?.image
                                                        : product.mentorships
                                                        ? product.mentorships?.image
                                                        : ''
                                                }
                                                alt="product"
                                                width={60}
                                                height={60}
                                                className="object-cover max-md:h-[40px] max-md:w-[40px]"
                                            />
                                            <p className="text-sub3 font-medium">
                                                {product.courses
                                                    ? product.courses?.name
                                                    : product.bundles
                                                    ? product.bundles.name
                                                    : product.indicators
                                                    ? product.indicators.title
                                                    : product.mentorships
                                                    ? product.mentorships.name
                                                    : ''}
                                            </p>
                                        </div>
                                        <p className="text-sub2 font-medium max-md:text-cap2">
                                            $
                                            {product.courses
                                                ? product.courses?.price
                                                : product.bundles
                                                ? product.bundles.price
                                                : product.indicators
                                                ? product.indicators.price
                                                : product.mentorships
                                                ? product.mentorships.price
                                                : ''}
                                        </p>
                                    </div>
                                    <div className="border-b border-gray-700" />
                                    <div className="flex items-center justify-between">
                                        <p className="text-sub3  font-medium max-md:text-cap2">Price</p>
                                        <p className="text-sub1 font-medium max-md:text-cap1">
                                            $
                                            {product.courses
                                                ? product.courses?.price
                                                : product.bundles
                                                ? product.bundles.price
                                                : product.indicators
                                                ? product.indicators.price
                                                : product.mentorships
                                                ? product.mentorships.price
                                                : ''}
                                        </p>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <p className="text-sub3 font-medium max-md:text-cap2">Discount</p>
                                        <p className="text-sub1 font-medium max-md:text-cap1">
                                            -$
                                            {product.courses
                                                ? parseInt(product.courses?.price ?? '0') - Math.round(product.amount - product.amount * (tax / 100))
                                                : product.bundles
                                                ? product.bundles.price - Math.round(product.amount - product.amount * (tax / 100))
                                                : product.indicators
                                                ? parseInt(product.indicators.price ?? '0') - Math.round(product.amount - product.amount * (tax / 100))
                                                : product.mentorships 
                                                ? parseInt(product.mentorships.price?? '0') - Math.round(product.amount - product.amount * (tax / 100))
                                                : 0 }
                                        </p>
                                    </div>
                                    <div className="border-b border-gray-400" />
                                    
                                    <div className="flex items-center justify-between">
                                        <p className="text-sub3 font-medium max-md:text-cap2">Tax</p>
                                        <p className="text-sub1 font-medium max-md:text-cap1">
                                            +${Math.round(product.amount * (tax / 100))}
                                        </p>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <p className="text-b3 font-semibold max-md:text-sub3">Total</p>
                                        <p className="text-b3 font-semibold max-md:text-sub3">${product.amount}</p>
                                    </div>
                                    <div className="border-b border-gray-700" />
                                </div>
                                <div className="flex flex-col gap-6">
                                    <h1 className="text-sub3 font-medium max-md:text-cap3">Payment Method</h1>
                                    {product.paymentMethod == 'stripe' ? (
                                        <Stripe />
                                    ) : product.paymentMethod == 'coinbase' ? (
                                        <Coinbase />
                                    ) : (
                                        <Gift />
                                    )}
                                </div>
                                <div className="flex flex-col gap-6">
                                    <h1 className="text-sub3 font-semibold max-md:text-cap2">
                                        Having troubles with your order?
                                    </h1>
                                    <div className="w-[300px] max-md:w-full">
                                        <Link href={'/help'}>
                                            <Button variant="primary" rounded>
                                                Get Help {'>'}
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </Dialog.Panel> 
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    )
}

export default OrderModel
