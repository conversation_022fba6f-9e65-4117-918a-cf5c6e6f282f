import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import {
    Bch,
    Bitcoin,
    Bnb,
    Btc,
    Card,
    Eth,
    Shib,
    Sol,
    Usdt,
    Atlos,
    Stripe,
    Visa,
    Mastercard,
    Union,
    Amex,
    PayPal,
} from '@public/checkout'

type Props = {
    activeTabIndex: string
    setActiveTabIndex: (x: 'stripe' | 'coinbase' | 'paypal') => void
}

export const SusbcriptionPaymentMethod = ({ activeTabIndex, setActiveTabIndex }: Props) => {
    return (
        <div
            className={cn(
                'flex h-full flex-col gap-6 overflow-x-hidden p-1',
                activeTabIndex === 'stripe' ? 'max-h-max' : 'max-md:max-h-48 max-[405px]:max-h-fit'
            )}>
            <div className="relative grid grid-cols-3 gap-4 border-b-4 border-[#E2E2E2] text-sub3 transition-colors max-md:text-[14px] max-sm:gap-0">
                <label className="flex cursor-pointer items-center justify-normal gap-1 pb-4">
                    <span className="rounded-full border border-gray-700 p-3.5">
                        <Card />
                    </span>
                    <span className="select-none font-semibold">Debit/Credit Card</span>
                    <input
                        hidden
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setActiveTabIndex(e.target.value as 'stripe' | 'coinbase' | 'paypal')
                        }}
                        checked={activeTabIndex == 'stripe'}
                        type="radio"
                        name="payment"
                        value="stripe"
                    />
                </label>
                <label className="flex cursor-pointer items-center justify-normal gap-1 pb-4 ">
                    <input
                        hidden
                        checked={activeTabIndex == 'coinbase'}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setActiveTabIndex(e.target.value as 'stripe' | 'coinbase' | 'paypal')
                        }}
                        type="radio"
                        name="payment"
                        value="coinbase"
                    />
                    <span>
                        <Bitcoin />
                    </span>
                    <span className="select-none font-semibold">Crypto</span>
                </label>
                <label className="flex cursor-pointer items-center justify-normal gap-1 pb-4 ">
                    <input
                        hidden
                        checked={activeTabIndex == 'paypal'}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setActiveTabIndex(e.target.value as 'stripe' | 'coinbase' | 'paypal')
                        }}
                        type="radio"
                        name="payment"
                        value="paypal"
                    />
                    <span>
                        <PayPal />
                    </span>
                    <span className="select-none font-semibold">Paypal</span>
                </label>
                <span
                    className={cn(
                        'absolute -bottom-1 left-0 col-span-2 block h-1 w-1/2 bg-blue transition-all duration-300',
                        activeTabIndex == 'stripe'
                            ? 'translate-x-0'
                            : activeTabIndex == 'coinbase'
                            ? 'translate-x-[50%]'
                            : activeTabIndex == 'paypal'
                            ? 'translate-x-[100%]'
                            : ''
                    )}
                />
            </div>
            <div
                className={cn(
                    'relative flex !w-[200%] justify-between gap-2 text-sub3 transition-all duration-300 max-md:text-[14px]',
                    activeTabIndex == 'stripe'
                        ? 'translate-x-0'
                        : activeTabIndex == 'coinbase'
                        ? '-translate-x-[33%]'
                        : activeTabIndex == 'paypal'
                        ? '-translate-x-[66%]'
                        : ''
                )}>
                {/* Stripe */}
                <div className="relative flex w-[33%] grid-flow-col flex-col gap-3 max-sm:pt-3">
                    <p className="text-sub3 leading-none">
                        Pay using your country cards. Choose from a wide range of cards.
                    </p>
                    <span className="flex w-full items-center gap-6 max-sm:gap-2 max-[400px]:gap-0.5">
                        <Visa />
                        <Mastercard />
                        <span className='max-sm:hidden'>
                            <Union />
                        </span>
                        <span className='max-sm:hidden'>
                            <Amex />
                        </span>
                        <span className="text-sub3">+ More</span>
                    </span>
                    <span className="flex items-center gap-1.5">
                        <span className="text-sub3 leading-none text-gray-700">Powered by</span>
                        <Stripe />
                    </span>
                </div>

                {/* Coinbase */}
                <div className="relative flex h-fit w-[33%] flex-col gap-3">
                    <p className="text-sub3 leading-none md:pr-6">
                        Pay using cryptocurrency on Atlos. Choose from a wide range of popular cryptocurrencies.
                    </p>
                    <div className="flex items-center justify-normal gap-6 max-md:flex-col max-md:gap-3">
                        <div className="flex w-fit items-center overflow-x-hidden max-md:w-full max-md:justify-between md:gap-6">
                            <Eth /> <Usdt /> <Btc /> <Bnb />{' '}
                            <span className="max-md:hidden">
                                <Bch />
                            </span>{' '}
                            <Sol /> <Shib />
                            <br className="md:hidden" />
                        </div>
                        <p className="w-fit text-sub3">+40 More</p>
                    </div>
                    <span className="flex items-center gap-1.5">
                        <span className="text-sub3 leading-none text-gray-700">Powered by</span>
                        <Atlos />
                    </span>
                </div>
                {/* Paypal */}
            <div className="relative flex h-fit w-[33%] flex-col gap-3">
                <p className="text-sub3 leading-none">
                    Pay securely with PayPal using your PayPal balance, bank account, or credit card.
                </p>
                <span className="flex w-full items-center gap-6">
                    {/* <PayPalIcon /> Display the PayPal Icon */}
                    <span className="text-sub3">+ More</span>
                </span>
                <span className="flex items-center gap-1.5">
                    <span className="text-sub3 leading-none text-gray-700">Powered by</span>
                    <ImageShortcut
                    src="/checkout/paypal.png"
                    alt="Paypal"
                    className="object-contain h-15 w-81"
                    width={81}
                    height={15}
                />
                    {/* <PayPalIcon /> Display the PayPal Icon */}
                </span>
            </div>
            </div>
        </div>
    )
}
