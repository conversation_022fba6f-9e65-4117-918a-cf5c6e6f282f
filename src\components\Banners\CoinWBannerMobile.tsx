'use client'
import Link from 'next/link'
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation'
import { adsImage, sponsoredByCoinW } from '@public/coinw-trading-competion'
import ImageShortcut from '../Ui/Image'
import { GetCurrentUserClient } from "@/lib/session"

const CoinWBannerMobile = () => {

    const pathname = usePathname();
    const [showBanner, setShowBanner] = useState(true);
    const [countdown, setCountdown] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });
    const user = GetCurrentUserClient();

    useEffect(() => {
        const competitionEndDate: any = new Date("2024-05-28T00:00:00");
        const interval = setInterval(() => {
            const now: any = new Date();
            const timeDifference = competitionEndDate - now;
            if (timeDifference > 0) {
                const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);
                setCountdown({ days, hours, minutes, seconds });
            } else {
                clearInterval(interval);
                setShowBanner(false); // Hide the banner when the competition ends
            }
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        const closeButton = document.getElementById('close-banner-button');
        const closeBanner = () => {
            setShowBanner(false);
        };
        if (closeButton) {
            closeButton.addEventListener('click', closeBanner);
        }
        return () => {
            if (closeButton) {
                closeButton.removeEventListener('click', closeBanner);
            }
        };
    }, [pathname]);

    useEffect(() => {
        const closeButtonMobile = document.getElementById('close-banner-button-mobile');
        const closeButtonBannerMobile = () => {
            setShowBanner(false);
        };
        if (closeButtonMobile) {
            closeButtonMobile.addEventListener('click', closeButtonBannerMobile);
        }
        return () => {
            if (closeButtonMobile) {
                closeButtonMobile.removeEventListener('click', closeButtonBannerMobile);
            }
        };
    }, [pathname]);

    return (
        <div>
            {showBanner && pathname === '/' && (
                <div id="bottom-banner" className=" fixed border rounded-t-lg bg-[#000] bottom-0 start-0 z-50 flex flex-col items-center w-full p-4 md:rounded-t-md">
                    <button id="close-banner-button-mobile" data-dismiss-target="#sticky-banner" type="button" className="absolute top-0 right-0 text-white text-lg  focus:outline-none p-2 flex-shrink-0 inline-flex justify-center w-7 h-7 items-center  hover:bg-gray-700 hover:text-gray-900 rounded-lg text-cap1 sm:text-sm">
                        <svg className="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span className="sr-only">Close banner</span>
                    </button>

                    <ImageShortcut
                        src={adsImage}
                        width={150}
                        height={130}
                        alt="thumbnail"
                        className="object-contain h-130 w-auto p-2"
                        priority={true}
                    />
                    <h2 className="text-white text-sub3 font-bold mb-2 mt-2">Join in CoinW trading competition</h2>
                    <p className="text-[#ffffff] mb-4 text-cap2">Win up to <span className="text-[#FCC229]">$1,000,000</span> in prizes! Open to all CU <br /> traders, no matter your experience</p>
                    <div className="grid grid-flow-col gap-5 text-center auto-cols-max text-white mb-4">
                        <div className="grid grid-flow-col gap-5 text-center auto-cols-max text-white">
                            <div className="text-[#BCBCBC] text-cap2 flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
                                <span className="countdown font-mono text-5xl">
                                    <span className='text-white text-b1'>{countdown.days}</span>
                                </span>
                                Days
                            </div>
                            <div className="text-[#BCBCBC] text-cap2 flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
                                <span className="countdown font-mono text-5xl">
                                    <span className='text-white text-b1'>{countdown.hours}</span>
                                </span>
                                Hours
                            </div>
                            <div className="text-[#BCBCBC] text-cap2 flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
                                <span className="countdown font-mono text-5xl">
                                    <span className='text-white text-b1'>{countdown.minutes}</span>
                                </span>
                                Min
                            </div>
                            <div className="text-[#BCBCBC] text-cap2 flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
                                <span className="countdown font-mono text-5xl">
                                    <span className='text-white text-b1'>{countdown.seconds}</span>
                                </span>
                                Sec
                            </div>
                        </div>
                    </div>

                    {user ? (<Link
                        aria-label="View products"
                        href={'/coinw-trading-competition'}
                        className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub2 font-semibold px-8 py-3 mb-4 transition-colors duration-150 inline-block"
                    >
                        Join Now {'>'}
                    </Link>) : (<Link
                        aria-label="View products"
                        href={'/coinw-trading-competition'}
                        className="bg-[#FCC229] text-black hover:bg-yellow-600 active:bg-yellow-800 rounded-lg text-sub2 sm:text-sub2 font-semibold px-8 py-3 mb-4 transition-colors duration-150 inline-block"
                    >
                        Join Now {'>'}
                    </Link>)}

                    <ImageShortcut
                        src={sponsoredByCoinW}
                        width={150}
                        height={50}
                        alt="thumbnail"
                        className="object-contain h-130 w-auto p-2"
                        priority={true}
                    />
                </div>
            )}
        </div>
    )
}

export default CoinWBannerMobile