@tailwind base;
@tailwind components;
@tailwind utilities;

/* Scrollbar Styling */
* {
    scrollbar-width: thin;
    scrollbar-color: #aaaaaa transparent;
    scroll-behavior: smooth;
}

*::-webkit-scrollbar {
    width: 7px;
}

*::-webkit-scrollbar-track {
    background: #dbdbdb;
}

*::-webkit-scrollbar-thumb {
    background-color: #aaaaaa;
    border-radius: 248px;
}

/* Text limit to 4 lines */
.text-cap {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Fixing button styles */
.classButton {
    border-style: none !important;
    @apply flex flex-row text-sub3 font-medium items-center gap-[0.625rem];
}

/* Spinner */
.spinner {
    background: conic-gradient(#0000 10%, #2655ff);
    -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
    animation: s3 1s infinite linear;
    @apply w-8 h-8 rounded-[50%];
}

@keyframes s3 {
    to {
        transform: rotate(1turn);
    }
}
input[type="text"]::-ms-clear,
input[type="text"]::-ms-reveal {
    display: none;
}
input[type="text"]::-webkit-search-cancel-button,
input[type="text"]::-webkit-search-decoration,
input[type="text"]::-webkit-search-results-button,
input[type="text"]::-webkit-search-results-decoration {
    display: none;
}
/* Burger */
.menu-icon {
    position: relative;
    width: 35px;
    height: 50px;
    cursor: pointer;

    .menu-icon__cheeckbox {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        cursor: pointer;
        z-index: 2;
        -webkit-touch-callout: none;
        position: absolute;
        opacity: 0;
    }
    div {
        margin: auto;
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        width: 22px;
        height: 12px;
    }
    span {
        position: absolute;
        display: block;
        width: 100%;
        height: 2px;
        background-color: var(--bar-bg, #2655ff);
        border-radius: 1px;
        transition: all 0.2s cubic-bezier(0.1, 0.82, 0.76, 0.965);

        &:first-of-type {
            top: 0;
        }
        &:last-of-type {
            bottom: 0;
        }
    }
    &.active,
    .menu-icon__cheeckbox:checked + div {
        span {
            &:first-of-type {
                transform: rotate(45deg);
                top: 5px;
            }
            &:last-of-type {
                transform: rotate(-45deg);
                bottom: 5px;
            }
        }
    }

    &.active:hover span:first-of-type,
    &.active:hover span:last-of-type,
    &:hover .menu-icon__cheeckbox:checked + div span:first-of-type,
    &:hover .menu-icon__cheeckbox:checked + div span:last-of-type {
        width: 22px;
    }
}

// Gradient
.gradient {
    @apply bg-[linear-gradient(90.27deg,#2655FF_2.42%,#267DFF_52.32%,#2655FF_96.18%)] hover:bg-[linear-gradient(90.27deg,#224DE6_2.42%,#267DFF_52.32%,#224DE6_96.18%)];
}

/* Remove Arrows/Spinners */
@layer utilities {
    @layer responsive {
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .no-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
}

// Discord Banner
.bgg {
    background: url("/join/discordbanner.png") center center/cover no-repeat;
    background-color: #2647b2;
}
.bgg-small {
    background: url("/join/discordbannersmall.png") center center/cover no-repeat;
    background-color: #2647b2;
}

.membershipcard {
    background: linear-gradient(155.23deg, #1635a3 2.18%, #2a68c5 59.79%, #1635a3 110.43%);
    /* Neutral/300 */

    border: 1px solid #eff0f7;
    /* General/Shadow 02 */

    box-shadow: 0px 2px 12px rgba(20, 20, 43, 0.08);
}

.membershipcard2 {
    background: linear-gradient(155.23deg, #6E4C31 2.18%, #EFB77C 59.79%, #6E4C31 110.43%);
    /* Neutral/300 */

    border: 1px solid #EFB77C;
    /* General/Shadow 02 */

    box-shadow: 0px 2px 12px rgba(20, 20, 43, 0.08);
}
.qs {
    .popover {
        background-color: rgba(0, 0, 0, 0.85);
        border-radius: 5px;
        bottom: 42px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
        color: #fff;
        display: none;
        font-size: 12px;
        left: -2.625rem;
        padding: 7px 10px;
        position: absolute;
        width: 123px;
        z-index: 4;

        &:before {
            border-top: 7px solid rgba(0, 0, 0, 0.85);
            border-right: 7px solid transparent;
            border-left: 7px solid transparent;
            bottom: -7px;
            content: "";
            display: block;
            left: 50%;
            margin-left: -9px;
            position: absolute;
        }
    }
    &:hover {
        .popover {
            display: block;
        }
    }
}

// Discord Banner
.banner {
    background-image: linear-gradient(180deg, #01223a 0%, rgba(12, 113, 195, 0.27) 100%);
    @apply w-full h-full inset-0 absolute;
}

// Earn Colors
.binance {
    @apply bg-[#010511] text-white;
}
.coinbase {
    @apply bg-[#1653F0] text-white;
}
.bybit {
    @apply bg-[#14192C] text-white;
}
.celcius {
    @apply bg-[linear-gradient(180deg,#392BC6_0%,#8D32BB_100%)] text-white;
}
.crypto {
    @apply bg-[#0A2F77] text-white;
}
.brave {
    @apply bg-[#ED5001] text-white;
}
.superrare {
    @apply bg-[#000000] text-white;
}
.nexo {
    @apply bg-[#2853C2] text-white;
}
.blockfi {
    @apply bg-[#007CAC] text-white;
}
.cu {
    @apply bg-[#001D4C] text-white;
}
.paxful {
    @apply bg-[#6B259F] text-white;
}
.binance2 {
    @apply bg-[#11191F] text-[#EDB800];
}
.compound {
    @apply bg-[#00D395] text-white;
}

// Blog
#search_blog::-webkit-search-cancel-button {
    -webkit-appearance: none;
    display: inline-block;
    width: 12px;
    cursor: pointer;
    height: 12px;
    margin-left: 10px;
    background: linear-gradient(
            45deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0) 43%,
            #000 45%,
            #000 55%,
            rgba(0, 0, 0, 0) 57%,
            rgba(0, 0, 0, 0) 100%
        ),
        linear-gradient(135deg, transparent 0%, transparent 43%, #000 45%, #000 55%, transparent 57%, transparent 100%);
}

// Popup Modal
.customModal {
    background: transparent !important;
    padding: 0px !important;
}

// Affiliates page
.bg-kit-desktop {
    background-color: #0f1a38;
}

// Membership page
.membershipcard {
    color: white;
}

// Hide Scrollbar
.hide-scroll-bar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
    display: none;
}

// Mirror text
.mirror-text::after {
    content: "Learn. Trade. Invest. Play";
    display: flex;
    margin-top: -1.8rem;
    margin-left: 0.015px;
    transform: rotateX(180deg);
    -webkit-background-clip: text;
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 10%, rgba(255, 255, 255, 0.5));
    color: transparent;
}

// Custom background
.custom-bg {
    background:
        linear-gradient(270deg, rgba(16, 18, 25, 0) 0%, #004159 88%),
        // url(<path-to-image>),
        lightgray 0px 0px / 103.998% 100% no-repeat,
        #000;
}

// Countdown background
.countdown-bg {
    background: url("/custom/countdown.jpg") center center/cover no-repeat;
    background-attachment: fixed;
}

.signup-bg {
    background: url("/public/icons/crypto-university-bg.png");
}