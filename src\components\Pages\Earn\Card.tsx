import Link from 'next/link'
import { cn } from '@/lib/cn'
import ImageShortcut from '@/components/Ui/Image'

type Props = {
    item: {
        img: any
        title: string
        description: string
        background: string
        button?: string
        link: string
    }
}

const Card = (item: Props) => {
    return (
        <Link
            aria-label="Card"
            href={item.item.link + ''}
            target="_blank"
            className={cn(item.item.background, 'rounded-[12px] p-7 lg:max-w-[320px]')}>
            <div className="flex flex-col gap-10">
                <ImageShortcut src={item.item.img} alt={item.item.title} className="object-contain" />

                <div className="space-y-3">
                    <h3 className="text-b3 font-semibold">{item.item.title}</h3>
                    <div className="space-y-6 text-sub3 font-medium">
                        <p>{item.item.description}</p>
                        <p>{item.item.button}</p>
                    </div>
                </div>
            </div>
        </Link>
    )
}

export default Card
