import { redirect } from 'next/navigation'
import ImageShortcut from '@/components/Ui/Image'
import ResetForm from '@/components/Pages/Forgot/ResetForm'
import Forgot from "../../../../public/forgot/resetpassword.png"

export const metadata = {
    title: 'Reset Password',
    description: 'Reset your password',
};

interface PageProps {
    searchParams: {
        email: string;
        token: string;
    }
}

async function RedirectParam(searchParams: PageProps['searchParams']) {
    const { email, token } = searchParams
    if (!email || !token) {
        redirect("/forgot-password")
    }
}

const ResetPasswordPage = async ({ searchParams }: PageProps) => {
    await RedirectParam(searchParams)

    return (
        <section className="flex flex-col text-black font-sans h-full flex-grow justify-center max-md:justify-start overflow-hidden max-sm:pb-6">
            <div className="container mx-auto flex max-xl:flex-col-reverse items-center justify-between">
                <div className="flex flex-col gap-16 max-md:gap-8 max-w-[647px] flex-grow w-full">
                    <h1 className="text-h3 max-md:text-headline max-xl:text-center max-w-[647px] max-md:w-full flex flex-col gap-6 max-md:gap-3 lg:w-full ">Reset Password</h1>
                    <div className="max-w-[450px] max-xl:max-w-full">
                        <ResetForm email={searchParams.email} token={searchParams.token} />
                    </div>
                </div>
                <ImageShortcut
                    src={Forgot}
                    width={649}
                    height={649}
                    className="max-xl:w-[210px] max-xl:h-auto"
                    alt="Reset Password"
                    priority
                />
            </div>
        </section>
    );
};

export default ResetPasswordPage;