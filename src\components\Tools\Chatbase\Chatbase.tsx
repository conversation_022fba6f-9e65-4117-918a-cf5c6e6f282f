'use client';
import React, { useEffect } from "react";

// Extend the Window interface to include chatbase
declare global {
    interface Window {
        chatbase?: any;
    }
}

const Chatbase: React.FC = () => {
    useEffect(() => {
        if (!window.chatbase || window.chatbase("getState") !== "initialized") {
            window.chatbase = (...args: any) => {
                if (!window.chatbase!.q) {
                    window.chatbase!.q = [];
                }
                window.chatbase!.q.push(args);
            };
            window.chatbase = new Proxy(window.chatbase, {
                get(target, prop) {
                    if (prop === "q") return target.q;
                    return (...args: any) => target(prop, ...args);
                }
            });
        }

        const onLoad = () => {
            const script = document.createElement("script");
            script.src = "https://www.chatbase.co/embed.min.js";
            script.id = "bRsAcdjGTeRUiZl8m6gP2";
            script.dataset.domain = "www.chatbase.co";
            document.body.appendChild(script);
        };

        if (document.readyState === "complete") {
            onLoad();
        } else {
            window.addEventListener("load", onLoad);
        }
    }, []); // Runs only once when the component mounts.

    return null; // This component does not render anything.
};

export default Chatbase;
