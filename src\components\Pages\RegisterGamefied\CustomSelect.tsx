import Select, { GroupBase, StylesConfig } from "react-select";
import { FieldAttributes, FormikProps } from "@/lib/formik";
type OptionType = {
  value: string;
  label: string | React.JSX.Element;
};

interface CustomSelectProps extends FieldAttributes<any> {
  label?: string;
  options: OptionType[];
  formik: FormikProps<any>;
  style: StylesConfig<OptionType, false, GroupBase<OptionType>>;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  label,
  options,
  formik,
  style,
  isSearchable,
  defaultValue,
  innerRef,
  ...props
}) => {
  const handleChange = (selectedOption: OptionType | null) => {
    formik.setFieldValue(
      props.name,
      selectedOption ? selectedOption.value : null
    );
  };

  const handleBlur = () => {
    formik.setFieldTouched(props.name, true);
  };

  return (
    <Select
      {...props}
      onChange={handleChange}
      onBlur={handleBlur}
      options={options}
      value={options.find((option) => option.value === props.value)}
      theme={(theme) => ({ ...theme, borderRadius: 0 })}
      placeholder={label}
      isSearchable={isSearchable}
      styles={{
        ...style,
      }}
      openMenuOnFocus
      onFocus={() => {
        if (props.name === "phonePrefix" || props.name === "phoneSuffix") {
          formik.setFieldTouched("phonePrefix", false);
          formik.setFieldTouched("phoneSuffix", false);
        } else {
          formik.setFieldTouched(props.name, false);
        }
      }}
    />
  );
};

export default CustomSelect;
