import Link from 'next/link'
import { cn } from '@/lib/cn'
import Current from './CurrentV2'
import { Whishlist } from '@public/home'
import numberWithCommas from '@/lib/formatNumber'
import ImageShortcut from '@/components/Ui/Image'

const Calls = ({ calls }: any) => {
    return (
        <div className="space-y-9 pb-28 max-md:border-b max-md:border-gray-700 max-md:pb-[4.375rem]">
            <h2 className="text-headline font-medium max-md:text-callout">1 on 1 Calls</h2>

            <div className="grid grid-cols-3 gap-x-9 gap-y-7 max-md:grid-cols-2 max-sm:grid-cols-1">
                {calls.length > 0 ? (
                    calls.map((call: any) => (
                        <div
                            key={call.id}
                            className="flex h-full flex-col gap-6 rounded-lg border border-gray-700 p-4 pb-5 max-md:gap-5 max-sm:p-3 max-sm:pb-[1.125rem] md:max-w-[380px]">
                            <ImageShortcut
                                src={call.image}
                                height={216}
                                width={380}
                                className="w-full object-cover"
                                alt={call.name}
                            />

                            <div className="flex h-full flex-col justify-between gap-3">
                                <div className="space-y-4">
                                    <h5 className="text-b3 font-medium max-md:text-sub1">
                                        1 on 1 Call - <span className="capitalize">{call.name}</span>
                                    </h5>
                                    <p className="text-sub3 max-md:text-[14px] max-md:leading-5">
                                        {call.short_description}
                                    </p>
                                </div>

                                <div className="flex flex-col gap-3">
                                    <div className="flex justify-between">
                                        <div className="flex items-center gap-1">
                                            <svg
                                                width="26"
                                                height="25"
                                                viewBox="0 0 26 25"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M23.0709 12.5013C23.0709 18.1686 18.4713 22.7681 12.804 22.7681C7.13667 22.7681 2.53711 18.1686 2.53711 12.5013C2.53711 6.83394 7.13667 2.23438 12.804 2.23438C18.4713 2.23438 23.0709 6.83394 23.0709 12.5013Z"
                                                    stroke="#081228"
                                                    strokeWidth="1.54003"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                />
                                                <path
                                                    d="M16.6147 15.7673L13.4319 13.8679C12.8775 13.5394 12.4258 12.7488 12.4258 12.102V7.89258"
                                                    stroke="#081228"
                                                    strokeWidth="1.54003"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                />
                                            </svg>
                                            <p className="text-sub3 text-gray-900 max-md:text-[14px] max-md:leading-4">
                                                60 Minutes
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-[0.375rem]">
                                            <Whishlist />
                                            <p className="select-none text-sub3 max-md:text-[14px] max-md:leading-4">
                                                Wishlist
                                            </p>
                                        </div>
                                    </div>
                                    <div className={cn('flex items-end justify-between', call.sale && 'items-center')}>
                                        {call.sale ? (
                                            <div>
                                                <h5 className="flex items-center gap-1 text-callout font-medium max-md:text-sub2">
                                                    ${call.finalPrice - call.tax}{' '}
                                                    <span className="text-cap1 text-green-dark max-md:text-cap3">
                                                        (-{call.sale}%)
                                                    </span>
                                                </h5>
                                                <p className="text-sub2 text-gray-900 line-through max-md:text-sub3">
                                                    ${parseInt(call.price || '0') - call.tax}
                                                </p>
                                            </div>
                                        ) : (
                                            <p className="text-callout font-medium max-md:text-sub2">
                                                ${numberWithCommas(call.finalPrice - call.tax)}
                                            </p>
                                        )}

                                        <Link
                                            aria-label="Learn More"
                                            href={`/mentorships/${call.slug}`}
                                            className="text-sub3 font-semibold underline-offset-2 hover:underline max-md:text-[14px] max-md:leading-4">
                                            Learn More &gt;
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <Current {...call} />
                            </div>
                        </div>
                    ))
                ) : (
                    <p>No Calls available</p>
                )}
            </div>
        </div>
    )
}

export default Calls
