import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import yellowbutton from '../../../../../public/icons/yellowbutton.svg'
import devprogramvideo from '../../../../../public/icons/devprogramvideo.svg'
import yellowarrow from '../../../../../public/icons/yellowarrow.svg'
import watchvideotext from '../../../../../public/icons/watchvideotext.svg'
import enrollnowbutton from '../../../../../public/icons/enrollnowbutton.svg'
import profiledev from '../../../../../public/icons/profiledev.svg'
import Check from '../../../../../public/icons/Check.svg'
import celo from '../../../../../public/icons/celo.svg'
import xrp from '../../../../../public/icons/xrp.svg'
import eth from '../../../../../public/icons/Check.svg'
import hedera from '../../../../../public/icons/hedera.svg'
import ICP from '../../../../../public/icons/ICP.svg'


const DevProgram = () => {
  return (
      <>
          <div className="w-full bg-[#262216]">
              <div className=" mx-auto grid max-w-[400px] py-10 sm:max-w-[700px] md:max-w-[1400px]">
                  <div>
                      <h2 className="w-[776px] text-[50px] font-[600] text-white">The Africa Web3 Dev Program</h2>
                      <div className="flex justify-between">
                          <h3 className="text-[48px] font-[500] leading-[72px] text-green">
                              $1.3 Million{' '}
                              <span className="text-[28px] font-[500] leading-[42px] text-white ">
                                  in salaries paid
                              </span>
                          </h3>
                          <section className="flex gap-4">
                              <Image src={yellowarrow} alt="devProgram" className="pt-5" width={40.12} height={82.79} />
                              <Image src={watchvideotext} alt="devProgram" width={206} height={62} />
                          </section>
                      </div>
                  </div>
                  <section className="flex justify-between">
                      <div className="">
                          <section className="my-4">
                              <Image src={profiledev} alt="devProgram" width={452} height={110} />
                          </section>

                          {/* <section className="relative my-4 rounded-md border border-[#FCC229] p-2 ">
                          <h3 className="text-[24px] font-[600] text-[#BCCAFF]">Lead Dev</h3>
                          <h3 className="text-[32px] font-[600] text-white">Olu Akinwande </h3>
                          <h3 className="text-[18px] font-[400] text-[#D0D0DD]">Founder Of Montech Studios INC</h3>
                      </section> */}
                          <section className="my-3">
                              <h3 className="text-[24px] font-[600] text-[#BCCAFF]">Mission</h3>
                              <h3 className="text-[32px] font-[600] text-white">Converting web2 devs into web3 devs</h3>
                          </section>
                          <Link href="/dev-program" className="my-2">
                              <Image src={enrollnowbutton} alt="devProgram" width={385} height={68} />
                          </Link>
                      </div>
                      <div className="">
                          <Image src={devprogramvideo} alt="devProgram" width={641} height={361} />
                      </div>
                  </section>
              </div>
          </div>
          <div>
            <section className='grid md:grid-cols-5'>
                    <Image src={celo} alt="celo" width={157} height={58.59} />
                    <Image src={xrp} alt="celo" width={157} height={58.59} />
                    <Image src={eth} alt="celo" width={157} height={58.59} />
                    <Image src={hedera} alt="celo" width={157} height={58.59} />
                    <Image src={ICP} alt="celo" width={157} height={58.59} />
            </section>
              <h2 className="text-[36px] font-[600]">Get Ready to Start</h2>
              <p className="text-[20px] font-[400]">
                  We offer a range of support, including workshops, training, networking events, and access to funding
                  opportunities. Our program is very intense. To be prepared and make the most out of the four weeks,
                  you will:
              </p>
              <ul className="my-3 grid gap-2 text-left text-[13.62px] text-white md:my-7 md:grid-cols-2 md:text-[16px]">
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Be part of a community of like-minded individuals
                  </li>
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Have the opportunity to work on exciting projects
                  </li>
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Share your knowledge and skills
                  </li>
                  <li className="flex gap-2">
                      {' '}
                      <Image src={Check} width={20} height={20} alt="Check" className="mb-1" />
                      Contribute to the development of the blockchain ecosystem
                  </li>
              </ul>
              <h3 className='text-[36px] text-[#2655FF] underline my-3'>Build your network with top tech companies. Connect with our <span className='text-[24px] font-[700]'>15,000 alumni + hiring partners.</span>15,000 alumni + hiring partners.</h3>
          </div>
      </>
  )
}

export default DevProgram
