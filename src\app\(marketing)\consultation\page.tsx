import Mentorship from '@/components/Pages/Products/Mentorship'
import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'
import { MentorshipModel } from '@/types/MentorshipModel'

export const metadata = {
    title: 'Consultation',
    description:
        'Learn about trading and investing in Cryptocurrencies, Altcoins, Top Crypto Exchanges, Indicators. Learn how to Trade BTC, ETH and other cryptocurrencies.',
    keywords: ['Consultation', 'Crypto University', 'Cryptocurrency', 'Bitcoin', 'Ethereum', 'Trading', 'Investing'],
}

const GetCalls = async () => {
    try {
        const response = await fetchInstance('/mentorship/all/published/coaching', {
            next: { revalidate: 10 },
        })
        return response.mentorships
    } catch (error) {
        console.error('Error Calls:', error)
        return error
    }
}
const GetConsultation = async () => {
    try {
        const response = await fetchInstance('/mentorship/all/published/not-coaching', {
            next: { revalidate: 10 },
        })
        return response.mentorships
    } catch (error) {
        console.error('Error Consultation:', error)
        return error
    }
}

const Consultation = async () => {
    const calls: MentorshipModel[] = await GetCalls()
    const consultation: MentorshipModel[] = await GetConsultation()

    return (
        <section className="flex w-full flex-col text-black">
            <Mentorship calls={calls} consultation={consultation} />
            <CTA />
        </section>
    )
}

export default Consultation
