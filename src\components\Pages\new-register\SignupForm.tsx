"use client"
import { useState } from 'react'
import { FcGoogle } from 'react-icons/fc'
import { <PERSON>a<PERSON><PERSON>, FaEnvelope, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEyeSlash } from 'react-icons/fa'
import { MdOutlineKeyboardArrowRight } from 'react-icons/md'
import { FaRegUser } from 'react-icons/fa'
import { MdOutlineMail } from 'react-icons/md'
import { CiLock } from 'react-icons/ci'

interface SignUpFormProps {
    userData: {
        name: string;
        email: string;
        password: string;
    };
    errors: {
        name: string;
        email: string;
        password: string;
    };
    passwordStrength: number;
    onInputChange: (name: string, value: string) => void;
    onContinue: () => void;
}

export default function SignUpForm({ userData, errors, passwordStrength, onInputChange, onContinue }: SignUpFormProps) {
    const [showPassword, setShowPassword] = useState(false)
    const [passwordTouched, setPasswordTouched] = useState(false)
    

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onContinue();
    };

    return (
        <div className="mx-auto w-full max-w-md sm:rounded-[21.6px] bg-white p-4 sm:p-8 sm:shadow-md">
            <h1 className="mb-6 text-center text-[20px] font-bold text-[#081228]">Start your crypto journey today!</h1>

            <button className="mb-4 flex w-full items-center justify-center rounded-full border border-[#AAAAAA] bg-white px-4 py-2 text-[16.2px] font-[500] text-[#081228] transition duration-300 hover:bg-gray-50">
                <FcGoogle className="mr-2" size={20} />
                Sign up with Google
            </button>

            <div className="relative my-6">
                <hr className="border-[#AAAAAA]" />
                <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform bg-white px-[0.5px] text-[12px] sm:text-[14px] font-[500] text-[#081228] ">
                    Or, Sign up with your email
                </span>
            </div>

            <form onSubmit={handleSubmit}>
                <div>
                    <h2 className="mb-2 text-[14px] font-[400]">
                        Your Name<span className="text-[#FC0019]">*</span>
                    </h2>
                    <div className="relative mb-4">
                        <FaRegUser className="absolute left-3 top-3 text-[#081228]" />
                        <input
                            type="text"
                            value={userData.name}
                            onChange={e => onInputChange('name', e.target.value)}
                            placeholder="e.g John Smith"
                            className={`w-full rounded-full border py-2 pl-10 pr-3 text-[14px] focus:outline-none focus:ring-2 ${
                                errors.name
                                    ? 'border-[#FC0019]'
                                    : userData.name
                                    ? 'border-[#00BF77]'
                                    : 'border-[#AAAAAA]'
                            }`}
                            required
                        />
                        {errors.name && <p className="text-xs mt-1 text-[#FC0019]">{errors.name}</p>}
                    </div>
                </div>

                <div>
                    <h2 className="mb-2 text-[14px] font-[400]">
                        Email<span className="text-[#FC0019]">*</span>
                    </h2>
                    <div className="relative mb-4">
                        <MdOutlineMail className="absolute left-3 top-3 text-[#081228]" />
                        <input
                            type="email"
                            value={userData.email}
                            onChange={e => onInputChange('email', e.target.value)}
                            placeholder="<EMAIL>"
                            className={`w-full rounded-full border py-2 pl-10 pr-3 text-[14px] focus:outline-none focus:ring-2 ${
                                errors.email
                                    ? 'border-[#FC0019]'
                                    : userData.email
                                    ? 'border-[#00BF77]'
                                    : 'border-[#AAAAAA]'
                            }`}
                            required
                        />
                        {errors.email && (
                            <div className="mt-1 rounded-md bg-[#FFEBEE] p-2 text-[11.7px] text-[#FC0019]">
                                {errors.email}
                            </div>
                        )}
                    </div>
                </div>

                <div>
                    <h2 className="mb-2 text-[14px] font-[400]">
                        Password<span className="text-[#FC0019]">*</span>
                    </h2>
                    <div className="relative mb-4">
                        <CiLock className="absolute left-3 top-2 h-5 w-5 text-[#000]" />
                        <input
                            type={showPassword ? 'text' : 'password'}
                            value={userData.password}
                            onChange={e => {
                                onInputChange('password', e.target.value)
                                if (!passwordTouched) setPasswordTouched(true)
                            }}
                            placeholder="enter your password"
                            className={`w-full rounded-full border py-2 pl-10 pr-10 text-[14px] focus:outline-none focus:ring-2 ${
                                errors.password
                                    ? 'border-[#FC0019]'
                                    : userData.password.length >= 8
                                    ? 'border-[#00BF77]'
                                    : 'border-[#AAAAAA]'
                            }`}
                            required
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-2.5 text-[#AAAAAA]">
                            {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
                        </button>
                    </div>

                    {passwordTouched && (
                        <>
                            <div className="mb-4 flex justify-between text-[11.7px] font-[500] text-[#929292]">
                                <span>Password Strength:</span>
                                <span
                                    className={`font-[500] ${
                                        passwordStrength === 1
                                            ? 'text-[#FC0019]'
                                            : passwordStrength === 2
                                            ? 'text-[#FF7F00]'
                                            : passwordStrength === 3
                                            ? 'text-[#FFDF00]'
                                            : passwordStrength === 4
                                            ? 'text-[#00BF77]'
                                            : passwordStrength === 5
                                            ? 'text-[#00BF77]'
                                            : 'text-[#FC0019]'
                                    }`}>
                                    {passwordStrength === 1
                                        ? 'Weak'
                                        : passwordStrength === 2
                                        ? 'Fair'
                                        : passwordStrength === 3
                                        ? 'Good'
                                        : passwordStrength === 4
                                        ? 'Strong'
                                        : passwordStrength === 5
                                        ? 'Very Strong'
                                        : 'Very Weak'}
                                </span>
                            </div>
                            <div className="mb-4 h-2 w-full rounded-full bg-gray-200">
                                <div
                                    className={`h-full rounded-full ${
                                        passwordStrength === 1
                                            ? 'bg-[#FC0019]'
                                            : passwordStrength === 2
                                            ? 'bg-[#FF7F00]'
                                            : passwordStrength === 3
                                            ? 'bg-[#FFDF00]'
                                            : passwordStrength === 4
                                            ? 'bg-[#00BF77]'
                                            : passwordStrength === 5
                                            ? 'bg-[#00BF77]'
                                            : ''
                                    }`}
                                    style={{ width: `${passwordStrength * 20}%` }}></div>
                            </div>
                        </>
                    )}

                    {errors.password ? (
                        <div className="mb-4 rounded-md bg-[#FFEBEE] p-2 text-[11.7px] text-[#FC0019]">
                            {errors.password}
                        </div>
                    ) : (
                        <p className="mb-4 text-[10px] text-[#929292]">
                            Minimum 8 characters, mix of letters, numbers, and symbols
                        </p>
                    )}

                    <p className="mb-4 text-[10px] text-[#929292]">
                        By signing up, you agree to our{' '}
                        <a href="#" className="text-[#2655FF] hover:underline">
                            Terms of Use
                        </a>{' '}
                        and{' '}
                        <a href="#" className="text-[#2655FF] hover:underline">
                            Privacy Policy
                        </a>
                        .
                    </p>

                    <button
                        type="submit"
                        className="flex w-full items-center justify-center rounded-full bg-[#2655FF] py-2 text-[14px] font-[500] text-white transition duration-300">
                        Continue <MdOutlineKeyboardArrowRight className="h-4 w-4 font-[600]" />
                    </button>
                </div>

                <p className="mt-6 text-center text-[12px] font-[500]  text-[#929292]">
                    Already have an account?{' '}
                    <a href="#" className="font-[600] text-[#2655FF] hover:underline ">
                        Sign in now
                    </a>
                </p>
            </form>
        </div>
    )
}
