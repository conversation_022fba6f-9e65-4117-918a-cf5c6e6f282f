'use client'
import { useState } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import { cn } from '@/lib/cn'
import TransactionModel from '@/types/TransactionModel'
import OrderModel from './OrderModel'

interface Props {
    transaction: TransactionModel
    tax: number
}

const OrderTemplate = ({ transaction, tax }: Props) => {
    const [openModel, setOpenModel] = useState(false)
    return (
        <>
            <div className={cn('absolute inset-0 top-0 z-50 flex justify-center', openModel ? '' : 'hidden')}>
                <OrderModel tax={tax} setOpenModel={setOpenModel} openModel={openModel} product={transaction} />
            </div>
            <div className="grid grid-cols-12 place-content-center place-self-center border-b border-gray-400 pb-4 text-cap1 font-medium capitalize max-md:hidden">
                <div className="col-span-3 flex items-center gap-2">
                    <ImageShortcut
                        src={
                            transaction.courses
                                ? transaction.courses?.image
                                : transaction.bundles
                                ? transaction.bundles?.image
                                : transaction.indicators
                                ? transaction.indicators?.image
                                : transaction.mentorships
                                ? transaction.mentorships?.image
                                : ''
                        }
                        alt="product"
                        width={40}
                        height={40}
                        className="object-cover"
                    />
                    <p>
                        {transaction.courses
                            ? transaction.courses?.name
                            : transaction.bundles
                            ? transaction.bundles.name
                            : transaction.indicators
                            ? transaction.indicators.title
                            : transaction.mentorships
                            ? transaction.mentorships.name
                            : ''}
                    </p>
                </div>
                <p className="col-span-2 flex items-center">{transaction.tx_hash}</p>
                <p className="col-span-2 flex items-center">{transaction.created_at.split('T')[0]}</p>
                <p
                    className={cn(
                        'col-span-2 flex items-center text-cap1',
                        transaction.status == 'pending'
                            ? 'text-yellow'
                            : transaction.status == 'succeeded'
                            ? 'text-green-dark'
                            : 'text-red'
                    )}>
                    {transaction.status}
                </p>
                <p className="col-span-2 flex items-center ">${transaction.amount}</p>
                <p
                    onClick={() => setOpenModel(true)}
                    className="col-span-1 flex cursor-pointer items-center text-sub3 font-semibold text-blue underline">
                    View Order
                </p>
            </div>
            <div className="flex w-full flex-col gap-4 rounded-md p-4 shadow-md md:hidden">
                <div className="flex items-center gap-3">
                    <ImageShortcut
                        src={
                            transaction.courses
                                ? transaction.courses?.image
                                : transaction.bundles
                                ? transaction.bundles?.image
                                : transaction.indicators
                                ? transaction.indicators?.image
                                : transaction.mentorships
                                ? transaction.mentorships?.image
                                : ''
                        }
                        alt="product"
                        width={62}
                        height={62}
                        className="object-cover"
                    />
                    <div className="flex flex-col">
                        <p className="text-cap1 font-medium capitalize">
                            {transaction.courses
                                ? transaction.courses?.name
                                : transaction.bundles
                                ? transaction.bundles.name
                                : transaction.indicators
                                ? transaction.indicators.title
                                : transaction.mentorships
                                ? transaction.mentorships.name
                                : ''}
                        </p>
                        <p className="text-cap2 text-gray-700">Order ID: {transaction.tx_hash}</p>
                    </div>
                </div>
                <div className="border-b border-gray-300" />
                <div className="flex flex-col gap-2">
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Date</p>
                        <p className="font-medium">{transaction.created_at.split('T')[0]}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Total Amount</p>
                        <p className="font-medium">{transaction.amount}</p>
                    </div>
                    <div className="flex items-center justify-between text-cap2">
                        <p className=" text-gray-700">Status</p>
                        <p
                            className={cn(
                                'col-span-2 flex items-center text-cap2',
                                transaction.status == 'pending'
                                    ? 'text-yellow'
                                    : transaction.status == 'succeeded'
                                    ? 'text-green-dark'
                                    : 'text-red'
                            )}>
                            {transaction.status}
                        </p>
                    </div>
                </div>
                <p
                    onClick={() => setOpenModel(true)}
                    className="col-span-1 flex cursor-pointer items-center justify-center text-sub3 font-semibold text-blue underline">
                    View Order
                </p>
            </div>
        </>
    )
}

export default OrderTemplate
