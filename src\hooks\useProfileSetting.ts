import axios from "@/lib/axios"
import { useState } from "react"

const useProfileSetting = (token: string) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const reset = () => {
    setError("");
    setIsLoading(false);
  };

  const updateProfile = async (data: any) => {
    setError("");
    setIsLoading(true);

    try {
      const res = await axios.patch("/auth/profile", data, {
        headers: {
          authorization: `Bearer ${token}`,
        },
      });

      return res;
    } catch (error: any) {
      console.error({ error });
      setError(error.response.data.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };


  return {
    error,
    reset,
    isLoading,
    updateProfile,
  };
};

export default useProfileSetting;
