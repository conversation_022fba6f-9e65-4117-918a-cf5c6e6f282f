import ImageShortcut from "@/components/Ui/Image";
import HeroImage from "public/join/hero.png";

const Hero = () => {
  return (
    <div className="flex flex-col w-full text-[#f3f3f3] bg-black/80">
      <div className="container mx-auto pt-[3.25rem] max-md:pt-44 pb-[6.625rem] relative max-[768px]:overflow-hidden">
        <ImageShortcut
          src={HeroImage}
          width={719}
          height={571}
          priority
          alt="Hero Image"
          className="absolute top-0 right-0 z-10 w-[719px] h-[571px] object-cover 
                    max-[768px]:w-[354px] max-[768px]:h-[354px] max-[768px]:-right-5 
                    max-[768px]:-top-[60px] max-[1100px]:w-[415px] max-[1100px]:h-[415px] 
                    max-[768px]:block"
        />

        <div className="flex flex-col gap-4 max-w-[633px] max-md:max-w-full">
          <h1 className="font-semibold z-20 text-h3 leading-[72px] max-md:text-b1">
            Join the Crypto <br />
            University Community
          </h1>
          <p className="text-sub3 z-20 max-md:text-[14px] leading-[150%] max-w-[550px] max-md:max-w-full">
            Crypto University is more than a crypto ecosystem. It&apos;s a
            vibrant global community powered by CU users from all walks of life,
            who come together both in real life and online to pursue their
            passions and advance the crypto cause.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Hero;
