"use client"

import CertificateGenerator from "@/components/Pages/Certificates/CertificateGenerator"
import DownloadButton from "@/components/Pages/Certificates/DownloadButton"

const AboutPage = () => {

    const handleDownload = () => {
        // Call a function to handle downloading the certificate
    };


    return (
        <div>
       <div>
            {/* <DownloadButton studentName="Chisomo Wisiki" courseName="Web3 MasterClass Course" completionDate="23 June, 2024" /> */}
            <CertificateGenerator studentName="Chisomo Wisiki" courseName="Web3 MasterClass Course" completionDate="23 June, 2024" />
        </div>
    </div>
    )
}

export default AboutPage


// "use client"
// import { useState } from 'react';
// import html2canvas from 'html2canvas';

// const CertificatePage = () => {
//     const [imageGenerated, setImageGenerated] = useState(false);

//     const generateImage = () => {
//         // Select the element containing your content
//         const element:any = document.getElementById('content');

//         // Use html2canvas to convert the content to an image
//         html2canvas(element).then(canvas => {
//             // Convert the canvas to a base64 image data
//             const imgData = canvas.toDataURL('image/png');

//             // Create a temporary link element
//             const link = document.createElement('a');
//             link.href = imgData;
//             link.download = 'certificate.png'; // Set the filename here

//             // Programmatically click the link to trigger download
//             document.body.appendChild(link);
//             link.click();

//             // Clean up
//             document.body.removeChild(link);

//             // Update state to indicate image generation
//             setImageGenerated(true);
//         });
//     };

//     return (
//         <div>
//             <div id="content" style={{ backgroundImage: "url('/certificates/CryptoUCertificateTemplate.jpg')", width: '100%', height: '100vh', backgroundSize: 'cover' }}>
//                 <h1 style={{ color: '#3A56FA', fontWeight: 'bold', fontSize: '40px', textAlign: 'center', paddingTop: '36vh' }}>Chisomo Wisiki</h1>
//                 <h1 style={{ color: '#000', fontWeight: 'bold', fontSize: '28px', textAlign: 'center', paddingTop: '10vh' }}>Web3 MasterClass Course</h1>
//                 <h1 style={{ color: '#000', fontWeight: 'bold', fontSize: '28px', textAlign: 'center', paddingTop: '10vh' }}>23 June, 2024</h1>
//             </div>
//             {!imageGenerated && <button onClick={generateImage}>Generate Image</button>}
//             {imageGenerated && <p>Image Generated!</p>}
//         </div>
//     )
// }

// export default CertificatePage;

