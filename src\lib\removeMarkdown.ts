// Remove makdown from string and return plain text, also remove * and [&hellip;] and [&nbsp;] if found. Also change &#8217; to ' and &#8220; to ". Also remove brackets [] and () if found.

export const removeMarkdown = (text: string) => {
    return text
        .replace(/<[^>]*>?/gm, '')
        .replace(/&hellip;/g, '')
        .replace(/&nbsp;/g, '')
        .replace(/&#8217;/g, "'")
        .replace(/&#8220;/g, '"')
        .replace(/&#8221;/g, '"')
        .replace(/&#8211;/g, '-')
        .replace(/&#8212;/g, '-')
        .replace(/&#8230;/g, '...')
        .replace(/&#8216;/g, "'")
        .replace(/&#8218;/g, "'")
        .replace(/&#8222;/g, '"')
        .replace(/&#8226;/g, '*')
        .replace(/&#8219;/g, "'")
        .replace(/&#8242;/g, "'")
        .replace(/&#8243;/g, '"')
        .replace(/&#8250;/g, '>')
        .replace(/&#8249;/g, '<')
        .replace(/\[.*?\]/g, '')
        .replace(/\(.*?\)/g, '')
        .replace(/\s\s+/g, ' ')
        .replace(/\n/g, ' ')
        .replace(/\t/g, ' ')
        .replace(/\r/g, ' ')
        .replace(/\*/g, '')
        .trim();
}   