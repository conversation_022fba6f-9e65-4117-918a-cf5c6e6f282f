import { cn } from '@/lib/cn';
import { FC } from 'react'

interface LabelProps {
    children: React.ReactNode;
    required?: boolean;
    uppercase?: boolean;
    className?: string;
}

const Label: FC<LabelProps> = ({ children, required, uppercase = true , className }) => {
    return (
        <p className={cn('text-sub3 select-none font-normal', (uppercase ? 'uppercase' : 'normal-case'), className)}>
            {children}
            {required ? <span className='text-[#FC0019]'>*</span> : null}
        </p>
    );
}

export default Label;