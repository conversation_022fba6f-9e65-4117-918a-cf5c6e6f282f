import Link from 'next/link'
import Button from '@/components/Ui/Button'
import ImageShortcut from '@/components/Ui/Image'

const Earn = () => {
    return (
        <section id='earn' className="w-full bg-yellow-light text-black">
            <div className="container mx-auto bg-yellow-light">
                <div className="flex items-center gap-3 max-md:flex-col max-md:gap-2 max-md:pb-8 ">
                    <div className="flex items-center gap-3">
                        <ImageShortcut
                            src={'/affiliate/earn1.png'}
                            alt="Earn"
                            width={345}
                            height={345}
                            className="object-contain max-md:h-[150px] max-md:w-[150px]"
                        />
                        <ImageShortcut
                            src={'/affiliate/earn2.png'}
                            alt="Earn"
                            width={345}
                            height={345}
                            className="-ml-48 object-contain max-md:-ml-20 max-md:h-[150px] max-md:w-[150px]"
                        />
                    </div>
                    <div className="flex flex-col items-start gap-8 max-md:items-center max-md:gap-6">
                        <h1 className="text-headline font-medium capitalize max-md:text-center max-md:text-b3">
                            Affiliates receive 30% of sales, and <br className="max-md:hidden" /> partners can earn up
                            to 35%
                        </h1>
                        <div className=" w-full max-w-[261px] max-md:max-w-full">
                            <Link href={'/dashboard/affiliate'}>
                                <Button className="" rounded variant="primary">
                                    Start Earning {'>'}
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Earn
