'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import { Video } from '@public/home'
import React, { useState } from 'react'
import Image from '@/components/Ui/Image'
import Button from '@/components/Ui/Button'
import { MentorshipModel } from '@/types/MentorshipModel'
import ProgressBar from '@/components/Ui/ProgressBar'

interface props {
    mentorship: MentorshipModel
}

const CardConsultation = ({ mentorship }: props) => {
    const [onCLick, setOnClick] = useState(false)
    function capitalizeMentorshipName(mentorshipName: string) {
        return mentorshipName.replace(/\b\w/g, function (match) {
            return match.toUpperCase()
        })
    }
    return (
        <div
            className={`flex h-[300px] sm:h-[250px] w-[260px] min-w-[260px] max-w-[260px] cursor-pointer flex-col gap-6 overflow-hidden rounded-lg border border-gray-700 transition-all max-md:w-full max-md:min-w-full max-md:max-w-[100%]  ${onCLick ? 'h-full justify-between pb-2 ' : ''
                }`}>
            <div
                onClick={() => setOnClick(!onCLick)}
                className={cn(`flex flex-col gap-3 `, !onCLick ? 'pb-2' : 'pb-0')}>
                <div className="relative">
                    <Image
                        src={mentorship.image}
                        width={207}
                        height={240}
                        className="h-[135px] w-full select-none rounded-lg object-cover  max-md:h-[154px]"
                        alt={mentorship.name}
                    />

                </div>
                <div className="flex flex-col gap-3 px-3">
                    <h2 className="select-none text-sub3 font-medium">
                        {capitalizeMentorshipName(mentorship.name).length > 42 ? (
                            <span>{capitalizeMentorshipName(mentorship.name).substring(0, 42)}...</span>
                        ) : (
                            <span>{capitalizeMentorshipName(mentorship.name)}</span>
                        )}
                    </h2>
                    <div className="flex items-center gap-2">
                        <p className="select-none font-bold text-[#38761d] font-sans text-cap1">
                            Price: ${mentorship.price}
                        </p>

                    </div>
                    <div className="flex items-center gap-2">
                        <p className="select-none font-sans text-cap1">
                            {mentorship.transactions.length == 0 ? 'N/A' : ( `Purchased on: ${mentorship.transactions[0].created_at.split('T')[0]}`)}
                        </p>
                    </div>

                </div>
            </div>
        </div>
    )
}

export default CardConsultation
