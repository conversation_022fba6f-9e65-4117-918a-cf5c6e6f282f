import Subscriptions from '@/components/Pages/Subscriptions/Subscriptions'
import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'

export const metadata = {
    title: 'Subscriptions',
    description:
        "Crypto University is the world's #1 Cryptocurrency education platform. Get access to our customized courses to help you succeed in crypto.",
    keywords: ['Subscriptions', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const GetSubscriptions = async () => {
    try {
        const response = await fetchInstance('/subscription/all/published', {
            next: { revalidate: 10 },
        })
        return response.subscription
    } catch (error) {
        console.error('Error Calls:', error)
        return error
    }
}

const SubscriptionsPage = async () => {
    const subscriptions = await GetSubscriptions()
    return (
        <section className="flex w-full flex-col text-black">
            <Subscriptions subscriptions={subscriptions} />
            <CTA />
        </section>
    )
}

export default SubscriptionsPage
