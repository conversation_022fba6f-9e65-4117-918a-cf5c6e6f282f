// hooks/useMultiStepForm.ts
import { useState } from "react";
import { FormikErrors, FormikValues } from "formik";
import { Yup, useFormik } from "@/lib/formik";
import useAuth from "./useAuth";

type ValidationSchemaKeys =
  | "name"
  | "fullname"
  | "email"
  | "phoneSuffix"
  | "phonePrefix"
  | "country"
  | "password";

const validationSchemas = [
  Yup.object({
    name: Yup.string().required("Name is required"),
  }),
  Yup.object({
    fullname: Yup.string()
      .required("Name is required")
      .matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)+$/, "Put your full name")
  }),
  Yup.object({
    email: Yup.string()
      .required("Email is required")
      .matches(
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Invalid Email"
      ),
  }),
  Yup.object({
    country: Yup.string().required("Country is required"),
  }),
  Yup.object({
    phoneSuffix: Yup.string()
      .required("Country code is required")
      .matches(/((\+\d{1,2}\s?)?1?\-?\.?\s?\(?\d{3}\)?[\s.-]?)?\d{3}[\s.-]?\d{4}/, "Invalid Phone Number"),
    phonePrefix: Yup.string().required("Phone number is required"),
  }),
  Yup.object({
    password: Yup.string()
      .required("New Password is required")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/,
        "Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character"
      )
      .min(8, "New Password must be at least 8 characters"),
  }),
];

const arr: Array<ValidationSchemaKeys> = [
  "name",
  "fullname",
  "email",
  "country",
  "phonePrefix",
  "phoneSuffix",
  "password",
];
export default function useMultistepForm() {
  const [step, setStep] = useState(-1);
  const [formData, setFormData] = useState<FormikValues>({});
  const { register, error, reset, isLoading } = useAuth();

  const initialValues = {
    name: "",
    fullname: "",
    email: "",
    country: "",
    phonePrefix: "",
    phoneSuffix: "",
    password: "",
    ...formData,
  };

  const validationSchema = validationSchemas[step];

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, helpers) => {
      setFormData((prevData) => ({ ...prevData, ...values }));

      if (step < validationSchemas.length - 1) {
        goNext();
      } else {
        const resp = await register({
          display_name: formData.name,
          fullname: formData.fullname,
          email: formData.email,
          code: formData.phonePrefix,
          phone: formData.phoneSuffix,
          country: formData.country,
          password: formik.values.password, //here we are using formik.values.password because the value didnt setted in the state yet
        },"");
        if (resp?.ok) {
          goNext();
        }
      }
    },
    initialErrors: {
      name: "",
      fullname: "",
      country: "",
      phonePrefix: "",
      phoneSuffix: "",
      email: "",
      password: "",
    },
  });

  const goNext = () => {
    if (step < 6) setStep(step + 1);
  };
  const goBack = () => {
    if (step > 0) setStep(step - 1);
  };
  const goTo = (index: number) => {
    if (index >= 0 || index <= 5) setStep(index);
  };

  const showError = (index: number, ref: React.RefObject<HTMLInputElement>) => {
    const keys = Object.keys(formik.values);


    if (index == 4) {
      if (ref.current) {
        ref.current.onfocus = () => {
          formik.setFieldTouched('phoneSuffix', false);
        };
      }

      if (
        formik.touched.phoneSuffix &&
        ((formik.errors["phonePrefix"] &&
          formik.errors["phonePrefix"].length > 0) ||
          (formik.errors["phoneSuffix"] &&
            formik.errors["phoneSuffix"].length > 0))
      ) {
        return true;
      } else {
        return false;
      }
    }

    if (index > 4) {
      const key = keys[index + 1];
      if (ref.current) {
        ref.current.onfocus = () => {
          formik.setFieldTouched(key, false);
        };
      }

      return (
        index === step &&
        formik.touched[arr[index + 1]] &&
        keys.includes(key) &&
        (formik.errors as { [key: string]: string })[key]?.length > 0 &&
        !!(formik.errors as { [key: string]: string })[key]

      );
    } else {
      const key = keys[index];

      if (ref.current) {
        ref.current.onfocus = () => {
          formik.setFieldTouched(key, false);
        };
      }

      return (
        index === step &&
        formik.touched[arr[index]] &&
        keys.includes(key) &&
        (formik.errors as { [key: string]: string })[key]?.length > 0 &&
        !!(formik.errors as { [key: string]: string })[key]
      );
    }
  };

  const disableSubmit = (index: number) => {
    const errors = formik.errors as FormikErrors<{
      name: string;
      fullname: string;
      country: string;
      phonePrefix: string;
      phoneSuffix: string;
      email: string;
      password: string;
    }>;

    if (index == 4) {
      if (
        step === index &&
        ((errors["phonePrefix"] && errors["phonePrefix"]!.length > 0) ||
          formik.values["phonePrefix"] === "" ||
          (errors["phoneSuffix"] && errors["phoneSuffix"]!.length > 0) ||
          formik.values["phoneSuffix"] === "")
      ) {
        return true;
      } else {
        return false;
      }
    }

    if (
      step > 4 &&
      step === index &&
      ((errors[arr[index + 1]] && errors[arr[index + 1]]!.length > 0) ||
        formik.values[arr[index + 1]] === "")
    ) {
      return true;
    }
    if (
      step === index &&
      ((errors[arr[index]] && errors[arr[index]]!.length > 0) ||
        formik.values[arr[index]] === "")
    ) {
      return true;
    }
    return false;
  };

  return {
    step,
    goTo,
    reset,
    formik,
    goNext,
    goBack,
    registerError: error,
    isLoading,
    showError,
    disableSubmit,
    nbOfSteps: validationSchemas.length,
  };
}
