import CTA from '@/components/Ui/CTA'
import fetchInstance from '@/lib/fetch'
import Courses from '@/components/Pages/Courses/Courses'

export const metadata = {
    title: 'Courses',
    description:
        "Crypto University is the world's #1 Cryptocurrency education platform. Get access to our customized courses to help you succeed in crypto.",
    keywords: ['Courses', 'Crypto University', 'Crypto U', 'Crypto', 'Blockchain', 'Cryptocurrency'],
}

const GetCourses = async () => {
    try {
        const response = await fetchInstance('/website-home-page/top-courses', {
            next: { revalidate: 10 },
        })
        return response.popularCourses
        // const response = await fetchInstance('/course/all/public', {
        //     next: { revalidate: 10 },
        // })
        // return response.courses
    } catch (error) {
        console.error('Error Calls:', error)
        return error
    }
}


const CoursesPage = async () => {
    const courses = await GetCourses()
    return (
        <section className="flex w-full flex-col text-black">
            <Courses courses={courses} />
            <CTA />
        </section>
    )
}

export default CoursesPage
