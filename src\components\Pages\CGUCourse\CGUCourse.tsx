
"use client"
import { Search } from "@public/global";
import ImageShortcut from "@/components/Ui/Image";
import Link from "next/link";
import { Disclosure, Transition } from '@/lib/headlessui'
import PortableText from '@/components/Ui/portable'
import { cn } from "@/lib/cn";

const CGUCourse = () => {
  const headerDetails = [
    {
      image: "/cgu/man-climbing-stairs2x.png",
      title: 'Beginner Friendly',
      description: 'I have learned so much and refined so much of my knowledge about Bitcoin and Altcoins…and just being a more intentional investor than others'
    },
    {
      image: "/cgu/reading-book2x.png",
      title: 'Learn at your pace',
      description: 'We have endless amount of content. You pick and choose which area to start with based on your goals or prior experience. You can also refer back to our lessons whenever faced with a challenge.'
    },
    {
      image: "/cgu/chat-bubble2x.png",
      title: 'Beginner Friendly',
      description: 'You’ll be part of our <a href="https://discord.gg/M9cwwCP49c" className="text-yellow">Discord</a> server where you can ask questions to experts and other learners.'
    }
  ]
  const headerItems = [
    {
      title: 'Understand what blockchain is and what problems it solves.'
    },
    {
      title: 'Understand what Bitcoin is.'
    },
    {
      title: 'Demonstrate an understanding of wallets vs exchanges.'
    }
    ,
    {
      title: 'Define Defi'
    }
    ,
    {
      title: 'Define NFTs and Identify the types of NFTs'
    }
  ]
  const courseOutline = [
    {
      title: "01 - Introduction",
      content: "<ul className=\"list-disc\"><li>Introduction to CGU course</li></ul>"
    },
    {
      title: "02 - Fundamentals",
      content: "<ul className=\"list-disc\"><li>Intro to Blockchain</li><li>Identifying the Problem</li><li>What is Blockchain</li><li>What is Ethereum</li><li>Block Explorers</li><li>Blockchain use cases</li><li>What is Bitcoin?</li><li>Altcoins</li><li>Stablecoins</li><li>How to buy Bitcoin</li></ul>"

    },
    {
      title: "03 - Wallets",
      content: "<ul className=\"list-disc\"><li>Hot vs Cold wallets</li><li>Metamask – How to set up Metamask</li><li>How to add other blockchains on Metamask</li><li>Ledger nano tutorial</li></ul>"

    },
    {
      title: "04 - Exchanges",
      content: "<ul className=\"list-disc\"><li>Binance</li><li>Paxful overview</li><li>How to buy on Paxful</li><li>How to create a trade or offer on Paxful</li></ul>"

    },
    {
      title: "05 - Defi",
      content: "<ul className=\"list-disc\"><li>Defi Introduction</li><li>Decentralized exchanges</li><li>Staking CGU</li></ul>"

    },
    {
      title: "06 - NFTs",
      content: "<ul className=\"list-disc\"><li>What is an NFT</li><li>What system are NFTs part of?</li><li>What is an NFT associated with?</li><li>What are NFTs made of?</li><li>NFT parameters</li><li>What can NFTs do</li><li>Types of NFTs</li></ul>"

    }
    ,
    {
      title: "Course Materials (Resources)",
      content: "<ul className=\"list-disc\"><li>In collaboration with CGU, Crypto U presents a Crypto basics course for CGU players. <a href=\"https://cryptouniversity.network/wp-content/uploads/2022/06/Course-Reader_-Crypto-Basics-Course-For-CGU-Players-7.pdf\" className=\"text-[#0000FF]\">Crypto Gaming United (CGU)</a> is a platform that brings people from developing countries together to build a new virtual economy and earn a sustainable income, while learning new digital skills and engaging with the global blockchain gaming community. </li><li>Course reader: <a href=\"https://cryptouniversity.network/wp-content/uploads/2022/06/Course-Reader_-Crypto-Basics-Course-For-CGU-Players-7.pdf\" className=\"text-[#0000FF]\">Link</a></li></ul>"
    },
    {
      title: "Assignments",
      content: "<ul className=\"list-disc\"><li>Assignment 1 – Fundamentals</li><li>Assignment 2 – Wallets</li><li>Assignment 3 – DeFi</li></ul>"
    },
    {
      title: "Course Quiz",
      content: "<ul className=\"list-disc\"><li>20 Questions</li></ul>"
    }
  ]
  const followers = [
    {
      url: "https://twitter.com/greybtc",
      icon: "/cgu/icons8-twitterx-48.png",
      name: "26K Followers"
    },
    {
      url: "https://www.youtube.com/c/CryptoHustleLive",
      icon: "/cgu/icons8-youtube-48.png",
      name: "160K Subscribers"
    }
  ]
  const followers2 = [
    {
      url: "https://www.instagram.com/greybtc/",
      icon: "/cgu/icons8-instagram-48.png",
      name: "26K Followers"
    },
    {
      url: "https://www.linkedin.com/in/greyjabesi/",
      icon: "/cgu/icons8-linkedin-48.png",
      name: "LinkedIn"
    }
  ]
  return (
    <>
      <section className="bg-slate-900 p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 pt-12">
          <div className="mt-10">
            <ImageShortcut
              src={'/cgu/CGU-Gamers-Course.png'}
              width={400}
              height={700}
              alt="thumbnail"
              className=" object-contain h-700 w-400"
              priority
            />
          </div>

          <div>
            <h1 className="text-white text-h2 font-bold mb-4 mt-4 ">Crypto Basics For CGU Digizen</h1>
            <p className="text-white pt-4 mb-4">Crypto Global United & Crypto University present, Crypto Basics Course
              For CGU Digizens. Learn
              new digital skills and engage with the global blockchain community.</p>
            <ul className="text-white ml-4">
              <li>
                {
                  headerItems.map(item => (
                    <div key={item.title} className="flex">
                      <div className="flex-none w-9 h-14">
                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="22" fill="none" viewBox="0 0 23 22">
                          <path fill="#00BF77"
                            d="M11.198 22c6.185 0 11.198-4.925 11.198-11S17.383 0 11.198 0C5.013 0 0 4.925 0 11s5.013 11 11.198 11z">
                          </path>
                          <path fill="#00BF77" d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                          <path stroke="#fff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5"
                            d="M5.6 11.55l3.359 3.3 7.838-7.7"></path>
                        </svg>
                      </div>
                      <div className="flex-initial w-90">
                        {item.title}
                      </div>
                    </div>
                  ))
                }

              </li>
            </ul>
            <p className="text-white text-headline my-6">$0</p>
            <Link
              aria-label="View products"
              href={"/courses/crypto-basics-for-cgu-digizen"}
              className="bg-yellow hover:bg-[#00a043]  active:bg-[#017933] rounded-lg text-sub3 font-semibold py-3 px-6 mt-6 transition-colors duration-150"
            >
              Register Now
            </Link>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mx-10 pb-4 mt-10">
          {
            headerDetails.map(header => (
              <div key={header.title} className="h-100 w-100 bg-gray-750 text-white rounded-md">
                <ImageShortcut
                  src={header.image}
                  width={20}
                  height={20}
                  alt="thumbnail"
                  className="mx-2  object-scale-down  h-20 w-20"
                  priority
                />
                <h1 className="py-4 mx-2 text-2xl font-bold">{header.title}</h1>
                <div className="p-2" dangerouslySetInnerHTML={{ __html: header.description }} />
              </div>
            ))
          }


        </div>
      </section>
      <section className="mb-4">
        <div>
          <h1 className="text-center mt-10 text-h3 font-bold">Course Introduction</h1>
        </div>
        <div className="flex justify-center rounded mt-10 p-6">
          <iframe className="rounded" width="720" height="500"  src="https://www.youtube.com/embed/71i3nCpqCwQ" title="Introduction to CGU course" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowFullScreen></iframe>
        </div>
      </section>
      <section className="mb-4 p-6">
        <div>
          <h1 className="text-center mt-10 text-h3 font-bold">Your Instructor</h1>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 m-10">
          <div>
            <ImageShortcut
              src={'/cgu/GreyJabesi.jpeg'}
              width={1280}
              height={720}
              alt="thumbnail"
              className=" object-contain h-80 w-96"
              priority
            />

          </div>
          <div>
            <h1 className="text-headline">Grey Jabesi</h1>
            <p className="mt-10 text-lg">Founded Crypto U in 2018 as a way to help newcomers avoid scams in the
              cryptocurrency space. In addition, we also wanted to provide advanced education for investors,
              traders and professionals.

              Cryptocurrency is the ultimate financial tool. At Crypto U, we aim to educate and empower people all
              over the world through Cryptocurrency and Blockchain knowledge.</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 mt-4 gap-8 ">
              {followers.map(follower => (
                <Link key={follower.icon}
                  aria-label="View products"
                  href={follower.url}
                  className=""
                >
                  <div className="border-gray-600 border rounded-full p-2">
                    <div className="flex justify-center">
                      <div className="flex flex-row ...">
                        <div> <ImageShortcut
                          src={follower.icon}
                          width={5}
                          height={5}
                          alt="thumbnail"
                          className=" object-contain h-5 w-5"
                          priority
                        /></div>
                        <div className="ml-2">{follower.name}</div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 mt-4 gap-8 ">
              {followers2.map(follower => (
                <Link key={follower.icon}
                  aria-label="View products"
                  href={follower.url}
                  className=""
                >
                  <div className="border-gray-600 border rounded-full p-2">
                    <div className="flex justify-center">
                      <div className="flex flex-row ...">
                        <div> <ImageShortcut
                          src={follower.icon}
                          width={5}
                          height={5}
                          alt="thumbnail"
                          className=" object-contain h-5 w-5"
                          priority
                        /></div>
                        <div className="ml-2">{follower.name}</div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>
      <section className="mb-4 p-6">
        <div>
          <h1 className="text-center mt-10 text-h3 font-bold">Course Outline</h1>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 m-10 gap-8">
          <div className="bg-gray-300 rounded-md p-4">
            <div className="flex justify-center">
              <ImageShortcut
                src={'/cgu/CGU-Gamers-Course.png'}
                width={1280}
                height={720}
                alt="thumbnail"
                className=" object-contain h-80 w-96"
                priority
              />
            </div>
            <div className="flex justify-center">
              <Link
                aria-label="View products"
                href={"/courses/crypto-basics-for-cgu-digizen"}
                className="bg-yellow hover:bg-[#00a043] active:bg-[#017933] rounded-lg text-sub3 font-semibold py-3 px-6 mt-4 transition-colors duration-150"
              >
                Register Now
              </Link>
            </div>
          </div>
          <div>
            {courseOutline.map((question, index) => (
              <Disclosure key={index}>
                {({ open }) => (
                  <div
                    key={index}
                    className={cn(
                      'px-6 py-5 max-md:min-w-full max-md:px-[10px] max-md:py-4',
                      index + 1 !== question.title.length && 'border-b border-gray-700',
                      true ? 'max-lg:min-w-full' : ''
                    )}>
                    <div
                      key={index}
                      className="flex w-full flex-col gap-4 bg-white transition-all duration-300 px-6">
                      <Disclosure.Button
                        className={
                          'relative flex w-full cursor-pointer select-none items-center justify-between gap-4 text-left text-sub3 max-md:text-sub4'
                        }>
                        <div
                          className={cn(
                            'max-w-[700px] font-medium text-black transition-[color] duration-300',
                            open && 'text-blue'
                          )}>
                          {question.title}
                        </div>
                        <span
                          className={cn(
                            'flex min-h-[24px] min-w-[24px] flex-col items-center justify-center rounded-full text-white transition-[transform_background] duration-300 ',
                            open ? 'bg-blue' : 'rotate-180 bg-black'
                          )}>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="10"
                            height="7"
                            fill="none"
                            viewBox="0 0 10 7">
                            <path
                              stroke="#fff"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="1.5"
                              d="M9.242 5.5l-4.12-4.083L1 5.5"
                            />
                          </svg>
                        </span>
                      </Disclosure.Button>
                      <Transition
                        show={open}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0">
                        <div className="font-sans text-sub3 transition-[max-height_opacity] duration-300 max-md:text-sub4">
                          <div dangerouslySetInnerHTML={{ __html: question.content }} />
                        </div>
                      </Transition>
                    </div>
                  </div>
                )}
              </Disclosure>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default CGUCourse;