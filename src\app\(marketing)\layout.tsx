import Header from "@/components/Nav/Header"
import Footer from "@/components/Footer/Footer"
import HeaderMobile from "@/components/Nav/HeaderMobile"

interface MarketingLayoutProps {
    children: React.ReactNode
}

export default async function MarketingLayout({ children }: MarketingLayoutProps) {
    return (
        <>
            <Header />
            <HeaderMobile />
            <div className="flex min-h-screen pt-20 flex-col">
                <main className="flex-grow flex">
                    {children}
                </main>
            </div>
            <Footer />
        </>
    )
}