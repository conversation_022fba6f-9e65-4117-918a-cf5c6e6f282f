"use client";
import { useState } from 'react';
import Link from 'next/link';
import Button from '@/components/Ui/Button';

const RiskReversalGuarantee = () => {
  const [activeTab, setActiveTab] = useState(0);

  const guarantees = [
    {
      title: "30-Day Money Back Guarantee",
      icon: "🔒",
      description: "Not making profits within 30 days? Get every penny back. No questions asked.",
      details: [
        "Full refund within 30 days",
        "No questions asked policy",
        "Keep all bonuses even if you refund",
        "24/7 support for refund requests"
      ]
    },
    {
      title: "Profit Guarantee",
      icon: "💰",
      description: "If you don't make at least $1,000 in your first 60 days, we'll work with you until you do.",
      details: [
        "Personal coaching until you profit",
        "Extended access to all materials",
        "One-on-one strategy sessions",
        "Custom portfolio review"
      ]
    },
    {
      title: "Lifetime Support",
      icon: "🚀",
      description: "Get lifetime access to our community and all future updates at no extra cost.",
      details: [
        "Lifetime Discord access",
        "All future course updates",
        "New strategy releases",
        "Market crash protection updates"
      ]
    }
  ];

  const riskComparisons = [
    {
      risk: "Staying broke and missing the crypto boom",
      solution: "Join 47,832+ profitable students",
      icon: "😱"
    },
    {
      risk: "Losing money on bad crypto investments",
      solution: "Follow our proven 97% success rate system",
      icon: "📉"
    },
    {
      risk: "Working a 9-5 job forever",
      solution: "Achieve financial freedom like Mike & Sarah",
      icon: "⏰"
    },
    {
      risk: "Missing out on life-changing wealth",
      solution: "Start building your crypto fortune today",
      icon: "💸"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-40 h-40 bg-green-400/10 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-yellow-400/10 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-60 h-60 bg-blue-400/5 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto relative z-10 px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="bg-green-600 text-white px-8 py-4 rounded-2xl mb-8 border-4 border-yellow-400 animate-pulse inline-block">
            <div className="text-2xl font-black">🔒 ZERO RISK - ALL REWARD!</div>
            <div className="text-lg font-bold">We Remove ALL The Risk For You!</div>
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-black mb-8 leading-tight">
            🛡️ <span className="text-green-400">BULLETPROOF GUARANTEES</span> That Make This <span className="text-yellow-400">100% RISK-FREE!</span>
          </h1>
          
          <div className="bg-yellow-400 text-black p-8 rounded-3xl mb-8 border-4 border-green-600 max-w-4xl mx-auto">
            <p className="text-2xl md:text-3xl font-bold leading-relaxed">
              We&apos;re so confident in our system that we&apos;ll <span className="text-red-600">guarantee your success</span> or give you <span className="text-green-600">every penny back!</span>
            </p>
          </div>
        </div>

        {/* Guarantee Tabs */}
        <div className="mb-16">
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {guarantees.map((guarantee, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(index)}
                className={`px-6 py-3 rounded-2xl font-bold text-lg transition-all ${
                  activeTab === index
                    ? 'bg-yellow-400 text-black border-4 border-green-600'
                    : 'bg-green-600 text-white border-2 border-yellow-400 hover:bg-green-500'
                }`}
              >
                {guarantee.icon} {guarantee.title}
              </button>
            ))}
          </div>

          <div className="bg-black/50 backdrop-blur-sm p-8 rounded-3xl border-4 border-yellow-400">
            <div className="text-center">
              <div className="text-6xl mb-6">{guarantees[activeTab].icon}</div>
              <h3 className="text-3xl font-black text-yellow-400 mb-6">
                {guarantees[activeTab].title}
              </h3>
              <p className="text-2xl font-bold mb-8 text-green-300">
                {guarantees[activeTab].description}
              </p>
              
              <div className="grid md:grid-cols-2 gap-4">
                {guarantees[activeTab].details.map((detail, index) => (
                  <div key={index} className="bg-green-600/20 p-4 rounded-xl border border-green-400">
                    <div className="flex items-center">
                      <span className="text-green-400 mr-3">✅</span>
                      <span className="font-semibold">{detail}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Risk Comparison */}
        <div className="mb-16">
          <h2 className="text-3xl md:text-4xl font-black text-center mb-12">
            🤔 WHAT&apos;S THE <span className="text-red-400">REAL RISK</span> HERE?
          </h2>

          <div className="grid lg:grid-cols-2 gap-8">
            <div className="bg-red-600 p-8 rounded-3xl border-4 border-yellow-400">
              <h3 className="text-2xl font-black text-center mb-8">
                😱 RISK OF DOING NOTHING:
              </h3>
              <div className="space-y-4">
                {riskComparisons.map((item, index) => (
                  <div key={index} className="bg-red-700/50 p-4 rounded-xl border border-red-400">
                    <div className="flex items-start">
                      <span className="text-3xl mr-4">{item.icon}</span>
                      <div>
                        <div className="font-bold text-lg text-red-200">{item.risk}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-green-600 p-8 rounded-3xl border-4 border-yellow-400">
              <h3 className="text-2xl font-black text-center mb-8">
                🚀 REWARD OF TAKING ACTION:
              </h3>
              <div className="space-y-4">
                {riskComparisons.map((item, index) => (
                  <div key={index} className="bg-green-700/50 p-4 rounded-xl border border-green-400">
                    <div className="flex items-start">
                      <span className="text-3xl mr-4">✅</span>
                      <div>
                        <div className="font-bold text-lg text-green-200">{item.solution}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Social Proof for Guarantees */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-800 p-12 rounded-3xl border-4 border-yellow-400 mb-16">
          <div className="text-center">
            <h3 className="text-3xl font-black text-white mb-8">
              🏆 OUR TRACK RECORD SPEAKS FOR ITSELF:
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-yellow-400 text-black p-6 rounded-2xl">
                <div className="text-3xl font-black mb-2">99.7%</div>
                <div className="text-sm font-bold">Satisfaction Rate</div>
              </div>
              <div className="bg-green-600 text-white p-6 rounded-2xl">
                <div className="text-3xl font-black mb-2">47,832+</div>
                <div className="text-sm font-bold">Happy Students</div>
              </div>
              <div className="bg-blue-600 text-white p-6 rounded-2xl">
                <div className="text-3xl font-black mb-2">$2.4M+</div>
                <div className="text-sm font-bold">Student Profits</div>
              </div>
              <div className="bg-red-600 text-white p-6 rounded-2xl">
                <div className="text-3xl font-black mb-2">&lt;0.3%</div>
                <div className="text-sm font-bold">Refund Rate</div>
              </div>
            </div>

            <div className="bg-black/50 backdrop-blur-sm p-6 rounded-2xl border-2 border-yellow-400">
              <p className="text-xl font-bold text-yellow-200">
                💡 <strong>Fun Fact:</strong> Less than 0.3% of our students ask for refunds because our system actually works!
              </p>
            </div>
          </div>
        </div>

        {/* Final CTA with Guarantee */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-green-600 via-green-700 to-green-800 p-12 rounded-3xl border-4 border-yellow-400 relative overflow-hidden">
            {/* Animated elements */}
            <div className="absolute top-4 left-4 animate-bounce">
              <div className="text-4xl">🔒</div>
            </div>
            <div className="absolute top-4 right-4 animate-bounce delay-500">
              <div className="text-4xl">💰</div>
            </div>
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-pulse">
              <div className="text-4xl">🚀</div>
            </div>

            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-black text-white mb-6">
                🛡️ ZERO RISK, MAXIMUM REWARD - GUARANTEED!
              </h3>
              
              <div className="bg-yellow-400 text-black p-6 rounded-2xl mb-8 inline-block">
                <div className="text-2xl font-black">TRY IT RISK-FREE FOR 30 DAYS!</div>
                <div className="text-lg font-bold">If you don&apos;t love it, get every penny back!</div>
              </div>

              <p className="text-2xl text-green-100 mb-8 font-bold max-w-4xl mx-auto">
                You have <span className="text-yellow-400">nothing to lose</span> and <span className="text-yellow-400">everything to gain</span>. 
                Join 47,832+ students who took the leap and changed their lives forever!
              </p>

              <div className="flex flex-col lg:flex-row gap-6 justify-center items-center mb-8">
                <Link href="/register" className="w-full lg:w-auto">
                  <Button className="w-full lg:w-auto px-16 py-8 text-3xl font-black bg-yellow-400 hover:bg-yellow-500 text-black hover:scale-110 transform transition-all shadow-2xl rounded-2xl">
                    🔒 YES! START MY RISK-FREE TRIAL!
                  </Button>
                </Link>
              </div>

              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-black/30 p-4 rounded-xl">
                  <div className="font-bold">🔒 SSL Secured</div>
                  <div>Your data is safe</div>
                </div>
                <div className="bg-black/30 p-4 rounded-xl">
                  <div className="font-bold">⚡ Instant Access</div>
                  <div>Start immediately</div>
                </div>
                <div className="bg-black/30 p-4 rounded-xl">
                  <div className="font-bold">💰 Money Back</div>
                  <div>30-day guarantee</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RiskReversalGuarantee;
