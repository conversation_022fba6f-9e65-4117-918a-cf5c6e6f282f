import { NextResponse } from 'next/server'

export async function GET() {
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.WHOP_BEARER}`,
        },
    }

    const response = await (
        await fetch((process.env.WHOP_PRODUCT_API as string) + 'prod_BaUOMN9EsXukr?expand=plans', options)
    ).json()

    return NextResponse.json(
        {
            icon: {
                src: '/memberships/card1.png',
                alt: 'Alpha Group Monthly Plan',
            },
            billed: {
                text: 'Monthly',
                color: 'white',
            },
            price: {
                text: "500",
                color: 'white',
            },
            sub: {
                text: "Billed <strong>Monthly</strong>",
                color: 'white',
            },
            button: {
                text: "Get 1 Month",
                color: 'white',
            },
            plan: {
                text: 'Flexible Plan',
                color: 'white',
            },
          
            // plans: response.plans[0].direct_link,
            plans: '/checkout/subscription/' + 'alpha-group-monthly' + '?previous=' + '/subscriptions',
        },
        { status: 200 }
    )
}
