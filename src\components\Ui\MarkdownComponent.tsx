import { Markdown, matter } from "@/lib/markdown";

interface Props {
    text: string | <PERSON>uffer
}

const MarkdownComponent = ({ text }: Props) => {
    const matterResult = matter(String(text))
    return (
        <div className="prose-h1:py-1 prose-h1:text-headline prose-h1:max-md:text-cap1
                        prose-h2:py-1 prose-h2:text-sub2 prose-h2:max-md:text-cap2
                        prose-h3:py-1 prose-h3:text-sub3 prose-h3:max-md:text-cap3
                        prose-p:py-1 prose-p:text-sub2 prose-p:max-md:text-cap2 
                        prose-a:py-1 prose-a:text-blue prose-a:hover:text-blue/80
                        prose-li:py-1 prose-li:text-sub2 prose-li:max-md:text-cap2
                        prose-ul:py-1 prose-ul:list-disc prose-ul:list-inside
                        prose-ol:py-1 prose-ol:list-decimal prose-ol:list-inside
                        prose-img:py-3 prose-img:w-full
                        prose-code:py-1 prose-code:bg-gray-100 prose-code:p-1 prose-code:rounded 
                        prose-span:py-1 prose-span:text-sub2 prose-span:max-md:text-cap2
                        prose-pre:py-1 prose-pre:bg-gray-100 prose-pre:p-1 prose-pre:rounded
                        prose-blockquote:py-1 prose-blockquote:border-l-4 prose-blockquote:border-gray-900  prose-blockquote:pl-2
        ">
            <Markdown >
                {matterResult.content}
            </Markdown>
        </div>
    )
}

export default MarkdownComponent