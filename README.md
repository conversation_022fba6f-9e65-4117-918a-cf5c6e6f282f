# Crypto University - E-Learning Platform
This Next.js project provides a comprehensive platform for internal use within the company. Employees can access and utilize various features to enhance their knowledge and skills in the cryptocurrency domain.

## Features

-   **User Authentication**: Employees can create accounts and log in to access personalized features.
-   **Course Catalog**: A wide range of courses on cryptocurrency topics is available for employees to enroll in.
-   **Blog Section**: Internal blog posts provide informative content about the latest trends in the cryptocurrency industry.
-   **Membership Management**: Employees can manage their membership plans, upgrade or cancel subscriptions.
-   **Affiliate Program**: Employees can participate in the affiliate program to earn rewards by referring new users.
-   **Contact**: Internal communication channels are available for employees to reach out to the Crypto University team.
-   **Responsive Design**: The website is designed to provide an optimal viewing experience across different devices.

## Tech Stack

The Crypto University project utilizes the following technologies:

-   **Next.js**: A React framework for building server-side rendered and static websites.
-   **React**: A JavaScript library for building user interfaces.
-   **Tailwind CSS**: A utility-first CSS framework for designing responsive and modern user interfaces.

## Getting Started

Do not forget to copy the env file

    git clone [url]
    npm install
    npm run dev

## Contributing

As the Crypto University project is a private project for internal use within the company, external contributions are not accepted. However, employees are encouraged to contribute to the project by suggesting new features, reporting issues, or providing feedback to the development team.

## License

The Crypto University project is proprietary and confidential to Crypto University. Unauthorized use, reproduction, or distribution of any part of the project is strictly prohibited.
